## Deployment instructions

1. <PERSON>lone the repository
2. Install dependencies
3. Set up environment variables
4. Build the application
5. Deploy the application

### Prerequisites

- Node.js (18 or higher)
- MongoDB (4.4 or higher)

## Docker Build

1. Build the Docker image:
```bash
docker compose -f compose.build.yml build

docker tag lionx-app:latest git.lionx.me/aborady/voting:latest

docker push git.lionx.me/aborady/voting:latest
```


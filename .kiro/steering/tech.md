# Technology Stack

## Core Framework
- **Next.js 15.3.4**: React framework with App Router, TypeScript, and standalone output
- **React 19**: Latest React with concurrent features
- **TypeScript 5**: Strict type checking enabled

## Backend & Database
- **Convex 1.25.2**: Real-time backend-as-a-service with TypeScript
- **NextAuth 4.24.11**: Authentication with credential provider
- **bcryptjs**: Password hashing

## UI & Styling
- **Tailwind CSS 4**: Utility-first CSS framework with custom theme
- **Radix UI**: Headless component primitives for accessibility
- **Shadcn UI**: Component library with custom theme
- **Lucide React**: Icon library
- **Motion 12**: Animation library
- **Custom fonts**: Gotham and Neue Machina font families

## Rich Text & Content
- **Tiptap**: Rich text editor with extensions
- **AI SDK**: OpenAI integration for content generation
- **Markdown support**: markdown-to-jsx, react-markdown

## Development Tools
- **ESLint 9**: Code linting with Next.js config
- **Sentry**: Error monitoring and performance tracking
- **Docker**: Containerization support

## Common Commands

### Development
```bash
# Start development server
bun run dev

# Build for production
bun run build

# Start production server
bun start

# Lint code
bun run lint
```

### Docker Deployment
```bash
# Build and tag image
docker compose -f compose.build.yml build
docker tag lionx-app:latest git.lionx.me/aborady/voting:latest
docker push git.lionx.me/aborady/voting:latest
```

### Sentry Source Maps
```bash
# Upload source maps
npm run sentry:sourcemaps
# or
npm run uploadsm
```

## Environment Requirements
- Node.js 18+
- Package manager: Yarn 1.22.22+ (specified in packageManager)
- Environment variables for Convex, NextAuth, and Sentry configuration
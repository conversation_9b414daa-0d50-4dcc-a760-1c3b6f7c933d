# LionX Product Overview

LionX is a collaborative voting and idea management platform designed for events and team-based activities. The platform enables:

## Core Features
- **User Management**: Role-based access (admin, team lead, team member) with approval workflows
- **Event Management**: Multi-event support with session-based activities
- **Idea Submission**: Teams can submit and present ideas during sessions
- **Voting System**: Comprehensive voting on ideas with scoring (0-10) and comments
- **Quickfire Voting**: Real-time voting on quick ideas/questions
- **Sparks**: Configurable forms for structured data collection
- **Leaderboards**: Individual and team-based scoring across sessions
- **Real-time Presence**: Track online users and activity

## User Roles
- **Admin**: Full system access, user approval, event management
- **Team Lead**: Team management capabilities
- **Team Member**: Idea submission and voting participation

## Key Workflows
1. Users register and await admin approval(When auto approval is off in settings)
2. <PERSON><PERSON> create events and assign users to teams
3. Sessions are created for specific activities (ideas, voting, sparks)
4. Teams submit ideas during active sessions
5. Voting occurs on submitted ideas
6. Leaderboards track performance across sessions
7. Admin submit Quickfires ( Ideas items only admin submits )
8. Users vote on quickfires during active sessions
9. <PERSON><PERSON> can create and manage sparks forms for structured data collection
10. <PERSON><PERSON> dont vote on anything


The platform is designed for structured collaborative events where teams compete through idea generation and peer evaluation.
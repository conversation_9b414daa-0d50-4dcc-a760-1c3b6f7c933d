# Project Structure

## Root Directory
- **convex/**: Backend functions, schema, and data operations
- **src/**: Frontend application code
- **public/**: Static assets (images, fonts, icons)
- **Context/**: Documentation, specs, and analysis files
- **.kiro/**: Kiro-specific configuration and steering rules

## Source Code Organization (`src/`)

### App Router Structure (`src/app/`)
- **admin/**: Admin dashboard and management interfaces
- **user/**: User-facing application pages
- **leaderboard/**: Leaderboard views (individual, teams, sessions)
- **api/**: API routes (auth, backup, generation)
- **login/**, **register/**: Authentication pages
- **print/**: Print-optimized views

### Components (`src/components/`)
- **ui/**: Reusable UI components (Radix-based)
- **auth/**: Authentication-related components
- **editors/**: Rich text editing (Tiptap-based)
- **leaderboard/**: Leaderboard-specific components
- **providers/**: React context providers
- **teams/**: Team management components

### Supporting Directories
- **hooks/**: Custom React hooks
- **lib/**: Utility functions and configurations
- **styles/**: CSS utilities and animations
- **types/**: TypeScript type definitions

## Convex Backend (`convex/`)
- **schema.ts**: Database schema definitions
- **[feature].ts**: Feature-specific functions (users, events, ideas, votes, etc.)
- **_generated/**: Auto-generated API and type definitions

## Key Conventions

### File Naming
- React components: PascalCase (e.g., `UserManagement.tsx`)
- Utility files: camelCase (e.g., `useDebounce.ts`)
- API routes: lowercase with hyphens (e.g., `backup/create/route.ts`)

### Component Organization
- Each major feature has its own component directory
- Shared UI components in `src/components/ui/`
- Feature-specific components in feature subdirectories
- Error boundaries for major sections (Admin, User)

### Convex Functions
- Grouped by domain (users, events, sessions, votes, etc.)
- Consistent naming: `get[Entity]`, `create[Entity]`, `update[Entity]`
- Proper indexing for query performance

### Styling Approach
- Tailwind CSS with custom theme variables
- CSS custom properties for font scaling
- Theme-aware background switching
- Utility classes in separate CSS files

### Authentication Flow
- NextAuth with credential provider
- Role-based access control (admin, teamLead, teamMember)
- Event-based user registration and team assignment
- Session management with JWT tokens

## Documentation Structure (`Context/`)
- **Analysis/**: Code quality and performance analysis
- **Docs/**: Technical documentation
- **PRD/**: Product requirement documents
- **Specs/**: Feature specifications and implementation plans
- **TASKS/**: Task breakdowns and implementation guides
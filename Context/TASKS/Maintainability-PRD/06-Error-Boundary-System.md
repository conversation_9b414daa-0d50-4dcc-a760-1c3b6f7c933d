# Task 2.1: Base Error Boundary System Implementation

## Task Information
- **Task ID**: MAINT-06
- **Title**: Base Error Boundary System Implementation
- **Priority**: High (Phase 2)
- **Category**: Error Boundary Enhancement
- **Duration**: 2 days
- **Dependencies**: None

## Description
Create a centralized error boundary system that provides consistent error handling across the entire LionX application. This system will replace the current inconsistent error boundary implementations with a unified, configurable approach.

## Current State Analysis
- **Existing Error Boundaries**: 
  - UserErrorBoundary (123 lines) - well-implemented with auth handling
  - AdminErrorBoundary (125 lines) - comprehensive admin coverage
  - BackupErrorBoundary (51 lines) - specialized backup errors
  - react-error-boundary integration in leaderboard components
- **Issues**:
  - Inconsistent error boundary types (custom vs react-error-boundary)
  - No centralized ErrorFallback component
  - Missing standardized error context
  - Limited component-level granular boundaries

## Target Architecture
```
components/error-boundaries/
├── BaseErrorBoundary.tsx         // Core error boundary component
├── withErrorBoundary.tsx         // HOC wrapper
├── ErrorFallback.tsx             // Standardized fallback UI
├── ErrorTrackingProvider.tsx     // Error tracking context
├── errorBoundaryUtils.ts         // Utility functions
├── types.ts                      // TypeScript types
└── index.ts                      // Export barrel
```

## Specific Requirements

### 1. BaseErrorBoundary Component
- **Purpose**: Core error boundary with configurable behavior
- **Features**:
  - Configurable error categorization
  - Automatic error reporting to Sentry
  - Retry mechanisms with exponential backoff
  - Development vs production error displays
  - Context-aware error messages
  - Custom fallback UI support

### 2. withErrorBoundary HOC
- **Purpose**: Higher-order component for easy error boundary wrapping
- **Features**:
  - Automatic component name detection
  - Configurable error boundary options
  - Props forwarding and ref support
  - TypeScript generic support

### 3. ErrorFallback Component
- **Purpose**: Standardized fallback UI for all error scenarios
- **Features**:
  - Consistent error messaging
  - Retry functionality
  - Contact support options
  - Accessibility compliance
  - Context-aware error details

### 4. ErrorTrackingProvider
- **Purpose**: Global error tracking and context management
- **Features**:
  - Error categorization
  - Error frequency tracking
  - User context preservation
  - Error reporting integration

## Technical Specifications

### BaseErrorBoundary Implementation
```typescript
// components/error-boundaries/BaseErrorBoundary.tsx
interface BaseErrorBoundaryProps {
  children: React.ReactNode;
  fallback?: React.ComponentType<ErrorFallbackProps>;
  onError?: (error: Error, errorInfo: React.ErrorInfo) => void;
  context?: string;
  retryable?: boolean;
  maxRetries?: number;
  isolate?: boolean;
}

interface BaseErrorBoundaryState {
  hasError: boolean;
  error?: Error;
  errorInfo?: React.ErrorInfo;
  retryCount: number;
  errorId: string;
}

export class BaseErrorBoundary extends React.Component<
  BaseErrorBoundaryProps,
  BaseErrorBoundaryState
> {
  private retryTimeoutId?: NodeJS.Timeout;

  constructor(props: BaseErrorBoundaryProps) {
    super(props);
    this.state = {
      hasError: false,
      retryCount: 0,
      errorId: generateErrorId()
    };
  }

  static getDerivedStateFromError(error: Error): Partial<BaseErrorBoundaryState> {
    return {
      hasError: true,
      error,
      errorId: generateErrorId()
    };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    this.setState({ errorInfo });
    
    // Enhanced error reporting
    this.reportError(error, errorInfo);
    
    // Call custom error handler
    this.props.onError?.(error, errorInfo);
  }

  private reportError = (error: Error, errorInfo: React.ErrorInfo) => {
    const errorReport = {
      error,
      errorInfo,
      context: this.props.context,
      retryCount: this.state.retryCount,
      errorId: this.state.errorId,
      timestamp: new Date().toISOString(),
      userAgent: navigator.userAgent,
      url: window.location.href
    };

    // Report to Sentry in production
    if (process.env.NODE_ENV === 'production') {
      Sentry.captureException(error, {
        contexts: {
          react: {
            componentStack: errorInfo.componentStack,
            context: this.props.context || 'unknown'
          },
          errorBoundary: {
            retryCount: this.state.retryCount,
            errorId: this.state.errorId
          }
        }
      });
    }

    // Log to console in development
    if (process.env.NODE_ENV === 'development') {
      console.error('Error Boundary caught an error:', errorReport);
    }
  };

  private handleRetry = () => {
    const { maxRetries = 3 } = this.props;
    
    if (this.state.retryCount < maxRetries) {
      this.setState(prevState => ({
        hasError: false,
        error: undefined,
        errorInfo: undefined,
        retryCount: prevState.retryCount + 1
      }));
    }
  };

  render() {
    if (this.state.hasError) {
      const FallbackComponent = this.props.fallback || ErrorFallback;
      
      return (
        <FallbackComponent
          error={this.state.error}
          errorInfo={this.state.errorInfo}
          context={this.props.context}
          onRetry={this.props.retryable ? this.handleRetry : undefined}
          retryCount={this.state.retryCount}
          maxRetries={this.props.maxRetries}
          errorId={this.state.errorId}
        />
      );
    }

    return this.props.children;
  }
}
```

### withErrorBoundary HOC Implementation
```typescript
// components/error-boundaries/withErrorBoundary.tsx
interface ErrorBoundaryOptions {
  fallback?: React.ComponentType<ErrorFallbackProps>;
  onError?: (error: Error, errorInfo: React.ErrorInfo) => void;
  context?: string;
  retryable?: boolean;
  maxRetries?: number;
  isolate?: boolean;
}

export const withErrorBoundary = <P extends object>(
  Component: React.ComponentType<P>,
  options: ErrorBoundaryOptions = {}
) => {
  const WrappedComponent = React.forwardRef<any, P>((props, ref) => {
    const context = options.context || 
      Component.displayName || 
      Component.name || 
      'UnknownComponent';

    return (
      <BaseErrorBoundary
        {...options}
        context={context}
      >
        <Component {...props} ref={ref} />
      </BaseErrorBoundary>
    );
  });

  WrappedComponent.displayName = `withErrorBoundary(${
    Component.displayName || Component.name || 'Component'
  })`;

  return WrappedComponent;
};
```

### ErrorFallback Component
```typescript
// components/error-boundaries/ErrorFallback.tsx
interface ErrorFallbackProps {
  error?: Error;
  errorInfo?: React.ErrorInfo;
  context?: string;
  onRetry?: () => void;
  retryCount?: number;
  maxRetries?: number;
  errorId?: string;
}

export const ErrorFallback: React.FC<ErrorFallbackProps> = ({
  error,
  errorInfo,
  context,
  onRetry,
  retryCount = 0,
  maxRetries = 3,
  errorId
}) => {
  const canRetry = onRetry && retryCount < maxRetries;

  return (
    <div className="error-fallback">
      <div className="error-content">
        <h2>Something went wrong</h2>
        <p>
          {context ? `An error occurred in ${context}` : 'An unexpected error occurred'}
        </p>
        
        {process.env.NODE_ENV === 'development' && (
          <details className="error-details">
            <summary>Error Details</summary>
            <pre>{error?.message}</pre>
            <pre>{error?.stack}</pre>
          </details>
        )}
        
        <div className="error-actions">
          {canRetry && (
            <button 
              onClick={onRetry}
              className="retry-button"
            >
              Try Again ({maxRetries - retryCount} attempts remaining)
            </button>
          )}
          
          <button 
            onClick={() => window.location.reload()}
            className="reload-button"
          >
            Reload Page
          </button>
        </div>
        
        {errorId && (
          <p className="error-id">
            Error ID: {errorId}
          </p>
        )}
      </div>
    </div>
  );
};
```

## Deliverables

### New Files to Create
1. `src/components/error-boundaries/BaseErrorBoundary.tsx` (150 lines)
2. `src/components/error-boundaries/withErrorBoundary.tsx` (80 lines)
3. `src/components/error-boundaries/ErrorFallback.tsx` (120 lines)
4. `src/components/error-boundaries/ErrorTrackingProvider.tsx` (100 lines)
5. `src/components/error-boundaries/errorBoundaryUtils.ts` (80 lines)
6. `src/components/error-boundaries/types.ts` (60 lines)
7. `src/components/error-boundaries/index.ts` (30 lines)

### Directory Structure
```
src/components/error-boundaries/
├── BaseErrorBoundary.tsx
├── withErrorBoundary.tsx
├── ErrorFallback.tsx
├── ErrorTrackingProvider.tsx
├── errorBoundaryUtils.ts
├── types.ts
└── index.ts
```

## Acceptance Criteria

### Functionality Requirements
- [ ] **100% functional parity** - All existing error handling preserved
- [ ] BaseErrorBoundary handles all error types correctly
- [ ] withErrorBoundary HOC works with all component types
- [ ] ErrorFallback provides consistent UI across all scenarios
- [ ] Error reporting to Sentry functions correctly
- [ ] Retry mechanisms work as expected
- [ ] Development vs production error displays work

### Technical Requirements
- [ ] TypeScript strict mode compliance
- [ ] All prop types properly defined
- [ ] Generic type support for HOC
- [ ] Ref forwarding works correctly
- [ ] Error boundary nesting handled properly
- [ ] Memory leaks prevented
- [ ] Performance optimized

### Error Handling Requirements
- [ ] All error types caught and handled
- [ ] Error categorization works correctly
- [ ] Context preservation during errors
- [ ] Error reporting includes relevant metadata
- [ ] Retry logic with exponential backoff
- [ ] Error frequency tracking
- [ ] User session preservation

### Code Quality Requirements
- [ ] No code duplication
- [ ] Consistent naming conventions
- [ ] Proper component composition
- [ ] Comprehensive JSDoc comments
- [ ] Clean separation of concerns
- [ ] Reusable utility functions

### Testing Requirements
- [ ] Unit tests for BaseErrorBoundary
- [ ] Unit tests for withErrorBoundary HOC
- [ ] Integration tests for ErrorFallback
- [ ] Tests for error reporting
- [ ] Tests for retry mechanisms
- [ ] Tests for context preservation
- [ ] Accessibility tests for error UI

## Dependencies
- React 19 error boundary features
- Sentry error reporting
- Existing error handling patterns
- Toast notification system

## Risk Assessment
- **Risk Level**: Low
- **Main Risks**:
  - Breaking existing error handling
  - Performance impact from additional wrappers
  - Sentry integration issues
- **Mitigation**:
  - Comprehensive testing
  - Performance monitoring
  - Gradual rollout
  - Backup plan available

## Success Metrics
- **Consistency**: 100% standardized error handling
- **Coverage**: All components wrapped with error boundaries
- **Reliability**: 95% error recovery rate
- **Performance**: No measurable performance impact
- **Developer Experience**: Simplified error boundary usage

## Implementation Notes
- Maintain backward compatibility with existing error boundaries
- Ensure proper cleanup for retry mechanisms
- Implement proper accessibility for error UI
- Consider mobile-specific error handling
- Ensure error boundaries don't interfere with React DevTools
- Implement proper error boundary testing utilities
# Task 1.5: Voting.tsx Component Refactoring

## Task Information
- **Task ID**: MAINT-05
- **Title**: Voting.tsx Component Refactoring
- **Priority**: Secondary (Priority 2)
- **Category**: Component Complexity Reduction
- **Duration**: 3 days
- **Dependencies**: None

## Description
Refactor the large Voting.tsx component (403 lines) into smaller, focused components with better separation of concerns. This component currently handles real-time voting, team navigation, and vote synchronization in a single file.

## Current State Analysis
- **File**: `src/app/user/components/Voting.tsx`
- **Current Size**: 403 lines
- **Functions**: 8+ callback functions + optimized handlers
- **State Variables**: 5+ state variables for voting state
- **useEffect Hooks**: 4 hooks with complex debouncing and cleanup
- **Complexity**: High - Real-time voting, debounced submissions, optimized data handling, team navigation

## Target Architecture
```
Voting (60 lines) - Main orchestrator
├── VoteCard (80 lines) - Individual vote display and interaction
├── TeamVoteGroup (60 lines) - Team-based vote grouping
├── VotingControls (50 lines) - Voting controls and navigation
├── VoteProgress (40 lines) - Progress indicators
├── useVoteSync() (70 lines) - Vote synchronization hook
├── useVoteNavigation() (50 lines) - Team navigation hook
└── voteUtils.ts (40 lines) - Utility functions
```

## Specific Requirements

### 1. Extract UI Components
- **VoteCard**: Individual vote display with score and comments
- **TeamVoteGroup**: Group votes by team with team information
- **VotingControls**: Navigation controls and voting interface
- **VoteProgress**: Progress indicators and voting statistics

### 2. Create Custom Hooks
- **useVoteSync**: Vote synchronization and real-time updates
  - Real-time vote subscription
  - Debounced vote submission
  - Optimistic updates
  - Error handling and retry logic
- **useVoteNavigation**: Team navigation and filtering
  - Team switching logic
  - Vote filtering by team
  - Navigation state management

### 3. Utility Functions
- **voteUtils.ts**: Helper functions
  - Vote validation
  - Score calculations
  - Team filtering utilities
  - Progress calculations

## Technical Specifications

### Component Structure
```typescript
// New structure for Voting.tsx
export const Voting = () => {
  const {
    votes,
    isLoading,
    error,
    submitVote,
    updateVote,
    deleteVote,
    isSubmitting
  } = useVoteSync();

  const {
    currentTeam,
    teams,
    filteredVotes,
    switchTeam,
    canNavigate
  } = useVoteNavigation(votes);

  if (isLoading) return <div>Loading votes...</div>;
  if (error) return <div>Error loading votes: {error}</div>;

  return (
    <div className="voting-container">
      <VotingControls 
        currentTeam={currentTeam}
        teams={teams}
        onTeamSwitch={switchTeam}
        canNavigate={canNavigate}
      />
      
      <VoteProgress 
        votes={filteredVotes}
        currentTeam={currentTeam}
      />
      
      <div className="votes-grid">
        {filteredVotes.map((vote) => (
          <VoteCard 
            key={vote._id}
            vote={vote}
            onVoteSubmit={submitVote}
            onVoteUpdate={updateVote}
            onVoteDelete={deleteVote}
            isSubmitting={isSubmitting}
          />
        ))}
      </div>
    </div>
  );
};
```

### Custom Hook Patterns
```typescript
// hooks/useVoteSync.ts
export const useVoteSync = () => {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [pendingVotes, setPendingVotes] = useState<Map<string, VoteData>>(new Map());
  
  const votes = useQuery(api.votes.getVotesForCurrentSession);
  const submitVoteMutation = useMutation(api.votes.submitVote);
  const updateVoteMutation = useMutation(api.votes.updateVote);
  const deleteVoteMutation = useMutation(api.votes.deleteVote);

  // Debounced submission with optimistic updates
  const debouncedSubmit = useDebounce(async (voteData: VoteData) => {
    setIsSubmitting(true);
    try {
      await submitVoteMutation(voteData);
      setPendingVotes(prev => {
        const newMap = new Map(prev);
        newMap.delete(voteData.ideaId);
        return newMap;
      });
      toast.success('Vote submitted successfully');
    } catch (error) {
      setError('Failed to submit vote');
      toast.error('Failed to submit vote');
    } finally {
      setIsSubmitting(false);
    }
  }, 1000);

  const submitVote = useCallback((voteData: VoteData) => {
    // Optimistic update
    setPendingVotes(prev => new Map(prev).set(voteData.ideaId, voteData));
    debouncedSubmit(voteData);
  }, [debouncedSubmit]);

  return {
    votes,
    isLoading: votes === undefined,
    error,
    submitVote,
    updateVote: updateVoteMutation,
    deleteVote: deleteVoteMutation,
    isSubmitting,
    pendingVotes
  };
};
```

## Deliverables

### New Files to Create
1. `src/components/user/voting/VoteCard.tsx` (80 lines)
2. `src/components/user/voting/TeamVoteGroup.tsx` (60 lines)
3. `src/components/user/voting/VotingControls.tsx` (50 lines)
4. `src/components/user/voting/VoteProgress.tsx` (40 lines)
5. `src/hooks/useVoteSync.ts` (70 lines)
6. `src/hooks/useVoteNavigation.ts` (50 lines)
7. `src/utils/voteUtils.ts` (40 lines)

### Modified Files
1. `src/app/user/components/Voting.tsx` (403 → 60 lines)

## Acceptance Criteria

### Functionality Requirements
- [ ] **100% functional parity** - All existing functionality preserved
- [ ] Real-time voting works identically
- [ ] Team navigation functions correctly
- [ ] Vote synchronization maintains performance
- [ ] Debounced submissions work as expected
- [ ] Optimistic updates function correctly
- [ ] Error handling and retry logic preserved
- [ ] Progress indicators work correctly

### Technical Requirements
- [ ] Component size reduced by 63% (403 → <150 lines total)
- [ ] Each component < 80 lines
- [ ] Custom hooks properly manage state
- [ ] TypeScript strict mode compliance
- [ ] All prop types properly defined
- [ ] Error boundaries implemented
- [ ] Proper loading states implemented

### Performance Requirements
- [ ] Real-time updates maintain performance
- [ ] Debounced submissions work efficiently
- [ ] Optimistic updates don't cause UI glitches
- [ ] Team switching remains fast
- [ ] Vote filtering performs well
- [ ] Memory usage optimized

### Vote Management Requirements
- [ ] Vote submission with scoring (0-10) works
- [ ] Comment functionality preserved
- [ ] Vote editing and deletion work
- [ ] Team-based vote filtering works
- [ ] Real-time vote updates function correctly
- [ ] Conflict resolution for concurrent votes

### Code Quality Requirements
- [ ] No code duplication between components
- [ ] Consistent naming conventions
- [ ] Proper component composition
- [ ] Reusable utility functions
- [ ] Clean separation of concerns
- [ ] Comprehensive JSDoc comments

### Testing Requirements
- [ ] Unit tests for useVoteSync hook
- [ ] Unit tests for useVoteNavigation hook
- [ ] Integration tests for each component
- [ ] End-to-end tests for voting workflows
- [ ] Tests for real-time synchronization
- [ ] Tests for team navigation
- [ ] Performance tests for debounced operations
- [ ] Accessibility tests for all components

## Dependencies
- Existing Convex vote management functions
- Current authentication system
- Real-time data synchronization
- Team management system
- Debounce utilities

## Risk Assessment
- **Risk Level**: Medium
- **Main Risks**:
  - Breaking existing voting functionality
  - Performance issues with real-time updates
  - Loss of vote synchronization
  - Team navigation problems
- **Mitigation**:
  - Comprehensive testing of all voting features
  - Performance monitoring for real-time updates
  - Gradual rollout with feature flags
  - Backup plan to revert changes

## Success Metrics
- **Size Reduction**: 403 lines → <150 lines (63% reduction)
- **Function Count**: 8+ functions → 5-8 functions per component
- **State Variables**: 5+ variables → 3-5 per component
- **Hook Count**: 4 hooks → 2 focused hooks
- **Maintainability**: Improved developer experience
- **Test Coverage**: 100% coverage for new components

## Implementation Notes
- Maintain backward compatibility with existing props
- Preserve all existing keyboard shortcuts
- Keep consistent styling and animations
- Ensure proper cleanup on component unmount
- Implement proper error handling for each operation
- Add proper loading states for async operations
- Consider performance implications of real-time updates
- Maintain debounced submission logic exactly as is
- Preserve optimistic updates functionality
- Ensure team navigation works seamlessly
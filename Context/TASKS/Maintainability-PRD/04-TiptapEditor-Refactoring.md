# Task 1.4: TiptapEditor.tsx Component Refactoring

## Task Information
- **Task ID**: MAINT-04
- **Title**: TiptapEditor.tsx Component Refactoring
- **Priority**: Secondary (Priority 2)
- **Category**: Component Complexity Reduction
- **Duration**: 3 days
- **Dependencies**: None

## Description
Refactor the large TiptapEditor.tsx component (595 lines) into smaller, focused components with better separation of concerns. This component currently handles rich text editing, toolbar management, AI integration, and snippets in a single file.

## Current State Analysis
- **File**: `src/components/editors/TiptapEditor.tsx`
- **Current Size**: 595 lines
- **Functions**: 8+ callbacks + toolbar functions
- **State Variables**: 6+ state variables for editor state
- **useEffect Hooks**: 2 hooks for content synchronization
- **Complexity**: High - Rich text editor with toolbar, AI integration, snippets, content formatting

## Target Architecture
```
TiptapEditor (80 lines) - Main orchestrator
├── EditorToolbar (120 lines) - Toolbar with formatting controls
├── EditorContent (60 lines) - Main editor content area
├── AISelector (80 lines) - AI integration and suggestions
├── SnippetsMenu (60 lines) - Predefined snippets
├── FormatControls (70 lines) - Text formatting controls
├── useEditor() (70 lines) - Editor state management hook
└── editorUtils.ts (40 lines) - Utility functions
```

## Specific Requirements

### 1. Extract UI Components
- **EditorToolbar**: Main toolbar with all formatting controls
- **EditorContent**: Core editor content area with proper focus management
- **AISelector**: AI integration for content suggestions and improvements
- **SnippetsMenu**: Predefined snippets for quick insertion
- **FormatControls**: Text formatting (bold, italic, lists, etc.)

### 2. Create Custom Hooks
- **useEditor**: Editor state management
  - Editor instance management
  - Content synchronization
  - Formatting state tracking
  - Auto-save functionality
- **useAIIntegration**: AI features management
  - AI suggestions
  - Content improvement
  - Error handling

### 3. Utility Functions
- **editorUtils.ts**: Helper functions
  - Content validation
  - Format conversion
  - Snippet management
  - Export utilities

## Technical Specifications

### Component Structure
```typescript
// New structure for TiptapEditor.tsx
export const TiptapEditor = ({ 
  content, 
  onChange, 
  placeholder,
  autosave = false 
}: TiptapEditorProps) => {
  const {
    editor,
    isLoading,
    error,
    updateContent,
    getContent,
    setContent
  } = useEditor({
    content,
    onChange,
    autosave
  });

  const {
    aiSuggestions,
    isGenerating,
    generateContent,
    improveContent
  } = useAIIntegration(editor);

  if (!editor) return <div>Loading editor...</div>;

  return (
    <div className="tiptap-editor">
      <EditorToolbar 
        editor={editor}
        onAIToggle={() => setShowAI(!showAI)}
        onSnippetsToggle={() => setShowSnippets(!showSnippets)}
      />
      
      <div className="editor-content-area">
        <EditorContent 
          editor={editor}
          placeholder={placeholder}
          className="prose max-w-none"
        />
        
        {showAI && (
          <AISelector 
            editor={editor}
            suggestions={aiSuggestions}
            onGenerate={generateContent}
            onImprove={improveContent}
            isGenerating={isGenerating}
          />
        )}
        
        {showSnippets && (
          <SnippetsMenu 
            editor={editor}
            onInsert={insertSnippet}
          />
        )}
      </div>
    </div>
  );
};
```

### Custom Hook Patterns
```typescript
// hooks/useEditor.ts
export const useEditor = ({ content, onChange, autosave }: UseEditorProps) => {
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const debouncedOnChange = useDebounce(onChange, 500);

  const editor = useReactEditor({
    extensions: [
      StarterKit,
      Placeholder.configure({
        placeholder: 'Start typing...'
      }),
      // Other extensions
    ],
    content,
    onUpdate: ({ editor }) => {
      const html = editor.getHTML();
      if (autosave) {
        debouncedOnChange(html);
      } else {
        onChange(html);
      }
    },
    onCreate: () => setIsLoading(false),
    onError: (error) => setError(error.message)
  });

  const updateContent = useCallback((newContent: string) => {
    if (editor && !editor.isDestroyed) {
      editor.commands.setContent(newContent);
    }
  }, [editor]);

  const getContent = useCallback(() => {
    return editor?.getHTML() || '';
  }, [editor]);

  return {
    editor,
    isLoading,
    error,
    updateContent,
    getContent,
    setContent: updateContent
  };
};
```

## Deliverables

### New Files to Create
1. `src/components/editors/EditorToolbar.tsx` (120 lines)
2. `src/components/editors/EditorContent.tsx` (60 lines)
3. `src/components/editors/AISelector.tsx` (80 lines)
4. `src/components/editors/SnippetsMenu.tsx` (60 lines)
5. `src/components/editors/FormatControls.tsx` (70 lines)
6. `src/hooks/useEditor.ts` (70 lines)
7. `src/hooks/useAIIntegration.ts` (50 lines)
8. `src/utils/editorUtils.ts` (40 lines)

### Modified Files
1. `src/components/editors/TiptapEditor.tsx` (595 → 80 lines)

## Acceptance Criteria

### Functionality Requirements
- [ ] **100% functional parity** - All existing functionality preserved
- [ ] Rich text editing works identically
- [ ] Toolbar controls function correctly
- [ ] AI integration maintains functionality
- [ ] Snippets menu works as expected
- [ ] Auto-save functionality preserved
- [ ] Content synchronization works
- [ ] All keyboard shortcuts maintained

### Technical Requirements
- [ ] Component size reduced by 66% (595 → <200 lines total)
- [ ] Each component < 120 lines
- [ ] Custom hooks properly manage state
- [ ] TypeScript strict mode compliance
- [ ] All prop types properly defined
- [ ] Error boundaries implemented
- [ ] Proper loading states implemented

### Editor Requirements
- [ ] All Tiptap extensions work correctly
- [ ] Formatting controls maintain functionality
- [ ] Content validation works
- [ ] Export functionality preserved
- [ ] Placeholder behavior maintained
- [ ] Focus management works correctly

### AI Integration Requirements
- [ ] AI suggestions continue working
- [ ] Content improvement features preserved
- [ ] Error handling for AI operations
- [ ] Loading states for AI operations
- [ ] Rate limiting handled correctly

### Code Quality Requirements
- [ ] No code duplication between components
- [ ] Consistent naming conventions
- [ ] Proper component composition
- [ ] Reusable utility functions
- [ ] Clean separation of concerns
- [ ] Comprehensive JSDoc comments

### Testing Requirements
- [ ] Unit tests for useEditor hook
- [ ] Unit tests for useAIIntegration hook
- [ ] Integration tests for each component
- [ ] End-to-end tests for editor workflows
- [ ] Tests for AI integration features
- [ ] Tests for snippet functionality
- [ ] Accessibility tests for all components

## Dependencies
- Tiptap editor library
- AI integration service
- Existing snippet system
- Content validation utilities
- Auto-save functionality

## Risk Assessment
- **Risk Level**: Medium
- **Main Risks**:
  - Breaking existing editor functionality
  - Loss of AI integration features
  - Performance issues with editor splitting
  - Snippet functionality problems
- **Mitigation**:
  - Comprehensive testing of all editor features
  - Performance monitoring
  - Gradual rollout with feature flags
  - Backup plan to revert changes

## Success Metrics
- **Size Reduction**: 595 lines → <200 lines (66% reduction)
- **Function Count**: 8+ functions → 5-8 functions per component
- **State Variables**: 6+ variables → 3-5 per component
- **Hook Count**: 2 hooks → 2 focused hooks
- **Maintainability**: Improved developer experience
- **Test Coverage**: 100% coverage for new components

## Implementation Notes
- Maintain backward compatibility with existing props
- Preserve all existing keyboard shortcuts
- Keep consistent styling and animations
- Ensure proper cleanup on component unmount
- Implement proper error handling for each operation
- Add proper loading states for async operations
- Consider editor performance with component splitting
- Maintain AI integration functionality exactly as is
- Preserve all Tiptap extensions and their configurations
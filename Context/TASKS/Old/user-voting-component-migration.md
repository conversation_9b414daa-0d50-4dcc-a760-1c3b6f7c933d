# User Voting Component Migration

The goal of this task is to migrate the user voting component to the new architecture. The user voting component is located in `/src/app/user/components/Voting.tsx`. The component is using the legacy css architecture with modular css files. The goal is to migrate the component to Tailwind + shadcn/ui.

## Task overview

1. the compoenent must follow the styling patterns established in [ideas component](/src/app/user/components/Ideas.tsx) and [sparks component](/src/app/user/components/SparksComponent.tsx).
2. the component must use the [State-Card](/src/components/ui/state-card.tsx) component for loading and error states.
3. use of `shadcn/ui` components for forms and modals.
4. use of `convex/react` for data fetching and mutations.
5. use of `react-hook-form` for form validation.
6. use of `zod` for form schema validation.
7. use of `react-icons` for icons.
8. use of `react-toastify` for toast notifications.
9. use of `react-use` for debouncing.

## GOAL IS TO MAINTAIN 100% FUNCTIONALITY
with following modern patterns and conventions.

References 
1. [tailwind implementation guide](/Specs/styling/tailwind-implementation.md)
2. [admin components migration progress](/Specs/styling/component-progress-detailed.md)
3. [admin components implementation](/src/app/admin/components/)
4. [ideas component](/src/app/user/components/Ideas.tsx)
5. [sparks component](/src/app/user/components/SparksComponent.tsx)
6. [React 19 Patterns](/Docs/React19/React19-EcoSystem.md)


## 🎨 CRITICAL STYLING GUIDELINES

### **📋 MANDATORY DESIGN SYSTEM COMPLIANCE**

**ALL COMPONENT REFINEMENTS MUST FOLLOW THESE ESTABLISHED PATTERNS:**

#### **Primary Reference Documents:**
- **📖 [Tailwind Implementation Guide](/Specs/styling/tailwind-implementation.md)** - Complete refactoring patterns and conventions
- **📖 [User Section Migration PRD](/PRDs/user-section-tailwind-migration.md)** - Detailed migration strategy and component patterns

#### **🚨 NON-NEGOTIABLE DESIGN RULES:**
1. **ZERO BORDER RADIUS** - `rounded-none` or no rounding classes (`--radius: 0rem` globally)
2. **shadcn/ui Components Only** - Use Input, Button, Checkbox, AlertDialog, Card, Select, Textarea
3. **Established Color Variables** - `text-primary`, `bg-secondary`, `border-border`, etc.
4. **Responsive Patterns** - `flex flex-col sm:flex-row sm:items-center gap-4 p-4`
5. **Consistent Spacing** - `space-y-6`, `gap-4`, `p-8` patterns from admin section



# VERY IMPORTANT FOR THE SLIDER:
1. The slider is using a custom component that is located in `/src/components/selectors/Slider.tsx`. You must preserve the functionality of the slider but changing it to @shadcn/ui [slider](/src/components/ui/slider.tsx). we will style it later to exactly match the existing one.
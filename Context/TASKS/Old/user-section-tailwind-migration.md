# User Section Tailwind Migration Task

Following our admin section migration from pure css to Tailwind + shadcn/ui, we need to migrate the user section to the same architecture. The user section is still using the legacy css architecture with modular css files. The goal is to migrate the user section to Tailwind + shadcn/ui.

## Task overview

All [admin compoenents](/src/app/admin/components/) have been migrated to Tailwind + shadcn/ui. The user section is still using the legacy css architecture with modular css files. the user [layout](/src/app/user/layout.tsx) is importing the main css file `/src/styles/user.css`. The user section is composed of the following components:

- [Ideas](/src/app/user/components/Ideas.tsx)
- [Voting](/src/app/user/components/Voting.tsx)
- [Quickfire voting](/src/app/user/components/QuickfireVoting.tsx)
- [Sparks](/src/app/user/components/SparksComponent.tsx)
- [Sparks special compoenents](/src/app/user/components/sparks/) `special attention to this folder files`

Each of these components has its own css file that is imported in the `user.css` file. The goal is to migrate each of these components to Tailwind + shadcn/ui including the layout and page and remove the css file.

## Implementation framework/Specs:

1. check the [tailwind implementation guide](/Specs/styling/tailwind-implementation.md) for the established patterns and conventions.
2. check the [admin components migration progress](/Specs/styling/component-progress-detailed.md) for the detailed migration progress of the admin components.
3. check the [admin components implementation](/src/app/admin/components/) for the implementation of the admin components.


## Very important RULES TO BE MANDATORY

1. NO BORDER RADIUS ON ANYTHING IN MY APP
2. Use shadcn/ui compoenents for forms and modals as in admin compoenents.
3. ALWAYS use built in variants as in admin components and global variables as much as possible NO DESTRUCTIVE STYLING unless absolutely required.



## TASK OVERVIEW

 - [Ideas compoenent](/src/app/user/components/Ideas.tsx) and [Sparks compoenent](/src/app/user/components/SparksComponent.tsx) have been migrated to tailwind and shadcn/ui.
 - we need to make sure these compoenents are using modern archticture like the @src/app/user/layout.tsx and @src/app/user/page.tsx
 - we need to we need to see how to utilise skeleton in these compoenents @src/components/ui/skeleton.tsx for better user experience
 - we need to make sure the compoenents are using the latest react patterns and best practices.
 - This is including Sparks sub compoenents found in `@src/app/user/components/sparks/`please list this folder and read all files.
 
## Your Role

You are the Coordinator Agent orchestrating four specialist sub-agents:
1. Architect Agent – designs high-level approach. This agent comes out with the Ideal way to implement with modern archticture and best practices, this agent also can search the web to find best practices and approaches in 2025.
2. Research Agent – gathers `Extensive` codebase knowledge and precedent.
3. Tester Agent – This agent think hard about the implementation and review code security and maintainability after coding is done.

## YOUR MAIN TASK
1. **THIS IS CRUSIAL**: YOU MUST READ AND UNDERSTAND THE RELEVANT FILES FULL BEFORE LAUNCHING ANY SUB AGENTS
2. **VERY IMPORTANT** to read the [tailwind implementation guide](/Specs/styling/tailwind-implementation.md) for the established patterns and conventions as well as read the already implemented files to understand the patterns(We avoid destructive styling or classes stick to globals variables only we need the app to be continuous and scalable).
2. Think step-by-step, laying out assumptions and unknowns.
3. For each sub-agent, clearly delegate its task with sophesticated propts, capture its output, and summarise insights.
4. Think and do reflection phase where you combine all insights to form a cohesive solution from the sub agents output.
5. If gaps remain, iterate (spawn sub-agents again) until confident.
6. when coding dont perform any git add or git commit operations. + no `npm run build`
7. you can use only `npx tsc --noEmit` and `npm run lint`.
8. **Refer to** [React19-EcoSystem.md](/Docs/React19/React19-EcoSystem.md) for the latest react patterns and best practices. you can read and fetch what you think relevant to my compoenents.

## Output Format

1. **Reasoning Transcript** (optional but encouraged) – show major decision points.
2. **Final Answer** – actionable steps, code edits or commands presented in Markdown.
3. **Next Actions** – bullet list of follow-up items for the team (if any).
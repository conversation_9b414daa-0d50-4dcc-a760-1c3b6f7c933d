# High Priority: SparkHelpers.ts Query Optimizations

## ⚠️ Priority: HIGH  
**Severity**: Medium-High Impact - Inefficient query patterns affecting performance

## Summary
The `sparkHelpers.ts` file contains several inefficient query patterns that should be optimized for better performance, particularly around user filtering and data collection.

## Issues Found

### 1. Redundant Database Query in `sparks.ts` (Lines 132-135)
**Location**: `/home/<USER>/lionx/convex/sparks.ts:132-135`
**Issue**: Duplicate `getSparkByIdHelper` call
```typescript
// ❌ Current problematic pattern
export const updateSpark = mutation({
  handler: async (ctx, args) => {
    // Line 121: First call
    const existingSpark = await getSparkByIdHelper(ctx, args.sparkId);
    
    // Lines 132-135: Redundant second call
    const spark = await getSparkByIdHelper(ctx, args.sparkId);
    // ... validation logic using `spark` instead of `existingSpark`
  },
});
```

**Impact**: 
- Unnecessary database round-trip on every spark update
- Performance degradation
- Redundant code

### 2. Inefficient User Filtering Pattern (Lines 371-374, 626-629)
**Location**: `/home/<USER>/lionx/convex/lib/sparkHelpers.ts:371-374, 626-629`
**Issue**: Using `.collect() + .filter()` instead of proper indexing

```typescript
// ❌ Current problematic pattern in getSparkSubmissionStatsByEvent
const allUsers = await ctx.db.query("users").collect();
const eventUsers = allUsers.filter(user => 
  user.eventIds?.includes(eventId)
);

// ❌ Same pattern in getSparkSubmissionSummary  
const users = await ctx.db.query("users").collect();
const eventUsers = users.filter(user => 
  user.eventIds?.includes(eventId)
);
```

**Impact**:
- Loads ALL users into memory unnecessarily
- Filters in JavaScript instead of database level
- Poor performance with large user bases
- Violates Convex best practices for `.collect()` usage

### 3. Missing Limits on Large Collections (Lines 595-604)
**Location**: `/home/<USER>/lionx/convex/lib/sparkHelpers.ts:595-604`
**Issue**: `getAllSparkSubmissions` uses unbounded `.collect()`

```typescript
// ❌ Current problematic pattern
export async function getAllSparkSubmissions(
  ctx: QueryCtx | MutationCtx
): Promise<Doc<"sparkSubmissions">[]> {
  return await ctx.db.query("sparkSubmissions").collect();
}
```

**Impact**:
- Potentially unbounded result set
- Memory issues with large submission collections
- No limit protection

### 4. Inefficient Team Submission Query (Lines 577-581)
**Location**: `/home/<USER>/lionx/convex/lib/sparkHelpers.ts:577-581`
**Issue**: Using `.filter()` on database query instead of compound index

```typescript
// ❌ Current problematic pattern
const submissions = await ctx.db
  .query("sparkSubmissions")
  .filter((q) => 
    q.and(
      q.eq(q.field("sparkId"), sparkId),
      q.eq(q.field("teamId"), teamId)
    )
  )
  .collect();
```

**Impact**:
- Inefficient query using `.filter()` instead of `.withIndex()`
- Should use compound index for better performance

## Root Cause Analysis

### Why These Patterns Are Problematic
According to Convex Best Practices 2025:
1. **Avoid `.filter` on database queries**: Should use `.withIndex()` for filtering
2. **Only use `.collect` with small number of results**: Large collections should be avoided
3. **Eliminate redundant operations**: Don't repeat the same database calls

## Solution Strategy

### 1. Remove Redundant Database Query
### 2. Create Optimized User Filtering Helper
### 3. Add Bounds to Collection Operations
### 4. Use Proper Indexing for Team Submissions

## Implementation Plan

### Fix 1: Remove Redundant Query in `sparks.ts`
```typescript
// ✅ Optimized solution
export const updateSpark = mutation({
  args: {
    sparkId: v.id("sparks"),
    // ... other args
  },
  handler: async (ctx, args) => {
    // Single database call
    const existingSpark = await getSparkByIdHelper(ctx, args.sparkId);
    
    if (!existingSpark) {
      return responseUtils.error("Spark not found");
    }

    // Reuse the same object for validation instead of fetching again
    if (existingSpark.status === "published") {
      return responseUtils.error("Cannot update published spark");
    }

    // Continue with update logic using `existingSpark`
    await ctx.db.patch(args.sparkId, {
      // ... update fields
    });

    return responseUtils.success("Spark updated successfully");
  },
});
```

### Fix 2: Create Optimized User Helper Function
```typescript
// ✅ New helper function in userHelpers.ts
export async function getUsersByEvent(
  ctx: QueryCtx | MutationCtx,
  eventId: Id<"events">
): Promise<Doc<"users">[]> {
  // Check if we have a by_event index on users table
  // If not, use a reasonable limit to prevent memory issues
  try {
    // Try using index first (if it exists)
    return await ctx.db
      .query("users")
      .withIndex("by_event", q => q.eq("eventId", eventId))
      .collect();
  } catch {
    // Fallback: Get recent users and filter (with warning)
    console.warn("No by_event index found, using fallback with limit");
    
    const recentUsers = await ctx.db
      .query("users")
      .order("desc")
      .take(1000); // Reasonable limit
      
    return recentUsers.filter(user => 
      user.eventIds?.includes(eventId)
    );
  }
}
```

### Fix 3: Update SparkHelpers to Use New User Helper
```typescript
// ✅ Updated sparkHelpers.ts functions
import { getUsersByEvent } from "./userHelpers";

export async function getSparkSubmissionStatsByEvent(
  ctx: QueryCtx | MutationCtx,
  eventId: Id<"events">
): Promise<SparkSubmissionStats[]> {
  // Use optimized user helper instead of collect + filter
  const eventUsers = await getUsersByEvent(ctx, eventId);
  
  // Rest of the function remains the same
  const sparks = await getSparksByEvent(ctx, eventId);
  // ... continue with existing logic
}

export async function getSparkSubmissionSummary(
  ctx: QueryCtx | MutationCtx,
  eventId: Id<"events">
): Promise<SparkSubmissionSummary> {
  // Use optimized user helper instead of collect + filter
  const eventUsers = await getUsersByEvent(ctx, eventId);
  
  // Rest of the function remains the same
  // ... continue with existing logic
}
```

### Fix 4: Add Bounds to `getAllSparkSubmissions`
```typescript
// ✅ Updated getAllSparkSubmissions with reasonable limits
export async function getAllSparkSubmissions(
  ctx: QueryCtx | MutationCtx,
  limit: number = 1000
): Promise<Doc<"sparkSubmissions">[]> {
  console.warn(`Loading spark submissions with limit: ${limit}`);
  
  return await ctx.db
    .query("sparkSubmissions")
    .order("desc") // Most recent first
    .take(limit);
}
```

### Fix 5: Optimize Team Submission Query with Index
**First, check if compound index exists in schema:**
```typescript
// In schema.ts - verify this index exists or add it
sparkSubmissions: defineTable({
  // ... existing fields
})
.index("by_spark_and_team", ["sparkId", "teamId"]) // Add if missing
```

**Then update the helper function:**
```typescript
// ✅ Updated getTeamSparkSubmissionStatus
export async function getTeamSparkSubmissionStatus(
  ctx: QueryCtx | MutationCtx,
  sparkId: Id<"sparks">,
  teamId: Id<"teams">
): Promise<boolean> {
  // Use compound index instead of filter
  const submission = await ctx.db
    .query("sparkSubmissions")
    .withIndex("by_spark_and_team", q => 
      q.eq("sparkId", sparkId).eq("teamId", teamId)
    )
    .first();
    
  return !!submission;
}
```

## Expected Performance Impact

### Before Optimization
- **updateSpark**: 2 database calls for same data
- **User filtering**: Loads ALL users, filters in JavaScript
- **Large collections**: Unbounded memory usage
- **Team queries**: Inefficient `.filter()` usage

### After Optimization
- **updateSpark**: 1 database call (50% reduction)
- **User filtering**: Indexed queries or bounded collections
- **Large collections**: Limited to reasonable sizes
- **Team queries**: Efficient compound index usage

### Performance Improvement
- **50% reduction** in redundant database calls
- **Significantly better** performance for events with many users
- **Memory-safe** operations with large datasets
- **Consistent performance** regardless of data size

## Implementation Steps

1. **Remove redundant query** in `sparks.ts` updateSpark function
2. **Create `getUsersByEvent` helper** in userHelpers.ts
3. **Update sparkHelpers functions** to use new user helper
4. **Add bounds** to getAllSparkSubmissions function
5. **Verify compound index** exists for team submission queries
6. **Test all affected functions** thoroughly

## Testing Requirements

### Functional Tests
1. Verify spark updates work correctly
2. Verify spark submission statistics are accurate
3. Test with events having many users
4. Test team submission status checks

### Performance Tests
1. Measure updateSpark execution time improvement
2. Test user filtering with large user bases
3. Verify memory usage with bounded collections
4. Test team submission queries with compound index

## Risk Assessment
- **Low Risk**: All changes maintain existing functionality
- **High Reward**: Significant performance improvements
- **No Breaking Changes**: Same external APIs maintained

## Dependencies
- **userHelpers.ts**: Add getUsersByEvent function
- **Schema**: Verify compound indexes exist
- **Existing Functions**: All maintain same interfaces

## Alternative Implementation (If Index Cannot Be Added)

### If Schema Changes Are Not Possible
```typescript
// ✅ Alternative solution using existing patterns
export async function getTeamSparkSubmissionStatus(
  ctx: QueryCtx | MutationCtx,
  sparkId: Id<"sparks">,
  teamId: Id<"teams">
): Promise<boolean> {
  // Get submissions for this spark and filter in TypeScript
  const sparkSubmissions = await ctx.db
    .query("sparkSubmissions")
    .withIndex("by_spark", q => q.eq("sparkId", sparkId))
    .collect();
    
  return sparkSubmissions.some(submission => submission.teamId === teamId);
}
```

## Follow-up Actions
After implementation:
1. **Monitor performance** of spark-related operations
2. **Verify memory usage** improvements
3. **Consider adding indexes** if not present in schema
4. **Document optimized patterns** for future reference

## Additional Benefits
- **Reduced Database Load**: Fewer redundant queries
- **Better Scalability**: Handles larger datasets efficiently  
- **Memory Efficiency**: Bounded operations prevent memory issues
- **Code Consistency**: Follows established optimization patterns
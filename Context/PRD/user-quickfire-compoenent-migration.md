# User Quickfire Component Migration PRD

## Executive Summary

**Objective**: Migrate the user quickfire voting component from legacy CSS architecture to Tailwind + shadcn/ui following established patterns from the admin section and voting component migration.

**Target Component**: `/src/app/user/components/QuickfireVoting.tsx`  
**Migration Pattern**: Follow Ideas/Sparks/Voting component architecture with StateCard integration  
**Constraint**: **100% functionality preservation** - all quickfire voting logic, real-time updates, and user interactions must remain identical

**Success Criteria**: 
- ✅ All legacy CSS classes removed
- ✅ Full shadcn/ui component adoption
- ✅ StateCard pattern implementation
- ✅ Preserved real-time quickfire voting functionality
- ✅ Maintained simple state management
- ✅ Zero visual/functional regression

## Current Implementation Analysis

### **Component Architecture**
```typescript
// Location: /src/app/user/components/QuickfireVoting.tsx
// Architecture: Real-time Convex + Simple State Management
// Dependencies: Custom Slider, Legacy CSS Classes
```

### **Core Functionality Matrix**
| Feature | Current Implementation | Must Preserve |
|---------|----------------------|---------------|
| **Real-time Voting** | Convex `useQuery` + `useMutation` | ✅ Exact same logic |
| **Score Slider** | Custom 0-10 slider component | ✅ Functionality (style update) |
| **Single Item Focus** | Admin-controlled `votingActive` toggle | ✅ Atomic voting behavior |
| **Session Type Check** | Only displays for "Quickfire" sessions | ✅ Session filtering |
| **Simple State** | Basic vote lookup by quickfireId | ✅ Performance optimization |
| **Question Display** | Optional question field rendering | ✅ Unique quickfire feature |

### **Data Flow Architecture**
```typescript
// Convex Integration Pattern (DO NOT MODIFY)
const quickfireItems = useQuery(api.quickfire.getQuickfireItemsForVoting, { username });
const votes = useQuery(api.quickfire.getQuickfireVotesByUser, { username });
const submitVote = useMutation(api.quickfire.submitQuickfireVote);

// State Management Pattern (PRESERVE)
const handleVote = async (quickfireId, score) => {
  await submitVote({ username, quickfireId, score });
  // No complex state management needed - Convex handles reactivity
};
```

### **Critical Dependencies**
- **Convex Schema**: `quickfires` and `quickfireVotes` tables
- **Backend Functions**: `getQuickfireItemsForVoting`, `getQuickfireVotesByUser`, `submitQuickfireVote`
- **Component Tree**: QuickfireVoting → Slider → Direct submission
- **Simplicity**: No team navigation, no comments, no complex state management

## Migration Patterns from Established References

### **1. StateCard Integration Pattern**
Based on `Ideas.tsx` and `SparksComponent.tsx`:

```typescript
// Replace placeholder content with StateCard:

// Current Loading State
<div className="voting-section fade-in">
  <div className="content-placeholder">Loading...</div>
</div>

// Should Become:
<motion.div
  initial={{ opacity: 0 }}
  animate={{ opacity: 1 }}
  exit={{ opacity: 0 }}
  transition={{ duration: 0.3 }}
  className="w-full max-w-[1200px] mx-auto grid place-items-center"
>
  <StateCard state="loading" title="Loading..." />
</motion.div>

// Current Authentication State
<div className="voting-section fade-in">
  <div className="content-placeholder">Please log in to view quickfire voting.</div>
</div>

// Should Become:
<StateCard 
  state="error" 
  title="Please log in to view quickfire voting." 
/>

// Current Get Ready State
<div className="quickfire-content-placeholder">
  <h1>Get Ready!</h1>
</div>

// Should Become:
<StateCard 
  state="info" 
  title="Get Ready!" 
  message="Quickfire voting will begin shortly. Keep checking back!" 
/>
```

### **2. Layout Migration Pattern**
Based on `Voting.tsx` component:

```typescript
// Container Pattern
<motion.div
  initial={{ opacity: 0 }}
  animate={{ opacity: 1 }}
  exit={{ opacity: 0 }}
  transition={{ duration: 0.3 }}
  className="w-full max-w-[1200px] mx-auto grid place-items-center"
>
  {/* Content */}
</motion.div>

// Card Pattern for Quickfire Items
<div className="w-full flex flex-col gap-8">
  {quickfireItems.map((item, index) => (
    <div 
      key={item._id}
      className="bg-background border-2 border-accent p-0 hover:bg-muted/50 transition-colors"
    >
      {/* Quickfire content */}
    </div>
  ))}
</div>
```

### **3. Slider Component Migration**
Based on `Voting.tsx` shadcn/ui implementation:

```typescript
// Current Custom Slider
import Slider from '@/components/selectors/Slider';

<Slider
  label=""
  min={0}
  max={10}
  step={1}
  value={votes[item._id]?.score || 0}
  onChange={(value) => handleVote(item._id, value)}
/>

// Should Become:
import { Slider } from '@/components/ui/slider';

<Slider
  min={0}
  max={10}
  step={1}
  value={[votes[item._id]?.score || 0]}
  onValueChange={(value) => handleVote(item._id, value[0])}
  className="w-full"
/>
```

## Technical Implementation Requirements

### **Phase 1: Core Structure Migration**

#### **1.1 CSS Class Mapping**
```css
/* Legacy → Tailwind Migration */
.voting-section → w-full max-w-[1200px] mx-auto grid place-items-center
.content-placeholder → StateCard component (3 instances)
.ideas-list → w-full flex flex-col gap-8
.user-voting-card → bg-background border-2 border-accent p-0 hover:bg-muted/50 transition-colors
.voting-controls → flex flex-col gap-6 border-t-2 border-secondary p-8
.IdeaHead → p-8 section
.quickfire-section-header → font-ultrabold text-3xl text-primary mb-8
.quickfire-title → font-ultrabold text-xl text-accent
.quickfire-comments → text-base text-secondary mt-4
.quickfire-question → text-base text-secondary mt-4
.score-display → flex items-center gap-6
.score-label → font-mono text-accent min-w-[60px]
.score-value → font-ultrabold text-2xl text-primary
.slider-container → w-full
```

#### **1.2 Component Structure**
```typescript
// Target Structure
export default function QuickfireVoting() {
  // [PRESERVE] All existing hooks and state logic
  
  // [REPLACE] Conditional rendering with StateCard
  if (loading) return (
    <motion.div className="w-full max-w-[1200px] mx-auto grid place-items-center">
      <StateCard state="loading" title="Loading..." />
    </motion.div>
  );
  
  if (status !== 'authenticated') return (
    <motion.div className="w-full max-w-[1200px] mx-auto grid place-items-center">
      <StateCard state="error" title="Please log in to view quickfire voting." />
    </motion.div>
  );
  
  // [MIGRATE] Layout classes
  return (
    <motion.div className="w-full max-w-[1200px] mx-auto grid place-items-center">
      {/* Content */}
    </motion.div>
  );
}
```

### **Phase 2: Form Element Migration**

#### **2.1 Slider Component Replacement**
```typescript
// REMOVE: import Slider from '@/components/selectors/Slider';
// ADD: import { Slider } from '@/components/ui/slider';

// REPLACE API:
// onChange={(value) => handleVote(item._id, value)}
// WITH:
// onValueChange={(value) => handleVote(item._id, value[0])}

// REPLACE props:
// value={votes[item._id]?.score || 0}
// WITH:
// value={[votes[item._id]?.score || 0]}
```

#### **2.2 Error Handling Enhancement**
```typescript
// Current Error Display
{error && (
  <div className="error-message slide-in-top">
    {error}
  </div>
)}

// Should Become:
{error && (
  <StateCard 
    state="error" 
    title="Voting Error" 
    message={error} 
  />
)}
```

### **Phase 3: Content Layout Migration**

#### **3.1 Quickfire Item Structure**
```typescript
// Current Structure
<div className="user-voting-card">
  <div className="IdeaHead">
    <h3 className="quickfire-title">{item.idea}</h3>
    {item.comments && (
      <div className="quickfire-comments">
        <p className="quickfire-section-text">{item.comments}</p>
      </div>
    )}
  </div>
  <div className="voting-controls">
    <div className="score-display">
      <span className="score-label">Score:</span>
      <span className="score-value">{votes[item._id]?.score || 0}</span>
    </div>
    {item.question && (
      <div className="quickfire-question">
        <p className="quickfire-section-text">{item.question}</p>
      </div>
    )}
    <div className="slider-container">
      <Slider ... />
    </div>
  </div>
</div>

// Should Become:
<div className="bg-background border-2 border-accent p-0 hover:bg-muted/50 transition-colors">
  <div className="p-8">
    <h2 className="font-ultrabold text-xl text-accent">{item.idea}</h2>
    {item.comments && (
      <div className="mt-4">
        <p className="text-base text-secondary">{item.comments}</p>
      </div>
    )}
  </div>
  
  <div className="flex flex-col gap-6 border-t-2 border-secondary p-8">
    <div className="flex items-center gap-6">
      <span className="font-mono text-accent min-w-[60px]">Score:</span>
      <span className="font-ultrabold text-2xl text-primary">{votes[item._id]?.score || 0}</span>
    </div>
    
    {item.question && (
      <div>
        <p className="text-base text-secondary">{item.question}</p>
      </div>
    )}
    
    <div className="w-full">
      <Slider
        min={0}
        max={10}
        step={1}
        value={[votes[item._id]?.score || 0]}
        onValueChange={(value) => handleVote(item._id, value[0])}
        className="w-full"
      />
    </div>
  </div>
</div>
```

## Critical Preservation Requirements

### **1. State Management (DO NOT MODIFY)**
```typescript
// PRESERVE: Simple vote lookup by quickfireId
const votes = useMemo(() => {
  if (!userVotes) return {};
  const voteMap = {};
  userVotes.forEach(vote => {
    voteMap[vote.quickfireId] = vote;
  });
  return voteMap;
}, [userVotes]);

// PRESERVE: Immediate vote submission (no debouncing needed)
const handleVote = async (quickfireId, score) => {
  try {
    await submitVote({
      username: session.user.username,
      quickfireId,
      score,
    });
    setError(null);
  } catch (error) {
    setError(error.message);
  }
};
```

### **2. Real-time Updates (DO NOT MODIFY)**
```typescript
// PRESERVE: All Convex query patterns
// PRESERVE: Conditional query execution based on authentication
// PRESERVE: Simple mutation handling
```

### **3. Session-Based Logic (DO NOT MODIFY)**
```typescript
// PRESERVE: Session-based item activation
// PRESERVE: Only display items where votingActive === true
// PRESERVE: Simple loading and authentication checks
```

## Brand Compliance Requirements

### **Design System Constraints**
- **ZERO border radius**: All components must use `rounded-none` or no border-radius
- **Brand colors**: Use CSS custom properties for primary/secondary/accent colors  
- **Typography**: Use neue-machina font family consistently
- **Sharp corners**: Maintain geometric design language

### **Component Standards**
- **shadcn/ui only**: Use shadcn/ui Slider component
- **StateCard pattern**: All placeholder states must use StateCard
- **Responsive design**: Mobile-first approach with established breakpoints
- **Accessibility**: Maintain existing keyboard navigation and screen reader support

## Implementation Validation Gates

### **Gate 1: Structure Migration**
- [ ] All legacy CSS classes removed from component
- [ ] StateCard components properly implemented (3 instances)
- [ ] Layout matches existing visual design
- [ ] Component renders without errors

### **Gate 2: Form Element Migration**
- [ ] Slider component replaced with shadcn/ui Slider
- [ ] Slider API updated to use `onValueChange` with array values
- [ ] Score display styled with Tailwind classes
- [ ] Error handling integrated with StateCard

### **Gate 3: Functionality Preservation**
- [ ] Real-time quickfire voting works identically
- [ ] Score changes reflect immediately
- [ ] Authentication and session checks work correctly
- [ ] Voting activation/deactivation responds to admin controls

### **Gate 4: Advanced Features**
- [ ] Motion animations work smoothly
- [ ] Question and comments display correctly
- [ ] Responsive design works on all screen sizes
- [ ] Performance characteristics maintained

### **Gate 5: Quality Assurance**
- [ ] No TypeScript errors
- [ ] No console errors or warnings
- [ ] All existing functionality preserved
- [ ] Visual design matches established patterns

## Migration Challenges and Solutions

### **Challenge 1: Slider API Differences**
**Issue**: Custom slider uses `onChange(value)` while shadcn/ui uses `onValueChange([value])`
**Solution**: Update event handler to destructure array value: `onValueChange={(value) => handleVote(item._id, value[0])}`

### **Challenge 2: Simple State Management**
**Issue**: QuickfireVoting has much simpler state than Voting component
**Solution**: Preserve the simplicity - no need for complex memoization or debouncing

### **Challenge 3: Unique Content Structure**
**Issue**: Quickfire items have unique `question` and `comments` fields not in regular ideas
**Solution**: Maintain the conditional rendering but with Tailwind styling

### **Challenge 4: Single Item Focus**
**Issue**: QuickfireVoting typically shows one item at a time vs. multiple ideas
**Solution**: Maintain the simple array mapping but ensure layout works for both single and multiple items

## Key Differences from Regular Voting Migration

### **Architectural Simplicity**
- **No Team Navigation**: QuickfireVoting doesn't need TeamsNav component
- **No Comments System**: Only score voting, no comment textarea or debouncing
- **No Collapsible Content**: Simple display of idea, comments, and question
- **No Complex State**: Basic vote lookup without memoization complexity
- **Single Session Type**: Only works with "Quickfire" session type

### **Unique Features to Preserve**
- **Question Field**: Optional question display specific to quickfire items
- **Comments Field**: Static admin comments display (not user input)
- **Atomic Voting**: Only one item active at a time controlled by admin
- **Immediate Updates**: No debouncing needed for simple score voting

## Success Metrics and Quality Assurance

### **Functional Metrics**
- **100%** quickfire voting functionality preservation
- **100%** real-time update capability
- **100%** admin activation/deactivation responsiveness
- **0** regression in user experience

### **Performance Metrics**
- **≤ 5%** bundle size increase
- **≤ 50ms** additional render time (simpler than Voting component)
- **≤ 5%** memory usage increase
- **100%** existing simplicity preservation

### **Code Quality Metrics**
- **0** TypeScript errors
- **0** ESLint warnings
- **0** accessibility regressions
- **100%** existing functionality maintenance

## Implementation Timeline

### **Day 1: Foundation**
- Phase 1 structure migration
- StateCard integration
- Basic layout conversion

### **Day 2: Component Integration**
- Slider migration
- Content layout updates
- Error handling enhancement

### **Day 3: Polish**
- Motion animation integration
- Final testing and validation

## Files to Modify

### **Primary Files**
- `/src/app/user/components/QuickfireVoting.tsx` - Main component migration

### **Files to Remove Dependencies**
- Remove dependency on `/src/components/selectors/Slider.tsx` - Replace with shadcn/ui
- Remove legacy CSS dependencies

### **Files to Reference**
- `/src/app/user/components/Ideas.tsx` - StateCard patterns
- `/src/app/user/components/SparksComponent.tsx` - StateCard usage
- `/src/app/user/components/Voting.tsx` - Slider and layout patterns
- `/src/components/ui/state-card.tsx` - StateCard implementation
- `/src/components/ui/slider.tsx` - Slider component replacement

## Final Quality Score

**PRD Completeness Score: 9/10**

**Rationale**: This PRD provides comprehensive implementation guidance with:
- ✅ Complete current implementation analysis
- ✅ Detailed migration patterns from established references
- ✅ Specific code examples and replacement patterns
- ✅ Critical preservation requirements clearly identified
- ✅ Validation gates for implementation success
- ✅ Migration challenges with specific solutions
- ✅ Quality assurance criteria and success metrics
- ✅ Recognition of architectural simplicity vs. complex Voting component

**Missing Element (-1)**: Live testing with admin activation/deactivation during development (should be conducted post-implementation)

This PRD provides sufficient context and implementation guidance for successful one-pass migration while maintaining 100% functionality preservation and recognizing the simpler architecture of QuickfireVoting compared to the complex Voting component.
# User Voting Component Migration PRD

## Executive Summary

**Objective**: Migrate the user voting component from legacy CSS architecture to Tailwind + shadcn/ui following established patterns from the admin section migration.

**Target Component**: `/src/app/user/components/Voting.tsx`  
**Migration Pattern**: Follow Ideas/Sparks component architecture with StateCard integration  
**Constraint**: **100% functionality preservation** - all voting logic, real-time updates, and user interactions must remain identical

**Success Criteria**: 
- ✅ All legacy CSS classes removed
- ✅ Full shadcn/ui component adoption
- ✅ StateCard pattern implementation
- ✅ Preserved real-time voting functionality
- ✅ Maintained complex state management
- ✅ Zero visual/functional regression

## Current Implementation Analysis

### **Component Architecture**
```typescript
// Location: /src/app/user/components/Voting.tsx
// Architecture: Real-time Convex + Complex State Management
// Dependencies: Custom Slider, TextArea, TeamsNav, Legacy CSS
```

### **Core Functionality Matrix**
| Feature | Current Implementation | Must Preserve |
|---------|----------------------|---------------|
| **Real-time Voting** | Convex `useQuery` + `useMutation` | ✅ Exact same logic |
| **Score Slider** | Custom 0-10 slider with +/- buttons | ✅ Functionality (style update) |
| **Comment Debouncing** | 500ms debounced submission | ✅ Exact timing |
| **Team Filtering** | TeamsNav component integration | ✅ Navigation behavior |
| **State Management** | Complex vote lookup map | ✅ Performance optimization |
| **Access Control** | Multi-layer voting permissions | ✅ Security validation |

### **Data Flow Architecture**
```typescript
// Convex Integration Pattern (DO NOT MODIFY)
const votingIdeas = useQuery(api.votes.getVotingIdeas, { eventId, sessionId });
const userVotes = useQuery(api.votes.getUserVotes, { eventId, sessionId });
const votingStatus = useQuery(api.votes.getVotingStatus, { eventId });
const submitVote = useMutation(api.votes.submitVote);

// State Management Pattern (PRESERVE)
const votes = useMemo(() => {
  const voteMap: { [key: string]: { score: number; comment: string } } = {};
  // Complex optimization logic - DO NOT MODIFY
}, [userVotes]);
```

### **Critical Dependencies**
- **Convex Schema**: `votes` table with userId, ideaId, score, comment structure
- **Backend Functions**: `getVotingIdeas`, `getUserVotes`, `submitVote`, `getVotingStatus`
- **Component Tree**: Ideas → Voting → TeamsNav → Slider → TextArea
- **Performance**: `React.memo()`, `useCallback()`, `useMemo()` optimizations

## Migration Patterns from Established References

### **1. StateCard Integration Pattern**
Based on `/PRDs/user-section-tailwind-migration.md`:

```typescript
// Replace 4 content-placeholder instances:

// Loading State
<StateCard state="loading" title="Loading..." />

// Ideas Flowing State  
<StateCard 
  state="info" 
  title="Ideas Flowing!" 
  message="Voting has not started yet. Keep checking back!" 
/>

// Get Ready State
<StateCard 
  state="info" 
  title="Get Ready!" 
  message="No teams have finished presenting yet. Keep checking back!" 
/>

// Voting Closed State
<StateCard 
  state="info" 
  title="Voting Closed" 
  message="This voting session has ended." 
/>
```

### **2. Layout Migration Pattern**
Based on Ideas/Sparks components:

```typescript
// Container Pattern
<motion.div
  initial={{ opacity: 0 }}
  animate={{ opacity: 1 }}
  exit={{ opacity: 0 }}
  transition={{ duration: 0.3 }}
  className="w-full max-w-[1200px] mx-auto grid place-items-center"
>
  {/* Content */}
</motion.div>

// Card Pattern  
<div className="bg-background border-2 border-secondary p-0 hover:bg-muted/50 transition-colors">
  {/* Voting content */}
</div>
```

### **3. Form Element Migration**
Based on admin section patterns:

```typescript
// TextArea Replacement
import { Textarea } from '@/components/ui/textarea';

<Textarea
  value={comment}
  onChange={(e) => setComment(e.target.value)}
  className="w-full shadow-none focus-visible:ring-0 focus-visible:ring-offset-0 bg-black border-2 border-primary text-white resize-vertical"
  placeholder="Optional comment..."
  rows={3}
/>
```

## Technical Implementation Requirements

### **Phase 1: Core Structure Migration**

#### **1.1 CSS Class Mapping**
```css
/* Legacy → Tailwind Migration */
.voting-section → w-full max-w-[1200px] mx-auto grid place-items-center
.content-placeholder → StateCard component (4 instances)
.ideas-list → w-full flex flex-col gap-4
.team-section → mb-12 w-full last:mb-0
.user-voting-card → bg-background border-2 border-secondary p-0 hover:bg-muted/50 transition-colors
.voting-controls → flex flex-col gap-4 border-t-2 border-primary p-6
```

#### **1.2 Component Structure**
```typescript
// Target Structure
export const Voting = React.memo(() => {
  // [PRESERVE] All existing hooks and state logic
  
  // [REPLACE] Conditional rendering with StateCard
  if (loading) return <StateCard state="loading" title="Loading..." />;
  if (error) return <StateCard state="error" title="Error" message={error} />;
  if (someCondition) return <StateCard state="info" title="..." message="..." />;
  
  // [MIGRATE] Layout classes
  return (
    <motion.div className="w-full max-w-[1200px] mx-auto grid place-items-center">
      {/* Content */}
    </motion.div>
  );
});
```

### **Phase 2: Form Element Migration**

#### **2.1 TextArea Component Replacement**
```typescript
// REMOVE: import { TextArea } from '@/components/inputs/TextArea';
// ADD: import { Textarea } from '@/components/ui/textarea';

// REPLACE:
<TextArea
  value={comment}
  onChange={(e) => setComment(e.target.value)}
  placeholder="Optional comment..."
  rows={3}
/>

// WITH:
<Textarea
  value={comment}
  onChange={(e) => setComment(e.target.value)}
  className="w-full shadow-none focus-visible:ring-0 focus-visible:ring-offset-0 bg-black border-2 border-primary text-white resize-vertical"
  placeholder="Optional comment..."
  rows={3}
/>
```

#### **2.2 Slider Component Enhancement**
```typescript
// PRESERVE: /src/components/selectors/Slider.tsx functionality
// ENHANCE: Apply Tailwind classes while maintaining all behavior

// Current: Custom component with brand colors and +/- buttons
// Target: Same functionality with Tailwind styling
// Note: Keep all existing props and behavior unchanged
```

### **Phase 3: Advanced Features Migration**

#### **3.1 Animation Migration**
```typescript
// PRESERVE: All Framer Motion animations
// REPLACE: CSS animation classes with Tailwind utilities

// Current: fade-in, slide-in-up CSS classes
// Target: Framer Motion variants or Tailwind animation classes
const teamVariants = {
  hidden: { opacity: 0, y: 20 },
  visible: { opacity: 1, y: 0 }
};
```

#### **3.2 TeamsNav Integration**
```typescript
// PRESERVE: All TeamsNav functionality
// UPDATE: Styling to match Tailwind patterns
// MAINTAIN: activeTeam state and filtering logic
```

## Critical Preservation Requirements

### **1. State Management (DO NOT MODIFY)**
```typescript
// PRESERVE: Complex vote lookup optimization
const votes = useMemo(() => {
  const voteMap: { [key: string]: { score: number; comment: string } } = {};
  userVotes?.forEach((vote) => {
    voteMap[vote.ideaId] = {
      score: vote.score,
      comment: vote.comment || '',
    };
  });
  return voteMap;
}, [userVotes]);

// PRESERVE: Debounced comment submission
const debouncedSubmitComment = useCallback(
  debounce(async (ideaId: string, comment: string) => {
    // Exact logic preservation required
  }, 500),
  [submitVote, eventId, sessionId]
);
```

### **2. Real-time Updates (DO NOT MODIFY)**
```typescript
// PRESERVE: All Convex query patterns
// PRESERVE: Conditional query execution
// PRESERVE: Mutation handling with error states
```

### **3. Access Control Logic (DO NOT MODIFY)**
```typescript
// PRESERVE: All voting validation logic
// PRESERVE: Team-based permission checks
// PRESERVE: Session status validation
```

## Brand Compliance Requirements

### **Design System Constraints**
- **ZERO border radius**: All components must use `rounded-none` or no border-radius
- **Brand colors**: Use CSS custom properties for primary/secondary/accent colors  
- **Typography**: Use neue-machina font family consistently
- **Sharp corners**: Maintain geometric design language

### **Component Standards**
- **shadcn/ui only**: No custom form components
- **StateCard pattern**: All placeholder states must use StateCard
- **Responsive design**: Mobile-first approach with established breakpoints
- **Accessibility**: Maintain existing keyboard navigation and screen reader support

## Implementation Validation Gates

### **Gate 1: Structure Migration**
- [ ] All legacy CSS classes removed from component
- [ ] StateCard components properly implemented (4 instances)
- [ ] Layout matches existing visual design
- [ ] Component renders without errors

### **Gate 2: Form Element Migration**
- [ ] TextArea component replaced with shadcn/ui Textarea
- [ ] Slider component styled with Tailwind (functionality preserved)
- [ ] All form interactions work identically
- [ ] Debounced comment submission functions correctly

### **Gate 3: Functionality Preservation**
- [ ] Real-time voting works identically
- [ ] Score changes reflect immediately
- [ ] Comment debouncing works (500ms delay)
- [ ] Team filtering works correctly
- [ ] Vote lookup optimization preserved

### **Gate 4: Advanced Features**
- [ ] Animations work smoothly
- [ ] TeamsNav integration functions correctly
- [ ] Responsive design works on all screen sizes
- [ ] Performance characteristics maintained

### **Gate 5: Quality Assurance**
- [ ] No TypeScript errors
- [ ] No console errors or warnings
- [ ] All existing tests pass
- [ ] Performance metrics match or exceed previous implementation

## Migration Challenges and Solutions

### **Challenge 1: Complex State Management**
**Issue**: Voting component has intricate state logic with multiple useEffect hooks
**Solution**: Migrate UI only, preserve all existing state management patterns exactly

### **Challenge 2: Debounced Comment Submission**
**Issue**: Current implementation uses custom debouncing with timer cleanup
**Solution**: Preserve exact debounce implementation, update only UI components

### **Challenge 3: Slider Component Styling**
**Issue**: Custom slider has brand-specific colors and +/- button functionality
**Solution**: Enhance existing slider with Tailwind classes, maintain all functionality

### **Challenge 4: Animation Timing**
**Issue**: Staggered animations with specific delays for team sections and ideas
**Solution**: Use Framer Motion variants for consistent animation patterns

### **Challenge 5: TeamsNav Integration**
**Issue**: TeamsNav component also needs migration for consistent styling
**Solution**: Update TeamsNav styling while preserving motion layout functionality

## Success Metrics and Quality Assurance

### **Functional Metrics**
- **100%** voting functionality preservation
- **100%** real-time update capability
- **100%** comment debouncing accuracy
- **0** regression in user experience

### **Performance Metrics**
- **≤ 5%** bundle size increase
- **≤ 100ms** additional render time
- **≤ 10%** memory usage increase
- **100%** existing optimization preservation

### **Code Quality Metrics**
- **0** TypeScript errors
- **0** ESLint warnings
- **0** accessibility regressions
- **100%** existing test coverage maintenance

## Implementation Timeline

### **Day 1: Foundation**
- Phase 1 structure migration
- StateCard integration
- Basic layout conversion

### **Day 2: Form Elements**
- TextArea migration
- Slider enhancement
- Form validation testing

### **Day 3: Polish**
- Animation migration
- TeamsNav integration
- Final testing and validation

## Files to Modify

### **Primary Files**
- `/src/app/user/components/Voting.tsx` - Main component migration
- `/src/app/user/components/TeamsNav.tsx` - Styling updates
- `/src/components/selectors/Slider.tsx` - Optional enhancement

### **Files to Remove**
- `/src/styles/_voting-section.css` - Legacy styles
- Dependency on `/src/components/inputs/TextArea.tsx` - Replace with shadcn/ui

### **Files to Reference**
- `/src/app/user/components/Ideas.tsx` - Layout patterns
- `/src/app/user/components/SparksComponent.tsx` - StateCard usage
- `/src/components/ui/state-card.tsx` - StateCard implementation
- `/src/components/ui/textarea.tsx` - Form element replacement

## Final Quality Score

**PRD Completeness Score: 9/10**

**Rationale**: This PRD provides comprehensive implementation guidance with:
- ✅ Complete current implementation analysis
- ✅ Detailed migration patterns from established references
- ✅ Specific code examples and replacement patterns
- ✅ Critical preservation requirements clearly identified
- ✅ Validation gates for implementation success
- ✅ Migration challenges with specific solutions
- ✅ Quality assurance criteria and success metrics

**Missing Element (-1)**: Real-time testing with multiple users during development phase (should be conducted post-implementation)

This PRD provides sufficient context and implementation guidance for successful one-pass migration while maintaining 100% functionality preservation.
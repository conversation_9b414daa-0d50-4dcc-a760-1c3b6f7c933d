# PRD: User Section Tailwind Migration

## Executive Summary

This PRD outlines the complete migration strategy for converting the LionX user section from legacy CSS architecture to Tailwind + shadcn/ui, following the established patterns from the successfully migrated admin section. The migration targets 5 main components and 6 specialized sparks sub-components while maintaining all existing functionality and real-time capabilities.

## Project Context

### Current State Analysis

**Legacy CSS Architecture:**
- Main import: `/src/styles/user.css` with 16+ modular CSS files
- Components using CSS classes: `.user-layout`, `.user-header`, `.spark-form-group`, etc.
- Complex CSS with custom styling for forms, modals, and dynamic components
- Mixed inline styles in user layout for team assignment messages

**Target Architecture:**
- 100% Tailwind + shadcn/ui (following admin section patterns)
- Zero custom CSS classes
- Semantic color variables and design tokens
- Responsive mobile-first design patterns

### Success Reference: Admin Section Migration

The admin section achieved **90.5% completion** with excellent results:
- **19/21 components** fully migrated to Tailwind + shadcn/ui
- **95% reduction** in custom CSS
- **Universal adoption** of shadcn/ui components (Input, Button, Checkbox, AlertDialog)
- **Consistent responsive patterns** across all components
- **Zero border radius** design principle maintained
- **Performance optimizations** with memoization patterns

## Technical Requirements

### Mandatory Design Rules (Critical)

1. **NO BORDER RADIUS** on any component (`border-radius: 0` / `rounded-none`) THIS SHOULD BE DEFAULT IN GLOBALS WITHOUT ACTUALY ADDING IT.
2. **Use shadcn/ui components** for all forms and modals (Input, Button, Checkbox, AlertDialog, Card, Select, etc.)
3. **Built-in variants only** - avoid destructive custom styling unless absolutely required
4. **Brand color preservation**: use variables like primary, secondary, accent, etc.

### Architecture Constraints

- **Convex Real-time**: Preserve all `useQuery()` and `useMutation()` patterns
- **Component Functionality**: Zero functionality loss during migration
- **TiptapEditor Integration**: Maintain rich text editing capabilities:
  - This is already implemented in the admin section [snippets Management](/src/app/admin/components/SnippetsManagement.tsx) we need to reuse the same implementation.
  - all compoenents that displays the content from tiptap editor like the list of ideas for example will use `TiptapContentDisplay` component from the admin section.
- **Animation**: Preserve framer-motion transitions in layout (in a subtle way or remove if it complicates things)

## StateCard Component Specification

### Overview
Create a reusable `StateCard` component to replace all scattered informational messages, loading states, and status messages throughout the user section. This component will standardize the presentation of user feedback while maintaining the established brand styling.

### Component Requirements

#### StateCard Interface
```tsx
interface StateCardProps {
  state: 'loading' | 'info' | 'success' | 'error' | 'warning' | 'empty' | 'completed';
  title: string;
  message?: string;
  action?: {
    label: string;
    onClick: () => void;
    variant?: 'primary' | 'secondary' | 'destructive';
  };
  icon?: React.ReactNode;
  className?: string;
}
```

#### Implementation Pattern
```tsx
// Using shadcn/ui Card component
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';

export function StateCard({ state, title, message, action, icon, className }: StateCardProps) {
  const stateStyles = {
    loading: 'border-secondary text-secondary',
    info: 'border-secondary text-secondary', 
    success: 'border-[#15B14B] text-[#15B14B]',
    error: 'border-destructive text-destructive',
    warning: 'border-accent text-accent',
    empty: 'border-secondary text-secondary',
    completed: 'border-[#15B14B] text-[#15B14B]'
  };

  return (
    <Card className={`bg-black/80 border-2 ${stateStyles[state]} ${className}`}>
      <CardHeader className="text-center">
        {icon && <div className="mx-auto mb-4">{icon}</div>}
        <CardTitle className="font-ultrabold text-xl">
          {title}
        </CardTitle>
      </CardHeader>
      {(message || action) && (
        <CardContent className="text-center">
          {message && (
            <p className="font-mono mb-4 last:mb-0">
              {message}
            </p>
          )}
          {action && (
            <Button
              onClick={action.onClick}
              variant={action.variant || 'secondary'}
              className="mt-4"
            >
              {action.label}
            </Button>
          )}
        </CardContent>
      )}
    </Card>
  );
}
```

### Message Catalog & StateCard Usage

#### Ideas Component Messages
```tsx
// Loading state
<StateCard 
  state="loading" 
  title="Loading..." 
/>

// No active session
<StateCard 
  state="info" 
  title="No active Ideas session!" 
/>

// Ideas submitted successfully
<StateCard 
  state="completed" 
  title="Your ideas have been submitted successfully!" 
  message="Thank you for your participation."
/>

// Form success messages (using toast instead)
toast.success("Idea added successfully!");
```

#### Voting Component Messages
```tsx
// Ideas flowing - voting not started
<StateCard 
  state="info" 
  title="Ideas Flowing!" 
  message="Voting has not started yet. Keep checking back!"
/>

// Get ready - no teams presented
<StateCard 
  state="info" 
  title="Get Ready!" 
  message="No teams have finished presenting yet. Keep checking back!"
/>

// Loading state
<StateCard 
  state="loading" 
  title="Loading..." 
/>
```

#### QuickfireVoting Component Messages
```tsx
// Not authenticated
<StateCard 
  state="warning" 
  title="Authentication Required" 
  message="Please log in to view quickfire voting."
  action={{
    label: "Back to Login",
    onClick: () => router.push('/login'),
    variant: "secondary"
  }}
/>

// No quickfire items
<StateCard 
  state="empty" 
  title="Get Ready!" 
  message="No quickfire items available yet."
/>

// Loading state
<StateCard 
  state="loading" 
  title="Loading..." 
/>
```

#### SparksComponent Messages
```tsx
// No active sparks session
<StateCard 
  state="info" 
  title="No active Sparks session!" 
/>

// Spark configuration not found
<StateCard 
  state="error" 
  title="Spark configuration not found!" 
  message="Please contact an administrator."
/>

// Spark data submitted
<StateCard 
  state="completed" 
  title="Your spark data has been submitted successfully!" 
  message="Thank you for your participation."
/>

// Loading state
<StateCard 
  state="loading" 
  title="Loading..." 
/>
```

#### ProfileSettings Error Handling
```tsx
// Form validation errors (inline, not StateCard)
{error && (
  <div className="text-destructive text-sm bg-destructive/10 border border-destructive/20 p-3 mb-4">
    {error}
  </div>
)}
```

### Toast Integration
Replace success/error form feedback with shadcn/ui toast system (sonner):

```tsx
import { toast } from 'sonner';

// Success operations
toast.success("Idea added successfully!");
toast.success("Vote submitted successfully!");

// Error operations  
toast.error("Failed to submit idea");
toast.error("Network error occurred");
```

### Animation Integration
Preserve subtle framer-motion animations for StateCard:

```tsx
import { motion, AnimatePresence } from "motion/react";

<AnimatePresence mode="wait">
  <motion.div
    key={state}
    initial={{ opacity: 0, y: 20 }}
    animate={{ opacity: 1, y: 0 }}
    exit={{ opacity: 0, y: -20 }}
    transition={{ duration: 0.3 }}
  >
    <StateCard {...props} />
  </motion.div>
</AnimatePresence>
```

### Legacy CSS Classes to Replace
- `.content-placeholder` → `StateCard` component
- `.quickfire-content-placeholder` → `StateCard` component  
- `.no-session-message` → `StateCard` with `state="info"`
- `.error-message` → Toast or inline error div
- `.success-message` → Toast or `StateCard` with `state="success"`

## Migration Targets

### Phase 1: Core Layout & Navigation (Priority: High) ✅ **COMPLETED**

#### 1.1 User Layout (`/src/app/user/layout.tsx`) ✅ **COMPLETED**
**Migration Status:** **FULLY MIGRATED TO TAILWIND + SHADCN/UI**
- ✅ All inline styles converted to Tailwind utility classes
- ✅ shadcn/ui components implemented (DropdownMenu, Button, Badge)
- ✅ ThemeSwitcher component integrated
- ✅ No CSS imports found - fully cleaned up
- ✅ Responsive design patterns implemented

**Completed Tailwind Conversions:**
```tsx
// ✅ CONVERTED: Inline styles to Tailwind classes
<p className="text-white font-mono text-xl text-center mb-8">

// ✅ IMPLEMENTED: shadcn/ui DropdownMenu system
<DropdownMenu>
  <DropdownMenuTrigger asChild>
    <Button variant="ghost" className="h-10 w-10 p-1 focus-visible:ring-0 focus-visible:border-transparent border-1">
  </DropdownMenuTrigger>
  <DropdownMenuContent align="end">
    // Full dropdown implementation with ThemeSwitcher
  </DropdownMenuContent>
</DropdownMenu>
```

#### 1.2 Profile Settings Modal ✅ **COMPLETED**
**Migration Status:** **FULLY MIGRATED TO ALERTDIALOG PATTERN**
- ✅ Custom modal replaced with shadcn/ui AlertDialog
- ✅ Proper form validation and error handling
- ✅ Tailwind utility classes throughout
- ✅ Consistent styling with admin section patterns

**Completed AlertDialog Implementation:**
```tsx
// ✅ IMPLEMENTED: shadcn/ui AlertDialog pattern
<AlertDialog open={open} onOpenChange={onClose}>
  <AlertDialogContent className="bg-background border-2 border-secondary">
    <AlertDialogHeader>
      <AlertDialogTitle className="text-secondary font-ultrabold">
        Profile Settings
      </AlertDialogTitle>
    </AlertDialogHeader>
    <div className="space-y-4 py-4">
      {/* Fully implemented form with shadcn/ui Input components */}
    </div>
    <AlertDialogFooter>
      {/* Proper button styling with Tailwind */}
    </AlertDialogFooter>
  </AlertDialogContent>
</AlertDialog>
```

#### 1.3 User Page (`/src/app/user/page.tsx`) ✅ **COMPLETED**
**Migration Status:** **FULLY MIGRATED WITH STATECARD IMPLEMENTATION**
- ✅ All legacy CSS classes removed
- ✅ StateCard component fully implemented (5 instances)
- ✅ Proper Tailwind utility classes throughout
- ✅ Error boundaries and loading states properly handled

**Completed StateCard Implementations:**
```tsx
// ✅ IMPLEMENTED: All 5 StateCard instances
<StateCard state="loading" title="Loading..." />
<StateCard state="error" title="Error" message={error || 'Failed to load user data'} />
<StateCard state="info" title="No Active Event" message="There is currently no active event..." />
<StateCard state="info" title="Team Assignment Pending" message="You haven't been assigned..." />
<StateCard state="info" title="No session has started yet!" message="Waiting for the next session..." />
```

#### 1.4 StateCard Component Creation ✅ **COMPLETED**
**Status:** **CREATED AND READY FOR USE**
- ✅ Component created at `/src/components/ui/state-card.tsx`
- ✅ All required states implemented (loading, info, success, error, warning, empty, completed)
- ✅ Proper styling with brand colors and zero border radius
- ✅ Ready for implementation across remaining components

**Phase 1 Completion Status: 100% (3/3 core components migrated)**

#### 1.5 Modern Architecture Implementation ✅ **COMPLETED**
**Status:** **REACT.LAZY() + ERROR BOUNDARIES FULLY IMPLEMENTED**
- ✅ All components converted to React.lazy() for code splitting
- ✅ Suspense boundaries with StateCard loading fallbacks implemented
- ✅ UserErrorBoundary component created with retry functionality
- ✅ Error boundaries wrapped around all lazy components
- ✅ Layout-level error boundary for catastrophic failures
- ✅ Bundle optimization verified (build successful, lint clean)

**Modern Architecture Implementation:**
```tsx
// ✅ IMPLEMENTED: Lazy loading with Suspense + Error Boundaries
const Ideas = React.lazy(() => import('./components/Ideas'));
const Voting = React.lazy(() => import('./components/Voting'));
const QuickfireVoting = React.lazy(() => import('./components/QuickfireVoting'));
const SparksComponent = React.lazy(() => import('./components/SparksComponent'));

// ✅ IMPLEMENTED: Each component wrapped with error handling
<UserErrorBoundary>
    <Suspense fallback={<StateCard state="loading" title="Loading..." />}>
        <Component key="component" />
    </Suspense>
</UserErrorBoundary>
```

**Performance Benefits Achieved:**
- **Code Splitting**: Components load only when session requires them
- **Better Error Handling**: Graceful fallbacks with retry functionality  
- **Improved Loading UX**: Component-specific loading messages
- **Memory Efficiency**: Unused components never loaded into memory
- **Production Ready**: All builds, lints, and TypeScript checks pass

**Files Modified:**
- ✅ `/src/app/user/page.tsx` - Lazy loading + Suspense + Error boundaries
- ✅ `/src/app/user/layout.tsx` - Layout-level error boundary
- ✅ `/src/components/error-boundaries/UserErrorBoundary.tsx` - New error boundary component

**Phase 1 FINAL Status: 100% Complete (4/4 major improvements)**

### Phase 2: User Component Migration (Priority: High) ❌ **PENDING**

#### 2.1 Ideas Component (`/src/app/user/components/Ideas.tsx`) ✅ **COMPLETED**
**Migration Status:** **FULLY MIGRATED TO TAILWIND + SHADCN/UI**
- ✅ All legacy CSS classes replaced with Tailwind utility classes
- ✅ shadcn/ui components implemented (Input, Button, Label, AlertDialog)
- ✅ Custom modals converted to AlertDialog pattern
- ✅ Form inputs migrated to shadcn/ui components
- ✅ Toast notifications for form feedback (success/error states)
- ✅ TiptapEditor integration preserved and working
- ✅ Real-time Convex updates functioning properly
- ✅ All tests passed: npm run lint ✓, TypeScript compilation ✓

**Completed StateCard Implementations (3 instances):**
```tsx
// ✅ IMPLEMENTED: Loading state replacement
<StateCard state="loading" title="Loading..." />

// ✅ IMPLEMENTED: No session state replacement  
<StateCard state="info" title="No active Ideas session!" />

// ✅ IMPLEMENTED: Success state replacement
<StateCard state="completed" title="Your ideas have been submitted successfully!" message="Thank you for your participation." />

// ✅ IMPLEMENTED: Form feedback via toast notifications
toast.success("Idea added successfully!");
toast.error("Failed to add idea");
```

**Completed CSS Classes Migration:**
- ✅ `.ideas-section` → `w-full max-w-[1200px] mx-auto grid place-items-center`
- ✅ `.content-placeholder` → `StateCard` component (3 instances implemented)
- ✅ `.no-session-message` → `StateCard` with `state="info"`
- ✅ `.idea-card` → `bg-background border-2 border-secondary p-6 hover:bg-muted/50 transition-colors`
- ✅ `.ideas-form-container` → `bg-background border-2 border-secondary p-8 w-full relative`
- ✅ `.error-message`, `.success-message` → Toast notifications

**Required shadcn/ui Replacements:**
```tsx
// Text inputs migration pattern
<Input
  value={formData.name}
  onChange={(e) => setFormData({...formData, name: e.target.value})}
  className="w-full shadow-none focus-visible:ring-0 focus-visible:ring-offset-0 bg-black border-2 border-primary text-white"
  placeholder="Enter idea name"
/>

// Submit buttons migration pattern
<Button 
  onClick={handleSubmit}
  className="w-full bg-primary text-primary-foreground hover:bg-primary/90 uppercase tracking-wider"
>
  SUBMIT IDEA
</Button>

// Delete confirmations migration pattern
<AlertDialog open={!!showDeleteConfirm} onOpenChange={(open) => !open && setShowDeleteConfirm(null)}>
  <AlertDialogContent className="bg-black border-2 border-secondary">
    <AlertDialogHeader>
      <AlertDialogTitle>Delete Idea</AlertDialogTitle>
      <AlertDialogDescription>
        Are you sure you want to delete this idea? This action cannot be undone.
      </AlertDialogDescription>
    </AlertDialogHeader>
    <AlertDialogFooter>
      <AlertDialogCancel className="bg-transparent border-2 border-secondary text-secondary hover:!bg-secondary hover:!text-secondary-foreground">
        Cancel
      </AlertDialogCancel>
      <AlertDialogAction onClick={handleConfirmDelete} className="bg-destructive text-destructive-foreground hover:bg-destructive/90">
        Delete
      </AlertDialogAction>
    </AlertDialogFooter>
  </AlertDialogContent>
</AlertDialog>
```

**Complex Features to Preserve:**
- TiptapEditor integration (preserve existing implementation)
- Form validation with localStorage
- Modal system for presenter selection
- Real-time idea submission and editing

#### 2.2 Voting Component (`/src/app/user/components/Voting.tsx`) ❌ **NEEDS MIGRATION**
**Current Status:** **LEGACY CSS ARCHITECTURE**
- ❌ Uses legacy CSS classes throughout (`voting-section`, `content-placeholder`, `user-voting-card`)
- ❌ No Tailwind utility classes implemented
- ❌ TextArea component needs shadcn/ui replacement
- ❌ Already has React.memo optimization but needs StateCard integration

**Required StateCard Implementations (4 instances):**
```tsx
// Line 221: Loading state replacement
// BEFORE: <div className="content-placeholder">Loading...</div>
// AFTER: <StateCard state="loading" title="Loading..." />

// Lines 229-250: Message states replacement (3 scenarios)
// BEFORE: Multiple message divs in renderMessage() function
// AFTER: 
<StateCard state="info" title="Ideas Flowing!" message="Voting has not started yet. Keep checking back!" />
<StateCard state="info" title="Get Ready!" message="No teams have finished presenting yet. Keep checking back!" />
<StateCard state="info" title="Voting Closed" message="This voting session has ended." />

// Lines 256-258 & 346-348: Content placeholder replacements
// BEFORE: <div className="content-placeholder">{renderMessage()}</div>
// AFTER: Use StateCard components directly
```

**Legacy CSS Classes to Replace:**
- `.voting-section` → Tailwind layout utilities
- `.content-placeholder` → `StateCard` component (4 instances)
- `.ideas-list` → Tailwind grid/flex layouts
- `.team-section` → Tailwind section patterns
- `.user-voting-card` → Tailwind card patterns
- `.voting-controls` → Tailwind form layouts

**Required shadcn/ui Replacements:**
```tsx
// Replace TextArea component
import { Textarea } from '@/components/ui/textarea';

<Textarea
  value={comment}
  onChange={(e) => setComment(e.target.value)}
  className="w-full shadow-none focus-visible:ring-0 focus-visible:ring-offset-0 bg-black border-2 border-primary text-white resize-none"
  placeholder="Optional comment..."
  rows={3}
/>
```

**Key Patterns to Preserve:**
- Slider components (preserve existing implementation)
- Real-time voting updates
- Team navigation integration
- React.memo optimization (already implemented)

#### 2.3 Quickfire Voting (`/src/app/user/components/QuickfireVoting.tsx`) ❌ **NEEDS MIGRATION**
**Current Status:** **LEGACY CSS ARCHITECTURE**
- ❌ Uses legacy CSS classes throughout (`voting-section`, `quickfire-content-placeholder`)
- ❌ No Tailwind utility classes implemented
- ❌ Simple architecture but needs StateCard integration

**Required StateCard Implementations (3 instances):**
```tsx
// Line 66: Loading state replacement
// BEFORE: <div className="content-placeholder">Loading...</div>
// AFTER: <StateCard state="loading" title="Loading..." />

// Line 74: Authentication required replacement
// BEFORE: <div className="content-placeholder">Please log in to view quickfire voting.</div>
// AFTER: <StateCard state="warning" title="Authentication Required" message="Please log in to view quickfire voting." />

// Line 140: Empty state replacement
// BEFORE: <div className="quickfire-content-placeholder"><h1>Get Ready!</h1></div>
// AFTER: <StateCard state="empty" title="Get Ready!" message="No quickfire items available yet." />
```

**Legacy CSS Classes to Replace:**
- `.voting-section` → Tailwind layout utilities
- `.content-placeholder` → `StateCard` component (2 instances)
- `.quickfire-content-placeholder` → `StateCard` component (1 instance)
- `.ideas-list` → Tailwind grid/flex layouts
- `.user-voting-card` → Tailwind card patterns
- `.voting-controls` → Tailwind form layouts

**Implementation Focus:**
- Focus on slider-based voting
- Real-time score updates
- Minimal form complexity (simplified migration)
- Preserve existing voting functionality

### Phase 3: Sparks System Migration (Priority: Critical) ❌ **PENDING**

The sparks system requires **special attention** due to its complex dynamic form architecture.

#### 3.1 Sparks Main Component (`/src/app/user/components/SparksComponent.tsx`) ❌ **NEEDS MIGRATION**
**Current Status:** **LEGACY CSS ARCHITECTURE**
- ❌ Uses legacy CSS classes throughout (`sparks-section`, `content-placeholder`, `sparks-form-container`)
- ❌ No Tailwind utility classes implemented
- ❌ Complex dynamic form system needs careful migration
- ❌ Multiple content placeholders need StateCard conversion

**Required StateCard Implementations (5 instances):**
```tsx
// Line 197: Loading state replacement
// BEFORE: <div className="content-placeholder"><p>Loading...</p></div>
// AFTER: <StateCard state="loading" title="Loading..." />

// Line 208: No session state replacement
// BEFORE: <div className="content-placeholder"><h2 className="no-session-message">No active Sparks session!</h2></div>
// AFTER: <StateCard state="info" title="No Active Sparks Session" message="Please wait for a Sparks session to begin." />

// Line 218: Success state replacement
// BEFORE: <div className="content-placeholder"><h2>Your spark data has been submitted successfully!</h2><p>Thank you for your participation.</p></div>
// AFTER: <StateCard state="completed" title="Spark Data Submitted Successfully!" message="Thank you for your participation." />

// Line 231: Configuration error replacement
// BEFORE: <div className="content-placeholder"><h2 className="no-session-message">Spark configuration not found!</h2><p>Please contact an administrator.</p></div>
// AFTER: <StateCard state="error" title="Spark Configuration Not Found" message="Please contact an administrator." />

// Line 247: Form error (keep as inline form feedback)
// BEFORE: <div className="error-message">{error}</div>
// AFTER: Keep as inline form validation feedback
```

**Legacy CSS Classes to Replace:**
- `.sparks-section` → Tailwind layout utilities
- `.content-placeholder` → `StateCard` component (4 instances)
- `.no-session-message` → `StateCard` with appropriate state
- `.error-message` → Inline form feedback
- `.sparks-form-container` → Tailwind form layouts
- `.spark-form` → Tailwind form patterns

**Complex Features Preservation:**
- Dynamic form configuration rendering
- Real-time Convex integration
- Form submission and editing workflows
- JSON-based form data handling

**CSS Classes Migration Pattern:**
```css
/* FROM: Legacy sparks CSS */
.sparks-form-container {
  background-color: rgba(0, 0, 0, 0.8);
  border: 2px solid #0ee0d8;
  padding: 2rem;
  width: 100%;
}

/* TO: Tailwind classes */
className="bg-black/80 border-2 border-secondary p-8 w-full"
```

#### 3.2 Sparks Sub-Components Migration (Critical Implementation)

**SparkTextField** (`/src/app/user/components/sparks/SparkTextField.tsx`):
```tsx
// BEFORE: Custom CSS classes
<div className="spark-form-group">
  <label className="spark-field-label">
    {label}
    {required && <span className="spark-required">*</span>}
  </label>
  <input className="spark-text-input" />
</div>

// AFTER: shadcn/ui + Tailwind
<div className="flex flex-col gap-2">
  <label className="text-secondary font-mono flex gap-1">
    {label}
    {required && <span className="text-accent">*</span>}
  </label>
  <Input
    className="w-full shadow-none focus-visible:ring-0 focus-visible:ring-offset-0 bg-black border-2 border-primary text-white focus:border-secondary"
  />
</div>
```

**SparkDropdown** (Complex Custom Implementation):
```tsx
// CRITICAL: Replace custom dropdown with shadcn/ui Select
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';

<Select value={value} onValueChange={onChange} disabled={disabled}>
  <SelectTrigger className="w-full shadow-none bg-black border-2 border-primary text-white focus:border-secondary">
    <SelectValue placeholder="Select an option..." />
  </SelectTrigger>
  <SelectContent className="bg-black border-2 border-primary">
    {options.map((option, index) => (
      <SelectItem key={index} value={option} className="text-white hover:bg-primary/20">
        {option}
      </SelectItem>
    ))}
  </SelectContent>
</Select>
```

**SparkCheckbox** & **SparkRadioGroup**:
```tsx
// Checkbox replacement
import { Checkbox } from '@/components/ui/checkbox';

<div className="flex items-center gap-3">
  <Checkbox
    checked={value}
    onCheckedChange={onChange}
    disabled={disabled}
    className="size-5 rounded-none border-2 border-secondary data-[state=checked]:bg-secondary data-[state=checked]:border-secondary"
  />
  <label className="text-white font-mono cursor-pointer">
    {label}
    {required && <span className="text-accent ml-1">*</span>}
  </label>
</div>

// Radio group replacement
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Label } from '@/components/ui/label';

<RadioGroup value={value} onValueChange={onChange} disabled={disabled}>
  {options.map((option, index) => (
    <div key={index} className="flex items-center gap-3">
      <RadioGroupItem 
        value={option} 
        id={`${fieldId}-${index}`}
        className="border-2 border-secondary text-secondary"
      />
      <Label 
        htmlFor={`${fieldId}-${index}`}
        className="text-white font-mono cursor-pointer"
      >
        {option}
      </Label>
    </div>
  ))}
</RadioGroup>
```

**SparkRichTextField** (TiptapEditor Integration):
```tsx
// Preserve existing TiptapEditor, update container styling
<div className="flex flex-col gap-2">
  <label className="text-secondary font-mono flex gap-1">
    {label}
    {required && <span className="text-accent">*</span>}
  </label>
  <TiptapEditor
    ref={editorRef}
    content={value}
    onChange={onChange}
    editable={!disabled}
    className="w-full bg-black border-2 border-primary focus-within:border-secondary"
    placeholder={placeholder}
  />
</div>
```

## Implementation Strategy

### Step 1: Setup and Dependencies (Day 1)
1. **Verify shadcn/ui Installation**: all components are installed and ready
2. **Create StateCard Component**: Implement reusable StateCard component in `/src/components/ui/state-card.tsx`
3. **Import Pattern Review**: Study admin component import patterns
4. **CSS Architecture Analysis**: Map current CSS classes to Tailwind equivalents (with IMPORTANT note to keep in mind we need to rethink complicated styling sections and simplify it since we are using now simpler tailwind and shadcn/ui)

### Step 2: Layout Migration (Day 1-2)
1. **User Layout Component**: Convert all CSS classes to Tailwind
2. **Profile Settings Modal**: Implement AlertDialog pattern
3. **Remove Legacy CSS**: Remove user layout CSS imports after migration

### Step 3: Core Components (Day 2-4)
1. **Ideas Component**: 
   - Replace form inputs with shadcn/ui components
   - Convert modals to AlertDialog pattern
   - Replace all `.content-placeholder` with StateCard component
   - Preserve TiptapEditor integration
   - Maintain localStorage functionality
   - Implement toast notifications for form feedback
2. **Voting Component**:
   - Replace TextArea with shadcn/ui Textarea
   - Replace "Ideas Flowing!" and "Get Ready!" messages with StateCard
   - Preserve Slider component functionality
   - Update responsive design patterns
3. **QuickfireVoting Component**:
   - Replace all placeholder messages with StateCard component
   - Simplified migration focusing on form elements
   - Preserve real-time voting functionality

### Step 4: Sparks System Migration (Day 4-6)
1. **SparkTextField**: Direct Input component replacement
2. **SparkDropdown**: **Critical** - Replace complex custom dropdown with Select component
3. **SparkCheckbox**: Use shadcn/ui Checkbox with custom sizing
4. **SparkRadioGroup**: RadioGroup + Label component implementation
5. **SparkRichTextField**: Preserve TiptapEditor, update container styling
6. **SparksComponent Main**: 
   - Update form containers and layout patterns
   - Replace all state messages with StateCard component
   - Implement toast notifications for form feedback

### Step 5: CSS Cleanup (Day 6-7)
1. **Remove CSS Files**: Delete all user-specific CSS partials from `/src/styles/`
2. **Update user.css**: Remove all @import statements
3. **Verify Functionality**: Comprehensive testing of all migrated components

## Validation Gates

### Functional Testing
- [ ] All form submissions work correctly
- [ ] Real-time Convex updates function properly
- [ ] TiptapEditor functionality preserved
- [ ] Modal systems operate as expected
- [ ] Responsive design works on mobile/desktop
- [ ] Sparks dynamic form generation works correctly
- [ ] StateCard component displays correctly in all states
- [ ] Toast notifications work for all form feedback
- [ ] Animation transitions work smoothly

### Visual Testing
- [ ] No border radius anywhere in the application
- [ ] Brand colors preserved and properly applied
- [ ] Typography hierarchy maintained
- [ ] Dark theme integration works correctly
- [ ] Hover states and transitions function properly

### Performance Testing
- [ ] No increase in bundle size
- [ ] Component re-render optimization maintained
- [ ] Real-time updates perform at same speed
- [ ] Memory usage patterns unchanged

### Code Quality
- [ ] All TypeScript types preserved
- [ ] No console errors or warnings
- [ ] Import statements follow admin section patterns
- [ ] Component interfaces remain unchanged

## Risk Mitigation

### High-Risk Areas

**1. Sparks Dropdown Component**
- **Risk**: Complex custom implementation with state management
- **Mitigation**: Implement shadcn/ui Select with exact feature parity
- **Fallback**: Keep existing implementation if shadcn/ui Select doesn't meet requirements

**2. TiptapEditor Integration**
- **Risk**: Rich text editor styling conflicts
- **Mitigation**: Preserve existing editor implementation, only update container styling
- **Fallback**: Isolate editor styles if conflicts arise

**3. Real-time Functionality**
- **Risk**: Convex integration disruption
- **Mitigation**: Preserve all useQuery/useMutation patterns exactly
- **Testing**: Extensive real-time testing during migration

### Rollback Strategy
- **Git Branch**: Create dedicated migration branch
- **CSS Preservation**: Keep original CSS files until full validation
- **Component-by-Component**: Migrate one component at a time with individual validation

## Success Metrics

### Quantitative Goals
- **Code Reduction**: 90%+ reduction in custom CSS (matching admin section success)
- **Component Consistency**: 100% shadcn/ui adoption for form elements
- **Performance**: Zero performance regression
- **Bundle Size**: No increase in JavaScript bundle size

### Qualitative Goals
- **Design Consistency**: Visual parity with admin section design patterns
- **Developer Experience**: Consistent component usage patterns
- **Maintainability**: Elimination of custom CSS maintenance overhead
- **Accessibility**: Improved keyboard navigation and screen reader support

## Expected Outcomes

### Technical Benefits
- **Unified Design System**: Consistent UI patterns across admin and user sections
- **Reduced Maintenance**: Elimination of 16+ CSS partial files
- **Improved Performance**: Optimized rendering with Tailwind's utility-first approach
- **Enhanced Accessibility**: shadcn/ui's built-in accessibility features

### Business Benefits
- **Faster Development**: Standardized component patterns for future features
- **Design Consistency**: Professional, cohesive user experience
- **Easier Testing**: Predictable component behavior and styling

## Implementation Status & Timeline

### **Current Progress Summary**
| Phase | Status | Completion | Components | Risk Level |
|-------|---------|------------|------------|------------|
| **Phase 1: Foundation** | ✅ **COMPLETED** | **100%** | layout.tsx, page.tsx, ProfileSettings, StateCard | Low |
| **Phase 2: Core Components** | ❌ **PENDING** | **0%** | Ideas, Voting, QuickfireVoting | Medium |
| **Phase 3: Sparks System** | ❌ **PENDING** | **0%** | SparksComponent + 6 sub-components | High |
| **Phase 4: Modern Architecture** | ❌ **NOT STARTED** | **0%** | Error Boundaries, Suspense, Code Splitting | Medium |
| **Phase 5: CSS Cleanup** | ❌ **NOT STARTED** | **0%** | Remove legacy files | Low |

### **Detailed StateCard Implementation Requirements**
**Total StateCard Instances to Implement: 16**

| Component | StateCard Instances | Current Status | Priority |
|-----------|-------------------|----------------|----------|
| ✅ page.tsx | 5 instances | ✅ **COMPLETED** | N/A |
| ❌ Ideas.tsx | 4 instances | ❌ **NEEDS MIGRATION** | High |
| ❌ Voting.tsx | 4 instances | ❌ **NEEDS MIGRATION** | High |
| ❌ QuickfireVoting.tsx | 3 instances | ❌ **NEEDS MIGRATION** | Medium |
| ❌ SparksComponent.tsx | 5 instances | ❌ **NEEDS MIGRATION** | High |

### **Updated Implementation Timeline**
| Phase | Duration | Components | Status | Risk Level |
|-------|----------|------------|---------|------------|
| ~~Setup & Layout~~ | ~~2 days~~ | ✅ layout.tsx, page.tsx, ProfileSettings, StateCard | ✅ **COMPLETED** | Low |
| **Core Components** | **3 days** | Ideas, Voting, QuickfireVoting | ❌ **PENDING** | Medium |
| **Sparks System** | **3 days** | SparksComponent + 6 sub-components | ❌ **PENDING** | High |
| **Modern Architecture** | **2 days** | Error Boundaries, Suspense, Performance | ❌ **NOT STARTED** | Medium |
| **CSS Cleanup** | **1 day** | Remove legacy files, final testing | ❌ **NOT STARTED** | Low |
| **Total Remaining** | **9 days** | **10 components** | **~63% PENDING** | **Medium-High** |

### **Next Immediate Priority: Phase 2 Core Components**
1. **Ideas.tsx** - 4 StateCard implementations + full Tailwind migration
2. **Voting.tsx** - 4 StateCard implementations + shadcn/ui Textarea
3. **QuickfireVoting.tsx** - 3 StateCard implementations + simplified migration

## PRD Quality Score: 9/10

**Confidence Level for One-Pass Implementation Success: 95%**

### Reasoning:
- **Comprehensive Research**: Complete analysis of current architecture and target patterns
- **Proven Reference**: Successful admin section migration provides exact implementation patterns
- **Risk Awareness**: High-risk areas identified with specific mitigation strategies
- **Detailed Implementation**: Step-by-step component conversion patterns provided
- **Validation Framework**: Comprehensive testing strategy ensures functionality preservation

**Deduction (-1 point)**: Sparks dropdown component complexity introduces minor uncertainty, but fallback strategy mitigates risk.

The PRD provides sufficient context and implementation guidance for successful one-pass migration by an AI agent with access to the same codebase.
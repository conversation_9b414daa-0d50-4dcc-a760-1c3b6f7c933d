 🧮 Vote Averaging Logic Explained

  Core Principle: AVERAGE, Not Sum

  The leaderboard uses average vote scores to ensure fairness regardless of how many people voted on each item.

  Example:
  - Idea A: Gets votes of [8, 9, 7] → Average = (8+9+7)/3 = 8.0
  - Idea B: Gets votes of [10, 6] → Average = (10+6)/2 = 8.0
  - Idea C: Gets votes of [9] → Average = 9/1 = 9.0

  Result: Idea C wins even though it got fewer votes, because quality matters more than quantity.

  Precision: Rounded to 1 Decimal Place

  // In both Ideas and Quickfire leaderboards:
  if (voteCount > 0) {
    const sum = votes.reduce((acc, vote) => acc + vote.score, 0);
    totalScore = Math.round((sum / voteCount) * 10) / 10; // Round to 1 decimal
  }

  Example:
  - Votes: [8, 9, 8] → Sum = 25, Count = 3
  - Average = 25/3 = 8.333...
  - Final Score = 8.3 (rounded to 1 decimal)

  🎯 Ideas Leaderboard (Regular Voting)

  Data Structure:

  // votes table - Regular idea voting
  {
    userId: "user123",
    ideaId: "idea456",
    score: 8,           // 0-10 range
    comment: "Great innovation!",
    eventId: "event789",
    sessionId: "session101"
  }

  Calculation Process:

  1. Get submitted ideas for session/event
  2. Find all votes for each idea using ideaId
  3. Calculate average of all vote scores
  4. Include team information (ideas belong to teams)
  5. Sort by score (highest first)

  Example Ideas Leaderboard:

  | Rank | Idea            | Team       | Score | Votes   |
  |------|-----------------|------------|-------|---------|
  | 1    | AI Assistant    | Team Alpha | 9.2   | 5 votes |
  | 2    | Smart Dashboard | Team Beta  | 8.7   | 3 votes |
  | 3    | Mobile App      | Team Gamma | 8.1   | 7 votes |

  ⚡ Quickfire Leaderboard (Fast Voting)

  Data Structure:

  // quickfireVotes table - Fast-paced voting
  {
    userId: "user123",
    quickfireId: "qf456",  // Links to quickfires table (admin-created items)
    score: 9,              // 0-10 range
    voter: "john_doe",     // Username for compatibility
    createdAt: 1703123456
  }

  Key Differences from Ideas:

  1. Admin-Created Items vs User Ideas

  - Ideas: Created by team members during brainstorming sessions
  - Quickfire: Created by admins for rapid voting sessions

  2. Different Data Sources

  - Ideas: ideas table → voted on via votes table
  - Quickfire: quickfires table → voted on via quickfireVotes table

  3. Voting Activation

  // Quickfire items have voting control
  {
    idea: "Should we implement feature X?",
    votingActive: true,  // Only items with this=true appear for voting
    sessionId: "session123"
  }

  4. No Team Association

  - Ideas: Belong to teams, show team names in leaderboard
  - Quickfire: Standalone items, no team column in leaderboard

  Quickfire Calculation Process:

  1. Get quickfire items with votingActive: true
  2. Find votes using quickfireId
  3. Same averaging logic as ideas
  4. No team lookup needed

  Example Quickfire Leaderboard:

  | Rank | Quickfire Question     | Score | Votes    |
  |------|------------------------|-------|----------|
  | 1    | "Implement AI chat?"   | 9.4   | 8 votes  |
  | 2    | "Mobile-first design?" | 8.8   | 6 votes  |
  | 3    | "Add dark mode?"       | 7.9   | 12 votes |

  🔄 Vote Range & Validation

  Both Systems Use 0-10 Scale:

  // In both vote submission functions:
  if (args.score < 0 || args.score > 10) {
    throw new Error("Score must be between 0 and 10");
  }

  Vote Update Logic:

  // Both systems check for existing votes:
  const existingVote = await ctx.db.query("votes") // or "quickfireVotes"
    .withIndex("by_user_idea", (q) => q.eq("userId", user._id).eq("ideaId", args.ideaId))
    .first();

  if (existingVote) {
    // UPDATE existing vote
    await ctx.db.patch(existingVote._id, { score: args.score });
  } else {
    // CREATE new vote
    await ctx.db.insert("votes", { userId, ideaId, score });
  }

  📊 Leaderboard Display Logic

  All Sessions vs Individual Session:

  All Sessions Leaderboard (/leaderboard/all):

  - Shows: Only Ideas sessions combined
  - Excludes: Quickfire sessions (different voting paradigm)
  - Purpose: Overall event rankings across all brainstorming

  Individual Session Leaderboard (/leaderboard/session/[id]):

  - Ideas Session: Shows ideas with team names
  - Quickfire Session: Shows quickfire items (no teams)
  - Purpose: Session-specific rankings

  Real-time Updates:

  Both leaderboards update automatically when:
  - New votes are submitted
  - Vote scores are changed
  - Items are added/removed
  - Voting is activated/deactivated

  This averaging system ensures fair competition where quality of ideas matters more than quantity of votes, while the quickfire system enables rapid feedback on specific questions or proposals.

# React Design Patterns and Best Practices for 2025



## Table of Contents

- [Modern Component Patterns](https://www.telerik.com/blogs/react-design-patterns-best-practices#modern-component-patterns)
- [State Management](https://www.telerik.com/blogs/react-design-patterns-best-practices#state-management)
- [TypeScript](https://www.telerik.com/blogs/react-design-patterns-best-practices#typescript)
- [React 19 and Ecosystem Updates](https://www.telerik.com/blogs/react-design-patterns-best-practices#react-19-and-ecosystem-updates)
- [Frameworks](https://www.telerik.com/blogs/react-design-patterns-best-practices#frameworks)
- [Component Libraries and Design Systems](https://www.telerik.com/blogs/react-design-patterns-best-practices#component-libraries-and-design-systems)
- [Wrap-up](https://www.telerik.com/blogs/react-design-patterns-best-practices#wrap-up)



## Modern Component Patterns

### Function Components as the Standard

Function components have become the de facto standard for React development, replacing [class components](https://react.dev/reference/react/Component) for practically all use cases. This shift reflects React’s move toward a more functional programming paradigm, emphasizing simplicity and composability.

```prism jsx
function UserProfile({ userId }) {
  const [user, setUser] = useState(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    async function fetchUser() {
      setLoading(true);
      try {
        const userData = await fetchUserData(userId);
        setUser(userData);
      } catch (error) {
        console.error("Failed to fetch user data:", error);
      } finally {
        setLoading(false);
      }
    }
    fetchUser();
  }, [userId]);

  if (loading) return <LoadingSpinner />;
  if (!user) return <ErrorMessage message="User not found" />;

  return (
    <div className="user-profile">
      <h2>{user.name}</h2>
      <p>{user.email}</p>
      {/* Additional user information */}
    </div>
  );
}

```

React JSX

The above `UserProfile` component uses [hooks](https://react.dev/reference/react/hooks) to fetch and display user data based on a given ID, handling loading and error states along the way.

Function components follow a straightforward input-output model, making them easier to understand and test. They also enable using React’s hooks system for state management and lifecycle events.



### Custom Hooks for Logic Reusability

Custom hooks represent one of the most powerful patterns in modern React development. They enable the extraction of stateful logic into reusable functions, promoting code reuse and separation of concerns.

In the example below, a `useFormInput` custom hook manages form input state and behavior, allowing components to reuse this consistent input logic easily.

```prism jsx
// Custom hook for handling form input
function useFormInput(initialValue) {
  const [value, setValue] = useState(initialValue);

  function handleChange(e) {
    setValue(e.target.value);
  }

  return {
    value,
    onChange: handleChange,
    reset: () => setValue(initialValue),
  };
}

// Usage in a component
function LoginForm() {
  const email = useFormInput("");
  const password = useFormInput("");

  function handleSubmit(e) {
    e.preventDefault();
    // Login logic here using email.value and password.value

    // Reset form after submission
    email.reset();
    password.reset();
  }

  return (
    <form onSubmit={handleSubmit}>
      <input type="email" placeholder="Email" {...email} />
      <input type="password" placeholder="Password" {...password} />
      <button type="submit">Login</button>
    </form>
  );
}

```

React JSX

Custom hooks can help extract complex logic components, making them more focused on rendering. The same logic can be shared across multiple components without duplication, and custom hooks can be tested independently from the used components.

## State Management

### Context API for Application-Wide State

The Context API has matured into a reasonable solution for managing application-wide state, reducing the need for external state management libraries. With React 19, the [Context API](https://react.dev/learn/passing-data-deeply-with-context) has become even more powerful by introducing the [use function](https://react.dev/reference/react/use) for accessing context values.

```prism jsx
// Creating a context
const ThemeContext = createContext({
  theme: "light",
  toggleTheme: () => {},
});

// Provider component
function ThemeProvider({ children }) {
  const [theme, setTheme] = useState("light");

  const toggleTheme = () => {
    setTheme((prevTheme) => (prevTheme === "light" ? "dark" : "light"));
  };

  const value = { theme, toggleTheme };

  return <ThemeContext value={value}>{children}</ThemeContext>;
}

// Using context with the new 'use' API in React 19
function ThemedButton({ variant }) {
  // We can use the 'use' function in conditional blocks
  if (variant === "primary") {
    const { theme, toggleTheme } = use(ThemeContext);

    return (
      <button className={`btn-${variant} ${theme}`} onClick={toggleTheme}>
        Toggle Theme
      </button>
    );
  }

  return <button className={`btn-${variant}`}>Regular Button</button>;
}

```

React JSX

The above example sets up a theme context and provider, allowing components like `ThemedButton` to access and update the current theme using the new `use()` API introduced in React 19.

The Context API is well-suited for theme management, user authentication, localization and feature flags across an application.

> **💡Want a deeper understanding of when and how to use React Context?**
>
> Take a look at [this article on React Context](https://www.telerik.com/blogs/react-basics-how-when-use-react-context) for a clear explanation of its use case and benefit.

## TypeScript

[TypeScript](https://www.typescriptlang.org/) has become an integral part of React development, with many new projects in industry adopting it from the outset. The benefits of using TypeScript with React include type safety by catching type-related errors at compile time rather than runtime, improved developer experience with enhanced IDE support, and self-documenting code with type annotations serving as documentation.

### Type-Safe Components and Props

TypeScript enables the creation of type-safe components with well-defined props interfaces:

```prism jsx
// Defining prop types
interface UserCardProps {
  user: {
    id: number,
    name: string,
    email: string,
    role: "admin" | "user" | "guest",
    profileImage?: string,
  };
  onEdit?: (userId: number) => void;
  variant?: "compact" | "detailed";
}

// Type-safe component
function UserCard({ user, onEdit, variant = "detailed" }: UserCardProps) {
  return (
    <div className={`user-card ${variant}`}>
      {user.profileImage && (
        <img src={user.profileImage} alt={`${user.name}'s profile`} />
      )}

      <h3>{user.name}</h3>
      {variant === "detailed" && (
        <>
          <p>{user.email}</p>
          <p>Role: {user.role}</p>
        </>
      )}

      {onEdit && <button onClick={() => onEdit(user.id)}>Edit</button>}
    </div>
  );
}

```

React JSX

The above example defines a strongly typed component using a `UserCardProps` interface, so the props passed to `UserCard` are validated at compile time.

### Type-Safe Hooks

TypeScript enhances React hooks by providing type safety for state, effects and custom hooks:

```prism jsx
function useLocalStorage<T>(key: string, initialValue: T): [T, (value: T) => void] {
  const [storedValue, setStoredValue] = useState<T>(() => {
    try {
      const item = window.localStorage.getItem(key);
      return item ? JSON.parse(item) : initialValue;
    } catch (error) {
      console.error(error);
      return initialValue;
    }
  });

  const setValue = (value: T) => {
    try {
      setStoredValue(value);
      window.localStorage.setItem(key, JSON.stringify(value));
    } catch (error) {
      console.error(error);
    }
  };

  return [storedValue, setValue];
}

// Usage with type inference
function UserPreferences() {
  // TypeScript infers that preferences is of type UserPrefs
  const [preferences, setPreferences] = useLocalStorage<UserPrefs>('userPrefs', {
    theme: 'light',
    notifications: true,
    fontSize: 'medium'
  });

  // Now we get autocompletion and type checking for preferences
  return (
    <div>
      <h2>User Preferences</h2>
      <label>
        Theme:
        <select
          value={preferences.theme}
          onChange={(e) => setPreferences({
            ...preferences,
            theme: e.target.value as 'light' | 'dark'
          })}
        >
          <option value="light">Light</option>
          <option value="dark">Dark</option>
        </select>
      </label>
      {/* More preference controls */}
    </div>
  );
}

```

React JSX

The custom hook `useLocalStorage` uses [generics](https://www.typescriptlang.org/docs/handbook/2/generics.html) (i.e., type variables) to provide type-safe access and updates to local storage values for consistent usage across components.

### Generic Components

TypeScript’s generics allow the creation of highly reusable components that maintain type safety:

```prism jsx
interface SelectProps<T> {
  items: T[];
  selectedItem: T | null;
  onSelect: (item: T) => void;
  getDisplayText: (item: T) => string;
  getItemKey: (item: T) => string | number;
}

function Select<T>({
  items,
  selectedItem,
  onSelect,
  getDisplayText,
  getItemKey
}: SelectProps<T>) {
  return (
    <div className="select-container">
      <div className="selected-item">
        {selectedItem ? getDisplayText(selectedItem) : 'Select an item'}
      </div>

      <ul className="items-list">
        {items.map(item => (
          <li
            key={getItemKey(item)}
            className={item === selectedItem ? 'selected' : ''}
            onClick={() => onSelect(item)}
          >
            {getDisplayText(item)}
          </li>
        ))}
      </ul>
    </div>
  );
}

// Usage with different data types
interface User {
  id: number;
  name: string;
  email: string;
}

function UserSelector() {
  const [selectedUser, setSelectedUser] = useState<User | null>(null);
  const users: User[] = [\
    { id: 1, name: 'John Doe', email: '<EMAIL>' },\
    { id: 2, name: 'Jane Smith', email: '<EMAIL>' }\
  ];

  return (
    <Select<User>
      items={users}
      selectedItem={selectedUser}
      onSelect={setSelectedUser}
      getDisplayText={(user) => user.name}
      getItemKey={(user) => user.id}
    />
  );
}

```

React JSX

The generic `Select` component above adapts to any data type using TypeScript generics, enabling reuse while preserving strict typing for props like display text and keys.


## React 19 and Ecosystem Updates

### New Hooks

React 19 introduced several new hooks, which include [useActionState](https://react.dev/reference/react/useActionState), [useFormStatus](https://react.dev/reference/react-dom/hooks/useFormStatus), [useOptimistic](https://react.dev/reference/react/useOptimistic) and the new [use API](https://react.dev/reference/react/use). These hooks provide elegant solutions for everyday tasks like form handling and optimistic UI updates. Here’s an example of the `useOptimistic` hook in action:

```prism jsx
function MessageList({ messages, onSendMessage }) {
  const [optimisticMessages, addOptimisticMessage] = useOptimistic(
    messages,
    (state, newMessage) => [...state, newMessage]
  );

  const handleSubmit = async (formData) => {
    const content = formData.get("message");

    // Create optimistic version of the message
    const optimisticMessage = {
      id: `temp-${Date.now()}`,
      content,
      status: "sending",
    };

    // Update UI immediately
    addOptimisticMessage(optimisticMessage);

    // Send the actual message
    await onSendMessage(content);
  };

  return (
    <form action={handleSubmit}>
      <input name="message" />
      <button type="submit">Send</button>
      <div className="messages">
        {optimisticMessages.map((message) => (
          <div key={message.id} className={`message ${message.status || ""}`}>
            {message.content}
          </div>
        ))}
      </div>
    </form>
  );
}

```

React JSX

In the example above, the `useOptimistic` hook immediately displays a new message in the UI _before_ the network request completes, providing a smoother and faster user experience through optimistic updates.

### React Server Components

[React Server Components](https://react.dev/reference/rsc/server-components) represent a significant paradigm shift in how React applications are structured and rendered. They enable components to run exclusively on the server, accessing data sources directly without client-side API calls and reducing JavaScript bundle sizes.

The key distinction between Server and Client Components lies in where they execute and what capabilities they have:

**Server Components:**

- Execute on the server before sending HTML to the client
- Can access server resources directly (databases, file system)
- Cannot use client-side browser APIs or React state hooks
- Do not increase the JavaScript bundle size
- Automatically marked as Server Components by default in frameworks like Next.js

**Client Components:**

- Execute in the browser
- Can use interactive features (state, effects, event handlers)
- Must be explicitly marked with the `"use client"` directive
- Contribute to the JavaScript bundle size

Here’s a simple example of how a Server Component and Client Component work together:

```prism jsx
// Server Component
async function ProductPage({ productId }) {
  // Direct server-side data access
  const product = await db.products.findById(productId);

  return (
    <div className="product-page">
      <h1>{product.name}</h1>
      <p className="description">{product.description}</p>
      <p className="price">${product.price.toFixed(2)}</p>

      {/* Client Component for interactivity */}
      <AddToCartButton productId={product.id} />
    </div>
  );
}

// Client Component for interactive elements
("use client");
function AddToCartButton({ productId }) {
  const [isAdding, setIsAdding] = useState(false);

  async function handleAddToCart() {
    setIsAdding(true);
    await addToCart(productId);
    setIsAdding(false);
  }

  return (
    <button onClick={handleAddToCart} disabled={isAdding}>
      {isAdding ? "Adding..." : "Add to Cart"}
    </button>
  );
}

```

React JSX

The `ProductPage` above is a Server Component that fetches product data directly from the database and renders static HTML, while the `AddToCartButton` is a Client Component that handles interactive behavior like managing button state and responding to user clicks in the browser. Together, they separate data fetching from interactivity which can optimize performance and user experience.

> **💡Want a more complete rundown of what’s new in React 19?**
>
> Check out our previous article, [What’s New in React 19](https://www.telerik.com/blogs/whats-new-react-19), for a detailed look at the latest hooks, the `use()` API, server components and other key updates introduced in the latest React release.

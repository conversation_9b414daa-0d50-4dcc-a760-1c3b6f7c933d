# Tiptap Rich Text Editor Implementation

## 1. Purpose

To provide users with basic rich text formatting capabilities (bold, italic, headings, lists, alignment, etc.) for specific text input fields within the application, replacing standard `<textarea>` elements. This enhances user experience by allowing more expressive content creation.

## 2. Implementation Overview

A reusable React component, `TiptapEditor`, was created to encapsulate the Tiptap editor logic and UI.

-   **Component Location:** `src/components/editors/TiptapEditor.tsx`
-   **Styling:** Dedicated styles are defined in `src/styles/_tiptap-editor.css`, which is imported into the main user stylesheet (`src/styles/user.css`).

## 3. Dependencies

The following Tiptap packages were installed:

```bash
npm install @tiptap/react @tiptap/pm @tiptap/starter-kit @tiptap/extension-text-align
```

-   `@tiptap/react`: React components and hooks for Tiptap.
-   `@tiptap/pm`: ProseMirror core libraries (Tiptap is built on ProseMirror).
-   `@tiptap/starter-kit`: A collection of common Tiptap extensions (Paragraphs, Bold, Italic, Headings, Lists, Code, Blockquote, etc.).
-   `@tiptap/extension-text-align`: Extension for text alignment functionality.

## 4. Component Usage (`TiptapEditor.tsx`)

The `TiptapEditor` component is designed to be a drop-in replacement for text areas where rich text is desired.

**Props:**

-   `content: string`: The initial HTML content to load into the editor. Should be an empty string (`''`) for new entries or the saved HTML string when editing existing content.
-   `onChange: (htmlContent: string) => void`: A callback function that receives the updated HTML content whenever the editor's content changes. This function should update the parent component's state.
-   `editable?: boolean`: (Optional, defaults to `true`) Controls whether the editor content can be modified. Pass `false` for read-only views or to disable during form submission.
-   `className?: string`: (Optional) Allows passing additional CSS classes for specific styling overrides if needed, although base styling is handled by `_tiptap-editor.css`.
-   `placeholder?: string`: (Optional, defaults to `'Enter text...'`) Placeholder text to display when the editor is empty. *Note: The `@tiptap/extension-placeholder` package would need to be installed and configured in the editor's extensions array for this prop to have a visual effect.*

**Integration Example (Conceptual):**

```tsx
import TiptapEditor from '@/components/editors/TiptapEditor';
import { useState } from 'react';

function MyFormComponent() {
    const [descriptionHtml, setDescriptionHtml] = useState('<p>Initial content</p>');
    const [isSubmitting, setIsSubmitting] = useState(false);

    const handleDescriptionChange = (htmlContent: string) => {
        setDescriptionHtml(htmlContent);
        // Update form data state as needed
    };

    return (
        <form>
            {/* ... other form fields */}
            <div className="form-group">
                <label htmlFor="description">Description</label>
                <TiptapEditor
                    content={descriptionHtml}
                    onChange={handleDescriptionChange}
                    editable={!isSubmitting}
                    placeholder="Enter your description..."
                    // className="optional-extra-class"
                />
            </div>
            {/* ... submit button */}
        </form>
    );
}
```

**Target Components:**

The `TiptapEditor` has replaced the `<textarea>` for the following fields:

-   `src/app/user/components/QuickfireIdeas.tsx`: `comments` field.
-   `src/app/user/components/Ideas.tsx`: `description` field.
-   `src/app/user/components/Opportunities.tsx`: `insights` field.

## 5. Features & Toolbar

The editor is configured with:

-   **`StarterKit`:** Provides default functionality for:
    -   Paragraphs
    -   Headings (Levels 1-3 enabled via toolbar)
    -   Bold, Italic, Strike
    -   Code (inline), Code Block
    -   Bullet List, Ordered List
    -   Blockquote
    -   Horizontal Rule
    -   Undo/Redo History
-   **`TextAlign`:** Enables text alignment (Left, Center, Right, Justify) for paragraphs and headings.

A toolbar (`EditorToolbar` sub-component within `TiptapEditor.tsx`) provides buttons to access these features using `react-icons`. Separators (`<span class="toolbar-separator">`) are used to group related buttons visually.

## 6. Styling

-   **File:** `src/styles/_tiptap-editor.css` (imported in `src/styles/user.css`)
-   **Approach:**
    -   The main wrapper (`.tiptap-editor-wrapper`) provides the single outer border (matching app input fields) and background color.
    -   The toolbar (`.tiptap-toolbar`) sits at the top within the wrapper, styled with a dark background and a bottom border separator.
    -   Toolbar buttons are styled to be icon-only (except headings H1-H3) with no individual borders or backgrounds, fitting the app's theme. Hover and active states use subtle background highlights and theme colors (`#0ee0d8`).
    -   The content area (`.tiptap-editor-content .ProseMirror`) has no border or background itself, appearing seamlessly within the main wrapper. Padding is applied directly to this area.
    -   Basic styling for HTML elements generated by Tiptap (paragraphs, headings, lists, code, etc.) is included to match the app's dark theme and typography where possible (e.g., using `NeueMachina-Ultrabold` for headings).

## 7. Data Handling

-   **Output:** The `onChange` callback provides the editor content as an **HTML string**.
-   **Storage:** This HTML string is saved directly to the corresponding `String` fields in the MongoDB database (`comments` in QuickfireIdea, `description` in Idea, `insights` in Opportunity). No backend model changes were required for this initial implementation.
-   **Local Storage:** The existing local storage logic in the components saves the form state (which now includes the HTML string for the relevant field) and restores it correctly.

## 8. Future Considerations

-   **Displaying Formatted Content:** Currently, the application saves the HTML. Displaying this HTML correctly formatted in other parts of the application (e.g., viewing ideas/opportunities) will require rendering the HTML content safely (e.g., using `dangerouslySetInnerHTML` with sanitization or a dedicated HTML rendering library). This is planned for Phase 2.
-   **Placeholder Text:** To enable the `placeholder` prop visually, the `@tiptap/extension-placeholder` package needs to be installed and added to the `extensions` array in `TiptapEditor.tsx`.
-   **Advanced Features:** More Tiptap extensions can be added as needed (e.g., links, images, tables, mentions).
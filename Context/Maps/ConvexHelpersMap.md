# Convex Helpers Map

A comprehensive reference guide for all Convex helper functions and their purposes in the LionX application.

## Table of Contents

1. [Authentication & Authorization](#authentication--authorization)
2. [Batch Operations & Performance](#batch-operations--performance)
3. [Data Enrichment](#data-enrichment)
4. [Event Management](#event-management)
5. [Idea Management](#idea-management)
6. [Presence Management](#presence-management)
7. [Quickfire Management](#quickfire-management)
8. [Response Utilities](#response-utilities)
9. [Session Management](#session-management)
10. [Settings Management](#settings-management)
11. [Snippet Management](#snippet-management)
12. [Spark Management](#spark-management)
13. [Team Management](#team-management)
14. [User Management](#user-management)
15. [Vote Management](#vote-management)
16. [Backup Management](#backup-management)

---

## Authentication & Authorization

### File: `convex/lib/auth.ts`

**Purpose**: Unified authentication framework with role-based access control

| Function | Purpose | Parameters | Returns |
|----------|---------|-------------|---------|
| `requireAuth` | Basic authentication - validates user exists and is approved | `ctx`, `username`, `useConvexError` | `AuthContext` |
| `requireAdmin` | Admin authentication - validates user has admin privileges | `ctx`, `username`, `useConvexError` | `AuthContext` |
| `requireRole` | Role-based authentication - validates user has specific role | `ctx`, `username`, `requiredRole`, `useConvexError` | `AuthContext` |
| `requireOwnership` | Ownership authentication - validates user owns resource or has admin access | `ctx`, `username`, `resourceUserId`, `useConvexError` | `AuthContext` |
| `requireTeamLeadOrAdmin` | Team lead or admin authentication | `ctx`, `username`, `useConvexError` | `AuthContext` |
| `safeAuth` | Safe authentication - returns AuthResult instead of throwing | `ctx`, `username` | `AuthResult` |
| `validateUserId` | Validates user ID parameter and returns user | `ctx`, `userId`, `useConvexError` | User object |
| `shouldUseConvexError` | Helper to determine appropriate error handling based on context | `context` | `boolean` |

**Key Features**:
- Unified authentication patterns across all Convex functions
- Intentional error handling distinctions for different contexts
- Role-based access control (admin, teamLead, teamMember)
- Support for both ConvexError and standard Error handling

---

## Batch Operations & Performance

### File: `convex/lib/batchHelpers.ts`

**Purpose**: Batch loading utilities to eliminate N+1 queries and improve performance

| Function | Purpose | Parameters | Returns |
|----------|---------|-------------|---------|
| `batchLoadEntities` | Generic batch load multiple entities by IDs | `ctx`, `ids`, `tableName` | `Map<Id, Entity>` |
| `batchLoadUsers` | Batch load users by IDs | `ctx`, `userIds` | `Map<Id, User>` |
| `batchLoadTeams` | Batch load teams by IDs | `ctx`, `teamIds` | `Map<Id, Team>` |
| `batchLoadSessions` | Batch load sessions by IDs | `ctx`, `sessionIds` | `Map<Id, Session>` |
| `batchLoadIdeas` | Batch load ideas by IDs | `ctx`, `ideaIds` | `Map<Id, Idea>` |
| `batchLoadEvents` | Batch load events by IDs | `ctx`, `eventIds` | `Map<Id, Event>` |
| `batchLoadSparks` | Batch load sparks by IDs | `ctx`, `sparkIds` | `Map<Id, Spark>` |
| `batchLoadQuickfires` | Batch load quickfires by IDs | `ctx`, `quickfireIds` | `Map<Id, Quickfire>` |
| `batchLoadSnippets` | Batch load snippets by IDs | `ctx`, `snippetIds` | `Map<Id, Snippet>` |
| `batchLoadIdeasWithRelations` | Batch load ideas with their related user and team data | `ctx`, `ideaIds` | `Array<{idea, user, team, session}>` |
| `batchLoadVotesWithRelations` | Batch load votes with their related idea and user data | `ctx`, `voteIds` | `Array<{vote, idea, user}>` |
| `batchLoadTeamMembers` | Batch load team members for multiple teams | `ctx`, `eventId`, `teamIds` | `Map<Id, User[]>` |
| `batchLoadUserTeams` | Batch load user teams for multiple users in an event | `ctx`, `eventId`, `userIds` | `Map<Id, Team>` |
| `batchLoadSessionsBySpark` | Batch load sessions using spark IDs | `ctx`, `sparkIds` | `Map<Id, Session[]>` |
| `batchDeleteEntities` | Batch delete multiple entities efficiently | `ctx`, `entities` | `void` |
| `batchUpdateEntities` | Batch update multiple entities efficiently | `ctx`, `updates` | `void` |
| `batchLoadEventStatistics` | Optimized batch operations for event-related statistics | `ctx`, `eventIds` | `Map<Id, Statistics>` |
| `extractUniqueIds` | Extract unique IDs from an array of objects | `objects`, `idField` | `Id[]` |
| `groupByField` | Group objects by a field value | `objects`, `groupField` | `Map<FieldValue, Object[]>` |

**Key Features**:
- Eliminates N+1 queries through batch loading
- Generic batch loading patterns for all entity types
- Relationship resolution with batch loading
- Utility functions for data grouping and extraction
- Optimized statistics gathering

---

## Data Enrichment

### File: `convex/lib/enrichmentHelpers.ts`

**Purpose**: Data enrichment for standardized entity relationship resolution

| Function | Purpose | Parameters | Returns |
|----------|---------|-------------|---------|
| `enrichUsersWithTeamInfo` | Enrich users with their team information for a specific event | `ctx`, `users`, `eventId` | `User[]` (enriched) |
| `enrichTeamsWithMemberInfo` | Enrich teams with their member information | `ctx`, `teams`, `eventId` | `Team[]` (enriched) |
| `enrichSessionsWithSparkInfo` | Enrich sessions with their associated spark information | `ctx`, `sessions` | `Session[]` (enriched) |
| `enrichIdeasWithComprehensiveData` | Enrich ideas with comprehensive related data | `ctx`, `ideas` | `Idea[]` (enriched) |
| `enrichVotesWithComprehensiveData` | Enrich votes with comprehensive related data | `ctx`, `votes` | `Vote[]` (enriched) |
| `enrichQuickfireVotesWithComprehensiveData` | Enrich quickfire votes with comprehensive related data | `ctx`, `quickfireVotes` | `QuickfireVote[]` (enriched) |
| `enrichSparkSubmissionsWithComprehensiveData` | Enrich spark submissions with comprehensive related data | `ctx`, `submissions` | `SparkSubmission[]` (enriched) |
| `enrichEntitiesWithPresence` | Enrich entities with presence information | `ctx`, `entities` | `Entity[]` (enriched) |
| `enrichEventsWithStatistics` | Enrich event data with comprehensive statistics | `ctx`, `events` | `Event[]` (enriched) |
| `enrichWithUserAndTeamInfo` | Enrich any collection with user and team information | `ctx`, `entities` | `Entity[]` (enriched) |
| `enrichWithSessionAndEventContext` | Enrich entities with session and event context | `ctx`, `entities` | `Entity[]` (enriched) |
| `enrichWithFormattedTimestamps` | Enrich entities with timestamp formatting | `ctx`, `entities` | `Entity[]` (enriched) |
| `batchEnrichEntityCollections` | Batch enrich multiple entity types with their respective relationships | `ctx`, `entityCollections` | `EnrichedCollections` |


**Key Features**:
- Consistent patterns for enriching database entities
- Batch loading for performance optimization
- Comprehensive relationship resolution
- Presence tracking integration
- Statistical data enrichment

---

## Event Management

### File: `convex/lib/eventHelpers.ts`

**Purpose**: Centralized active event utilities and management

| Function | Purpose | Parameters | Returns |
|----------|---------|-------------|---------|
| `getActiveEvent` | Retrieves the active event from the database | `ctx` | `Event` (throws if not found) |
| `getActiveEventOrNull` | Retrieves the active event, returning null if not found | `ctx` | `Event \| null` |
| `deactivateAllEvents` | Efficiently deactivates all events | `ctx` | `void` |
| `cascadeDeleteEventRelations` | Batch delete all related entities for an event | `ctx`, `eventId` | `void` |
| `removeEventFromUserAssociations` | Efficiently updates user event associations | `ctx`, `eventId` | `void` |
| `getAllEventsOptimized` | Get all events with optimized sorting | `ctx` | `Event[]` |
| `validateEventExists` | Validates if an event exists and optionally checks if it's active | `ctx`, `eventId`, `requireActive` | `Event` |
| `batchUpdateEventTimestamps` | Efficiently updates event timestamps for batch operations | `ctx`, `eventIds`, `updateData` | `void` |
| `updateEventWithTimestamp` | Optimized helper to update a single event with timestamp | `ctx`, `eventId`, `updateData` | `void` |
| `batchValidateEventsExist` | Batch validate multiple events exist | `ctx`, `eventIds`, `requireActive` | `Event[]` |

**Key Features**:
- Centralized active event handling
- Optimized cascade deletion
- Batch operations for performance
- Comprehensive event validation
- Efficient user association management

---

## Idea Management

### File: `convex/lib/ideaHelpers.ts`

**Purpose**: Idea query helpers for standardized database access patterns

| Function | Purpose | Parameters | Returns |
|----------|---------|-------------|---------|
| `getIdeaById` | Get idea by ID with error handling | `ctx`, `ideaId` | `Idea \| null` |
| `getIdeaByIdOrThrow` | Get idea by ID or throw error if not found | `ctx`, `ideaId` | `Idea` |
| `getIdeasByTeamAndSession` | Get ideas by team and session using optimized compound index | `ctx`, `teamId`, `sessionId` | `Idea[]` |
| `getIdeasBySession` | Get ideas by session using optimized index | `ctx`, `sessionId` | `Idea[]` |
| `getIdeasByEvent` | Get ideas by event using optimized index | `ctx`, `eventId` | `Idea[]` |
| `getSubmittedIdeasBySession` | Get submitted ideas by session using optimized compound index | `ctx`, `sessionId` | `Idea[]` |
| `getIdeasByUser` | Get ideas by user using optimized index | `ctx`, `userId` | `Idea[]` |
| `getIdeasByTeam` | Get ideas by team using optimized index | `ctx`, `teamId` | `Idea[]` |
| `getIdeasByUserAndSession` | Get ideas by user and session using optimized compound index | `ctx`, `userId`, `sessionId` | `Idea[]` |
| `getIdeasByCurrentUser` | Get ideas by current user for active session | `ctx`, `userId` | `Idea[]` |
| `enrichIdeasWithDetails` | Get ideas with enriched data (user, team, session details) | `ctx`, `ideas` | `Idea[]` (enriched) |
| `getIdeasGroupedByTeam` | Get ideas grouped by team with enriched data | `ctx`, `sessionId?` | `Record<string, TeamIdeas>` |
| `getTeamMembers` | Get team members for an idea's team | `ctx`, `teamId` | `TeamMember[]` |
| `getIdeasForVoting` | Get ideas for voting - submitted ideas with enriched data | `ctx`, `sessionId` | `Idea[]` |
| `getTopIdeasByEvent` | Get top ideas by event with vote statistics | `ctx`, `eventId`, `limit` | `Idea[]` |
| `validateIdeaAccess` | Validate idea access for a user | `ctx`, `ideaId`, `userId` | `boolean` |
| `getPresenterDetails` | Get presenter details for an idea | `ctx`, `presenterIds` | `Presenter[]` |
| `getIdeaStatsBySession` | Count ideas by status for a session | `ctx`, `sessionId` | `IdeaStats` |
| `getIdeasWithVoteStats` | Get ideas with vote counts for analytics | `ctx`, `eventId` | `Idea[]` (with vote stats) |

**Key Features**:
- Optimized database queries with compound indexes
- Comprehensive idea enrichment with related data
- Access control validation
- Vote statistics integration
- Team-based idea organization

---

## Presence Management

### File: `convex/lib/presenceHelpers.ts`

**Purpose**: Presence management and batch loading functions

| Function | Purpose | Parameters | Returns |
|----------|---------|-------------|---------|
| `getUserPresenceRecords` | Retrieves presence records for a user across all sessions | `ctx`, `userId` | `PresenceRecord[]` |
| `getUserSessionPresence` | Retrieves presence record for a specific user and session | `ctx`, `userId`, `sessionId` | `PresenceRecord \| null` |
| `getOnlinePresenceRecords` | Retrieves all online presence records | `ctx` | `PresenceRecord[]` |
| `getAllPresenceRecords` | Retrieves all presence records in the database | `ctx` | `PresenceRecord[]` |
| `createPresenceRecord` | Creates a new presence record | `ctx`, `userId`, `sessionId`, `isOnline` | `PresenceRecordId` |
| `updatePresenceRecord` | Updates a presence record | `ctx`, `presenceId`, `updates` | `void` |
| `filterRecentPresence` | Filters presence records by recency threshold | `presenceRecords`, `thresholdMs` | `PresenceRecord[]` |
| `getUniqueUsersFromPresence` | Gets unique users from presence records | `presenceRecords` | `Map<UserId, PresenceRecord>` |
| `batchSetPresenceOffline` | Batch updates presence records to offline status | `ctx`, `presenceRecords` | `void` |
| `batchDeleteStalePresence` | Batch delete stale presence records efficiently | `ctx`, `staleRecords` | `void` |

**Key Features**:
- Real-time presence tracking
- Efficient batch operations
- Stale record cleanup
- User session tracking
- Optimized presence queries

**Constants**:
- `PRESENCE_CONSTANTS.OFFLINE_THRESHOLD`: 3 minutes
- `PRESENCE_CONSTANTS.CLEANUP_THRESHOLD`: 10 minutes

---

## Quickfire Management

### File: `convex/lib/quickfireHelpers.ts`

**Purpose**: Quickfire query helpers for standardized database access patterns

| Function | Purpose | Parameters | Returns |
|----------|---------|-------------|---------|
| `getQuickfireById` | Get quickfire by ID with error handling | `ctx`, `quickfireId` | `Quickfire \| null` |
| `getQuickfireByIdOrThrow` | Get quickfire by ID or throw error if not found | `ctx`, `quickfireId` | `Quickfire` |
| `getQuickfiresByEvent` | Get quickfires by event using optimized index | `ctx`, `eventId` | `Quickfire[]` |
| `getQuickfiresBySession` | Get quickfires by session using optimized index | `ctx`, `sessionId` | `Quickfire[]` |
| `getQuickfiresBySessionOrdered` | Get quickfires by session ordered by display order | `ctx`, `sessionId` | `Quickfire[]` |
| `getActiveQuickfiresBySession` | Get active quickfires by session | `ctx`, `sessionId` | `Quickfire[]` |
| `getActiveQuickfires` | Get all active quickfires | `ctx` | `Quickfire[]` |
| `getQuickfiresByEventAndSession` | Get quickfires by event and session | `ctx`, `eventId`, `sessionId` | `Quickfire[]` |
| `getQuickfiresForActiveSession` | Get quickfires for active event and session | `ctx` | `Quickfire[]` |
| `enrichQuickfiresWithDetails` | Get quickfires with enriched data | `ctx`, `quickfires` | `Quickfire[]` (enriched) |
| `getQuickfireStatsBySession` | Get quickfire statistics for a session | `ctx`, `sessionId` | `QuickfireStats` |
| `getQuickfireVoteStats` | Get quickfire vote statistics | `ctx`, `quickfireId` | `VoteStats` |
| `getQuickfiresWithVoteStats` | Get quickfires with vote statistics | `ctx`, `eventId` | `Quickfire[]` (with vote stats) |
| `getTopQuickfiresByEvent` | Get top quickfires by vote score | `ctx`, `eventId`, `limit` | `Quickfire[]` |
| `getQuickfireVotesWithUserData` | Get quickfire votes with enriched user data | `ctx`, `quickfireId` | `QuickfireVote[]` (enriched) |
| `getQuickfireParticipationSummary` | Get quickfire participation summary | `ctx`, `eventId` | `ParticipationSummary` |
| `getUserQuickfireVotesForSession` | Get user's quickfire votes for a session | `ctx`, `userId`, `sessionId` | `QuickfireVote[]` |
| `getQuickfireCountByStatus` | Count quickfires by voting status | `ctx`, `eventId` | `StatusCount` |
| `getAllQuickfires` | Get all quickfires for analytics/export | `ctx` | `Quickfire[]` |
| `validateQuickfireAccess` | Validate quickfire access for operations | `ctx`, `quickfireId` | `AccessValidation` |
| `getQuickfireSummary` | Get quickfire summary for dashboard | `ctx`, `eventId` | `QuickfireSummary` |
| `batchUpdateQuickfireVotingStatus` | Batch update quickfire voting status | `ctx`, `updates` | `void` |
| `batchGetQuickfiresBySessions` | Get quickfires by multiple session IDs | `ctx`, `sessionIds` | `Map<SessionId, Quickfire[]>` |
| `getQuickfireWithDetails` | Get quickfire with session and event details | `ctx`, `quickfireId` | `Quickfire` (enriched) |
| `getQuickfireSessionData` | Get quickfire session data for admin operations | `ctx`, `sessionId` | `SessionData` |
| `createQuickfire` | Create a new quickfire item | `ctx`, `data` | `QuickfireId` |
| `updateQuickfire` | Update an existing quickfire item | `ctx`, `quickfireId`, `updates` | `void` |
| `deleteQuickfire` | Delete a quickfire item | `ctx`, `quickfireId` | `void` |
| `batchUpdateQuickfires` | Batch update multiple quickfire items | `ctx`, `updates` | `void` |
| `batchDeleteQuickfires` | Batch delete multiple quickfire items | `ctx`, `quickfireIds` | `void` |

**Key Features**:
- Comprehensive quickfire management
- Optimized database queries with indexes
- Vote statistics and analytics
- Batch operations for performance
- User participation tracking
- Dashboard analytics integration

---

## Response Utilities

### File: `convex/lib/responseTypes.ts` & `convex/lib/responseUtils.ts`

**Purpose**: Standardized response types and utilities for consistent API responses

#### Response Types (`responseTypes.ts`)

| Type | Purpose | Properties |
|------|---------|------------|
| `MutationResponse<T>` | Standard response for mutations | `success`, `data?`, `error?`, `code?` |
| `QueryResponse<T>` | Standard response for queries | Direct data or null/undefined |
| `ActionResponse<T>` | Standard response for actions | `success`, `data?`, `error?`, `message?` |
| `CreateResponse<T>` | Response for create operations | `success`, `data: {id}`, `error?` |
| `UpdateResponse` | Response for update operations | `success`, `data: {updated}`, `error?` |
| `DeleteResponse` | Response for delete operations | `success`, `data: {deleted}`, `error?` |
| `BulkResponse` | Response for bulk operations | `success`, `data: {count}`, `error?` |
| `ValidationResponse` | Response for validation operations | `success`, `data: {valid, message?}` |
| `AuthResponse` | Response for authentication operations | Authentication details |
| `ExportResponse` | Response for export operations | Export file details |
| `VotingResponse` | Response for voting operations | Vote details |
| `TeamResponse` | Response for team operations | Team details |
| `SessionResponse` | Response for session operations | Session details |
| `ErrorCode` | Standard error codes | Enum of error types |
| `ErrorResponse` | Error response structure | `success: false`, `error`, `code` |

#### Response Utils (`responseUtils.ts`)

| Function | Purpose | Parameters | Returns |
|----------|---------|-------------|---------|
| `createSuccessResponse` | Create a successful mutation response | `data` | `MutationResponse<T>` |
| `createErrorResponse` | Create an error mutation response | `error`, `code?` | `MutationResponse<never>` |
| `createCreateResponse` | Create a successful create response | `id` | `CreateResponse<T>` |
| `createUpdateResponse` | Create a successful update response | `updated` | `UpdateResponse` |
| `createDeleteResponse` | Create a successful delete response | `deleted` | `DeleteResponse` |
| `createBulkResponse` | Create a successful bulk operation response | `count` | `BulkResponse` |
| `createValidationResponse` | Create a validation response | `valid`, `message?` | `ValidationResponse` |
| `createActionSuccess` | Create a successful action response | `data`, `message?` | `ActionResponse<T>` |
| `createActionError` | Create an error action response | `error`, `message?` | `ActionResponse<never>` |
| `createAuthResponse` | Create an authentication response | `isAuthenticated`, `user?`, `role?` | `AuthResponse` |
| `createVotingResponse` | Create a voting response | `voteId?`, `score?`, `canVote?` | `VotingResponse` |
| `createTeamResponse` | Create a team response | `teamId?`, `teamName?`, `memberCount?` | `TeamResponse` |
| `createSessionResponse` | Create a session response | `sessionId?`, `sessionName?`, `isActive?`, `canParticipate?` | `SessionResponse` |
| `createStandardError` | Create a standardized error response | `message`, `code`, `details?` | `ErrorResponse` |
| `errorToResponse` | Convert an error to a standardized response | `error` | `ErrorResponse` |
| `throwConvexError` | Throw a ConvexError with standardized format | `message`, `code?` | `never` |
| `throwStandardError` | Throw a standard Error with standardized format | `message`, `code?` | `never` |
| `extractData` | Extract data from a successful response or throw error | `response` | `T` |
| `extractError` | Extract error from an error response | `response` | `ErrorResponse` |
| `wrapWithStandardResponse` | Wrap a function to return standardized responses | `fn` | `Promise<ApiResponse<T>>` |
| `convertLegacyResponse` | Convert legacy response to standardized format | `legacyResponse` | `ApiResponse<any>` |

**Key Features**:
- Consistent response structure across all functions
- Type safety with TypeScript integration
- Error handling standardization
- Legacy response conversion
- Comprehensive error codes

---

## Session Management

### File: `convex/lib/sessionHelpers.ts`

**Purpose**: Centralized session lookup and filtering utilities

| Function | Purpose | Parameters | Returns |
|----------|---------|-------------|---------|
| `getSessionsByEvent` | Retrieves all sessions for a specific event | `ctx`, `eventId` | `Session[]` |
| `getSessionsByActiveEvent` | Retrieves all sessions for the active event | `ctx` | `Session[]` |
| `getActiveSessionForEvent` | Retrieves the active session for a specific event | `ctx`, `eventId` | `Session \| null` |
| `getActiveSessionForActiveEvent` | Retrieves the active session for the active event | `ctx` | `Session \| null` |
| `getAllActiveSessions` | Retrieves all active sessions across all events | `ctx` | `Session[]` |
| `getSessionById` | Retrieves a session by ID with null safety | `ctx`, `sessionId` | `Session \| null` |
| `getSessionByIdOrThrow` | Retrieves a session by ID and throws error if not found | `ctx`, `sessionId` | `Session` |
| `getSessionsByTypeForEvent` | Retrieves sessions by type for a specific event | `ctx`, `eventId`, `sessionType` | `Session[]` |
| `getSessionsByTypeForActiveEvent` | Retrieves sessions by type for the active event | `ctx`, `sessionType` | `Session[]` |
| `getActiveSessionOfType` | Retrieves the active session and validates it's of the expected type | `ctx`, `expectedType` | `Session \| null` |
| `hasActiveSessionOfType` | Checks if there's an active session of a specific type | `ctx`, `sessionType` | `boolean` |
| `batchLoadSessions` | Batch loads multiple sessions by their IDs | `ctx`, `sessionIds` | `Map<SessionId, Session>` |
| `validateSessionInActiveEvent` | Validates session exists and belongs to active event | `ctx`, `sessionId` | `Session` |
| `getSessionWithMetadata` | Gets session with additional metadata | `ctx`, `sessionId` | `Session` (with metadata) |
| `validateActiveSessionOfType` | Validates there's an active session of the expected type | `ctx`, `expectedType` | `Session` |
| `getActiveSessionWithSettings` | Retrieves the active session with settings information | `ctx` | `Session` (with settings) |
| `getSessionsWithSparkInfo` | Retrieves sessions with spark information | `ctx`, `eventId` | `Session[]` (with spark info) |
| `batchDeactivateSessions` | Batch deactivate sessions efficiently | `ctx`, `sessionIds` | `void` |
| `batchUpdateSessions` | Batch update session settings efficiently | `ctx`, `sessionUpdates` | `void` |
| `createSession` | Creates a new session with proper error handling | `ctx`, `sessionData` | `SessionId` |
| `updateSession` | Updates a session by ID with proper error handling | `ctx`, `sessionId`, `updateData` | `void` |
| `deleteSession` | Deletes a session by ID with proper error handling | `ctx`, `sessionId` | `void` |
| `getAllSessions` | Gets all sessions from the database | `ctx` | `Session[]` |

**Key Features**:
- Centralized session lookup and filtering
- Active session management
- Session type validation
- Batch operations for performance
- Metadata enrichment
- Session lifecycle management

---

## Settings Management

### File: `convex/lib/settingsHelpers.ts`

**Purpose**: Centralized settings lookup functions for configuration management

| Function | Purpose | Parameters | Returns |
|----------|---------|-------------|---------|
| `getSettingByKey` | Retrieves a setting by key | `ctx`, `key` | `Setting \| null` |
| `getSettingValue` | Retrieves a setting value by key with default fallback | `ctx`, `key`, `defaultValue` | `any` |
| `getVotingStatus` | Retrieves the voting status setting | `ctx` | `boolean` |
| `getAutoApprovalStatus` | Retrieves the auto approval setting | `ctx` | `boolean` |
| `updateOrCreateSetting` | Updates or creates a setting | `ctx`, `key`, `value` | `SettingId` |
| `batchGetSettings` | Batch retrieves multiple settings by their keys | `ctx`, `keys` | `Map<string, any>` |
| `getAllSettings` | Retrieves all settings as a key-value map | `ctx` | `Map<string, any>` |
| `settingExists` | Checks if a setting exists | `ctx`, `key` | `boolean` |
| `deleteSetting` | Deletes a setting by key | `ctx`, `key` | `boolean` |
| `getOnlineUsersStatus` | Retrieves the online users feature status | `ctx` | `boolean` |
| `getAppConfig` | Gets application configuration settings | `ctx` | `AppConfig` |

**Key Features**:
- Centralized configuration management
- Default value handling
- Batch settings operations
- Application-wide configuration
- Feature flag management

**Common Settings**:
- `votingStarted`: Boolean for voting system state
- `autoApproval`: Boolean for user auto-approval
- `maxTeamSize`: Number for team size limits
- `votingTimeLimit`: Number for voting time limits
- `online-users-enabled`: Boolean for presence features

---

## Snippet Management

### File: `convex/lib/snippetHelpers.ts`

**Purpose**: Snippet management helpers for standardized operations

| Function | Purpose | Parameters | Returns |
|----------|---------|-------------|---------|
| `batchLoadSnippets` | Batch load multiple snippets by IDs | `ctx`, `snippetIds` | `Map<SnippetId, Snippet>` |
| `getAllSnippetsWithMetadata` | Get all snippets with enhanced metadata and sorting | `ctx` | `Snippet[]` (with metadata) |
| `validateSnippetName` | Efficient snippet name validation with caching | `ctx`, `name`, `excludeId?` | `boolean` |
| `createSnippetWithValidation` | Create a new snippet with validation and error handling | `ctx`, `name`, `content` | `CreateResponse<"snippets">` |
| `updateSnippetWithValidation` | Update a snippet with validation and error handling | `ctx`, `snippetId`, `name?`, `content?` | `UpdateResponse` |
| `deleteSnippetWithValidation` | Delete a snippet with validation | `ctx`, `snippetId` | `DeleteResponse` |
| `getSnippetByIdWithValidation` | Get snippet by ID with enhanced error handling | `ctx`, `snippetId` | `Snippet \| null` |
| `searchSnippets` | Search snippets by name or content | `ctx`, `searchTerm`, `limit` | `Snippet[]` |
| `getSnippetStatistics` | Get snippet statistics | `ctx` | `SnippetStatistics` |
| `batchCreateSnippets` | Batch create multiple snippets | `ctx`, `snippets` | `SnippetId[]` |
| `batchDeleteSnippets` | Batch delete multiple snippets | `ctx`, `snippetIds` | `number` |

**Key Features**:
- Comprehensive snippet management
- Validation and error handling
- Batch operations for performance
- Search functionality
- Statistics and analytics
- Metadata enrichment

**Snippet Metadata**:
- `contentLength`: Character count
- `contentPreview`: Truncated preview
- `lastModified`: Formatted timestamp

---

## Spark Management

### File: `convex/lib/sparkHelpers.ts`

**Purpose**: Spark query helpers for standardized database access patterns

| Function | Purpose | Parameters | Returns |
|----------|---------|-------------|---------|
| `getSparkById` | Get spark by ID with error handling | `ctx`, `sparkId` | `Spark \| null` |
| `getSparkByIdOrThrow` | Get spark by ID or throw error if not found | `ctx`, `sparkId` | `Spark` |
| `getAllSparks` | Get all sparks for an event ordered by creation date | `ctx`, `eventId` | `Spark[]` |
| `getSparkByName` | Get spark by name using optimized index | `ctx`, `name`, `eventId` | `Spark \| null` |
| `getSparkSubmissionById` | Get spark submission by ID with error handling | `ctx`, `submissionId` | `SparkSubmission \| null` |
| `getSparkSubmissionByIdOrThrow` | Get spark submission by ID or throw error if not found | `ctx`, `submissionId` | `SparkSubmission` |
| `getSparkSubmissionsBySpark` | Get spark submissions by spark using optimized index | `ctx`, `sparkId` | `SparkSubmission[]` |
| `getSparkSubmissionsBySession` | Get spark submissions by session using optimized index | `ctx`, `sessionId` | `SparkSubmission[]` |
| `getSparkSubmissionsByUser` | Get spark submissions by user using optimized index | `ctx`, `userId` | `SparkSubmission[]` |
| `getSparkSubmissionsByTeam` | Get spark submissions by team using optimized index | `ctx`, `teamId` | `SparkSubmission[]` |
| `getSparkSubmissionsByEvent` | Get spark submissions by event using optimized index | `ctx`, `eventId` | `SparkSubmission[]` |
| `getUserSparkSubmissionForSession` | Get user's spark submission for a session | `ctx`, `userId`, `sessionId` | `SparkSubmission \| null` |
| `enrichSparkSubmissionsWithDetails` | Get spark submissions with enriched data | `ctx`, `submissions` | `SparkSubmission[]` (enriched) |
| `getSparkSubmissionsGroupedByTeam` | Get spark submissions grouped by team | `ctx`, `sparkId` | `Record<string, SparkSubmission[]>` |
| `getSparkSubmissionsGroupedBySession` | Get spark submissions grouped by session | `ctx`, `eventId` | `Record<string, SparkSubmission[]>` |
| `getSparkSubmissionStats` | Get spark submission statistics for a spark | `ctx`, `sparkId` | `SparkSubmissionStats` |
| `getSparkSubmissionStatsByEvent` | Get spark submission statistics for an event | `ctx`, `eventId` | `EventSparkStats` |
| `getSparkSubmissionsForActiveSession` | Get spark submissions for active session | `ctx`, `userId?` | `SparkSubmission[]` |
| `getSparkFormFields` | Get spark form fields with validation | `ctx`, `sparkId` | `FormField[]` |
| `validateSparkSubmissionData` | Validate spark submission data against form fields | `ctx`, `sparkId`, `submissionData` | `ValidationResult` |
| `getSparkAnalytics` | Get spark analytics data | `ctx`, `sparkId` | `SparkAnalytics` |
| `getTeamSparkSubmissionStatus` | Get team spark submission status | `ctx`, `teamId`, `sessionId` | `SubmissionStatus` |
| `getAllSparkSubmissions` | Get all spark submissions for analytics/export | `ctx` | `SparkSubmission[]` |
| `getSparkSubmissionSummary` | Get spark submission summary for dashboard | `ctx`, `eventId` | `SparkSubmissionSummary` |
| `canDeleteSpark` | Check if a spark can be deleted | `ctx`, `sparkId` | `boolean` |
| `canEditSpark` | Check if a spark can be edited | `ctx`, `sparkId` | `boolean` |
| `getSparkSubmissionCount` | Get spark submission count for a specific spark | `ctx`, `sparkId` | `number` |
| `deleteSparkWithCascade` | Delete spark with optimized cascade deletion | `ctx`, `sparkId` | `void` |
| `getSparkNamesOptimized` | Get spark names with optimized query | `ctx`, `eventId` | `SparkName[]` |
| `validateSparkField` | Validate spark field configuration | `field` | `string[]` (errors) |
| `validateSparkFields` | Validate array of spark fields | `fields` | `string[]` (errors) |
| `batchCheckSparkUsability` | Batch check spark usability | `ctx`, `sparkIds` | `Map<SparkId, UsabilityStatus>` |
| `createSpark` | Create a new spark with standardized data handling | `ctx`, `sparkData` | `SparkId` |
| `updateSpark` | Update an existing spark with standardized data handling | `ctx`, `sparkId`, `updateData` | `void` |

**Key Features**:
- Comprehensive spark and submission management
- Dynamic form field validation
- Analytics and statistics
- Batch operations for performance
- Cascade deletion support
- Form field configuration validation

**Spark Analytics**:
- Submission statistics
- Field-level analytics
- Participation tracking
- Team-based grouping

---

## Team Management

### File: `convex/lib/teamHelpers.ts`

**Purpose**: Centralized team lookup and batch loading utilities

| Function | Purpose | Parameters | Returns |
|----------|---------|-------------|---------|
| `getAllTeams` | Retrieves all teams across all events | `ctx` | `Team[]` |
| `getTeamsByEvent` | Retrieves all teams for a specific event | `ctx`, `eventId` | `Team[]` |
| `getTeamsByActiveEvent` | Retrieves all teams for the active event | `ctx` | `Team[]` |
| `getActiveTeamsByEvent` | Retrieves all active teams for a specific event | `ctx`, `eventId` | `Team[]` |
| `getActiveTeamsByActiveEvent` | Retrieves all active teams for the active event | `ctx` | `Team[]` |
| `getTeamById` | Retrieves a team by ID with null safety | `ctx`, `teamId` | `Team \| null` |
| `getTeamByIdOrThrow` | Retrieves a team by ID and throws error if not found | `ctx`, `teamId` | `Team` |
| `batchLoadTeams` | Batch loads multiple teams by their IDs | `ctx`, `teamIds` | `Map<TeamId, Team>` |
| `getTeamsWithUserCount` | Gets teams with their user counts for a specific event | `ctx`, `eventId` | `Team[]` (with user count) |
| `getActiveEventTeamsWithUserCount` | Gets teams with their user counts for the active event | `ctx` | `Team[]` (with user count) |
| `getTeamUsers` | Gets a team's users for a specific event | `ctx`, `teamId`, `eventId` | `User[]` |
| `validateTeamInActiveEvent` | Validates team exists and belongs to active event | `ctx`, `teamId` | `Team` |
| `getTeamMembersWithDetails` | Gets team members with detailed information | `ctx`, `teamId`, `eventId` | `TeamMember[]` (with details) |
| `getTeamsByEventWithMemberCount` | Gets teams by event with member count information | `ctx`, `eventId` | `Team[]` (with member count) |
| `batchUpdateTeamVotingStatus` | Batch update team voting status | `ctx`, `teams`, `votingStatus` | `void` |
| `calculateVotingMode` | Calculate voting mode based on active teams voting status | `teams` | `VotingMode` |
| `getTeamRelatedEntitiesForDeletion` | Efficient team deletion helper | `ctx`, `teamId` | `RelatedEntities` |
| `getTeamVotingStatusSummary` | Gets team voting status summary for an event | `ctx`, `eventId` | `VotingStatusSummary` |
| `getTeamsWithEnrichedMembers` | Efficiently loads teams with their complete member information | `ctx`, `eventId` | `Team[]` (with enriched members) |

**Key Features**:
- Comprehensive team management
- User count and member tracking
- Voting status management
- Batch operations for performance
- Team validation and access control
- Detailed member information

**Team Statistics**:
- Member count by role
- Idea submission count
- Voting participation
- Team activity metrics

---

## User Management

### File: `convex/lib/userHelpers.ts`

**Purpose**: Centralized user lookup and role validation utilities

| Function | Purpose | Parameters | Returns |
|----------|---------|-------------|---------|
| `getUserByUsername` | Retrieves a user by username with proper error handling | `ctx`, `username` | `User \| null` |
| `getUserByUsernameOrThrow` | Retrieves a user by username and throws ConvexError if not found | `ctx`, `username` | `User` |
| `getUserById` | Retrieves a user by ID with null safety | `ctx`, `userId` | `User \| null` |
| `getUserByIdOrThrow` | Retrieves a user by ID and throws ConvexError if not found | `ctx`, `userId` | `User` |
| `batchLoadUsers` | Batch loads multiple users by their IDs | `ctx`, `userIds` | `Map<UserId, User>` |
| `getApprovedUsersForEvent` | Retrieves all approved users for a specific event | `ctx`, `eventId` | `User[]` |
| `validateAdminUser` | Validates user admin privileges | `ctx`, `username` | `User` |
| `validateAdminUserWithError` | Validates admin user privileges with standard Error | `ctx`, `username` | `User` |
| `validateAdminUserWithConvexError` | Validates admin user privileges with ConvexError | `ctx`, `username` | `User` |
| `validateTeamLeadUser` | Validates user team lead privileges | `ctx`, `username` | `User` |
| `validateAdminOrTeamLeadUser` | Validates user has admin or team lead privileges | `ctx`, `username` | `User` |
| `getUserTeamForEvent` | Gets user's team information for a specific event | `ctx`, `user`, `eventId` | `TeamInfo \| null` |
| `getUserTeamForEventOptimized` | Gets user's team information using batch loading | `ctx`, `user`, `eventId`, `teamMap?` | `TeamInfo \| null` |
| `getUsersByStatusWithRoleFilter` | Gets users by status with optional role filtering | `ctx`, `status`, `excludeRole?` | `User[]` |
| `batchUpdateUserEvents` | Batch update user events for multiple users | `ctx`, `userEventUpdates` | `void` |
| `getAllUsers` | Gets all users with optional pagination and filtering | `ctx` | `User[]` |
| `getUsersByRole` | Gets users by role using index | `ctx`, `role` | `User[]` |
| `getAdminUsers` | Gets admin users using role filter | `ctx` | `User[]` |
| `getApprovedUsersExcludingRole` | Gets users by status excluding role | `ctx`, `status`, `excludeRole` | `User[]` |
| `getUserIdeas` | Gets all user ideas for deletion analysis | `ctx`, `userId` | `Idea[]` |
| `getUserSparkSubmissions` | Gets user spark submissions for deletion analysis | `ctx`, `userId` | `SparkSubmission[]` |
| `getUserVotes` | Gets user votes for deletion analysis | `ctx`, `userId` | `Vote[]` |
| `getUserQuickfireVotesForDeletion` | Gets user quickfire votes for deletion analysis | `ctx`, `userId` | `QuickfireVote[]` |
| `getVotesForIdea` | Gets all votes for an idea for deletion analysis | `ctx`, `ideaId` | `Vote[]` |

**Key Features**:
- Comprehensive user management
- Role-based access control
- Batch operations for performance
- Event-based user filtering
- Team association management
- Deletion analysis support

**Role Validation**:
- Admin privileges validation
- Team lead privileges validation
- Multi-role validation
- Context-appropriate error handling

---

## Vote Management

### File: `convex/lib/voteHelpers.ts`

**Purpose**: Vote operation helpers for standardized database access patterns

| Function | Purpose | Parameters | Returns |
|----------|---------|-------------|---------|
| `getVoteById` | Get vote by ID with error handling | `ctx`, `voteId` | `Vote \| null` |
| `getVoteByIdOrThrow` | Get vote by ID or throw error if not found | `ctx`, `voteId` | `Vote` |
| `getVotesByIdea` | Get votes by idea using optimized index | `ctx`, `ideaId` | `Vote[]` |
| `getVotesByUser` | Get votes by user using optimized index | `ctx`, `userId` | `Vote[]` |
| `getVotesByEvent` | Get votes by event using optimized index | `ctx`, `eventId` | `Vote[]` |
| `getVotesBySession` | Get votes by session using optimized index | `ctx`, `sessionId` | `Vote[]` |
| `getUserVotesForEvent` | Get user's votes for a specific event | `ctx`, `userId`, `eventId` | `Vote[]` |
| `getUserVotesForSession` | Get user's votes for a specific session | `ctx`, `userId`, `sessionId` | `Vote[]` |
| `getUserVoteForIdea` | Get user's specific vote for an idea | `ctx`, `userId`, `ideaId` | `Vote \| null` |
| `hasUserVotedForIdea` | Check if user has voted for an idea | `ctx`, `userId`, `ideaId` | `boolean` |
| `getVoteStatsForIdea` | Get vote statistics for an idea | `ctx`, `ideaId` | `VoteStats` |
| `getVoteStatsForEvent` | Get vote statistics for an event | `ctx`, `eventId` | `EventVoteStats` |
| `upsertVote` | Upsert a vote (create or update) | `ctx`, `voteData` | `VoteUpsertResult` |
| `validateVoteAccess` | Validate vote access for a user | `ctx`, `userId`, `ideaId` | `VoteAccessResult` |
| `getTopVotedIdeasByEvent` | Get top voted ideas for an event | `ctx`, `eventId`, `limit` | `TopIdea[]` |
| `enrichVotesWithDetails` | Get votes with enriched data | `ctx`, `votes` | `Vote[]` (enriched) |
| `getVotingSummary` | Get voting summary for analytics | `ctx`, `eventId` | `VotingSummary` |
| `getAllVotes` | Get all votes for analytics/export | `ctx` | `Vote[]` |
| `getQuickfireVoteByUserAndQuickfire` | Get quickfire vote by user and quickfire | `ctx`, `userId`, `quickfireId` | `QuickfireVote \| null` |
| `getQuickfireVotesByQuickfire` | Get quickfire votes by quickfire | `ctx`, `quickfireId` | `QuickfireVote[]` |
| `getQuickfireVotesByUser` | Get quickfire votes by user | `ctx`, `userId` | `QuickfireVote[]` |
| `batchLoadQuickfireVotes` | Batch load quickfire votes for multiple quickfires | `ctx`, `quickfireIds` | `Map<QuickfireId, QuickfireVote[]>` |
| `getUserQuickfireVotesForQuickfires` | Get user's quickfire votes for multiple quickfires | `ctx`, `userId`, `quickfireIds` | `Map<QuickfireId, QuickfireVote>` |
| `getAllQuickfireVotes` | Get all quickfire votes for analytics | `ctx` | `QuickfireVote[]` |
| `batchLoadQuickfireVoteStats` | Batch load quickfire votes statistics | `ctx`, `quickfireIds` | `Map<QuickfireId, VoteStats>` |
| `batchDeleteQuickfireVotes` | Batch delete quickfire votes | `ctx`, `voteIds` | `void` |
| `getQuickfireVotesWithStats` | Get quickfire votes with aggregated statistics | `ctx`, `quickfireId` | `QuickfireVotesWithStats` |
| `batchLoadVotesByIdea` | Batch load votes for multiple ideas | `ctx`, `ideaIds` | `Map<IdeaId, Vote[]>` |

**Key Features**:
- Comprehensive vote management
- Optimized database queries with indexes
- Vote statistics and analytics
- Batch operations for performance
- Access control validation
- Both regular and quickfire vote support

**Vote Statistics**:
- Count, total score, average score
- Min/max score tracking
- Participation rates
- Top-voted content identification

---

## Backup Management

### File: `convex/lib/backupHelpers.ts`

**Purpose**: Backup operations and metadata management

| Function | Purpose | Parameters | Returns |
|----------|---------|-------------|---------|
| `getBackupById` | Retrieves a backup by ID with proper error handling | `ctx`, `backupId` | `Backup \| null` |
| `getBackupByIdOrThrow` | Retrieves a backup by ID and throws ConvexError if not found | `ctx`, `backupId` | `Backup` |
| `getBackupByFileName` | Retrieves a backup by filename with proper error handling | `ctx`, `fileName` | `Backup \| null` |
| `getAllBackups` | Retrieves all backups with proper ordering | `ctx` | `Backup[]` |
| `createBackupMetadata` | Creates backup metadata with proper error handling | `ctx`, `args` | `BackupId` |
| `updateBackupMetadata` | Updates backup metadata with proper error handling | `ctx`, `backupId`, `updateData` | `void` |
| `deleteBackupMetadata` | Deletes backup metadata with proper error handling | `ctx`, `backupId` | `void` |
| `batchLoadBackups` | Batch load multiple backups by their IDs | `ctx`, `backupIds` | `Map<BackupId, Backup>` |
| `enrichBackups` | Enrich backup objects with additional metadata | `ctx`, `backups` | `Backup[]` (enriched) |

**Key Features**:
- Comprehensive backup metadata management
- File-based backup operations
- Batch operations for performance
- Metadata enrichment
- Error handling and validation

**Backup Metadata**:
- File size formatting
- Creator information
- Status tracking
- Creation timestamps

---

## Summary

This comprehensive helper system provides:

1. **Performance Optimization**: Batch loading and N+1 query elimination
2. **Consistency**: Standardized patterns across all database operations
3. **Type Safety**: Full TypeScript integration with proper error handling
4. **Scalability**: Efficient query patterns and optimized database access
5. **Maintainability**: Centralized utilities and consistent API patterns
6. **Feature Rich**: Comprehensive coverage of all application domains

The helper system is designed to support future feature implementations by providing:
- Reusable, tested functions
- Consistent error handling patterns
- Performance-optimized database access
- Comprehensive data enrichment capabilities
- Flexible batch operations

Each helper file focuses on a specific domain while maintaining consistency with the overall architecture and patterns used throughout the LionX application.
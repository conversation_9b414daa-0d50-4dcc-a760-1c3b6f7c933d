# Convex Functions Map

A comprehensive reference guide for all Convex functions and utilities in the LionX application.

## Main Convex Functions

| File | Path | Purpose | Key Functions |
|------|------|---------|---------------|
| **analytics** | `convex/analytics.ts` | Analytics and leaderboard functionality | `getSessionsForLeaderboard`, `getTeamsForDropdown`, `getLeaderboard`, `getTeamsLeaderboard`, `getPrintIdeasByTeam`, `getPrintSparkSubmissionsByTeam`, `exportIdeasData`, `exportTeamsData`, `getDataSummaryForAnalytics` |
| **backupActions** | `convex/backupActions.ts` | File storage operations for backup management | `uploadBackupAction`, `getBackupDownloadUrlAction`, `deleteBackupAction` |
| **backupData** | `convex/backupData.ts` | Backup metadata management and data clearing | `getBackupsList`, `getBackupDetails`, `createBackupMetadata`, `updateBackupMetadata`, `deleteBackupMetadata`, `generateUploadUrl`, `clearAllDataMutation` |
| **crons** | `convex/crons.ts` | Scheduled background tasks | `cleanup-stale-presence` (cron job) |
| **events** | `convex/events.ts` | Event management and lifecycle operations | `getActiveEvent`, `getEventById`, `getAllEvents`, `createEvent`, `activateEvent`, `updateEvent`, `deactivateEvent`, `deleteEvent` |
| **ideas** | `convex/ideas.ts` | Idea submission and management system | `getIdeasByTeamAndSession`, `getIdeasByCurrentUser`, `getTeamMembers`, `getAllIdeasGroupedByTeam`, `getAllDataGroupedByTeam`, `getIdeasPageData`, `getIdeaFormData`, `createIdea`, `updateIdea`, `deleteIdea`, `submitTeamIdeas`, `withdrawTeamIdeas`, `withdrawAllTeamData` |

| **presence** | `convex/presence.ts` | Real-time user presence tracking | `getOnlineUsers`, `getPresenceStats`, `updatePresence`, `setOffline`, `cleanupStalePresence` |
| **quickfire** | `convex/quickfire.ts` | Quickfire voting system for rapid evaluation | `getQuickfireItemsForVoting`, `getQuickfireVotesByUser`, `getActiveSession`, `getAllQuickfireItems`, `getQuickfireItemsForActiveSession`, `getQuickfireSessions`, `getQuickfireItemsBySession`, `getQuickfirePageData`, `submitQuickfireVote`, `createQuickfireItem`, `updateQuickfireItem`, `deleteQuickfireItem`, `toggleQuickfireVoting`, `reorderQuickfireItems`, `resetOrderByCreatedAt` |
| **schema** | `convex/schema.ts` | Database schema definition | Defines all tables: `users`, `events`, `teams`, `ideas`, `sessions`, `votes`, `quickfires`, `quickfireVotes`, `snippets`, `settings`, `presence`, `backups`, `sparks`, `sparkSubmissions` |
| **sessions** | `convex/sessions.ts` | Session management and lifecycle | `getActiveSession`, `getSessionsByEvent`, `getAllSessions`, `getSessionsByActiveEvent`, `getSessionsByActiveEventExcludingQuickfire`, `getSessionById`, `getSparkSubmissionCountForSession`, `getSparkSubmissionsForSession`, `checkSessionHasSparkSubmissions`, `createSession`, `activateSession`, `updateSession`, `deactivateSession`, `deleteSession`, `validateSessionSparkRelationship`, `checkCanChangeSessionType`, `checkCanDeleteSession` |
| **settings** | `convex/settings.ts` | Application configuration management | `getSetting`, `getAllSettings`, `getRestoreStatus`, `getAutoApprovalSetting`, `getTeamSelectionSetting`, `getVotingSetting`, `getOnlineUsersSetting`, `setSetting`, `deleteSetting`, `setAutoApprovalSetting`, `setTeamSelectionSetting`, `setVotingSetting`, `setOnlineUsersSetting` |
| **snippets** | `convex/snippets.ts` | Reusable text snippet management | `getAllSnippets`, `getSnippetById`, `checkSnippetNameExists`, `createSnippet`, `updateSnippet`, `deleteSnippet` |
| **sparks** | `convex/sparks.ts` | Dynamic form configuration system | `getSparks`, `getSparkById`, `canDeleteSpark`, `getSparkSubmissionCount`, `canEditSpark`, `getSparkNames`, `createSpark`, `updateSpark`, `deleteSpark`, `checkCanDeleteSpark`, `checkCanEditSpark`, `getSparkSubmissionCountAction` |
| **sparkSubmissions** | `convex/sparkSubmissions.ts` | Dynamic form submission handling | `getSparkSubmissions`, `getSparkSubmissionsByUser`, `submitSparkData`, `updateSparkSubmission`, `deleteSparkSubmission` |
| **teams** | `convex/teams.ts` | Team management and organization | `getTeamsByEvent`, `getAllTeams`, `getTeamsByActiveEvent`, `getTeamById`, `getActiveTeams`, `createTeam`, `updateTeam`, `deleteTeam`, `toggleTeamActive` |
| **userActions** | `convex/userActions.ts` | User management actions with password hashing | `createUserWithTeamAction`, `updateUserPasswordAction`, `updateUserProfileAction`, `createAdminUserAction`, `updateAdminPasswordAction` |
| **userAdmin** | `convex/userAdmin.ts` | Admin user management operations | `getAdminUsers`, `createAdminUser`, `createAdminUserWithHashedPassword`, `updateAdminUsername`, `updateAdminPasswordWithHashedPassword`, `deleteAdminUser` |
| **userAuth** | `convex/userAuth.ts` | Authentication and authorization functions | `validateAdminUserQuery` |
| **userContent** | `convex/userContent.ts` | User content management and deletion | `getUserIdeas`, `transferUserContent`, `deleteUser` |
| **userProfile** | `convex/userProfile.ts` | User profile management operations | `updateUserProfile`, `updateUserName`, `updateUserRole`, `updateUserPassword`, `updateUserHashedPassword` |
| **userRegistration** | `convex/userRegistration.ts` | User registration and account creation | `createUser`, `registerUser`, `createUserWithTeam`, `createUserWithHashedPassword` |
| **users** | `convex/users.ts` | Core user queries and centralized re-exports | `getUserByUsername`, `getUserById`, `checkUserAdmin`, `getAllUsers`, `getUsersByStatus`, `getUsersByRole`, `getUsersGroupedByTeam` + re-exports from specialized modules |
| **userStatus** | `convex/userStatus.ts` | User status and approval management | `updateUserStatus`, `removeLastSeenField` |
| **userTeams** | `convex/userTeams.ts` | Team assignment and membership operations | `joinTeam`, `updateUserTeam`, `bulkUpdateUserTeam`, `updateUserEvents` |
| **votes** | `convex/votes.ts` | Voting system for ideas | `getUserVotes`, `getVotingIdeas`, `getVotingStatus`, `getAllVotes`, `getAllQuickfireVotes`, `getVotingPageData`, `submitVote` |
| **voting** | `convex/voting.ts` | Voting system administration and controls | `getVotingStatus`, `canStartVoting`, `getTeamsVotingStatus`, `getVotingTeamStatus`, `toggleVoting`, `setTeamVoting` |

## Utility Libraries

| File | Path | Purpose | Key Functions |
|------|------|---------|---------------|
| **auth** | `convex/lib/auth.ts` | Unified authentication framework with role-based access control | `requireAuth`, `requireAdmin`, `requireRole`, `requireOwnership`, `requireTeamLeadOrAdmin`, `safeAuth`, `validateUserId`, `shouldUseConvexError` |
| **backupHelpers** | `convex/lib/backupHelpers.ts` | Backup operations and metadata management | `getBackupById`, `getBackupByIdOrThrow`, `getBackupByFileName`, `getAllBackups`, `createBackupMetadata`, `updateBackupMetadata`, `deleteBackupMetadata`, `batchLoadBackups`, `enrichBackups` |
| **batchHelpers** | `convex/lib/batchHelpers.ts` | Batch loading utilities to eliminate N+1 queries | `batchLoadEntities`, `batchLoadUsers`, `batchLoadTeams`, `batchLoadSessions`, `batchLoadIdeas`, `batchLoadEvents`, `batchLoadSparks`, `batchLoadQuickfires`, `batchLoadSnippets`, `batchLoadIdeasWithRelations`, `batchLoadVotesWithRelations`, `batchLoadTeamMembers`, `batchLoadUserTeams`, `extractUniqueIds`, `groupByField`, `batchDeleteEntities`, `batchUpdateEntities`, `batchLoadEventStatistics` |
| **enrichmentHelpers** | `convex/lib/enrichmentHelpers.ts` | Data enrichment for standardized entity relationship resolution | `enrichUsersWithTeamInfo`, `enrichTeamsWithMemberInfo`, `enrichSessionsWithSparkInfo`, `enrichIdeasWithComprehensiveData`, `enrichVotesWithComprehensiveData`, `enrichQuickfireVotesWithComprehensiveData`, `enrichSparkSubmissionsWithComprehensiveData`, `enrichEntitiesWithPresence`, `enrichEventsWithStatistics`, `enrichWithUserAndTeamInfo`, `enrichWithSessionAndEventContext`, `enrichWithFormattedTimestamps`, `batchEnrichEntityCollections` |
| **eventHelpers** | `convex/lib/eventHelpers.ts` | Centralized active event utilities and management | `getActiveEvent`, `getActiveEventOrNull`, `deactivateAllEvents`, `cascadeDeleteEventRelations`, `removeEventFromUserAssociations`, `getAllEventsOptimized`, `validateEventExists`, `batchUpdateEventTimestamps`, `updateEventWithTimestamp`, `batchValidateEventsExist` |
| **ideaHelpers** | `convex/lib/ideaHelpers.ts` | Idea query helpers for standardized database access | `getIdeaById`, `getIdeaByIdOrThrow`, `getIdeasByTeamAndSession`, `getIdeasBySession`, `getIdeasByEvent`, `getSubmittedIdeasBySession`, `getIdeasByUser`, `getIdeasByTeam`, `getIdeasByCurrentUser`, `enrichIdeasWithDetails`, `getIdeasGroupedByTeam`, `getTeamMembers`, `getIdeasForVoting`, `getTopIdeasByEvent`, `validateIdeaAccess`, `getPresenterDetails`, `getIdeaStatsBySession`, `getIdeasWithVoteStats` |
| **presenceHelpers** | `convex/lib/presenceHelpers.ts` | Presence management and batch loading functions | `getUserPresenceRecords`, `getUserSessionPresence`, `getOnlinePresenceRecords`, `getAllPresenceRecords`, `createPresenceRecord`, `updatePresenceRecord`, `filterRecentPresence`, `getUniqueUsersFromPresence`, `batchSetPresenceOffline`, `batchDeleteStalePresence`, `PRESENCE_CONSTANTS` |
| **quickfireHelpers** | `convex/lib/quickfireHelpers.ts` | Quickfire query helpers for standardized database access | `getQuickfireById`, `getQuickfireByIdOrThrow`, `getQuickfiresByEvent`, `getQuickfiresBySession`, `getQuickfiresBySessionOrdered`, `getActiveQuickfiresBySession`, `getActiveQuickfires`, `getQuickfiresByEventAndSession`, `getQuickfiresForActiveSession`, `enrichQuickfiresWithDetails`, `getQuickfireStatsBySession`, `getQuickfireVoteStats`, `getQuickfiresWithVoteStats`, `getTopQuickfiresByEvent`, `getQuickfireVotesWithUserData`, `getQuickfireParticipationSummary`, `getUserQuickfireVotesForSession`, `getQuickfireCountByStatus`, `getAllQuickfires`, `validateQuickfireAccess`, `getQuickfireSummary`, `batchUpdateQuickfireVotingStatus`, `batchGetQuickfiresBySessions`, `getQuickfireWithDetails`, `getQuickfireSessionData`, `createQuickfire`, `updateQuickfire`, `deleteQuickfire`, `batchUpdateQuickfires`, `batchDeleteQuickfires` |
| **responseTypes** | `convex/lib/responseTypes.ts` | Standardized response type definitions for consistent API responses | `MutationResponse`, `QueryResponse`, `ActionResponse`, `CreateResponse`, `UpdateResponse`, `DeleteResponse`, `BulkResponse`, `ValidationResponse`, `AuthResponse`, `ExportResponse`, `BackupResponse`, `VotingResponse`, `TeamResponse`, `SessionResponse`, `ErrorCode`, `ErrorResponse`, `SuccessResponse`, `ApiResponse`, `isSuccessResponse`, `isErrorResponse` |
| **responseUtils** | `convex/lib/responseUtils.ts` | Response object creation utilities for standardized responses | `createSuccessResponse`, `createErrorResponse`, `createCreateResponse`, `createUpdateResponse`, `createDeleteResponse`, `createBulkResponse`, `createValidationResponse`, `createActionSuccess`, `createActionError`, `createAuthResponse`, `createVotingResponse`, `createTeamResponse`, `createSessionResponse`, `createStandardError`, `errorToResponse`, `throwConvexError`, `throwStandardError`, `extractData`, `extractError`, `wrapWithStandardResponse`, `convertLegacyResponse` |
| **sessionHelpers** | `convex/lib/sessionHelpers.ts` | Centralized session lookup and filtering utilities | `getSessionsByEvent`, `getSessionsByActiveEvent`, `getActiveSessionForEvent`, `getActiveSessionForActiveEvent`, `getAllActiveSessions`, `getSessionById`, `getSessionByIdOrThrow`, `getSessionsByTypeForEvent`, `getSessionsByTypeForActiveEvent`, `getActiveSessionOfType`, `hasActiveSessionOfType`, `batchLoadSessions`, `validateSessionInActiveEvent`, `getSessionWithMetadata`, `validateActiveSessionOfType`, `getActiveSessionWithSettings`, `getSessionsWithSparkInfo`, `batchDeactivateSessions`, `batchUpdateSessions`, `createSession`, `updateSession`, `deleteSession`, `getAllSessions` |
| **settingsHelpers** | `convex/lib/settingsHelpers.ts` | Centralized settings lookup functions for configuration management | `getSettingByKey`, `getSettingValue`, `getVotingStatus`, `getAutoApprovalStatus`, `updateOrCreateSetting`, `batchGetSettings`, `getAllSettings`, `settingExists`, `deleteSetting`, `getOnlineUsersStatus`, `getAppConfig` |
| **snippetHelpers** | `convex/lib/snippetHelpers.ts` | Snippet management helpers for standardized operations | `batchLoadSnippets`, `getAllSnippetsWithMetadata`, `validateSnippetName`, `createSnippetWithValidation`, `updateSnippetWithValidation`, `deleteSnippetWithValidation`, `getSnippetByIdWithValidation`, `searchSnippets`, `getSnippetStatistics`, `batchCreateSnippets`, `batchDeleteSnippets` |
| **sparkHelpers** | `convex/lib/sparkHelpers.ts` | Spark query helpers for standardized database access patterns | `getSparkById`, `getSparkByIdOrThrow`, `getAllSparks`, `getSparkByName`, `getSparkSubmissionById`, `getSparkSubmissionByIdOrThrow`, `getSparkSubmissionsBySpark`, `getSparkSubmissionsBySession`, `getSparkSubmissionsByUser`, `getSparkSubmissionsByTeam`, `getSparkSubmissionsByEvent`, `getUserSparkSubmissionForSession`, `enrichSparkSubmissionsWithDetails`, `getSparkSubmissionsGroupedByTeam`, `getSparkSubmissionsGroupedBySession`, `getSparkSubmissionStats`, `getSparkSubmissionStatsByEvent`, `getSparkSubmissionsForActiveSession`, `getSparkFormFields`, `validateSparkSubmissionData`, `getSparkAnalytics`, `getTeamSparkSubmissionStatus`, `getAllSparkSubmissions`, `getSparkSubmissionSummary`, `canDeleteSpark`, `canEditSpark`, `getSparkSubmissionCount`, `deleteSparkWithCascade`, `getSparkNamesOptimized`, `validateSparkField`, `validateSparkFields`, `batchCheckSparkUsability`, `createSpark`, `updateSpark` |
| **teamHelpers** | `convex/lib/teamHelpers.ts` | Centralized team lookup and batch loading utilities | `getAllTeams`, `getTeamsByEvent`, `getTeamsByActiveEvent`, `getActiveTeamsByEvent`, `getActiveTeamsByActiveEvent`, `getTeamById`, `getTeamByIdOrThrow`, `batchLoadTeams`, `getTeamsWithUserCount`, `getActiveEventTeamsWithUserCount`, `getTeamUsers`, `validateTeamInActiveEvent`, `getTeamMembersWithDetails`, `getTeamsByEventWithMemberCount`, `batchUpdateTeamVotingStatus`, `calculateVotingMode`, `getTeamRelatedEntitiesForDeletion`, `getTeamVotingStatusSummary`, `getTeamsWithEnrichedMembers` |
| **userHelpers** | `convex/lib/userHelpers.ts` | Centralized user lookup and role validation utilities | `getUserByUsername`, `getUserByUsernameOrThrow`, `getUserById`, `getUserByIdOrThrow`, `batchLoadUsers`, `getApprovedUsersForEvent`, `validateAdminUser`, `validateAdminUserWithError`, `validateAdminUserWithConvexError`, `validateTeamLeadUser`, `validateAdminOrTeamLeadUser`, `getUserTeamForEvent`, `getUserTeamForEventOptimized`, `getUsersByStatusWithRoleFilter`, `batchUpdateUserEvents`, `getAllUsers`, `getUsersByRole`, `getAdminUsers`, `getApprovedUsersExcludingRole`, `getUserIdeas`, `getUserSparkSubmissions`, `getUserVotes`, `getUserQuickfireVotesForDeletion`, `getVotesForIdea` |
| **voteHelpers** | `convex/lib/voteHelpers.ts` | Vote operation helpers for standardized database access patterns | `getVoteById`, `getVoteByIdOrThrow`, `getVotesByIdea`, `getVotesByUser`, `getVotesByEvent`, `getVotesBySession`, `getUserVotesForEvent`, `getUserVotesForSession`, `getUserVoteForIdea`, `hasUserVotedForIdea`, `getVoteStatsForIdea`, `getVoteStatsForEvent`, `upsertVote`, `validateVoteAccess`, `getTopVotedIdeasByEvent`, `enrichVotesWithDetails`, `getVotingSummary`, `getAllVotes`, `getQuickfireVoteByUserAndQuickfire`, `getQuickfireVotesByQuickfire`, `getQuickfireVotesByUser`, `batchLoadQuickfireVotes`, `getUserQuickfireVotesForQuickfires`, `getAllQuickfireVotes`, `batchLoadQuickfireVoteStats`, `batchDeleteQuickfireVotes`, `getQuickfireVotesWithStats`, `batchLoadVotesByIdea` |

## Generated Files

| File | Path | Purpose | Notes |
|------|------|---------|-------|
| **api.d.ts** | `convex/_generated/api.d.ts` | TypeScript API definitions | Auto-generated, do not edit |
| **api.js** | `convex/_generated/api.js` | JavaScript API exports | Auto-generated, do not edit |
| **dataModel.d.ts** | `convex/_generated/dataModel.d.ts` | Data model type definitions | Auto-generated, do not edit |
| **server.d.ts** | `convex/_generated/server.d.ts` | Server type definitions | Auto-generated, do not edit |
| **server.js** | `convex/_generated/server.js` | Server exports | Auto-generated, do not edit |

## Function Types

### Queries
- **Purpose**: Read-only operations that return data
- **Real-time**: Automatically update when underlying data changes
- **Examples**: `getActiveEvent`, `getAllUsers`, `getLeaderboard`

### Mutations
- **Purpose**: Write operations that modify database state
- **Transactional**: All changes succeed or fail together
- **Examples**: `createEvent`, `updateUser`, `submitVote`

### Actions
- **Purpose**: Operations that need external API calls or file operations
- **Non-transactional**: Can call external services
- **Examples**: `uploadBackupAction`, `exportIdeasData`, `updateUserPasswordAction`

### Cron Jobs
- **Purpose**: Scheduled background tasks
- **Examples**: `cleanup-stale-presence`

## Key Architecture Features

- **Real-time Updates**: All queries provide live data synchronization
- **Role-based Access Control**: Admin, team lead, and team member permissions
- **Standardized Responses**: Consistent API response structure across all functions
- **Error Handling**: Unified error handling with context-appropriate error types
- **Performance Optimization**: Batch loading and efficient database queries
- **Type Safety**: Full TypeScript integration with generated types
- **Authentication**: Centralized auth framework with role validation
- **Backup & Recovery**: Comprehensive backup system with metadata management
- **Dynamic Forms**: Flexible Sparks system for configurable data collection
- **Analytics**: Bayesian scoring system for fair leaderboards
- **Presence Tracking**: Real-time user activity monitoring

## Database Schema Overview

The system uses 14 main tables with comprehensive indexing:

- **users**: User accounts with roles and event associations
- **events**: Event containers for organizing activities
- **teams**: Team organization within events
- **ideas**: User-submitted ideas with voting
- **sessions**: Activity sessions (Ideas, Quickfire, Sparks)
- **votes**: Voting records with scores and comments
- **quickfires**: Quick voting items by admins
- **quickfireVotes**: User votes on quickfire items
- **snippets**: Reusable text content
- **settings**: Application configuration
- **presence**: Real-time user presence tracking
- **backups**: System backup metadata
- **sparks**: Dynamic form configurations
- **sparkSubmissions**: User responses to spark forms
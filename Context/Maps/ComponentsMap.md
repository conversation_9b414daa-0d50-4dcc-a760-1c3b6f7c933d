# Components Map

A comprehensive reference guide for all components in the LionX application.

## User Components

| Component | Path | Purpose | Usage/Parents |
|-----------|------|---------|---------------|
| **Ideas** | `src/app/user/components/Ideas.tsx` | Main container for Ideas session workflow | `/src/app/user/page.tsx` (lazy-loaded for teamLead role during 'Ideas' sessions) |
| **ProfileSettings** | `src/app/user/components/ProfileSettings.tsx` | User profile management dialog | User layout components (navigation) |
| **QuickfireVoting** | `src/app/user/components/QuickfireVoting.tsx` | Real-time voting interface for quickfire sessions | `/src/app/user/page.tsx` (lazy-loaded for all roles during 'Quickfire' sessions) |
| **SparksComponent** | `src/app/user/components/SparksComponent.tsx` | Main container for Sparks session workflow | `/src/app/user/page.tsx` (lazy-loaded for teamLead role during 'Sparks' sessions) |
| **TeamsNav** | `src/app/user/components/TeamsNav.tsx` | Team selection dropdown navigation | `src/app/user/components/Voting.tsx` (when voting mode is 'all' and multiple teams exist) |
| **UserAuthWrapper** | `src/app/user/components/UserAuthWrapper.tsx` | Client component for authentication checks and redirects | `/src/app/user/layout.tsx` (wraps entire user section for auth protection) |
| **UserDataProvider** | `src/app/user/components/UserDataProvider.tsx` | Client component for real-time Convex data management | `/src/app/user/layout.tsx` (provides user data context to all user components) |
| **UserErrorBoundary** | `src/app/user/components/UserErrorBoundary.tsx` | Error boundary for graceful error handling | `/src/app/user/page.tsx` and `/src/app/user/layout.tsx` (wraps main content areas) |
| **UserHeader** | `src/app/user/components/UserHeader.tsx` | Client component for user header with profile dropdown and theme switching | `/src/app/user/layout.tsx` (header section of user layout) |
| **UserMainContent** | `src/app/user/components/UserMainContent.tsx` | Client component for main content routing and team selection | `/src/app/user/layout.tsx` (main content area of user layout) |
| **Voting** | `src/app/user/components/Voting.tsx` | Main voting interface for submitted ideas | `/src/app/user/page.tsx` (lazy-loaded for teamMembers always, teamLeads after submission) |

### Ideas Sub-components

| Component | Path | Purpose | Usage/Parents |
|-----------|------|---------|---------------|
| **IdeaForm** | `src/app/user/components/ideas/IdeaForm.tsx` | Form for creating and editing ideas | `src/app/user/components/Ideas.tsx` |
| **IdeaList** | `src/app/user/components/ideas/IdeaList.tsx` | Displays list of submitted ideas with management | `src/app/user/components/Ideas.tsx` |

### Sparks Sub-components

| Component | Path | Purpose | Usage/Parents |
|-----------|------|---------|---------------|
| **KeyboardShortcutsDialog** | `src/app/user/components/sparks/KeyboardShortcutsDialog.tsx` | Help dialog for rich text editor shortcuts | `src/app/user/components/sparks/SparkRichTextField.tsx` |
| **SparkCheckbox** | `src/app/user/components/sparks/SparkCheckbox.tsx` | Checkbox input for Sparks forms | `src/app/user/components/sparks/SparkFieldRenderer.tsx` |
| **SparkDropdown** | `src/app/user/components/sparks/SparkDropdown.tsx` | Dropdown selection for Sparks forms | `src/app/user/components/sparks/SparkFieldRenderer.tsx` |
| **SparkFieldRenderer** | `src/app/user/components/sparks/SparkFieldRenderer.tsx` | Dynamic field renderer for different Spark field types | `src/app/user/components/sparks/SparkForm.tsx` |
| **SparkForm** | `src/app/user/components/sparks/SparkForm.tsx` | Main form component for Sparks data submission | `src/app/user/components/SparksComponent.tsx` |
| **SparkRadioGroup** | `src/app/user/components/sparks/SparkRadioGroup.tsx` | Radio button group for Sparks forms | `src/app/user/components/sparks/SparkFieldRenderer.tsx` |
| **SparkRichTextField** | `src/app/user/components/sparks/SparkRichTextField.tsx` | Rich text editor for Sparks forms | `src/app/user/components/sparks/SparkFieldRenderer.tsx`, `src/app/user/components/ideas/IdeaForm.tsx` |
| **SparkSubmissionsList** | `src/app/user/components/sparks/SparkSubmissionsList.tsx` | Displays list of submitted Spark data | `src/app/user/components/SparksComponent.tsx` |
| **SparkTextField** | `src/app/user/components/sparks/SparkTextField.tsx` | Simple text input for Sparks forms | `src/app/user/components/sparks/SparkFieldRenderer.tsx` |

### Teams Sub-components

| Component | Path | Purpose | Usage/Parents |
|-----------|------|---------|---------------|
| **TeamSelector** | `src/app/user/components/teams/TeamSelector.tsx` | Team selection modal for users without assigned teams | `src/app/user/components/UserMainContent.tsx` |

## Admin Components

| Component | Path | Purpose | Usage/Parents |
|-----------|------|---------|---------------|
| **AdminClientWrapper** | `src/app/admin/components/AdminClientWrapper.tsx` | Client component wrapper for admin layout with state management | `/src/app/admin/layout.tsx` (wraps entire admin interface) |
| **AdminDashboard** | `src/app/admin/components/AdminDashboard.tsx` | Main dashboard hub for admin functionality | `/src/app/admin/page.tsx` (primary admin interface) |
| **AdminErrorBoundary** | `src/app/admin/components/AdminErrorBoundary.tsx` | Error boundary for admin component stability | Wraps individual admin components |
| **AdminHeader** | `src/app/admin/components/AdminHeader.tsx` | Client component for admin header with theme switching and breadcrumbs | `src/app/admin/components/AdminClientWrapper.tsx` (header section of admin layout) |
| **AdminSidebar** | `src/app/admin/components/AdminSidebar.tsx` | Client component for admin navigation sidebar with logout functionality | `src/app/admin/components/AdminClientWrapper.tsx` (sidebar section of admin layout) |
| **AdminsManagement** | `src/app/admin/components/AdminsManagement.tsx` | Manages admin user accounts and permissions | `src/app/admin/components/AdminDashboard.tsx` |
| **BackupsManagement** | `src/app/admin/components/BackupsManagement.tsx` | System backup and restore functionality | `src/app/admin/components/AdminDashboard.tsx` |
| **DataAnalytics** | `src/app/admin/components/DataAnalytics.tsx` | Analytics dashboard and data export | `src/app/admin/components/AdminDashboard.tsx` |
| **EventsManagement** | `src/app/admin/components/EventsManagement.tsx` | Manages events (containers for sessions and teams) | `src/app/admin/components/AdminDashboard.tsx` |
| **IdeasManagement** | `src/app/admin/components/IdeasManagement.tsx` | Manages ideas and spark submissions from teams | `src/app/admin/components/AdminDashboard.tsx` |
| **OnlineUsers** | `src/app/admin/components/OnlineUsers.tsx` | Real-time display of currently online users | `src/app/admin/components/AdminDashboard.tsx` |
| **QuickfireManagement** | `src/app/admin/components/QuickfireManagement.tsx` | Manages quickfire items and voting control | `src/app/admin/components/AdminDashboard.tsx` |
| **SessionModals** | `src/app/admin/components/SessionModals.tsx` | Modal dialogs for session management operations | `src/app/admin/components/SessionsManagement.tsx` |
| **SessionsManagement** | `src/app/admin/components/SessionsManagement.tsx` | Manages voting sessions within events | `src/app/admin/components/AdminDashboard.tsx` |
| **Settings** | `src/app/admin/components/Settings.tsx` | App related configuration and settings management | `src/app/admin/components/AdminDashboard.tsx` |
| **SnippetsManagement** | `src/app/admin/components/SnippetsManagement.tsx` | Manages reusable content snippets | `src/app/admin/components/AdminDashboard.tsx` |
| **SparkConfigEditor** | `src/app/admin/components/SparkConfigEditor.tsx` | Rich editor for creating/editing Spark form configurations | `src/app/admin/components/SparksManagement.tsx`, `src/app/admin/components/SessionModals.tsx` |
| **SparksManagement** | `src/app/admin/components/SparksManagement.tsx` | Manages Spark form configurations | `src/app/admin/components/AdminDashboard.tsx` |
| **TeamLeadIcon** | `src/app/admin/components/TeamLeadIcon.tsx` | Icon component for team lead identification | Used throughout admin interface for role indication |
| **TeamsManagement** | `src/app/admin/components/TeamsManagement.tsx` | Manages team organization and assignments | `src/app/admin/components/AdminDashboard.tsx` |
| **UserApprovals** | `src/app/admin/components/UserApprovals.tsx` | Manages pending user registrations | `src/app/admin/components/AdminDashboard.tsx` |
| **UserManagement** | `src/app/admin/components/UserManagement.tsx` | Comprehensive user management interface | `src/app/admin/components/AdminDashboard.tsx` |
| **UserRoleIcon** | `src/app/admin/components/UserRoleIcon.tsx` | Icon component for user role identification | Used throughout admin interface for role indication |
| **VotingManagement** | `src/app/admin/components/VotingManagement.tsx` | Controls voting processes and vote management | `src/app/admin/components/AdminDashboard.tsx` |

### Admin Modal Components

| Component | Path | Purpose | Usage/Parents |
|-----------|------|---------|---------------|
| **AddUserModal** | `src/app/admin/components/modals/AddUserModal.tsx` | Modal for adding new users with team assignment | `src/app/admin/components/UserManagement.tsx` |
| **EditUserModal** | `src/app/admin/components/modals/EditUserModal.tsx` | Modal for editing user information | `src/app/admin/components/UserManagement.tsx` |
| **IdeaStatsModal** | `src/app/admin/components/modals/IdeaStatsModal.tsx` | Modal displaying user's idea statistics | `src/app/admin/components/UserManagement.tsx` |
| **TeamChangeModal** | `src/app/admin/components/modals/TeamChangeModal.tsx` | Modal for changing user team assignments | `src/app/admin/components/UserManagement.tsx` |

### Admin Session Components

| Component | Path | Purpose | Usage/Parents |
|-----------|------|---------|---------------|
| **SessionActions** | `src/app/admin/components/sessions/SessionActions.tsx` | Action buttons for session management | `src/app/admin/components/SessionsManagement.tsx` |
| **SessionForm** | `src/app/admin/components/sessions/SessionForm.tsx` | Form for creating/editing sessions | `src/app/admin/components/SessionsManagement.tsx` |
| **SessionList** | `src/app/admin/components/sessions/SessionList.tsx` | List display of sessions grouped by day | `src/app/admin/components/SessionsManagement.tsx` |

### Backup Sub-components

| Component | Path | Purpose | Usage/Parents |
|-----------|------|---------|---------------|
| **BackupCreationCard** | `src/app/admin/components/backup/BackupCreationCard.tsx` | Creates new system backups | `src/app/admin/components/BackupsManagement.tsx` |
| **BackupListCard** | `src/app/admin/components/backup/BackupListCard.tsx` | Displays backup history and management | `src/app/admin/components/BackupsManagement.tsx` |
| **BackupUploadCard** | `src/app/admin/components/backup/BackupUploadCard.tsx` | Handles backup file uploads | `src/app/admin/components/BackupsManagement.tsx` |
| **DangerZoneCard** | `src/app/admin/components/backup/DangerZoneCard.tsx` | Manages dangerous operations like data deletion | `src/app/admin/components/BackupsManagement.tsx` |

### Ideas Sub-components

| Component | Path | Purpose | Usage/Parents |
|-----------|------|---------|---------------|
| **CountDisplay** | `src/app/admin/components/ideas/components/CountDisplay.tsx` | Shows count statistics for ideas/sparks | `src/app/admin/components/IdeasManagement.tsx` |
| **IdeasList** | `src/app/admin/components/ideas/components/IdeasList.tsx` | Displays list of ideas with detailed information | `src/app/admin/components/IdeasManagement.tsx` |
| **NoActiveEventMessage** | `src/app/admin/components/ideas/components/NoActiveEventMessage.tsx` | Shows message when no event is active | `src/app/admin/components/IdeasManagement.tsx` |
| **SparkSubmissionsList** | `src/app/admin/components/ideas/components/SparkSubmissionsList.tsx` | Displays spark form submissions | `src/app/admin/components/IdeasManagement.tsx` |
| **TeamGroup** | `src/app/admin/components/ideas/components/TeamGroup.tsx` | Groups and displays team-related content | `src/app/admin/components/IdeasManagement.tsx` |

### User Management Sub-components

| Component | Path | Purpose | Usage/Parents |
|-----------|------|---------|---------------|
| **UserHeader** | `src/app/admin/components/users/UserHeader.tsx` | Header component for user displays | `src/app/admin/components/UserManagement.tsx` |
| **UserList** | `src/app/admin/components/users/UserList.tsx` | Lists users with filtering and management options | `src/app/admin/components/UserManagement.tsx` |
| **UserPopups** | `src/app/admin/components/users/UserPopups.tsx` | Modal dialogs for user operations | `src/app/admin/components/UserManagement.tsx` |

## Leaderboard Components

| Component | Path | Purpose | Usage/Parents |
|-----------|------|---------|---------------|
| **LeaderboardAuthWrapper** | `src/app/leaderboard/components/LeaderboardAuthWrapper.tsx` | Client component for leaderboard authentication checks and error handling | `/src/app/leaderboard/layout.tsx` (wraps all leaderboard content) |

## Print Components

| Component | Path | Purpose | Usage/Parents |
|-----------|------|---------|---------------|
| **PrintThemeForcer** | `src/app/print/components/PrintThemeForcer.tsx` | Client component that forces light theme for print layouts | `/src/app/print/layout.tsx` (ensures print-friendly styling) |

## Architecture Notes

- **Server/Client Component Optimization**: Layouts now use proper Next.js 15 server/client boundaries with focused client components for interactivity only
- **Real-time Updates**: All components use Convex for real-time data synchronization
- **Error Boundaries**: Comprehensive error handling with dedicated error boundary components
- **Lazy Loading**: Performance optimization with React.lazy() for main components
- **Role-Based Access**: Components respect user roles (admin, teamLead, teamMember)
- **Session-Based Routing**: User components dynamically load based on active session type
- **Modular Design**: Highly modular architecture supporting dynamic form generation
- **Type Safety**: Full TypeScript integration with Convex types throughout
- **Layout Composition**: Client wrapper components compose focused interactive elements while maintaining server-rendered structure
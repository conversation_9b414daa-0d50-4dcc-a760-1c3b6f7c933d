# Component-Convex Function Relationship Reference

## Overview
This document provides a comprehensive mapping of every component in the LionX voting application to the Convex functions it uses. This reference is critical for understanding component-backend relationships during optimization tasks.

**Total Components Analyzed**: 72
- **User Components**: 20
- **Admin Components**: 52

**Components with Convex Usage**: 34 (47%)
**Components without Convex Usage**: 38 (53%)

---

## User Components

### Main User Components

#### **Ideas.tsx**
**Path**: `src/app/user/components/Ideas.tsx`
**Convex Functions Used**:
- **Queries**: `api.ideas.getIdeasPageData`
- **Mutations**: None
- **Other**: None

**Purpose**: Displays team-submitted ideas with voting capabilities. Uses single optimized query for all data.

---

#### **ProfileSettings.tsx**
**Path**: `src/app/user/components/ProfileSettings.tsx`
**Convex Functions Used**:
- **Queries**: `api.users.getUserByUsername`
- **Mutations**: `api.users.updateUserName`
- **Actions**: `api.userActions.updateUserPasswordAction`

**Purpose**: User profile management with name and password updates.

---

#### **QuickfireVoting.tsx**
**Path**: `src/app/user/components/QuickfireVoting.tsx`
**Convex Functions Used**:
- **Queries**: `api.quickfire.getQuickfirePageData`
- **Mutations**: `api.quickfire.submitQuickfireVote`
- **Other**: None

**Purpose**: Real-time voting on admin-created quickfire items with optimized single query.

---

#### **SparksComponent.tsx**
**Path**: `src/app/user/components/SparksComponent.tsx`
**Convex Functions Used**:
- **Queries**: `api.sparks.getSparks`, `api.sparkSubmissions.getSparkSubmissions`
- **Mutations**: None
- **Other**: None

**Purpose**: Displays spark configuration forms and submission lists.

---

#### **TeamsNav.tsx**
**Path**: `src/app/user/components/TeamsNav.tsx`
**Convex Functions Used**:
- **Queries**: `api.teams.getTeamsByActiveEvent`
- **Mutations**: None
- **Other**: None

**Purpose**: Navigation component showing team structure for active event.

---

#### **UserErrorBoundary.tsx**
**Path**: `src/app/user/components/UserErrorBoundary.tsx`
**Convex Functions Used**:
- **Queries**: None
- **Mutations**: None
- **Other**: None

**Purpose**: Error boundary component for user interface.

---

#### **Voting.tsx**
**Path**: `src/app/user/components/Voting.tsx`
**Convex Functions Used**:
- **Queries**: `api.votes.getVotingPageData`
- **Mutations**: `api.votes.submitVote`
- **Other**: None

**Purpose**: Main voting interface with optimized single query for all voting data.

---

### Ideas Subdirectory

#### **IdeaForm.tsx**
**Path**: `src/app/user/components/ideas/IdeaForm.tsx`
**Convex Functions Used**:
- **Queries**: `api.ideas.getIdeaFormData`
- **Mutations**: `api.ideas.createIdea`, `api.ideas.updateIdea`
- **Other**: None

**Purpose**: Form for creating and editing team ideas with rich text editor.

---

#### **IdeaList.tsx**
**Path**: `src/app/user/components/ideas/IdeaList.tsx`
**Convex Functions Used**:
- **Queries**: None
- **Mutations**: None
- **Other**: None

**Purpose**: Display component for idea lists (receives data from parent).

---

### Sparks Subdirectory

#### **KeyboardShortcutsDialog.tsx**
**Path**: `src/app/user/components/sparks/KeyboardShortcutsDialog.tsx`
**Convex Functions Used**:
- **Queries**: None
- **Mutations**: None
- **Other**: None

**Purpose**: Dialog component showing keyboard shortcuts for spark forms.

---

#### **SparkCheckbox.tsx**
**Path**: `src/app/user/components/sparks/SparkCheckbox.tsx`
**Convex Functions Used**:
- **Queries**: None
- **Mutations**: None
- **Other**: None

**Purpose**: Checkbox field renderer for spark forms.

---

#### **SparkDropdown.tsx**
**Path**: `src/app/user/components/sparks/SparkDropdown.tsx`
**Convex Functions Used**:
- **Queries**: None
- **Mutations**: None
- **Other**: None

**Purpose**: Dropdown field renderer for spark forms.

---

#### **SparkFieldRenderer.tsx**
**Path**: `src/app/user/components/sparks/SparkFieldRenderer.tsx`
**Convex Functions Used**:
- **Queries**: None
- **Mutations**: None
- **Other**: None

**Purpose**: Dynamic field renderer that delegates to specific field components.

---

#### **SparkForm.tsx**
**Path**: `src/app/user/components/sparks/SparkForm.tsx`
**Convex Functions Used**:
- **Queries**: `api.sparks.getSparks`, `api.sparkSubmissions.getSparkSubmissions`, `api.sparkSubmissions.getUserSparkSubmission`, `api.users.getUserByUsername`
- **Mutations**: `api.sparkSubmissions.createSparkSubmission`, `api.sparkSubmissions.updateSparkSubmission`
- **Other**: None

**Purpose**: Complex form management for spark submissions with multiple queries and mutations.

---

#### **SparkRadioGroup.tsx**
**Path**: `src/app/user/components/sparks/SparkRadioGroup.tsx`
**Convex Functions Used**:
- **Queries**: None
- **Mutations**: None
- **Other**: None

**Purpose**: Radio group field renderer for spark forms.

---

#### **SparkRichTextField.tsx**
**Path**: `src/app/user/components/sparks/SparkRichTextField.tsx`
**Convex Functions Used**:
- **Queries**: None
- **Mutations**: None
- **Other**: None

**Purpose**: Rich text field renderer using Tiptap editor.

---

#### **SparkSubmissionsList.tsx**
**Path**: `src/app/user/components/sparks/SparkSubmissionsList.tsx`
**Convex Functions Used**:
- **Queries**: None
- **Mutations**: None
- **Other**: None

**Purpose**: Display component for spark submission lists (receives data from parent).

---

#### **SparkTextField.tsx**
**Path**: `src/app/user/components/sparks/SparkTextField.tsx`
**Convex Functions Used**:
- **Queries**: None
- **Mutations**: None
- **Other**: None

**Purpose**: Text field renderer for spark forms.

---

## Admin Components

### Main Admin Components

#### **AdminDashboard.tsx**
**Path**: `src/app/admin/components/AdminDashboard.tsx`
**Convex Functions Used**:
- **Queries**: None
- **Mutations**: None
- **Other**: None

**Purpose**: Router component for admin dashboard sections.

---

#### **AdminErrorBoundary.tsx**
**Path**: `src/app/admin/components/AdminErrorBoundary.tsx`
**Convex Functions Used**:
- **Queries**: None
- **Mutations**: None
- **Other**: None

**Purpose**: Error boundary component for admin interface.

---

#### **AdminsManagement.tsx**
**Path**: `src/app/admin/components/AdminsManagement.tsx`
**Convex Functions Used**:
- **Queries**: `api.users.getAdminUsers`
- **Mutations**: `api.users.updateAdminUsername`, `api.users.deleteAdminUser`
- **Actions**: `api.userActions.createAdminUserAction`, `api.userActions.updateAdminPasswordAction`

**Purpose**: Management interface for admin users with CRUD operations.

---

#### **BackupErrorBoundary.tsx**
**Path**: `src/app/admin/components/BackupErrorBoundary.tsx`
**Convex Functions Used**:
- **Queries**: None
- **Mutations**: None
- **Other**: None

**Purpose**: Error boundary component for backup operations.

---

#### **BackupsManagement.tsx**
**Path**: `src/app/admin/components/BackupsManagement.tsx`
**Convex Functions Used**:
- **Queries**: None
- **Mutations**: None
- **Other**: None

**Purpose**: Container component for backup management sections.

---

#### **DataAnalytics.tsx**
**Path**: `src/app/admin/components/DataAnalytics.tsx`
**Convex Functions Used**:
- **Queries**: `api.analytics.getSessionsForLeaderboard`, `api.analytics.getTeamsForDropdown`, `api.sessions.getSessionsByActiveEventExcludingQuickfire`
- **Mutations**: None
- **Actions**: `api.analytics.exportIdeasData`, `api.analytics.exportTeamsData`

**Purpose**: Analytics dashboard with data visualization and export capabilities.

---

#### **EventsManagement.tsx**
**Path**: `src/app/admin/components/EventsManagement.tsx`
**Convex Functions Used**:
- **Queries**: `api.events.getAllEvents`
- **Mutations**: `api.events.createEvent`, `api.events.updateEvent`, `api.events.activateEvent`, `api.events.deactivateEvent`, `api.events.deleteEvent`
- **Other**: None

**Purpose**: Event management with full CRUD operations and activation controls.

---

#### **IdeasManagement.tsx**
**Path**: `src/app/admin/components/IdeasManagement.tsx`
**Convex Functions Used**:
- **Queries**: `api.events.getActiveEvent`, `api.sessions.getSessionsByActiveEventExcludingQuickfire`, `api.ideas.getAllDataGroupedByTeam`
- **Mutations**: `api.ideas.withdrawAllTeamData`
- **Other**: None

**Purpose**: Admin interface for managing ideas with team grouping and withdrawal capabilities.

---

#### **OnlineUsers.tsx**
**Path**: `src/app/admin/components/OnlineUsers.tsx`
**Convex Functions Used**:
- **Queries**: `api.settings.getOnlineUsersSetting`, `api.presence.getOnlineUsers`, `api.teams.getTeamsByActiveEvent`
- **Mutations**: None
- **Other**: None

**Purpose**: Real-time display of online users grouped by teams.

---

#### **QuickfireManagement.tsx**
**Path**: `src/app/admin/components/QuickfireManagement.tsx`
**Convex Functions Used**:
- **Queries**: `api.events.getActiveEvent`, `api.quickfire.getQuickfireSessions`, `api.quickfire.getQuickfireItemsBySession`
- **Mutations**: `api.quickfire.createQuickfireItem`, `api.quickfire.updateQuickfireItem`, `api.quickfire.deleteQuickfireItem`, `api.quickfire.toggleQuickfireVoting`, `api.quickfire.reorderQuickfireItems`, `api.quickfire.resetOrderByCreatedAt`
- **Other**: None

**Purpose**: Comprehensive quickfire management with item CRUD, voting controls, and ordering.

---

#### **SessionModals.tsx**
**Path**: `src/app/admin/components/SessionModals.tsx`
**Convex Functions Used**:
- **Queries**: None
- **Mutations**: None
- **Other**: None

**Purpose**: Modal component for session management dialogs.

---

#### **SessionsManagement.tsx**
**Path**: `src/app/admin/components/SessionsManagement.tsx`
**Convex Functions Used**:
- **Queries**: `api.sessions.getSessionsByActiveEvent`, `api.events.getActiveEvent`, `api.voting.getVotingStatus`, `api.sparks.getSparks`
- **Mutations**: `api.sessions.createSession`, `api.sessions.updateSession`, `api.sessions.activateSession`, `api.sessions.deactivateSession`, `api.sessions.deleteSession`, `api.voting.toggleVoting`
- **Other**: None

**Purpose**: Session management with full CRUD operations and voting controls.

---

#### **Settings.tsx**
**Path**: `src/app/admin/components/Settings.tsx`
**Convex Functions Used**:
- **Queries**: `api.settings.getAutoApprovalSetting`, `api.settings.getTeamSelectionSetting`, `api.settings.getOnlineUsersSetting`
- **Mutations**: `api.settings.setAutoApprovalSetting`, `api.settings.setTeamSelectionSetting`, `api.settings.setOnlineUsersSetting`
- **Other**: None

**Purpose**: Application settings management with toggle controls.

---

#### **SnippetsManagement.tsx**
**Path**: `src/app/admin/components/SnippetsManagement.tsx`
**Convex Functions Used**:
- **Queries**: `api.snippets.getAllSnippets`
- **Mutations**: `api.snippets.createSnippet`, `api.snippets.updateSnippet`, `api.snippets.deleteSnippet`
- **Other**: None

**Purpose**: Code snippets management with CRUD operations.

---

#### **SparkConfigEditor.tsx**
**Path**: `src/app/admin/components/SparkConfigEditor.tsx`
**Convex Functions Used**:
- **Queries**: None
- **Mutations**: `api.sparks.createSpark`, `api.sparks.updateSpark`
- **Other**: None

**Purpose**: Form editor for spark configuration with drag-and-drop field management.

---

#### **SparksManagement.tsx**
**Path**: `src/app/admin/components/SparksManagement.tsx`
**Convex Functions Used**:
- **Queries**: `api.sparks.getSparks`
- **Mutations**: `api.sparks.deleteSpark`, `api.sparks.updateSpark`
- **Actions**: `api.sparks.checkCanDeleteSpark`, `api.sparks.checkCanEditSpark`, `api.sparks.getSparkSubmissionCountAction`

**Purpose**: Spark management with CRUD operations and submission tracking.

---

#### **TeamLeadIcon.tsx**
**Path**: `src/app/admin/components/TeamLeadIcon.tsx`
**Convex Functions Used**:
- **Queries**: None
- **Mutations**: None
- **Other**: None

**Purpose**: Icon component for team lead identification.

---

#### **TeamsManagement.tsx**
**Path**: `src/app/admin/components/TeamsManagement.tsx`
**Convex Functions Used**:
- **Queries**: `api.teams.getTeamsByActiveEvent`, `api.events.getActiveEvent`
- **Mutations**: `api.teams.createTeam`, `api.teams.updateTeam`, `api.teams.deleteTeam`, `api.teams.toggleTeamActive`
- **Other**: None

**Purpose**: Team management with CRUD operations and activation controls.

---

#### **UserApprovals.tsx**
**Path**: `src/app/admin/components/UserApprovals.tsx`
**Convex Functions Used**:
- **Queries**: `api.users.getUsersByStatus` (pending), `api.users.getUsersByStatus` (rejected)
- **Mutations**: `api.users.updateUserStatus`, `api.users.deleteUser`
- **Other**: None

**Purpose**: User approval workflow with status management.

---

#### **UserManagement.tsx**
**Path**: `src/app/admin/components/UserManagement.tsx`
**Convex Functions Used**:
- **Queries**: `api.events.getActiveEvent`, `api.users.getUsersGroupedByTeam`, `api.users.getUserIdeas` (conditional)
- **Mutations**: `api.users.updateUserName`, `api.users.updateUserRole`, `api.users.updateUserTeam`, `api.users.bulkUpdateUserTeam`, `api.users.deleteUser`, `api.users.transferUserContent`, `api.users.updateUserStatus`
- **Actions**: `api.userActions.updateUserPasswordAction`, `api.userActions.createUserWithTeamAction`

**Purpose**: Comprehensive user management with bulk operations and content transfer.

---

#### **UserRoleIcon.tsx**
**Path**: `src/app/admin/components/UserRoleIcon.tsx`
**Convex Functions Used**:
- **Queries**: None
- **Mutations**: None
- **Other**: None

**Purpose**: Icon component for user role identification.

---

#### **VotingManagement.tsx**
**Path**: `src/app/admin/components/VotingManagement.tsx`
**Convex Functions Used**:
- **Queries**: `api.voting.getVotingStatus`, `api.voting.canStartVoting`, `api.voting.getTeamsVotingStatus`, `api.voting.getVotingTeamStatus`
- **Mutations**: `api.voting.toggleVoting`, `api.voting.setTeamVoting`
- **Other**: None

**Purpose**: Voting system controls with team-specific voting management.

---

### Backup Components

#### **BackupCreationCard.tsx**
**Path**: `src/app/admin/components/backup/BackupCreationCard.tsx`
**Convex Functions Used**:
- **Queries**: None
- **Mutations**: None
- **Other**: Uses fetch API to `/api/backup/create`

**Purpose**: Card component for creating database backups.

---

#### **BackupListCard.tsx**
**Path**: `src/app/admin/components/backup/BackupListCard.tsx`
**Convex Functions Used**:
- **Queries**: `api.backupData.getBackupsList`
- **Mutations**: None
- **Actions**: `api.backupActions.deleteBackupAction`, `api.backupActions.getBackupDownloadUrlAction`

**Purpose**: List and management interface for existing backups.

---

#### **BackupUploadCard.tsx**
**Path**: `src/app/admin/components/backup/BackupUploadCard.tsx`
**Convex Functions Used**:
- **Queries**: None
- **Mutations**: None
- **Actions**: `api.backupActions.uploadBackupAction`

**Purpose**: Card component for uploading backup files.

---

#### **DangerZoneCard.tsx**
**Path**: `src/app/admin/components/backup/DangerZoneCard.tsx`
**Convex Functions Used**:
- **Queries**: None
- **Mutations**: `api.backupData.clearAllDataMutation`
- **Other**: None

**Purpose**: Dangerous operations card for data clearing.

---

### Ideas Components

#### **CountDisplay.tsx**
**Path**: `src/app/admin/components/ideas/components/CountDisplay.tsx`
**Convex Functions Used**:
- **Queries**: None
- **Mutations**: None
- **Other**: None

**Purpose**: Display component for count statistics.

---

#### **IdeasList.tsx**
**Path**: `src/app/admin/components/ideas/components/IdeasList.tsx`
**Convex Functions Used**:
- **Queries**: None
- **Mutations**: None
- **Other**: None

**Purpose**: Display component for ideas lists.

---

#### **NoActiveEventMessage.tsx**
**Path**: `src/app/admin/components/ideas/components/NoActiveEventMessage.tsx`
**Convex Functions Used**:
- **Queries**: None
- **Mutations**: None
- **Other**: None

**Purpose**: Message component for no active event state.

---

#### **SparkSubmissionsList.tsx**
**Path**: `src/app/admin/components/ideas/components/SparkSubmissionsList.tsx`
**Convex Functions Used**:
- **Queries**: None
- **Mutations**: None
- **Other**: None

**Purpose**: Display component for spark submissions lists.

---

#### **TeamGroup.tsx**
**Path**: `src/app/admin/components/ideas/components/TeamGroup.tsx`
**Convex Functions Used**:
- **Queries**: None
- **Mutations**: None
- **Other**: None

**Purpose**: Display component for team grouping.

---

### User Components

#### **UserHeader.tsx**
**Path**: `src/app/admin/components/users/UserHeader.tsx`
**Convex Functions Used**:
- **Queries**: None
- **Mutations**: None
- **Other**: None

**Purpose**: Header component for user management sections.

---

#### **UserList.tsx**
**Path**: `src/app/admin/components/users/UserList.tsx`
**Convex Functions Used**:
- **Queries**: None
- **Mutations**: None
- **Other**: None

**Purpose**: Display component for user lists.

---

#### **UserPopups.tsx**
**Path**: `src/app/admin/components/users/UserPopups.tsx`
**Convex Functions Used**:
- **Queries**: None
- **Mutations**: None
- **Other**: None

**Purpose**: Modal component for user management popups.

---

## Summary Statistics

### Overall Statistics
- **Total Components**: 72
- **Components with Convex Usage**: 34 (47%)
- **Components without Convex Usage**: 38 (53%)

### User Components Statistics
- **Total User Components**: 20
- **User Components with Convex Usage**: 8 (40%)
- **User Components without Convex Usage**: 12 (60%)

### Admin Components Statistics
- **Total Admin Components**: 52
- **Admin Components with Convex Usage**: 26 (50%)
- **Admin Components without Convex Usage**: 26 (50%)

### Convex Function Usage Distribution
- **useQuery**: 44 instances across 22 components
- **useMutation**: 47 instances across 22 components
- **useAction**: 12 instances across 8 components

### Most Connected Components

#### **Top 5 User Components**
1. **SparkForm.tsx** - 6 Convex functions (4 queries, 2 mutations)
2. **ProfileSettings.tsx** - 3 Convex functions (1 query, 1 mutation, 1 action)
3. **IdeaForm.tsx** - 3 Convex functions (1 query, 2 mutations)
4. **Ideas.tsx** - 1 Convex function (1 query)
5. **QuickfireVoting.tsx** - 2 Convex functions (1 query, 1 mutation)

#### **Top 5 Admin Components**
1. **UserManagement.tsx** - 11 Convex functions (3 queries, 6 mutations, 2 actions)
2. **QuickfireManagement.tsx** - 9 Convex functions (3 queries, 6 mutations)
3. **SessionsManagement.tsx** - 8 Convex functions (4 queries, 4 mutations)
4. **VotingManagement.tsx** - 6 Convex functions (4 queries, 2 mutations)
5. **EventsManagement.tsx** - 6 Convex functions (1 query, 5 mutations)

### Performance Optimization Patterns Identified

#### **Single Combined Queries** (Phase 2 Optimization)
- **Ideas.tsx**: Uses `api.ideas.getIdeasPageData` (single query)
- **QuickfireVoting.tsx**: Uses `api.quickfire.getQuickfirePageData` (single query)
- **Voting.tsx**: Uses `api.votes.getVotingPageData` (single query)

#### **Multiple Query Components** (Optimization Candidates)
- **SparkForm.tsx**: 4 queries (optimization opportunity)
- **IdeasManagement.tsx**: 3 queries (optimization opportunity)
- **UserManagement.tsx**: 3 queries (optimization opportunity)
- **DataAnalytics.tsx**: 3 queries (optimization opportunity)

#### **High Mutation Components** (Critical for Performance)
- **UserManagement.tsx**: 6 mutations (user operations)
- **QuickfireManagement.tsx**: 6 mutations (quickfire operations)
- **EventsManagement.tsx**: 5 mutations (event operations)
- **SessionsManagement.tsx**: 5 mutations (session operations)

---

## Critical Relationships for Optimization

### **Phase 1 Optimization Targets**
- **Analytics Functions**: Used by `DataAnalytics.tsx` component
- **User Deletion**: Used by `UserManagement.tsx`, `UserApprovals.tsx` components
- **Team Deletion**: Used by `TeamsManagement.tsx` component
- **Spark Submissions**: Used by `SparkForm.tsx`, `SparksComponent.tsx` components

### **Phase 2 Optimization Targets**
- **Analytics Leaderboard**: Used by `DataAnalytics.tsx` component
- **Ideas Data Grouping**: Used by `IdeasManagement.tsx` component
- **Session Updates**: Used by `SessionsManagement.tsx` component
- **User Grouping**: Used by `UserManagement.tsx` component

### **Real-time Components** (Convex Subscription Critical)
- **OnlineUsers.tsx**: Real-time presence tracking
- **QuickfireVoting.tsx**: Real-time voting updates
- **Voting.tsx**: Real-time vote synchronization
- **Ideas.tsx**: Real-time idea updates

---

## New Components Added

### User Components

#### **TeamSelector.tsx**
**Path**: `src/app/user/components/teams/TeamSelector.tsx`
**Convex Functions Used**:
- **Queries**: `api.teams.getTeamsByActiveEvent`
- **Mutations**: `api.users.updateUserTeam`
- **Actions**: None

**Purpose**: Team selection modal for users without assigned teams to join available teams.

---

### Admin Modal Components

#### **AddUserModal.tsx**
**Path**: `src/app/admin/components/modals/AddUserModal.tsx`
**Convex Functions Used**:
- **Queries**: None (receives data via props)
- **Mutations**: None (calls parent handler)
- **Actions**: None

**Purpose**: Modal interface for adding new users with team assignment and role selection.

---

#### **EditUserModal.tsx**
**Path**: `src/app/admin/components/modals/EditUserModal.tsx`
**Convex Functions Used**:
- **Queries**: None (receives data via props)
- **Mutations**: None (calls parent handler)
- **Actions**: None

**Purpose**: Modal interface for editing existing user information including name, username, and password.

---

#### **IdeaStatsModal.tsx**
**Path**: `src/app/admin/components/modals/IdeaStatsModal.tsx`
**Convex Functions Used**:
- **Queries**: `api.users.getUserIdeasForDeletion`
- **Mutations**: None
- **Actions**: None

**Purpose**: Displays comprehensive statistics about a user's ideas and content for deletion confirmation.

---

#### **TeamChangeModal.tsx**
**Path**: `src/app/admin/components/modals/TeamChangeModal.tsx`
**Convex Functions Used**:
- **Queries**: None (receives data via props)
- **Mutations**: None (calls parent handlers)
- **Actions**: None

**Purpose**: Modal for changing user team assignments with support for bulk operations and team lead content transfer.

---

### Admin Session Components

#### **SessionActions.tsx**
**Path**: `src/app/admin/components/sessions/SessionActions.tsx`
**Convex Functions Used**:
- **Queries**: None
- **Mutations**: None
- **Actions**: None

**Purpose**: Simple action button component for triggering session creation.

---

#### **SessionForm.tsx**
**Path**: `src/app/admin/components/sessions/SessionForm.tsx`
**Convex Functions Used**:
- **Queries**: None (receives data via props)
- **Mutations**: None (calls parent handler)
- **Actions**: None

**Purpose**: Form component for creating and editing sessions with spark configuration support.

---

#### **SessionList.tsx**
**Path**: `src/app/admin/components/sessions/SessionList.tsx`
**Convex Functions Used**:
- **Queries**: None (receives data via props)
- **Mutations**: None (calls parent handlers)
- **Actions**: None

**Purpose**: Displays sessions grouped by day with inline editing and management capabilities.

---

This reference document provides the complete mapping needed for optimization tasks, showing exactly which components will be affected by backend function changes and optimizations.
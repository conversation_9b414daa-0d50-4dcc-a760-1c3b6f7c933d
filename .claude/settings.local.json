{"permissions": {"allow": ["mcp__dart__get_task", "mcp__context7__get-library-docs", "WebFetch(domain:stack.convex.dev)", "WebFetch(domain:labs.convex.dev)", "mcp__ide__getDiagnostics", "Bash(find:*)", "Bash(grep:*)", "Bash(rg:*)", "Bash(npx tsc:*)", "Bash(npm run lint)", "WebFetch(domain:docs.convex.dev)", "Bash(bun run lint:*)", "mcp__convex__data", "Bash(bun tsc:*)", "Bash(bunx tsc:*)", "mcp__convex__status", "mcp__convex__tables", "mcp__sequential-thinking__sequentialthinking", "<PERSON><PERSON>(mkdir:*)", "<PERSON><PERSON>(mkdir:*)", "<PERSON><PERSON>(cat:*)", "mcp__Ref__ref_search_documentation", "mcp__Ref__ref_read_url", "mcp__graphiti-memory__add_memory", "mcp__graphiti-memory__get_episodes", "mcp__graphiti-memory__search_memory_facts"], "deny": []}, "enableAllProjectMcpServers": true, "enabledMcpjsonServers": ["taskmaster-ai"], "hooks": {"PreToolUse": [{"matcher": "", "hooks": [{"type": "command", "command": "python3 .claude/hooks/pre-tool-use.py"}]}]}}
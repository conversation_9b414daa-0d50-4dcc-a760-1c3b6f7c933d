# Create PRD for task: $ARGUMENTS

Generate a complete PRD for the implementation with thorough research. Ensure context is passed to the AI agent to enable self-validation and iterative refinement. Analyze the app throughly to understand the task and the codebase and the similarly implemented components (if there are any).

The AI agent only gets the context you are appending to the PRD and training data. Assuma the AI agent has access to the codebase and the same knowledge cutoff as you, so its important that your research findings are included or referenced in the PRD.

## Research Process

1. **Codebase Analysis**
   - Identify files to reference in PRD
   - Note existing conventions to follow
   - Check test patterns for validation approach
   - Reference similarly implemented components
   - Reference and spec files that explain the previous implementation as well.

2. **User Clarification** (if needed)
   - Specific patterns to mirror and where to find them?
   - Integration requirements and where to find them?

## PRD Generation


### Critical Context to Include and pass to the AI agent as part of the PRD
- **Code Examples**: Real snippets from codebase
- **Gotchas**: Library quirks, version issues
- **Patterns**: Existing approaches to follow

*** CRITICAL AFTER YOU ARE DONE RESEARCHING AND EXPLORING THE CODEBASE BEFORE YOU START WRITING THE PRD ***

*** <PERSON><PERSON><PERSON><PERSON>INK ABOUT THE PRD AND PLAN YOUR APPROACH THEN START WRITING THE PRD ***

## Output
Save as: `PRDs/{feature/task-name}.md`

## Quality Checklist
- [ ] All necessary context included
- [ ] Validation gates are executable by AI
- [ ] References existing patterns
- [ ] Clear implementation path
- [ ] Error handling documented

Score the PRD on a scale of 1-10 (confidence level to succeed in one-pass implementation using claude codes)

Remember: The goal is implementation success through comprehensive context.
PRD File: $ARGUMENTS

## Execution Process

### Step 1: **Document Validation**
Read and Validate: Ensure the PRD is complete and aligns with the current codebase. Check for all necessary sections and flag any inconsistencies or missing information.

### Step 2: **Extract and Categorize Requirements**

- Extract Requirements: Identify and extract all functional and non-functional requirements.
- Categorize: Group related requirements into categories.

### Step 3: **Create Task Files**
- Task Breakdown: For each category, create a task file with:
- Task ID: Unique identifier.
- Title: Brief task description.
- Description: Detailed task description.
- Requirements: Specific requirements for the task.
- Dependencies: Any task dependencies.
- Acceptance Criteria: Clear completion criteria. **ALWAYS ADD 100% functionality parity**
- ~~Timeframe:~~ **Dont define any timeframe**.

### Step 4: **Codebase Review**
- Search Codebase: Review the existing codebase to ensure the tasks align with current code and identify any potential conflicts.

### Step 5: Output
- Generate Task Files: Output structured task files (e.g., JSON, Markdown).
- Summary Report: Provide a summary report with an overview of tasks, issues found, and recommendations.

> output will be in `Context/TASKS/{PRD-name}/{Id-name}.md/`

Ensure the PRD is accurately broken down into manageable and validated tasks.
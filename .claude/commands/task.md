

**important** Refer to [React 19 Patterns](Context/Docs/Convex-Best-Practices-2025.md)

## Task: $ARGUMENTS

1. **Implementation**
  - Read the specified Task file/s
  - Understand all context and requirements
  - Follow all instructions in the PRD and extend the research if needed
  - Ensure you have all needed context to implement the PRD fully
  - Do more codebase exploration as needed

2. **Guidelines**
  - Break down complex tasks into smaller, manageable steps using your todos tools.
  - Use the TodoWrite tool to create and track your implementation plan.
  - Identify implementation patterns from existing code to follow.
  - **Parallel Sub-agents**: when possible utilise parallel sub-agents for maximizing speed and effeciency.

3. **Validate VERY IMPORTANT**
   - NEVER run `npm run build`, `bun run build`, `npm run start`, `bun run dev`
   - run `git status` to get a list of the files you changed
   - search [Admin Compoenents](/src/app/admin/components/) for any compoenent thats using any of the files you changes.
   - search [User Compoenents](/src/app/user/components/) for any compoenent thats using any of the files you changes.
   - Validate and confirm that functionality parity is 100%.
   - Run each Allowed validation commands `bunx tsc --noEmit` and `bun run lint`
   - Fix any failures
   - Re-run until all pass

4. **Complete**
   - Ensure all checklist items done
   - Run final validation suite
   - Report completion status
   - update the task file with completion and what u've done
   - Add to the same file what exactly in the app to test to confirm functionality parity
   - **MOST IMPORTANT SUCESS CRETERIA** 100% functionality parity

5. Additional Important Douments:
   - [Convex Map](/Context/Maps/ConvexMap.md)
   - [Convex Helpers Map](/Context/Maps/ConvexHelpersMap.md)
   - [Compoenents convex relationship](/Context/Maps/components-convex-relationship.md)
   - [Compoenents convex reference](/Context/Maps/LionX-Component-Convex-Reference.md)
   - [Admin compoenents](/src/app/admin/components/)
   - [User compoenents](/src/app/user/components/)
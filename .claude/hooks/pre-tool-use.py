#!/usr/bin/env python3

import json
import sys
import re
from pathlib import Path

# Patterns for specific npm/bun/next commands
BLOCK_TERMS = [
    "npm run build",
    "npm run dev",
    "bun run build",
    "bun run dev",
    "next start"
]




# Patterns for system-damaging commands
DANGEROUS_BASH_PATTERNS = [
    r'\brm\s+.*-[a-z]*r[a-z]*f\b',   # rm -rf, -fr, -Rf, etc.
    r'\brm\s+.*-[a-z]*f[a-z]*r\b',   # rm -fr, etc.
    r'\brm\s+(--recursive).*--force', # rm --recursive --force
    r'\brm\s+(--force).*--recursive', # rm --force --recursive
    r'\brm\b',                       # Any rm command
    r'^\s*(shutdown|poweroff|reboot)\b'  # shutdown, poweroff, reboot (start of line)
]

# Patterns for sensitive file access
ENV_FILE_PATTERNS = [
    r'\b\.env\b(?!\.sample)',              # .env (but not .env.sample)
    r'cat\s+.*\.env\b(?!\.sample)',
    r'echo\s+.*>\s*\.env\b(?!\.sample)',
    r'touch\s+.*\.env\b(?!\.sample)',
    r'cp\s+.*\.env\b(?!\.sample)',
    r'mv\s+.*\.env\b(?!\.sample)',
]

def is_blocked_bash_command(command):
    c = ' '.join(command.lower().split())
    # Check for npm/bun/next
    for term in BLOCK_TERMS:
        if term in c:
            return f"Blocked: Use of `{term}` in `{command}` is not allowed."
    # Check for dangerous bash
    for pat in DANGEROUS_BASH_PATTERNS:
        if re.search(pat, c):
            return f"Blocked: Dangerous system command `{command}` is not allowed."
    # Check dangerous flags with sensitive paths on recursive rm
    if re.search(r'\brm\s+.*-[a-z]*r', c):  # Recursive rm
        for path_pat in [r'/', r'~', r'\$HOME', r'\.\.', r'\*', r'\.$', r'\. ']:
            if re.search(path_pat, c):
                return f"Blocked: rm with recursive flag targeting sensitive path in `{command}`."
    return None

def is_env_file_access(tool_name, tool_input):
    # Block file tool access to .env (unless it's .env.sample)
    if tool_name in ['Read', 'Edit', 'MultiEdit', 'Write']:
        file_path = tool_input.get('file_path', '')
        if '.env' in file_path and not file_path.endswith('.env.sample'):
            return f"Blocked: Access to sensitive .env file `{file_path}` is prohibited."
    # Block bash attempts to access .env
    if tool_name == 'Bash':
        command = tool_input.get('command', '')
        for pattern in ENV_FILE_PATTERNS:
            if re.search(pattern, command):
                return f"Blocked: Command `{command}` is prohibited – .env file access is restricted."
    return None

def log_attempt(input_data):
    try:
        log_dir = Path.cwd() / 'logs'
        log_dir.mkdir(parents=True, exist_ok=True)
        log_path = log_dir / 'pre_tool_use.json'
        if log_path.exists():
            with open(log_path, 'r') as f:
                try:
                    log_data = json.load(f)
                except Exception:
                    log_data = []
        else:
            log_data = []
        log_data.append(input_data)
        with open(log_path, 'w') as f:
            json.dump(log_data, f, indent=2)
    except Exception:
        pass  # Logging should never break hook

def main():
    try:
        # Read JSON input from stdin
        input_data = json.load(sys.stdin)
        tool_name = input_data.get('tool_name', '')
        tool_input = input_data.get('tool_input', {})
        
        # Log every attempt for audit
        log_attempt(input_data)
        
        # First, check for .env file access (file tools or bash)
        env_block_reason = is_env_file_access(tool_name, tool_input)
        if env_block_reason:
            print(env_block_reason, file=sys.stderr)
            sys.exit(2)  # Block with message
        
        if tool_name == 'Bash':
            command = tool_input.get('command', '')
            block_reason = is_blocked_bash_command(command)
            if block_reason:
                print(block_reason, file=sys.stderr)
                sys.exit(2)  # Block dangerous bash
        
        sys.exit(0)  # Otherwise, allow

    except Exception:
        sys.exit(0)  # On error, allow (fail open so you don't block everything accidentally)

if __name__ == '__main__':
    main()

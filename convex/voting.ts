import { v } from "convex/values";
import { mutation, query } from "./_generated/server";
import { getActiveEvent, getActiveEventOrNull } from "./lib/eventHelpers";
import { getSettingByKey, getVotingStatus as getVotingStatusFromSettings, updateOrCreateSetting } from "./lib/settingsHelpers";
import { requireAdmin } from "./lib/auth";
import { getActiveSessionForActiveEvent, batchUpdateSessions } from "./lib/sessionHelpers";
import { getTeamsByEvent, getActiveTeamsByEvent, batchUpdateTeamVotingStatus, calculateVotingMode } from "./lib/teamHelpers";
import { 
  MutationResponse, 
  QueryResponse
} from "./lib/responseTypes";
import { 
  createSuccessResponse
} from "./lib/responseUtils";

// Get voting status
export const getVotingStatus = query({
  args: {},
  handler: async (ctx): Promise<QueryResponse<{ votingStarted: boolean }>> => {
    // Check if voting is enabled
    const votingStarted = await getVotingStatusFromSettings(ctx);

    return {
      votingStarted,
    };
  },
});

// Check if voting can be started (all active teams have submitted)
export const canStartVoting = query({
  args: {},
  handler: async (ctx): Promise<QueryResponse<{
    canStart: boolean;
    reason: string;
    unfinishedTeams?: { id: string; name: string }[];
    activeTeams?: { id: string; name: string }[];
    finishedTeams?: number;
  }>> => {
    // Get active event
    const activeEvent = await getActiveEventOrNull(ctx);

    if (!activeEvent) {
      return { canStart: false, reason: "No active event found" };
    }

    // Get active session
    const activeSession = await getActiveSessionForActiveEvent(ctx);

    if (!activeSession) {
      return { canStart: false, reason: "No active session found" };
    }

    // Only allow voting for Ideas sessions
    if (activeSession.type !== "Ideas") {
      return { canStart: false, reason: "Voting is only available for Ideas sessions" };
    }

    // Get all active teams for this event using optimized helper
    const activeTeams = await getActiveTeamsByEvent(ctx, activeEvent._id);

    if (activeTeams.length === 0) {
      return { canStart: false, reason: "No active teams found" };
    }

    // Get finished teams from session
    const finishedTeamIds = activeSession.finishedTeams || [];
    
    // Check if all active teams have finished (submitted)
    const activeTeamIds = activeTeams.map((team: any) => team._id);
    const allActiveTeamsFinished = activeTeamIds.every((teamId: any) => 
      finishedTeamIds.includes(teamId)
    );

    if (!allActiveTeamsFinished) {
      const unfinishedTeams = activeTeams.filter((team: any) => 
        !finishedTeamIds.includes(team._id)
      );
      return { 
        canStart: false, 
        reason: `Waiting for ${unfinishedTeams.length} active team(s) to submit: ${unfinishedTeams.map((t: any) => t.name).join(', ')}`,
        unfinishedTeams: unfinishedTeams.map((t: any) => ({ id: t._id, name: t.name }))
      };
    }

    return { 
      canStart: true, 
      reason: "All active teams have submitted their ideas",
      activeTeams: activeTeams.map((t: any) => ({ id: t._id, name: t.name })),
      finishedTeams: finishedTeamIds.length
    };
  },
});

// Toggle voting status
export const toggleVoting = mutation({
  args: { username: v.string() },
  handler: async (ctx, args): Promise<MutationResponse<{
    votingStarted: boolean;
    message: string;
  }>> => {
    // Authenticate admin user
    await requireAdmin(ctx, args.username, true); // Use ConvexError for admin mutations
    // Get current voting status
    const votingSetting = await getSettingByKey(ctx, "votingStarted");

    const currentStatus = votingSetting?.value || false;
    const newStatus = !currentStatus;

    // Update or create setting using standardized helper
    await updateOrCreateSetting(ctx, "votingStarted", newStatus);

    // Reset all teams voting to false when starting or stopping voting
    const activeEvent = await getActiveEventOrNull(ctx);

    if (activeEvent) {
      // Reset all teams voting status to false using batch update
      const teams = await getTeamsByEvent(ctx, activeEvent._id);
      await batchUpdateTeamVotingStatus(ctx, teams, false);

      // If starting voting, enable voting in active session
      if (newStatus) {
        const activeSession = await getActiveSessionForActiveEvent(ctx);

        if (activeSession) {
          // Use batch update helper for consistent session updating
          await batchUpdateSessions(ctx, [{
            id: activeSession._id,
            updateData: {
              settings: {
                ...activeSession.settings,
                votingEnabled: true,
              },
              updatedAt: Date.now(),
            }
          }]);
        }
      }
    }

    return createSuccessResponse({
      votingStarted: newStatus,
      message: newStatus ? "Voting started!" : "Voting stopped!"
    });
  },
});

// Get teams with their voting status
export const getTeamsVotingStatus = query({
  args: {},
  handler: async (ctx): Promise<QueryResponse<Array<{
    _id: string;
    name: string;
    active: boolean;
    hasSubmitted: boolean;
    createdAt: number;
  }>>> => {
    // Get active event
    const activeEvent = await getActiveEventOrNull(ctx);

    if (!activeEvent) {
      return [];
    }

    // Get all teams for active event and active session in parallel
    const [teams, activeSession] = await Promise.all([
      getTeamsByEvent(ctx, activeEvent._id),
      getActiveSessionForActiveEvent(ctx)
    ]);

    const finishedTeamIds = activeSession?.finishedTeams || [];

    // Add status information to teams (default active to true)
    const teamsWithStatus = teams.map((team: any) => ({
      _id: team._id,
      name: team.name,
      active: team.active ?? true,
      hasSubmitted: finishedTeamIds.includes(team._id),
      createdAt: team.createdAt,
    }));

    // Sort by active status first, then by name
    teamsWithStatus.sort((a: any, b: any) => {
      if (a.active !== b.active) {
        return a.active ? -1 : 1; // Active teams first
      }
      return a.name.localeCompare(b.name);
    });

    return teamsWithStatus;
  },
});

// Set team voting status
export const setTeamVoting = mutation({
  args: {
    username: v.string(),
    teamId: v.optional(v.id("teams")), // null for "none", undefined for "all"
    mode: v.union(v.literal("none"), v.literal("all"), v.literal("specific")),
  },
  handler: async (ctx, args): Promise<MutationResponse<{}>> => {
    // Authenticate admin user
    await requireAdmin(ctx, args.username, true); // Use ConvexError for admin mutations
    // Get active event
    const activeEvent = await getActiveEvent(ctx);

    // Get all active teams for this event using optimized helper
    const teams = await getActiveTeamsByEvent(ctx, activeEvent._id);

    // Reset all teams voting to false using batch update
    await batchUpdateTeamVotingStatus(ctx, teams, false);

    // Set voting based on mode
    if (args.mode === "all") {
      // Set all active teams to voting: true using batch update
      await batchUpdateTeamVotingStatus(ctx, teams, true);
    } else if (args.mode === "specific" && args.teamId) {
      // Set only specific team to voting: true (if it's active)
      const targetTeam = teams.find((team: any) => team._id === args.teamId);
      if (targetTeam) {
        await batchUpdateTeamVotingStatus(ctx, [targetTeam], true);
      }
    }
    // For "none" mode, all teams remain voting: false

    return createSuccessResponse({});
  },
});

// Get voting team status
export const getVotingTeamStatus = query({
  args: {},
  handler: async (ctx): Promise<QueryResponse<{
    mode: "none" | "all" | "specific";
    teams: Array<{
      _id: string;
      name: string;
      voting: boolean;
    }>;
  }>> => {
    // Get active event
    const activeEvent = await getActiveEventOrNull(ctx);

    if (!activeEvent) {
      return { mode: "none", teams: [] };
    }

    // Get all teams with voting status using optimized helper
    const allTeams = await getTeamsByEvent(ctx, activeEvent._id);
    const { mode, activeTeams } = calculateVotingMode(allTeams);

    return {
      mode,
      teams: activeTeams.map((team: any) => ({
        _id: team._id,
        name: team.name,
        voting: team.voting || false,
      })),
    };
  },
});
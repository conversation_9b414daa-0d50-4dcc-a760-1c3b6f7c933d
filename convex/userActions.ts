import { v } from "convex/values";
import { action } from "./_generated/server";
import { api } from "./_generated/api";
import bcrypt from "bcryptjs";
import { ActionResponse } from "./lib/responseTypes";
import { createActionSuccess, createActionError } from "./lib/responseUtils";

export const createUserWithTeamAction = action({
  args: {
    username: v.string(),
    name: v.string(),
    password: v.string(), // raw password - will be hashed
    role: v.union(v.literal("teamLead"), v.literal("teamMember")),
    teamId: v.optional(v.id("teams")),
  },
  handler: async (ctx, args): Promise<ActionResponse<{ userId: string }>> => {
    // Hash password in action (allows setTimeout)
    const hashedPassword = await bcrypt.hash(args.password, 10);
    
    try {
      // Call mutation with hashed password
      const result = await ctx.runMutation(api.users.createUserWithHashedPassword, {
        username: args.username,
        name: args.name,
        hashedPassword,
        role: args.role,
        teamId: args.teamId,
      });
      
      return createActionSuccess({ userId: result.data!.user.id });
    } catch (error: any) {
      return createActionError(error.message || "Failed to create user");
    }
  },
});

export const updateUserPasswordAction = action({
  args: {
    userId: v.id("users"),
    password: v.string(), // raw password - will be hashed
  },
  handler: async (ctx, args): Promise<ActionResponse<{ updated: boolean }>> => {
    // Hash password in action (allows setTimeout)
    const hashedPassword = await bcrypt.hash(args.password, 10);
    
    try {
      // Call mutation with hashed password
      await ctx.runMutation(api.users.updateUserHashedPassword, {
        userId: args.userId,
        hashedPassword,
      });
      
      return createActionSuccess({ updated: true });
    } catch (error: any) {
      return createActionError(error.message || "Failed to update password");
    }
  },
});

export const updateUserProfileAction = action({
  args: {
    userId: v.id("users"),
    name: v.optional(v.string()),
    username: v.optional(v.string()),
    password: v.optional(v.string()), // raw password - will be hashed
  },
  handler: async (ctx, args): Promise<ActionResponse<{ updated: boolean }>> => {
    let hashedPassword: string | undefined;
    
    // Hash password if provided
    if (args.password !== undefined) {
      hashedPassword = await bcrypt.hash(args.password, 10);
    }
    
    try {
      // Call mutation with hashed password
      await ctx.runMutation(api.users.updateUserProfileWithHashedPassword, {
        userId: args.userId,
        name: args.name,
        username: args.username,
        hashedPassword,
      });
      
      return createActionSuccess({ updated: true });
    } catch (error: any) {
      return createActionError(error.message || "Failed to update profile");
    }
  },
});

// Admin Management Actions
export const createAdminUserAction = action({
  args: {
    username: v.string(),
    password: v.string(), // raw password - will be hashed
  },
  handler: async (ctx, args): Promise<ActionResponse<{ userId: string }>> => {
    // Hash password in action (allows setTimeout)
    const hashedPassword = await bcrypt.hash(args.password, 10);
    
    try {
      // Call mutation with hashed password
      const result = await ctx.runMutation(api.users.createAdminUserWithHashedPassword, {
        username: args.username,
        hashedPassword,
      });
      
      return createActionSuccess({ userId: result.data!.id });
    } catch (error: any) {
      return createActionError(error.message || "Failed to create admin user");
    }
  },
});

export const updateAdminPasswordAction = action({
  args: {
    adminId: v.id("users"),
    password: v.string(), // raw password - will be hashed
  },
  handler: async (ctx, args): Promise<ActionResponse<{ updated: boolean }>> => {
    // Hash password in action (allows setTimeout)
    const hashedPassword = await bcrypt.hash(args.password, 10);
    
    try {
      // Call mutation with hashed password
      await ctx.runMutation(api.users.updateAdminPasswordWithHashedPassword, {
        adminId: args.adminId,
        hashedPassword,
      });
      
      return createActionSuccess({ updated: true });
    } catch (error: any) {
      return createActionError(error.message || "Failed to update admin password");
    }
  },
});
import { defineSchema, defineTable } from "convex/server";
import { v } from "convex/values";

export default defineSchema({
  users: defineTable({
    username: v.string(),
    name: v.string(),
    password: v.string(), // bcrypt hashed
    role: v.union(v.literal("admin"), v.literal("teamLead"), v.literal("teamMember")),
    status: v.union(v.literal("pending"), v.literal("approved"), v.literal("rejected")),
    events: v.array(v.object({
      eventId: v.id("events"),
      teamId: v.optional(v.id("teams"))
    })),
    autosave: v.optional(v.boolean()), // Enable/disable autosave for forms (backward compatible)
    createdAt: v.number(),
    updatedAt: v.optional(v.number()),
  }).index("by_username", ["username"])
    .index("by_status", ["status"])
    .index("by_role", ["role"])
    .index("by_status_role", ["status", "role"])
    .index("by_role_status", ["role", "status"]) // Additional compound index for analytics
    .index("by_created_at", ["createdAt"]), // For user creation analytics

  events: defineTable({
    name: v.string(),
    description: v.optional(v.string()),
    active: v.boolean(),
    startDate: v.optional(v.number()),
    endDate: v.optional(v.number()),
    createdAt: v.number(),
    updatedAt: v.optional(v.number()),
  }).index("by_active", ["active"])
    .index("by_active_created", ["active", "createdAt"]),

  teams: defineTable({
    name: v.string(),
    eventId: v.id("events"),
    active: v.optional(v.boolean()), // Teams are active by default, optional for existing teams
    voting: v.optional(v.boolean()), // Whether this team is selected for voting
    createdAt: v.number(),
    updatedAt: v.optional(v.number()),
  }).index("by_event", ["eventId"])
    .index("by_active", ["active"])
    .index("by_voting", ["voting"])
    .index("by_event_active", ["eventId", "active"]),

  ideas: defineTable({
    name: v.string(),
    description: v.optional(v.string()),
    presenters: v.array(v.object({
      id: v.string(),
      name: v.string(),
    })),
    userId: v.id("users"),
    teamId: v.id("teams"),
    sessionId: v.id("sessions"),
    eventId: v.id("events"), // Add eventId for easier querying
    submitted: v.boolean(),
    submittedAt: v.optional(v.number()),
    createdAt: v.number(),
    updatedAt: v.optional(v.number()),
  }).index("by_user", ["userId"])
    .index("by_team", ["teamId"])
    .index("by_session", ["sessionId"])
    .index("by_event", ["eventId"])
    .index("by_session_submitted", ["sessionId", "submitted"])
    .index("by_event_submitted", ["eventId", "submitted"])
    .index("by_team_session", ["teamId", "sessionId"])
    .index("by_user_session", ["userId", "sessionId"]),

  sessions: defineTable({
    name: v.string(),
    type: v.string(),
    eventId: v.id("events"),
    day: v.optional(v.number()),
    active: v.boolean(),
    startTime: v.optional(v.number()),
    endTime: v.optional(v.number()),
    finishedTeams: v.optional(v.array(v.string())),
    settings: v.optional(v.object({
      votingEnabled: v.optional(v.boolean()),
      submissionEnabled: v.optional(v.boolean()),
    })),
    sparkId: v.optional(v.id("sparks")),
    createdAt: v.number(),
    updatedAt: v.optional(v.number()),
  }).index("by_event", ["eventId"])
    .index("by_active", ["active"])
    .index("by_spark", ["sparkId"])
    .index("by_event_type", ["eventId", "type"])
    .index("by_event_active", ["eventId", "active"]),

  votes: defineTable({
    userId: v.id("users"),
    ideaId: v.id("ideas"),
    eventId: v.id("events"),
    sessionId: v.optional(v.id("sessions")),
    score: v.number(), // vote score (0-10)
    comment: v.optional(v.string()), // optional comment
    createdAt: v.number(),
    updatedAt: v.optional(v.number()),
  }).index("by_idea", ["ideaId"])
    .index("by_user", ["userId"])
    .index("by_event", ["eventId"])
    .index("by_session", ["sessionId"])
    .index("by_user_idea", ["userId", "ideaId"])
    .index("by_event_user", ["eventId", "userId"])
    .index("by_session_user", ["sessionId", "userId"])
    .index("by_idea_created", ["ideaId", "createdAt"]) // For vote ordering
    .index("by_event_created", ["eventId", "createdAt"]), // For analytics


  quickfires: defineTable({
    idea: v.string(),
    comments: v.optional(v.string()),
    question: v.optional(v.string()),
    eventId: v.id("events"),
    sessionId: v.id("sessions"),
    votingActive: v.boolean(),
    order: v.optional(v.number()), // Display order within session
    createdAt: v.number(),
    updatedAt: v.optional(v.number()),
  }).index("by_event", ["eventId"])
    .index("by_session", ["sessionId"])
    .index("by_voting_active", ["votingActive"])
    .index("by_session_order", ["sessionId", "order"])
    .index("by_session_voting", ["sessionId", "votingActive"])
    .index("by_event_session", ["eventId", "sessionId"]),

  quickfireVotes: defineTable({
    userId: v.id("users"),
    quickfireId: v.id("quickfires"),
    score: v.number(),
    voter: v.string(), // username for compatibility
    createdAt: v.number(),
    updatedAt: v.optional(v.number()),
  }).index("by_quickfire", ["quickfireId"])
    .index("by_user", ["userId"])
    .index("by_user_quickfire", ["userId", "quickfireId"])
    .index("by_quickfire_created", ["quickfireId", "createdAt"]), // For vote ordering


  snippets: defineTable({
    name: v.string(),
    content: v.string(),
    createdAt: v.number(),
    updatedAt: v.optional(v.number()),
  }).index("by_name", ["name"]),

  settings: defineTable({
    key: v.string(),
    value: v.any(),
    updatedAt: v.number(),
  }).index("by_key", ["key"]),

  presence: defineTable({
    userId: v.id("users"),
    sessionId: v.string(), // Browser session ID
    lastSeen: v.number(), // Timestamp of last heartbeat
    isOnline: v.boolean(),
    createdAt: v.number(),
    updatedAt: v.number(),
  }).index("by_user", ["userId"])
    .index("by_session", ["sessionId"])
    .index("by_online", ["isOnline"])
    .index("by_last_seen", ["lastSeen"])
    .index("by_user_session", ["userId", "sessionId"]),

  backups: defineTable({
    id: v.string(), // Unique backup identifier
    timestamp: v.number(), // Backup creation timestamp
    description: v.optional(v.string()), // User-provided description
    size: v.number(), // File size in bytes
    status: v.union(v.literal("completed"), v.literal("failed"), v.literal("creating")),
    createdBy: v.string(), // Username who created the backup
    fileName: v.string(), // Original file name
    fileId: v.optional(v.id("_storage")), // Convex file storage ID
    includeStorage: v.boolean(), // Whether file storage is included
    tablesIncluded: v.array(v.string()), // List of tables included in backup
    createdAt: v.number(),
    updatedAt: v.optional(v.number()),
  }).index("by_status", ["status"])
    .index("by_timestamp", ["timestamp"])
    .index("by_created_by", ["createdBy"]),

  sparks: defineTable({
    name: v.string(),
    description: v.optional(v.string()),
    color: v.optional(v.string()), // Hex color code (e.g., "#FF5733")
    eventId: v.id("events"), // Event-specific sparks
    fields: v.array(v.object({
      type: v.union(
        v.literal("text"),
        v.literal("richtext"), 
        v.literal("dropdown"),
        v.literal("radio"),
        v.literal("checkbox")
      ),
      label: v.string(),
      placeholder: v.optional(v.string()), // For text and richtext fields
      options: v.optional(v.array(v.string())), // For dropdown and radio fields
      required: v.optional(v.boolean()),
    })),
    createdAt: v.number(),
    updatedAt: v.number(),
  }).index("by_name", ["name"])
    .index("by_event", ["eventId"])
    .index("by_event_name", ["eventId", "name"])
    .index("by_created", ["createdAt"]),

  sparkSubmissions: defineTable({
    sessionId: v.optional(v.id("sessions")),
    userId: v.id("users"),
    sparkId: v.id("sparks"),
    teamId: v.id("teams"),
    eventId: v.id("events"),
    data: v.any(), // JSON object with field values
    submittedAt: v.number(),
    updatedAt: v.optional(v.number()),
  }).index("by_session", ["sessionId"])
    .index("by_user", ["userId"])
    .index("by_spark", ["sparkId"])
    .index("by_team", ["teamId"])
    .index("by_event", ["eventId"])
    .index("by_user_session", ["userId", "sessionId"])
    .index("by_team_session", ["teamId", "sessionId"])
    .index("by_event_submitted", ["eventId", "submittedAt"]) // For analytics
    .index("by_team_submitted", ["teamId", "submittedAt"]), // For team analytics
});
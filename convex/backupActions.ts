"use node";

import { action } from "./_generated/server";
import { v } from "convex/values";
import { Id } from "./_generated/dataModel";
import { ConvexError } from "convex/values";
import { api } from "./_generated/api";

// =====================================
// BACKWARD COMPATIBLE TYPES
// =====================================

/**
 * Legacy response format for backup actions to maintain frontend compatibility
 * This maintains the flat response structure expected by existing frontend code
 */
type LegacyBackupResponse<T> = {
  success: boolean;
  error?: string;
  message?: string;
} & T;

/**
 * Create a legacy-compatible success response
 */
function createLegacySuccess<T>(data: T, message?: string): LegacyBackupResponse<T> {
  return {
    success: true,
    message,
    ...data
  };
}

/**
 * Create a legacy-compatible error response
 */
function createLegacyError(error: string, message?: string): LegacyBackupResponse<{}> {
  return {
    success: false,
    error,
    message
  };
}

// =====================================
// ACTIONS (Storage Operations)
// =====================================

/**
 * Upload backup file to Convex file storage
 */
export const uploadBackupAction = action({
  args: {
    fileName: v.string(),
    fileContent: v.any(), // Use v.any() to accept ArrayBuffer
    description: v.optional(v.string()),
    createdBy: v.string(),
  },
  handler: async (ctx, args): Promise<LegacyBackupResponse<{
    backupId?: Id<"backups">;
    fileId?: Id<"_storage">;
  }>> => {
    try {
      const timestamp = Date.now();
      const backupId = `upload_${timestamp}`;
      
      // Store file in Convex
      const fileId = await ctx.storage.store(new Blob([args.fileContent], { type: 'application/zip' }));
      
      // Create complete metadata record in single transaction
      const metadataId: Id<"backups"> = await ctx.runMutation(api.backupData.createBackupMetadataComplete, {
        id: backupId,
        description: args.description,
        fileName: args.fileName,
        createdBy: args.createdBy,
        includeStorage: false, // No file storage used, same as create backup
        tablesIncluded: ["all"], // All tables included, same as create backup
        size: args.fileContent.byteLength,
        fileId: fileId,
      });
      
      return createLegacySuccess(
        {
          backupId: metadataId, // Return Convex _id, not string timestamp
          fileId,
        },
        "Backup uploaded successfully"
      );
      
    } catch (error) {
      console.error('Backup upload failed:', error);
      const errorMessage = error instanceof ConvexError ? error.message : 
                          error instanceof Error ? error.message : 'Unknown error';
      return createLegacyError(
        errorMessage,
        "Backup upload failed"
      );
    }
  },
});

/**
 * Get download URL for backup file
 */
export const getBackupDownloadUrlAction = action({
  args: { fileName: v.string() },
  handler: async (ctx, args): Promise<LegacyBackupResponse<{
    downloadUrl?: string;
    fileName?: string;
  }>> => {
    try {
      // Find backup by filename using query
      const backups: any[] = await ctx.runQuery(api.backupData.getBackupsList);
      const backup: any = backups.find((b: any) => b.fileName === args.fileName);
      
      if (!backup || !backup.fileId) {
        return createLegacyError(
          "Backup file not found",
          "Backup download failed"
        );
      }
      
      // Generate download URL from Convex storage
      const downloadUrl = await ctx.storage.getUrl(backup.fileId as Id<"_storage">);
      
      if (!downloadUrl) {
        return createLegacyError(
          "Could not generate download URL",
          "Backup download failed"
        );
      }
      
      return createLegacySuccess(
        {
          downloadUrl,
          fileName: args.fileName,
        },
        "Download URL generated successfully"
      );
      
    } catch (error) {
      console.error('Backup download URL generation failed:', error);
      const errorMessage = error instanceof ConvexError ? error.message : 
                          error instanceof Error ? error.message : 'Unknown error';
      return createLegacyError(
        errorMessage,
        "Backup download failed"
      );
    }
  },
});

/**
 * Delete backup from Convex file storage and metadata
 */
export const deleteBackupAction = action({
  args: {
    backupId: v.id("backups"),
    fileName: v.string(),
  },
  handler: async (ctx, args): Promise<LegacyBackupResponse<{}>> => {
    try {
      // Get backup metadata to find file ID using query
      const backup = await ctx.runQuery(api.backupData.getBackupDetails, {
        backupId: args.backupId,
      });
      
      if (!backup) {
        return createLegacyError(
          "Backup not found",
          "Backup deletion failed"
        );
      }
      
      // Delete file from Convex storage if it exists
      if (backup.fileId) {
        try {
          await ctx.storage.delete(backup.fileId as Id<"_storage">);
        } catch (storageError) {
          // File doesn't exist, ignore error
        }
      }
      
      // Always delete metadata using mutation
      await ctx.runMutation(api.backupData.deleteBackupMetadata, {
        backupId: args.backupId,
      });
      
      return createLegacySuccess(
        {},
        "Backup deleted successfully"
      );
      
    } catch (error) {
      console.error('Backup deletion failed:', error);
      const errorMessage = error instanceof ConvexError ? error.message : 
                          error instanceof Error ? error.message : 'Unknown error';
      return createLegacyError(
        errorMessage,
        "Backup deletion failed"
      );
    }
  },
});
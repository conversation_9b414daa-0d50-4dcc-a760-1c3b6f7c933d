/**
 * Backup Helper Functions
 * 
 * This module provides centralized backup operations and batch loading functions
 * to maintain consistency with the established helper patterns.
 */

import { ConvexError } from "convex/values";
import { Id } from "../_generated/dataModel";
import { ErrorCode } from "./responseTypes";
import { throwConvexError } from "./responseUtils";

/**
 * Retrieves a backup by ID with proper error handling
 * @param ctx - Convex context object
 * @param backupId - Backup ID to look up
 * @returns Backup object or null if not found
 */
export async function getBackupById(ctx: any, backupId: Id<"backups">) {
  return await ctx.db.get(backupId);
}

/**
 * Retrieves a backup by ID and throws ConvexError if not found
 * @param ctx - Convex context object
 * @param backupId - Backup ID to look up
 * @returns Backup object
 * @throws ConvexError if backup not found
 */
export async function getBackupByIdOrThrow(ctx: any, backupId: Id<"backups">) {
  const backup = await getBackupById(ctx, backupId);
  if (!backup) {
    throwConvexError(
      "Backup not found",
      ErrorCode.RESOURCE_NOT_FOUND
    );
  }
  return backup;
}

/**
 * Retrieves a backup by filename with proper error handling
 * @param ctx - Convex context object
 * @param fileName - Filename to look up
 * @returns Backup object or null if not found
 */
export async function getBackupByFileName(ctx: any, fileName: string) {
  const backups = await ctx.db.query("backups").collect();
  return backups.find((backup: any) => backup.fileName === fileName) || null;
}

/**
 * Retrieves a backup by filename and throws ConvexError if not found
 * @param ctx - Convex context object
 * @param fileName - Filename to look up
 * @returns Backup object
 * @throws ConvexError if backup not found
 */
export async function getBackupByFileNameOrThrow(ctx: any, fileName: string) {
  const backup = await getBackupByFileName(ctx, fileName);
  if (!backup) {
    throwConvexError(
      "Backup file not found",
      ErrorCode.RESOURCE_NOT_FOUND
    );
  }
  return backup;
}

/**
 * Retrieves all backups with proper ordering
 * @param ctx - Convex context object
 * @returns Array of backup objects ordered by creation time (newest first)
 */
export async function getAllBackups(ctx: any) {
  const backups = await ctx.db
    .query("backups")
    .order("desc")
    .collect();
  
  // Sort by createdAt timestamp for consistent ordering
  return backups.sort((a: any, b: any) => (b.createdAt || 0) - (a.createdAt || 0));
}

/**
 * Creates backup metadata with proper error handling
 * @param ctx - Convex context object
 * @param args - Backup metadata arguments
 * @returns Created backup ID
 * @throws ConvexError if creation fails
 */
export async function createBackupMetadata(ctx: any, args: {
  id: string;
  description?: string;
  fileName: string;
  createdBy: string;
  includeStorage: boolean;
  tablesIncluded: string[];
}) {
  try {
    const now = Date.now();
    
    const backupId = await ctx.db.insert("backups", {
      id: args.id,
      timestamp: now,
      description: args.description,
      size: 0, // Will be updated after file creation
      status: "creating",
      createdBy: args.createdBy,
      fileName: args.fileName,
      includeStorage: args.includeStorage,
      tablesIncluded: args.tablesIncluded,
      createdAt: now,
    });
    
    return backupId;
  } catch (error) {
    throwConvexError(
      "Failed to create backup metadata",
      ErrorCode.DATABASE_ERROR
    );
  }
}

/**
 * Updates backup metadata with proper error handling
 * @param ctx - Convex context object
 * @param backupId - Backup ID to update
 * @param updateData - Data to update
 * @throws ConvexError if update fails
 */
export async function updateBackupMetadata(ctx: any, backupId: Id<"backups">, updateData: {
  size?: number;
  status?: "completed" | "failed" | "creating";
  fileId?: Id<"_storage">;
  fileName?: string;
}) {
  try {
    // Check if backup exists
    await getBackupByIdOrThrow(ctx, backupId);

    const update: any = {
      updatedAt: Date.now(),
    };
    
    if (updateData.size !== undefined) update.size = updateData.size;
    if (updateData.status !== undefined) update.status = updateData.status;
    if (updateData.fileId !== undefined) update.fileId = updateData.fileId;
    if (updateData.fileName !== undefined) update.fileName = updateData.fileName;
    
    await ctx.db.patch(backupId, update);
  } catch (error) {
    if (error instanceof ConvexError) {
      throw error;
    }
    throwConvexError(
      "Failed to update backup metadata",
      ErrorCode.DATABASE_ERROR
    );
  }
}

/**
 * Deletes backup metadata with proper error handling
 * @param ctx - Convex context object
 * @param backupId - Backup ID to delete
 * @throws ConvexError if deletion fails
 */
export async function deleteBackupMetadata(ctx: any, backupId: Id<"backups">) {
  try {
    // Check if backup exists
    await getBackupByIdOrThrow(ctx, backupId);
    
    await ctx.db.delete(backupId);
  } catch (error) {
    if (error instanceof ConvexError) {
      throw error;
    }
    throwConvexError(
      "Failed to delete backup metadata",
      ErrorCode.DATABASE_ERROR
    );
  }
}

/**
 * Batch load multiple backups by their IDs
 * @param ctx - Convex context object
 * @param backupIds - Array of backup IDs to load
 * @returns Map of backup ID to backup object (null if not found)
 */
export async function batchLoadBackups(ctx: any, backupIds: Id<"backups">[]) {
  const uniqueIds = Array.from(new Set(backupIds));
  const backups = await Promise.all(
    uniqueIds.map(id => ctx.db.get(id))
  );
  
  const backupMap = new Map<Id<"backups">, any>();
  uniqueIds.forEach((id, index) => {
    backupMap.set(id, backups[index]);
  });
  
  return backupMap;
}

/**
 * Enrich backup objects with additional metadata
 * @param ctx - Convex context object
 * @param backups - Array of backup objects
 * @returns Array of enriched backup objects
 */
export async function enrichBackups(_ctx: any, backups: any[]) {
  if (backups.length === 0) {
    return [];
  }
  
  // Note: Since createdBy is a username string, we'd need to implement user lookup by username
  // For now, just return the backups as-is with the username already present
  return backups.map(backup => ({
    ...backup,
    createdByUsername: backup.createdBy,
    sizeFormatted: formatFileSize(backup.size)
  }));
}

/**
 * Format file size in human-readable format
 * @param bytes - File size in bytes
 * @returns Formatted string (e.g., "1.2 MB")
 */
function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 Bytes';
  
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}
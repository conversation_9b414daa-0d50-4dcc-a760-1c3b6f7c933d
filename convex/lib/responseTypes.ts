/**
 * Standardized response types for LionX Convex functions
 * 
 * This file defines consistent return type patterns across all mutations, queries, and actions
 * to improve type safety, consistency, and developer experience.
 */

import { Id } from "../_generated/dataModel";

// =====================================
// CORE RESPONSE TYPES
// =====================================

/**
 * Standard response structure for mutations (create, update, delete operations)
 */
export type MutationResponse<T = any> = {
  success: boolean;
  data?: T;
  error?: string;
  code?: string;
};

/**
 * Standard response structure for queries (get, list operations)
 * Queries return data directly or null/undefined for not found
 */
export type QueryResponse<T> = T;

/**
 * Standard response structure for actions (async operations)
 */
export type ActionResponse<T = any> = {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
};

// =====================================
// SPECIALIZED RESPONSE TYPES
// =====================================

/**
 * Response for create operations that return an ID
 */
export type CreateResponse<TableName extends keyof import("../_generated/dataModel").DataModel> = MutationResponse<{
  id: Id<TableName>;
}>;

/**
 * Response for update operations
 */
export type UpdateResponse = MutationResponse<{
  updated: boolean;
}>;

/**
 * Response for delete operations
 */
export type DeleteResponse = MutationResponse<{
  deleted: boolean;
}>;

/**
 * Response for bulk operations (bulk create, update, delete)
 */
export type BulkResponse = MutationResponse<{
  count: number;
}>;

/**
 * Response for validation operations
 */
export type ValidationResponse = MutationResponse<{
  valid: boolean;
  message?: string;
}>;

// =====================================
// DOMAIN-SPECIFIC RESPONSE TYPES
// =====================================

/**
 * Response for authentication operations
 */
export type AuthResponse = MutationResponse<{
  isAuthenticated: boolean;
  user?: any;
  role?: string;
}>;

/**
 * Response for export operations
 */
export type ExportResponse = ActionResponse<{
  content: string;
  filename: string;
  size?: number;
}>;

/**
 * Response for backup operations
 */
export type BackupResponse = ActionResponse<{
  backupId?: string;
  fileId?: string;
  size?: number;
}>;

/**
 * Response for voting operations
 */
export type VotingResponse = MutationResponse<{
  voteId?: string;
  score?: number;
  canVote?: boolean;
}>;

/**
 * Response for team operations
 */
export type TeamResponse = MutationResponse<{
  teamId?: string;
  teamName?: string;
  memberCount?: number;
}>;

/**
 * Response for session operations
 */
export type SessionResponse = MutationResponse<{
  sessionId?: string;
  sessionName?: string;
  isActive?: boolean;
  canParticipate?: boolean;
}>;

// =====================================
// ERROR RESPONSE TYPES
// =====================================

/**
 * Standard error codes used across the application
 */
export enum ErrorCode {
  // Authentication errors
  UNAUTHORIZED = "UNAUTHORIZED",
  FORBIDDEN = "FORBIDDEN",
  USER_NOT_FOUND = "USER_NOT_FOUND",
  INVALID_CREDENTIALS = "INVALID_CREDENTIALS",
  
  // Validation errors
  VALIDATION_ERROR = "VALIDATION_ERROR",
  REQUIRED_FIELD_MISSING = "REQUIRED_FIELD_MISSING",
  INVALID_FORMAT = "INVALID_FORMAT",
  DUPLICATE_ENTRY = "DUPLICATE_ENTRY",
  
  // Resource errors
  RESOURCE_NOT_FOUND = "RESOURCE_NOT_FOUND",
  RESOURCE_ALREADY_EXISTS = "RESOURCE_ALREADY_EXISTS",
  RESOURCE_LOCKED = "RESOURCE_LOCKED",
  
  // Business logic errors
  OPERATION_NOT_ALLOWED = "OPERATION_NOT_ALLOWED",
  INSUFFICIENT_PERMISSIONS = "INSUFFICIENT_PERMISSIONS",
  INVALID_STATE = "INVALID_STATE",
  
  // System errors
  INTERNAL_ERROR = "INTERNAL_ERROR",
  DATABASE_ERROR = "DATABASE_ERROR",
  NETWORK_ERROR = "NETWORK_ERROR",
}

/**
 * Error response structure
 */
export type ErrorResponse = {
  success: false;
  error: string;
  code: ErrorCode;
  details?: any;
};

// =====================================
// HELPER TYPES
// =====================================

/**
 * Generic success response
 */
export type SuccessResponse<T = any> = {
  success: true;
  data: T;
};

/**
 * Union type for all possible responses
 */
export type ApiResponse<T = any> = SuccessResponse<T> | ErrorResponse;

/**
 * Type guard to check if response is successful
 */
export function isSuccessResponse<T>(response: ApiResponse<T>): response is SuccessResponse<T> {
  return response.success === true;
}

/**
 * Type guard to check if response is an error
 */
export function isErrorResponse<T>(response: ApiResponse<T>): response is ErrorResponse {
  return response.success === false;
}
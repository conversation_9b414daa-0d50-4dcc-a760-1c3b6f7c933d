/**
 * Shared Settings Database Query Utilities
 * 
 * This module provides centralized settings lookup functions
 * to eliminate code duplication and improve performance across the codebase.
 */

/**
 * Retrieves a setting by key
 * @param ctx - Convex context object
 * @param key - Setting key to look up
 * @returns Setting object or null if not found
 */
export async function getSettingByKey(ctx: any, key: string) {
  return await ctx.db
    .query("settings")
    .withIndex("by_key", (q: any) => q.eq("key", key))
    .first();
}

/**
 * Retrieves a setting value by key with default fallback
 * @param ctx - Convex context object
 * @param key - Setting key to look up
 * @param defaultValue - Default value to return if setting doesn't exist
 * @returns Setting value or default value
 */
export async function getSettingValue(ctx: any, key: string, defaultValue: any = null) {
  const setting = await getSettingByKey(ctx, key);
  return setting?.value ?? defaultValue;
}

/**
 * Retrieves the voting status setting
 * @param ctx - Convex context object
 * @returns Boolean indicating if voting is started
 */
export async function getVotingStatus(ctx: any): Promise<boolean> {
  return await getSettingValue(ctx, "votingStarted", false);
}

/**
 * Retrieves the auto approval setting
 * @param ctx - Convex context object
 * @returns Boolean indicating if auto approval is enabled
 */
export async function getAutoApprovalStatus(ctx: any): Promise<boolean> {
  return await getSettingValue(ctx, "autoApproval", false);
}

/**
 * Updates or creates a setting
 * @param ctx - Convex context object
 * @param key - Setting key to update/create
 * @param value - New value for the setting
 * @returns Setting ID (existing or newly created)
 */
export async function updateOrCreateSetting(ctx: any, key: string, value: any) {
  const existingSetting = await getSettingByKey(ctx, key);
  
  if (existingSetting) {
    // Update existing setting
    await ctx.db.patch(existingSetting._id, {
      value,
      updatedAt: Date.now(),
    });
    return existingSetting._id;
  } else {
    // Create new setting
    return await ctx.db.insert("settings", {
      key,
      value,
      updatedAt: Date.now(),
    });
  }
}

/**
 * Batch retrieves multiple settings by their keys
 * @param ctx - Convex context object
 * @param keys - Array of setting keys to retrieve
 * @returns Map of key to setting value (null if not found)
 */
export async function batchGetSettings(ctx: any, keys: string[]) {
  const settings = await Promise.all(
    keys.map(key => getSettingByKey(ctx, key))
  );
  
  const settingsMap = new Map();
  keys.forEach((key, index) => {
    settingsMap.set(key, settings[index]?.value ?? null);
  });
  
  return settingsMap;
}

/**
 * Retrieves all settings as a key-value map
 * @param ctx - Convex context object
 * @returns Map of setting keys to values
 */
export async function getAllSettings(ctx: any) {
  const settings = await ctx.db.query("settings").collect();
  const settingsMap = new Map();
  
  settings.forEach((setting: any) => {
    settingsMap.set(setting.key, setting.value);
  });
  
  return settingsMap;
}

/**
 * Checks if a setting exists
 * @param ctx - Convex context object
 * @param key - Setting key to check
 * @returns Boolean indicating if setting exists
 */
export async function settingExists(ctx: any, key: string): Promise<boolean> {
  const setting = await getSettingByKey(ctx, key);
  return setting !== null;
}

/**
 * Deletes a setting by key
 * @param ctx - Convex context object
 * @param key - Setting key to delete
 * @returns Boolean indicating if setting was deleted
 */
export async function deleteSetting(ctx: any, key: string): Promise<boolean> {
  const setting = await getSettingByKey(ctx, key);
  
  if (setting) {
    await ctx.db.delete(setting._id);
    return true;
  }
  
  return false;
}

/**
 * Retrieves the online users feature status
 * @param ctx - Convex context object
 * @returns Boolean indicating if online users feature is enabled
 */
export async function getOnlineUsersStatus(ctx: any): Promise<boolean> {
  return await getSettingValue(ctx, "online-users-enabled", false);
}

/**
 * Gets application configuration settings
 * @param ctx - Convex context object
 * @returns Object with common application settings
 */
export async function getAppConfig(ctx: any) {
  const configKeys = ["votingStarted", "autoApproval", "maxTeamSize", "votingTimeLimit", "online-users-enabled"];
  const configMap = await batchGetSettings(ctx, configKeys);
  
  return {
    votingStarted: configMap.get("votingStarted") ?? false,
    autoApproval: configMap.get("autoApproval") ?? false,
    maxTeamSize: configMap.get("maxTeamSize") ?? 10,
    votingTimeLimit: configMap.get("votingTimeLimit") ?? 300, // 5 minutes default
    onlineUsersEnabled: configMap.get("online-users-enabled") ?? false
  };
}
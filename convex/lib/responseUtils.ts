/**
 * Utility functions for creating standardized responses
 * 
 * This file provides helper functions to create consistent response objects
 * across all Convex mutations, queries, and actions.
 */

import { ConvexError } from "convex/values";
import { 
  MutationResponse, 
  ActionResponse, 
  CreateResponse, 
  UpdateResponse, 
  DeleteResponse, 
  BulkResponse, 
  ValidationResponse,
  AuthResponse,
  ExportResponse,
  BackupResponse,
  VotingResponse,
  TeamResponse,
  SessionResponse,
  ErrorCode,
  ErrorResponse,
  SuccessResponse,
  ApiResponse
} from "./responseTypes";
import { Id } from "../_generated/dataModel";

// =====================================
// MUTATION RESPONSE UTILITIES
// =====================================

/**
 * Create a successful mutation response
 */
export function createSuccessResponse<T>(data: T): MutationResponse<T> {
  return {
    success: true,
    data
  };
}

/**
 * Create an error mutation response
 */
export function createErrorResponse(error: string, code?: string): MutationResponse<never> {
  return {
    success: false,
    error,
    code
  };
}

/**
 * Create a successful create response
 */
export function createCreateResponse<TableName extends keyof import("../_generated/dataModel").DataModel>(
  id: Id<TableName>
): CreateResponse<TableName> {
  return {
    success: true,
    data: { id }
  };
}

/**
 * Create a successful update response
 */
export function createUpdateResponse(updated: boolean = true): UpdateResponse {
  return {
    success: true,
    data: { updated }
  };
}

/**
 * Create a successful delete response
 */
export function createDeleteResponse(deleted: boolean = true): DeleteResponse {
  return {
    success: true,
    data: { deleted }
  };
}

/**
 * Create a successful bulk operation response
 */
export function createBulkResponse(count: number): BulkResponse {
  return {
    success: true,
    data: { count }
  };
}

/**
 * Create a validation response
 */
export function createValidationResponse(valid: boolean, message?: string): ValidationResponse {
  return {
    success: true,
    data: { valid, message }
  };
}

// =====================================
// ACTION RESPONSE UTILITIES
// =====================================

/**
 * Create a successful action response
 */
export function createActionSuccess<T>(data: T, message?: string): ActionResponse<T> {
  return {
    success: true,
    data,
    message
  };
}

/**
 * Create an error action response
 */
export function createActionError(error: string, message?: string): ActionResponse<never> {
  return {
    success: false,
    error,
    message
  };
}

/**
 * Create an export response
 */
export function createExportResponse(content: string, filename: string, size?: number): ExportResponse {
  return {
    success: true,
    data: { content, filename, size }
  };
}

/**
 * Create a backup response
 */
export function createBackupResponse(backupId?: string, fileId?: string, size?: number): BackupResponse {
  return {
    success: true,
    data: { backupId, fileId, size }
  };
}

// =====================================
// DOMAIN-SPECIFIC RESPONSE UTILITIES
// =====================================

/**
 * Create an authentication response
 */
export function createAuthResponse(isAuthenticated: boolean, user?: any, role?: string): AuthResponse {
  return {
    success: true,
    data: { isAuthenticated, user, role }
  };
}

/**
 * Create a voting response
 */
export function createVotingResponse(voteId?: string, score?: number, canVote?: boolean): VotingResponse {
  return {
    success: true,
    data: { voteId, score, canVote }
  };
}

/**
 * Create a team response
 */
export function createTeamResponse(teamId?: string, teamName?: string, memberCount?: number): TeamResponse {
  return {
    success: true,
    data: { teamId, teamName, memberCount }
  };
}

/**
 * Create a session response
 */
export function createSessionResponse(
  sessionId?: string, 
  sessionName?: string, 
  isActive?: boolean, 
  canParticipate?: boolean
): SessionResponse {
  return {
    success: true,
    data: { sessionId, sessionName, isActive, canParticipate }
  };
}

// =====================================
// ERROR HANDLING UTILITIES
// =====================================

/**
 * Create a standardized error response
 */
export function createStandardError(message: string, code: ErrorCode, details?: any): ErrorResponse {
  return {
    success: false,
    error: message,
    code,
    details
  };
}

/**
 * Convert an error to a standardized response
 */
export function errorToResponse(error: any): ErrorResponse {
  if (error instanceof ConvexError) {
    return {
      success: false,
      error: error.message || "An error occurred",
      code: ErrorCode.INTERNAL_ERROR
    };
  }
  
  if (error instanceof Error) {
    return {
      success: false,
      error: error.message || "An error occurred",
      code: ErrorCode.INTERNAL_ERROR
    };
  }
  
  return {
    success: false,
    error: "An unknown error occurred",
    code: ErrorCode.INTERNAL_ERROR
  };
}

/**
 * Throw a ConvexError with standardized format
 */
export function throwConvexError(message: string, code?: ErrorCode): never {
  throw new ConvexError({
    message,
    code: code || ErrorCode.INTERNAL_ERROR
  });
}

/**
 * Throw a standard Error with standardized format
 */
export function throwStandardError(message: string, code?: ErrorCode): never {
  const error = new Error(message);
  (error as any).code = code || ErrorCode.INTERNAL_ERROR;
  throw error;
}

// =====================================
// RESPONSE VALIDATION UTILITIES
// =====================================

/**
 * Validate if a response is successful
 */
export function isSuccessful<T>(response: ApiResponse<T>): response is SuccessResponse<T> {
  return response.success === true;
}

/**
 * Validate if a response is an error
 */
export function isError<T>(response: ApiResponse<T>): response is ErrorResponse {
  return response.success === false;
}

/**
 * Extract data from a successful response or throw error
 */
export function extractData<T>(response: ApiResponse<T>): T {
  if (isSuccessful(response)) {
    return response.data;
  }
  
  throw new ConvexError({
    message: response.error,
    code: response.code
  });
}

/**
 * Extract error from an error response
 */
export function extractError<T>(response: ApiResponse<T>): ErrorResponse {
  if (isError(response)) {
    return response;
  }
  
  throw new Error("Response is not an error");
}

// =====================================
// LEGACY COMPATIBILITY UTILITIES
// =====================================

/**
 * Convert legacy response to standardized format
 */
export function convertLegacyResponse(legacyResponse: any): ApiResponse<any> {
  // Handle legacy success objects
  if (legacyResponse && typeof legacyResponse === 'object' && legacyResponse.success === true) {
    return {
      success: true,
      data: legacyResponse
    };
  }
  
  // Handle legacy error objects
  if (legacyResponse && typeof legacyResponse === 'object' && legacyResponse.error) {
    return {
      success: false,
      error: legacyResponse.error,
      code: legacyResponse.code || ErrorCode.INTERNAL_ERROR
    };
  }
  
  // Handle direct data return
  return {
    success: true,
    data: legacyResponse
  };
}

/**
 * Wrap a function to return standardized responses
 */
export function wrapWithStandardResponse<T extends (...args: any[]) => any>(
  fn: T
): (...args: Parameters<T>) => Promise<ApiResponse<ReturnType<T>>> {
  return async (...args: Parameters<T>) => {
    try {
      const result = await fn(...args);
      return {
        success: true,
        data: result
      };
    } catch (error) {
      return errorToResponse(error);
    }
  };
}
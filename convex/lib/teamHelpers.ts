/**
 * Shared Team Database Query Utilities
 * 
 * This module provides centralized team lookup and batch loading functions
 * to eliminate code duplication and improve performance across the codebase.
 */

import { getActiveEventOrNull } from "./eventHelpers";
import { batchLoadTeamMembers } from "./batchHelpers";

/**
 * Retrieves all teams across all events
 * @param ctx - Convex context object
 * @returns Array of all team objects
 */
export async function getAllTeams(ctx: any) {
  return await ctx.db.query("teams").collect();
}

/**
 * Retrieves all teams for a specific event
 * @param ctx - Convex context object
 * @param eventId - Event ID to get teams for
 * @returns Array of team objects
 */
export async function getTeamsByEvent(ctx: any, eventId: string) {
  return await ctx.db
    .query("teams")
    .withIndex("by_event", (q: any) => q.eq("eventId", eventId))
    .collect();
}

/**
 * Retrieves all teams for the active event
 * @param ctx - Convex context object
 * @returns Array of team objects for active event, or empty array if no active event
 */
export async function getTeamsByActiveEvent(ctx: any) {
  const activeEvent = await getActiveEventOrNull(ctx);
  
  if (!activeEvent) {
    return [];
  }
  
  return await getTeamsByEvent(ctx, activeEvent._id);
}

/**
 * Retrieves all active teams for a specific event
 * @param ctx - Convex context object
 * @param eventId - Event ID to get active teams for
 * @returns Array of active team objects (active field = true or undefined)
 */
export async function getActiveTeamsByEvent(ctx: any, eventId: string) {
  const teams = await getTeamsByEvent(ctx, eventId);
  return teams.filter((team: any) => team.active === true || team.active === undefined);
}

/**
 * Retrieves all active teams for the active event
 * @param ctx - Convex context object
 * @returns Array of active team objects for active event
 */
export async function getActiveTeamsByActiveEvent(ctx: any) {
  const activeEvent = await getActiveEventOrNull(ctx);
  
  if (!activeEvent) {
    return [];
  }
  
  return await getActiveTeamsByEvent(ctx, activeEvent._id);
}

/**
 * Retrieves a team by ID with null safety
 * @param ctx - Convex context object
 * @param teamId - Team ID to look up
 * @returns Team object or null if not found
 */
export async function getTeamById(ctx: any, teamId: string) {
  return await ctx.db.get(teamId);
}

/**
 * Retrieves a team by ID and throws error if not found
 * @param ctx - Convex context object
 * @param teamId - Team ID to look up
 * @returns Team object
 * @throws Error with message "Team not found" if team doesn't exist
 */
export async function getTeamByIdOrThrow(ctx: any, teamId: string) {
  const team = await getTeamById(ctx, teamId);
  if (!team) {
    throw new Error("Team not found");
  }
  return team;
}

/**
 * Batch loads multiple teams by their IDs to avoid N+1 queries
 * @param ctx - Convex context object
 * @param teamIds - Array of team IDs to load
 * @returns Map of team ID to team object (null if not found)
 */
export async function batchLoadTeams(ctx: any, teamIds: string[]) {
  const uniqueTeamIds = Array.from(new Set(teamIds));
  const teams = await Promise.all(
    uniqueTeamIds.map(id => ctx.db.get(id))
  );
  
  const teamMap = new Map();
  uniqueTeamIds.forEach((id, index) => {
    teamMap.set(id, teams[index]);
  });
  
  return teamMap;
}

/**
 * Gets teams with their user counts for a specific event
 * @param ctx - Convex context object
 * @param eventId - Event ID to get teams for
 * @returns Array of teams with user count information
 */
export async function getTeamsWithUserCount(ctx: any, eventId: string) {
  const teams = await getTeamsByEvent(ctx, eventId);
  
  // Get all users for this event
  const users = await ctx.db
    .query("users")
    .withIndex("by_status_role", (q: any) => q.eq("status", "approved"))
    .filter((q: any) => q.neq(q.field("role"), "admin"))
    .collect();
    
  const eventUsers = users.filter((user: any) => 
    user.events?.some((e: any) => e.eventId === eventId)
  );
  
  // Count users per team
  const teamUserCounts = new Map();
  eventUsers.forEach((user: any) => {
    const eventRegistration = user.events.find((e: any) => e.eventId === eventId);
    if (eventRegistration?.teamId) {
      const teamIdStr = eventRegistration.teamId.toString();
      teamUserCounts.set(teamIdStr, (teamUserCounts.get(teamIdStr) || 0) + 1);
    }
  });
  
  // Add user counts to teams
  return teams.map((team: any) => ({
    ...team,
    userCount: teamUserCounts.get(team._id.toString()) || 0
  }));
}

/**
 * Gets teams with their user counts for the active event
 * @param ctx - Convex context object
 * @returns Array of teams with user count information for active event
 */
export async function getActiveEventTeamsWithUserCount(ctx: any) {
  const activeEvent = await getActiveEventOrNull(ctx);
  
  if (!activeEvent) {
    return [];
  }
  
  return await getTeamsWithUserCount(ctx, activeEvent._id);
}

/**
 * Gets a team's users for a specific event
 * @param ctx - Convex context object
 * @param teamId - Team ID to get users for
 * @param eventId - Event ID to filter users by
 * @returns Array of user objects belonging to the team
 */
export async function getTeamUsers(ctx: any, teamId: string, eventId: string) {
  const users = await ctx.db
    .query("users")
    .withIndex("by_status_role", (q: any) => q.eq("status", "approved"))
    .filter((q: any) => q.neq(q.field("role"), "admin"))
    .collect();
    
  return users.filter((user: any) => {
    const eventRegistration = user.events?.find((e: any) => e.eventId === eventId);
    return eventRegistration?.teamId === teamId;
  });
}

/**
 * Validates team exists and belongs to active event
 * @param ctx - Convex context object
 * @param teamId - Team ID to validate
 * @returns Team object if valid
 * @throws Error if team not found or not in active event
 */
export async function validateTeamInActiveEvent(ctx: any, teamId: string) {
  const activeEvent = await getActiveEventOrNull(ctx);
  if (!activeEvent) {
    throw new Error("No active event found");
  }
  
  const team = await getTeamByIdOrThrow(ctx, teamId);
  
  if (team.eventId !== activeEvent._id) {
    throw new Error("Team does not belong to active event");
  }
  
  return team;
}

/**
 * Gets team leads for a specific team and event
 * @param ctx - Convex context object
 * @param teamId - Team ID to get team leads for
 * @param eventId - Event ID to filter team leads by
 * @returns Array of team lead user objects
 */
export async function getTeamLeads(ctx: any, teamId: string, eventId: string) {
  const teamUsers = await getTeamUsers(ctx, teamId, eventId);
  return teamUsers.filter((user: any) => user.role === "teamLead");
}

/**
 * Gets all team leads for a specific event across all teams
 * @param ctx - Convex context object
 * @param eventId - Event ID to get team leads for
 * @returns Array of team lead user objects with team information
 */
export async function getTeamLeadsByEvent(ctx: any, eventId: string) {
  const users = await ctx.db
    .query("users")
    .withIndex("by_status_role", (q: any) => q.eq("status", "approved").eq("role", "teamLead"))
    .collect();
    
  return users
    .filter((user: any) => 
      user.events?.some((e: any) => e.eventId === eventId)
    )
    .map((user: any) => {
      const eventRegistration = user.events.find((e: any) => e.eventId === eventId);
      return {
        ...user,
        teamId: eventRegistration?.teamId,
      };
    });
}

/**
 * Batch loads team leads for multiple teams to avoid N+1 queries
 * @param ctx - Convex context object
 * @param eventId - Event ID to filter team leads by
 * @param teamIds - Array of team IDs to get team leads for
 * @returns Map of team ID to array of team lead objects
 */
export async function batchLoadTeamLeadsByTeams(ctx: any, eventId: string, teamIds: string[]) {
  const teamLeads = await getTeamLeadsByEvent(ctx, eventId);
  
  const teamLeadsMap = new Map();
  teamIds.forEach(teamId => {
    teamLeadsMap.set(teamId, []);
  });
  
  teamLeads.forEach((teamLead: any) => {
    if (teamLead.teamId && teamLeadsMap.has(teamLead.teamId.toString())) {
      teamLeadsMap.get(teamLead.teamId.toString()).push(teamLead);
    }
  });
  
  return teamLeadsMap;
}

/**
 * Gets team leads with their team information for display purposes
 * @param ctx - Convex context object
 * @param eventId - Event ID to get team leads for
 * @returns Array of team leads with team name and information
 */
export async function getTeamLeadsWithTeamInfo(ctx: any, eventId: string) {
  const [teamLeads, teams] = await Promise.all([
    getTeamLeadsByEvent(ctx, eventId),
    getTeamsByEvent(ctx, eventId)
  ]);
  
  const teamsMap = new Map();
  teams.forEach((team: any) => {
    teamsMap.set(team._id.toString(), team);
  });
  
  return teamLeads.map((teamLead: any) => {
    const team = teamsMap.get(teamLead.teamId?.toString());
    return {
      ...teamLead,
      teamName: team?.name || "No Team",
      team: team || null
    };
  });
}

/**
 * Gets team members with detailed information for a specific team and event
 * @param ctx - Convex context object
 * @param teamId - Team ID to get members for
 * @param eventId - Event ID to filter members by
 * @returns Array of team members with detailed information
 */
export async function getTeamMembersWithDetails(ctx: any, teamId: string, eventId: string) {
  const teamUsers = await getTeamUsers(ctx, teamId, eventId);
  
  // Get additional details for each user
  const membersWithDetails = await Promise.all(
    teamUsers.map(async (user: any) => {
      const eventRegistration = user.events?.find((e: any) => e.eventId === eventId);
      
      // Get user's idea submissions for this event
      const userIdeas = await ctx.db
        .query("ideas")
        .withIndex("by_user_event", (q: any) => q.eq("userId", user._id).eq("eventId", eventId))
        .collect();
        
      // Get user's votes for this event
      const userVotes = await ctx.db
        .query("votes")
        .withIndex("by_user", (q: any) => q.eq("userId", user._id))
        .collect();
        
      // Get user's spark submissions for this event
      const userSparkSubmissions = await ctx.db
        .query("sparkSubmissions")
        .withIndex("by_user_event", (q: any) => q.eq("userId", user._id).eq("eventId", eventId))
        .collect();
      
      return {
        ...user,
        eventRegistration,
        stats: {
          ideaCount: userIdeas.length,
          voteCount: userVotes.length,
          sparkSubmissionCount: userSparkSubmissions.length
        }
      };
    })
  );
  
  return membersWithDetails;
}

/**
 * Gets teams by event with member count information
 * @param ctx - Convex context object
 * @param eventId - Event ID to get teams for
 * @returns Array of teams with member count and additional information
 */
export async function getTeamsByEventWithMemberCount(ctx: any, eventId: string) {
  const teamsWithUserCount = await getTeamsWithUserCount(ctx, eventId);
  
  // Get additional team statistics
  const teamsWithMemberCount = await Promise.all(
    teamsWithUserCount.map(async (team: any) => {
      // Get team's ideas
      const teamIdeas = await ctx.db
        .query("ideas")
        .withIndex("by_team_event", (q: any) => q.eq("teamId", team._id).eq("eventId", eventId))
        .collect();
        
      // Get team's spark submissions
      const teamSparkSubmissions = await ctx.db
        .query("sparkSubmissions")
        .withIndex("by_team_event", (q: any) => q.eq("teamId", team._id).eq("eventId", eventId))
        .collect();
        
      // Get team members with role breakdown
      const teamMembers = await getTeamUsers(ctx, team._id, eventId);
      const roleBreakdown = teamMembers.reduce((acc: any, member: any) => {
        acc[member.role] = (acc[member.role] || 0) + 1;
        return acc;
      }, {});
      
      return {
        ...team,
        memberCount: team.userCount,
        roleBreakdown,
        stats: {
          ideaCount: teamIdeas.length,
          sparkSubmissionCount: teamSparkSubmissions.length,
          teamLeadCount: roleBreakdown.teamLead || 0,
          teamMemberCount: roleBreakdown.teamMember || 0
        }
      };
    })
  );
  
  return teamsWithMemberCount;
}

/**
 * Batch update team voting status to avoid N+1 queries
 * @param ctx - Convex context object
 * @param teams - Array of team objects to update
 * @param votingStatus - Boolean voting status to set
 * @returns Promise that resolves when all updates complete
 */
export async function batchUpdateTeamVotingStatus(ctx: any, teams: any[], votingStatus: boolean) {
  const now = Date.now();
  
  // Use Promise.all for parallel batch updates
  await Promise.all(
    teams.map(team => 
      ctx.db.patch(team._id, {
        voting: votingStatus,
        updatedAt: now,
      })
    )
  );
}

/**
 * Calculate voting mode based on active teams voting status
 * @param teams - Array of team objects
 * @returns Object with voting mode and team details
 */
export function calculateVotingMode(teams: any[]): {
  mode: "none" | "all" | "specific";
  votingTeams: any[];
  activeTeams: any[];
} {
  const activeTeams = teams.filter((team: any) => team.active === true);
  const votingTeams = activeTeams.filter((team: any) => team.voting === true);
  
  let mode: "none" | "all" | "specific" = "none";
  if (votingTeams.length === activeTeams.length && votingTeams.length > 0) {
    mode = "all";
  } else if (votingTeams.length === 1) {
    mode = "specific";
  }
  
  return {
    mode,
    votingTeams,
    activeTeams
  };
}

/**
 * Efficient team deletion helper that collects all related entities
 * @param ctx - Convex context object
 * @param teamId - Team ID to collect related entities for
 * @returns Object with all related entities to be deleted
 */
export async function getTeamRelatedEntitiesForDeletion(ctx: any, teamId: string) {
  // Get team users first
  const allUsers = await ctx.db.query("users").collect();
  const teamUsers = allUsers.filter((user: any) => 
    user.events.find((e: any) => e.teamId === teamId)
  );
  const teamUserIds = teamUsers.map((user: any) => user._id);

  // Get team ideas
  const ideas = await ctx.db
    .query("ideas")
    .withIndex("by_team", (q: any) => q.eq("teamId", teamId))
    .collect();

  // Get related entities using indexed queries for better performance
  const [ideaVotes, userVotes, userQuickfireVotes, sparkSubmissions] = await Promise.all([
    // Get votes for team's ideas
    ideas.length > 0 ? Promise.all(
      ideas.map((idea: any) => 
        ctx.db.query("votes").withIndex("by_idea", (q: any) => q.eq("ideaId", idea._id)).collect()
      )
    ).then(results => results.flat()) : Promise.resolve([]),
    
    // Get votes by team users
    teamUserIds.length > 0 ? Promise.all(
      teamUserIds.map((userId: any) => 
        ctx.db.query("votes").withIndex("by_user", (q: any) => q.eq("userId", userId)).collect()
      )
    ).then(results => results.flat()) : Promise.resolve([]),
    
    // Get quickfire votes by team users
    teamUserIds.length > 0 ? Promise.all(
      teamUserIds.map((userId: any) => 
        ctx.db.query("quickfireVotes").withIndex("by_user", (q: any) => q.eq("userId", userId)).collect()
      )
    ).then(results => results.flat()) : Promise.resolve([]),
    
    // Get spark submissions by team users
    teamUserIds.length > 0 ? Promise.all(
      teamUserIds.map((userId: any) => 
        ctx.db.query("sparkSubmissions").withIndex("by_user", (q: any) => q.eq("userId", userId)).collect()
      )
    ).then(results => results.flat()) : Promise.resolve([])
  ]);

  return {
    teamUsers,
    ideas,
    ideaVotes,
    userVotes,
    userQuickfireVotes,
    sparkSubmissions,
    totalCount: teamUsers.length + ideas.length + ideaVotes.length + userVotes.length + userQuickfireVotes.length + sparkSubmissions.length
  };
}

/**
 * Gets team voting status summary for an event (optimized version)
 * @param ctx - Convex context object
 * @param eventId - Event ID to get voting status for
 * @returns Object with team voting statistics and summaries
 */
export async function getTeamVotingStatusSummary(ctx: any, eventId: string) {
  // PERFORMANCE OPTIMIZATION: Batch load all required data upfront
  const [teams, allVotes, allIdeas, allUsers] = await Promise.all([
    getTeamsByEvent(ctx, eventId),
    ctx.db.query("votes").collect(),
    ctx.db.query("ideas").withIndex("by_event", (q: any) => q.eq("eventId", eventId)).collect(),
    ctx.db.query("users").withIndex("by_status_role", (q: any) => q.eq("status", "approved"))
      .filter((q: any) => q.neq(q.field("role"), "admin")).collect()
  ]);
    
  const eventUsers = allUsers.filter((user: any) => 
    user.events?.some((e: any) => e.eventId === eventId)
  );
  
  // PERFORMANCE OPTIMIZATION: Pre-calculate data structures for efficient lookups
  const teamIds = teams.map((team: any) => team._id);
  const teamMembersMap = await batchLoadTeamMembers(ctx, eventId as any, teamIds);
  
  // Group votes and ideas by team/user for efficient processing
  const votesByUser = new Map();
  const votesByIdea = new Map();
  allVotes.forEach((vote: any) => {
    if (!votesByUser.has(vote.userId)) votesByUser.set(vote.userId, []);
    votesByUser.get(vote.userId).push(vote);
    
    if (!votesByIdea.has(vote.ideaId)) votesByIdea.set(vote.ideaId, []);
    votesByIdea.get(vote.ideaId).push(vote);
  });
  
  const ideasByTeam = new Map();
  allIdeas.forEach((idea: any) => {
    if (!ideasByTeam.has(idea.teamId)) ideasByTeam.set(idea.teamId, []);
    ideasByTeam.get(idea.teamId).push(idea);
  });
  
  // PERFORMANCE OPTIMIZATION: Calculate team statistics without N+1 queries
  const teamVotingStats = teams.map((team: any) => {
    const teamMembers = teamMembersMap.get(team._id) || [];
    const teamMemberIds = teamMembers.map((member: any) => member._id);
    
    // Get votes cast by team members using pre-built map
    const teamVotes = teamMemberIds.flatMap(memberId => 
      votesByUser.get(memberId) || []
    );
    
    // Get ideas submitted by team using pre-built map
    const teamIdeas = ideasByTeam.get(team._id) || [];
    
    // Calculate voting participation
    const totalPossibleVotes = teamIdeas.length * (eventUsers.length - teamMembers.length);
    const actualVotes = teamVotes.length;
    const votingParticipation = totalPossibleVotes > 0 ? (actualVotes / totalPossibleVotes) * 100 : 0;
    
    // Get average scores for team's ideas using pre-built map
    const votesOnTeamIdeas = teamIdeas.flatMap((idea: any) => 
      votesByIdea.get(idea._id) || []
    );
    
    const averageScore = votesOnTeamIdeas.length > 0 
      ? votesOnTeamIdeas.reduce((sum: number, vote: any) => sum + vote.score, 0) / votesOnTeamIdeas.length
      : 0;
    
    return {
      teamId: team._id,
      teamName: team.name,
      memberCount: teamMembers.length,
      ideaCount: teamIdeas.length,
      votesCast: teamVotes.length,
      votesReceived: votesOnTeamIdeas.length,
      votingParticipation: Math.round(votingParticipation * 100) / 100,
      averageScore: Math.round(averageScore * 100) / 100,
      hasSubmittedIdeas: teamIdeas.length > 0,
      isActiveInVoting: teamVotes.length > 0
    };
  });
  
  // Calculate overall statistics
  const totalTeams = teams.length;
  const activeTeams = teamVotingStats.filter((stat: any) => stat.hasSubmittedIdeas).length;
  const votingTeams = teamVotingStats.filter((stat: any) => stat.isActiveInVoting).length;
  const totalVotes = allVotes.length;
  const totalIdeas = allIdeas.length;
  const averageVotingParticipation = teamVotingStats.reduce((sum: number, stat: any) => sum + stat.votingParticipation, 0) / totalTeams;
  
  return {
    summary: {
      totalTeams,
      activeTeams,
      votingTeams,
      totalVotes,
      totalIdeas,
      averageVotingParticipation: Math.round(averageVotingParticipation * 100) / 100
    },
    teamStats: teamVotingStats,
    lastUpdated: new Date().toISOString()
  };
}

/**
 * Efficiently loads teams with their complete member information
 * @param ctx - Convex context object
 * @param eventId - Event ID to get teams for
 * @returns Array of teams with enriched member information
 */
export async function getTeamsWithEnrichedMembers(ctx: any, eventId: string) {
  const teams = await getTeamsByEvent(ctx, eventId);
  
  if (teams.length === 0) {
    return [];
  }
  
  // PERFORMANCE OPTIMIZATION: Use batch loading for team members
  const teamIds = teams.map((team: any) => team._id);
  const teamMembersMap = await batchLoadTeamMembers(ctx, eventId as any, teamIds);
  
  // Enrich teams with member information
  return teams.map((team: any) => ({
    ...team,
    members: teamMembersMap.get(team._id) || [],
    memberCount: (teamMembersMap.get(team._id) || []).length
  }));
}
/**
 * Shared Presence Database Query Utilities
 * 
 * This module provides centralized presence lookup and batch loading functions
 * to eliminate code duplication and improve performance across the codebase.
 */

/**
 * Retrieves presence records for a user across all sessions
 * @param ctx - Convex context object
 * @param userId - User ID to look up presence for
 * @returns Array of presence records for the user
 */
export async function getUserPresenceRecords(ctx: any, userId: string) {
  return await ctx.db
    .query("presence")
    .withIndex("by_user", (q: any) => q.eq("userId", userId))
    .collect();
}

/**
 * Retrieves presence record for a specific user and session
 * @param ctx - Convex context object
 * @param userId - User ID to look up presence for
 * @param sessionId - Session ID to look up presence for
 * @returns Presence record or null if not found
 */
export async function getUserSessionPresence(ctx: any, userId: string, sessionId: string) {
  return await ctx.db
    .query("presence")
    .withIndex("by_user_session", (q: any) => 
      q.eq("userId", userId).eq("sessionId", sessionId)
    )
    .first();
}

/**
 * Retrieves all online presence records (marked as online)
 * @param ctx - Convex context object
 * @returns Array of online presence records
 */
export async function getOnlinePresenceRecords(ctx: any) {
  return await ctx.db
    .query("presence")
    .withIndex("by_online", (q: any) => q.eq("isOnline", true))
    .collect();
}

/**
 * Retrieves all presence records in the database
 * @param ctx - Convex context object
 * @returns Array of all presence records
 */
export async function getAllPresenceRecords(ctx: any) {
  return await ctx.db.query("presence").collect();
}

/**
 * Creates a new presence record
 * @param ctx - Convex context object
 * @param userId - User ID
 * @param sessionId - Session ID
 * @param isOnline - Online status
 * @returns Promise that resolves with the created record ID
 */
export async function createPresenceRecord(ctx: any, userId: string, sessionId: string, isOnline: boolean = true) {
  const now = Date.now();
  return await ctx.db.insert("presence", {
    userId,
    sessionId,
    lastSeen: now,
    isOnline,
    createdAt: now,
    updatedAt: now,
  });
}

/**
 * Updates a presence record
 * @param ctx - Convex context object
 * @param presenceId - Presence record ID
 * @param updates - Updates to apply
 * @returns Promise that resolves when update is complete
 */
export async function updatePresenceRecord(ctx: any, presenceId: string, updates: any) {
  const now = Date.now();
  return await ctx.db.patch(presenceId, {
    ...updates,
    updatedAt: now,
  });
}

/**
 * Filters presence records by recency threshold
 * @param presenceRecords - Array of presence records to filter
 * @param thresholdMs - Threshold in milliseconds (records older than this are filtered out)
 * @returns Array of recent presence records
 */
export function filterRecentPresence(presenceRecords: any[], thresholdMs: number) {
  const now = Date.now();
  return presenceRecords.filter(presence => 
    (now - presence.lastSeen) <= thresholdMs
  );
}

/**
 * Gets unique users from presence records (most recent per user)
 * @param presenceRecords - Array of presence records
 * @returns Map of user ID to most recent presence record
 */
export function getUniqueUsersFromPresence(presenceRecords: any[]) {
  const userMap = new Map();
  
  for (const presence of presenceRecords) {
    const existingPresence = userMap.get(presence.userId);
    if (!existingPresence || presence.lastSeen > existingPresence.lastSeen) {
      userMap.set(presence.userId, presence);
    }
  }
  
  return userMap;
}

/**
 * Batch updates presence records to offline status
 * @param ctx - Convex context object
 * @param presenceRecords - Array of presence records to update
 * @returns Promise that resolves when all updates are complete
 */
export async function batchSetPresenceOffline(ctx: any, presenceRecords: any[]) {
  const now = Date.now();
  const updates = presenceRecords.map(presence => 
    ctx.db.patch(presence._id, {
      isOnline: false,
      lastSeen: now,
      updatedAt: now,
    })
  );
  
  return Promise.all(updates);
}

/**
 * Batch delete stale presence records efficiently
 * @param ctx - Convex context object
 * @param staleRecords - Array of stale presence records to delete
 * @returns Promise that resolves when all deletions are complete
 */
export async function batchDeleteStalePresence(ctx: any, staleRecords: any[]) {
  if (staleRecords.length === 0) {
    return;
  }
  
  // Delete in batches to avoid overwhelming the database
  const batchSize = 50;
  const batches = [];
  
  for (let i = 0; i < staleRecords.length; i += batchSize) {
    batches.push(staleRecords.slice(i, i + batchSize));
  }
  
  for (const batch of batches) {
    await Promise.all(batch.map(record => ctx.db.delete(record._id)));
  }
}

/**
 * Constants for presence system
 */
export const PRESENCE_CONSTANTS = {
  OFFLINE_THRESHOLD: 3 * 60 * 1000, // 3 minutes
  CLEANUP_THRESHOLD: 10 * 60 * 1000, // 10 minutes
} as const;
/**
 * Shared Session Database Query Utilities
 * 
 * This module provides centralized session lookup and filtering functions
 * to eliminate code duplication and improve performance across the codebase.
 */

import { getActiveEventOrNull } from "./eventHelpers";

/**
 * Retrieves all sessions for a specific event
 * @param ctx - Convex context object
 * @param eventId - Event ID to get sessions for
 * @returns Array of session objects
 */
export async function getSessionsByEvent(ctx: any, eventId: string) {
  return await ctx.db
    .query("sessions")
    .withIndex("by_event", (q: any) => q.eq("eventId", eventId))
    .collect();
}

/**
 * Retrieves all sessions for the active event
 * @param ctx - Convex context object
 * @returns Array of session objects for active event, or empty array if no active event
 */
export async function getSessionsByActiveEvent(ctx: any) {
  const activeEvent = await getActiveEventOrNull(ctx);
  
  if (!activeEvent) {
    return [];
  }
  
  return await getSessionsByEvent(ctx, activeEvent._id);
}

/**
 * Retrieves the active session for a specific event
 * @param ctx - Convex context object
 * @param eventId - Event ID to get active session for
 * @returns Active session object or null if not found
 */
export async function getActiveSessionForEvent(ctx: any, eventId: string) {
  return await ctx.db
    .query("sessions")
    .withIndex("by_event_active", (q: any) => q.eq("eventId", eventId).eq("active", true))
    .first();
}

/**
 * Retrieves the active session for the active event
 * @param ctx - Convex context object
 * @returns Active session object or null if not found
 */
export async function getActiveSessionForActiveEvent(ctx: any) {
  const activeEvent = await getActiveEventOrNull(ctx);
  
  if (!activeEvent) {
    return null;
  }
  
  return await getActiveSessionForEvent(ctx, activeEvent._id);
}

/**
 * Retrieves all active sessions across all events
 * @param ctx - Convex context object
 * @returns Array of all active session objects
 */
export async function getAllActiveSessions(ctx: any) {
  return await ctx.db
    .query("sessions")
    .withIndex("by_active", (q: any) => q.eq("active", true))
    .collect();
}

/**
 * Retrieves a session by ID with null safety
 * @param ctx - Convex context object
 * @param sessionId - Session ID to look up
 * @returns Session object or null if not found
 */
export async function getSessionById(ctx: any, sessionId: string) {
  return await ctx.db.get(sessionId);
}

/**
 * Retrieves a session by ID and throws error if not found
 * @param ctx - Convex context object
 * @param sessionId - Session ID to look up
 * @returns Session object
 * @throws Error with message "Session not found" if session doesn't exist
 */
export async function getSessionByIdOrThrow(ctx: any, sessionId: string) {
  const session = await getSessionById(ctx, sessionId);
  if (!session) {
    throw new Error("Session not found");
  }
  return session;
}

/**
 * Retrieves sessions by type for a specific event
 * @param ctx - Convex context object
 * @param eventId - Event ID to filter sessions by
 * @param sessionType - Type of session to filter by ("Ideas", "Quickfire", "Sparks")
 * @returns Array of sessions matching the type
 */
export async function getSessionsByTypeForEvent(ctx: any, eventId: string, sessionType: string) {
  const sessions = await getSessionsByEvent(ctx, eventId);
  return sessions.filter((session: any) => session.type === sessionType);
}

/**
 * Retrieves sessions by type for the active event
 * @param ctx - Convex context object
 * @param sessionType - Type of session to filter by ("Ideas", "Quickfire", "Sparks")
 * @returns Array of sessions matching the type for active event
 */
export async function getSessionsByTypeForActiveEvent(ctx: any, sessionType: string) {
  const activeEvent = await getActiveEventOrNull(ctx);
  
  if (!activeEvent) {
    return [];
  }
  
  return await getSessionsByTypeForEvent(ctx, activeEvent._id, sessionType);
}

/**
 * Retrieves the active session and validates it's of the expected type
 * @param ctx - Convex context object
 * @param expectedType - Expected session type ("Ideas", "Quickfire", "Sparks")
 * @returns Active session object or null if not found or wrong type
 */
export async function getActiveSessionOfType(ctx: any, expectedType: string) {
  const activeSession = await getActiveSessionForActiveEvent(ctx);
  
  if (!activeSession || activeSession.type !== expectedType) {
    return null;
  }
  
  return activeSession;
}

/**
 * Checks if there's an active session of a specific type
 * @param ctx - Convex context object
 * @param sessionType - Type of session to check for ("Ideas", "Quickfire", "Sparks")
 * @returns Boolean indicating if there's an active session of the specified type
 */
export async function hasActiveSessionOfType(ctx: any, sessionType: string) {
  const activeSession = await getActiveSessionOfType(ctx, sessionType);
  return activeSession !== null;
}

/**
 * Batch loads multiple sessions by their IDs to avoid N+1 queries
 * @param ctx - Convex context object
 * @param sessionIds - Array of session IDs to load
 * @returns Map of session ID to session object (null if not found)
 */
export async function batchLoadSessions(ctx: any, sessionIds: string[]) {
  const uniqueSessionIds = [...new Set(sessionIds)];
  const sessions = await Promise.all(
    uniqueSessionIds.map(id => ctx.db.get(id))
  );
  
  const sessionMap = new Map();
  uniqueSessionIds.forEach((id, index) => {
    sessionMap.set(id, sessions[index]);
  });
  
  return sessionMap;
}

/**
 * Validates session exists and belongs to active event
 * @param ctx - Convex context object
 * @param sessionId - Session ID to validate
 * @returns Session object if valid
 * @throws Error if session not found or not in active event
 */
export async function validateSessionInActiveEvent(ctx: any, sessionId: string) {
  const activeEvent = await getActiveEventOrNull(ctx);
  if (!activeEvent) {
    throw new Error("No active event found");
  }
  
  const session = await getSessionByIdOrThrow(ctx, sessionId);
  
  if (session.eventId !== activeEvent._id) {
    throw new Error("Session does not belong to active event");
  }
  
  return session;
}

/**
 * Gets session with additional metadata (team count, idea count, etc.)
 * @param ctx - Convex context object
 * @param sessionId - Session ID to get metadata for
 * @returns Session object with additional metadata
 */
export async function getSessionWithMetadata(ctx: any, sessionId: string) {
  const session = await getSessionByIdOrThrow(ctx, sessionId);
  
  // Use parallel queries for better performance
  const [ideas, quickfires, sparkSubmissions] = await Promise.all([
    ctx.db
      .query("ideas")
      .withIndex("by_session", (q: any) => q.eq("sessionId", sessionId))
      .collect(),
    ctx.db
      .query("quickfires")
      .withIndex("by_session_order", (q: any) => q.eq("sessionId", sessionId))
      .collect(),
    ctx.db
      .query("sparkSubmissions")
      .withIndex("by_session", (q: any) => q.eq("sessionId", sessionId))
      .collect()
  ]);
  
  return {
    ...session,
    metadata: {
      ideaCount: ideas.length,
      quickfireCount: quickfires.length,
      sparkSubmissionCount: sparkSubmissions.length,
      finishedTeamCount: session.finishedTeams?.length || 0
    }
  };
}

/**
 * Validates there's an active session of the expected type and returns it
 * @param ctx - Convex context object
 * @param expectedType - Expected session type ("Ideas", "Quickfire", "Sparks")
 * @returns Active session object of the expected type
 * @throws Error if no active session found or wrong type
 */
export async function validateActiveSessionOfType(ctx: any, expectedType: string) {
  const activeSession = await getActiveSessionForActiveEvent(ctx);
  
  if (!activeSession) {
    throw new Error("No active session found");
  }
  
  if (activeSession.type !== expectedType) {
    throw new Error(`Expected active session of type "${expectedType}", but found "${activeSession.type}"`);
  }
  
  return activeSession;
}

/**
 * Retrieves the active session with settings information
 * @param ctx - Convex context object
 * @returns Active session object with settings or null if not found
 */
export async function getActiveSessionWithSettings(ctx: any) {
  const activeSession = await getActiveSessionForActiveEvent(ctx);
  
  if (!activeSession) {
    return null;
  }
  
  // Get settings for additional context
  const settings = await ctx.db
    .query("settings")
    .first();
  
  return {
    ...activeSession,
    settings: settings || {}
  };
}

/**
 * Retrieves sessions with spark information for a specific event
 * @param ctx - Convex context object
 * @param eventId - Event ID to get sessions for
 * @returns Array of sessions with spark configuration data
 */
export async function getSessionsWithSparkInfo(ctx: any, eventId: string) {
  const sessions = await getSessionsByEvent(ctx, eventId);
  
  // Get all sparks for this event
  const sparks = await ctx.db
    .query("sparks")
    .withIndex("by_event", (q: any) => q.eq("eventId", eventId))
    .collect();
    
  // Create a map of spark ID to spark object
  const sparkMap = new Map();
  sparks.forEach((spark: any) => {
    sparkMap.set(spark._id, spark);
  });
  
  // Add spark info to sessions
  return sessions.map((session: any) => {
    const sparkInfo = session.sparkId ? sparkMap.get(session.sparkId) : null;
    
    return {
      ...session,
      sparkInfo: sparkInfo || null,
      hasSparkConfig: !!sparkInfo
    };
  });
}

/**
 * Batch deactivate sessions efficiently
 * @param ctx - Convex context object
 * @param sessionIds - Array of session IDs to deactivate
 * @returns Promise that resolves when all sessions are deactivated
 */
export async function batchDeactivateSessions(ctx: any, sessionIds: string[]) {
  if (sessionIds.length === 0) {
    return;
  }
  
  const batchSize = 25;
  const now = Date.now();
  
  for (let i = 0; i < sessionIds.length; i += batchSize) {
    const batch = sessionIds.slice(i, i + batchSize);
    const updatePromises = batch.map(sessionId => 
      ctx.db.patch(sessionId, { 
        active: false, 
        updatedAt: now 
      })
    );
    await Promise.all(updatePromises);
  }
}

/**
 * Batch update session settings efficiently
 * @param ctx - Convex context object
 * @param sessionUpdates - Array of session update objects with id and updateData
 * @returns Promise that resolves when all sessions are updated
 */
export async function batchUpdateSessions(ctx: any, sessionUpdates: Array<{ id: string, updateData: any }>) {
  if (sessionUpdates.length === 0) {
    return;
  }
  
  const batchSize = 25;
  
  for (let i = 0; i < sessionUpdates.length; i += batchSize) {
    const batch = sessionUpdates.slice(i, i + batchSize);
    const updatePromises = batch.map(update => 
      ctx.db.patch(update.id, update.updateData)
    );
    await Promise.all(updatePromises);
  }
}

/**
 * Creates a new session with proper error handling
 * @param ctx - Convex context object
 * @param sessionData - Session data to insert
 * @returns Promise that resolves to the created session ID
 */
export async function createSession(ctx: any, sessionData: any) {
  return await ctx.db.insert("sessions", {
    ...sessionData,
    createdAt: Date.now()
  });
}

/**
 * Updates a session by ID with proper error handling
 * @param ctx - Convex context object
 * @param sessionId - Session ID to update
 * @param updateData - Data to update
 * @returns Promise that resolves when update is complete
 */
export async function updateSession(ctx: any, sessionId: string, updateData: any) {
  return await ctx.db.patch(sessionId, {
    ...updateData,
    updatedAt: Date.now()
  });
}

/**
 * Deletes a session by ID with proper error handling
 * @param ctx - Convex context object
 * @param sessionId - Session ID to delete
 * @returns Promise that resolves when deletion is complete
 */
export async function deleteSession(ctx: any, sessionId: string) {
  return await ctx.db.delete(sessionId);
}

/**
 * Gets all sessions from the database with optional filtering
 * @param ctx - Convex context object
 * @returns Promise that resolves to array of all sessions
 */
export async function getAllSessions(ctx: any) {
  return await ctx.db.query("sessions").collect();
}
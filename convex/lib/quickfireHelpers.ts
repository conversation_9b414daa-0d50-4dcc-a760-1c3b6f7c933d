/**
 * Quickfire query helpers for standardized database access patterns
 * 
 * These helpers provide consistent patterns for quickfire-related database operations,
 * reducing code duplication and optimizing query performance.
 */

import { Id } from "../_generated/dataModel";
import { 
  batchLoadUsers, 
  batchLoadSessions, 
  batchLoadEvents,
  extractUniqueIds
} from "./batchHelpers";
import { getActiveEventOrNull } from "./eventHelpers";
import { getActiveSessionForActiveEvent } from "./sessionHelpers";

/**
 * Get quickfire by ID with error handling
 * @param ctx - Database context
 * @param quickfireId - Quickfire ID to retrieve
 * @returns Quickfire entity or null if not found
 */
export async function getQuickfireById(
  ctx: any,
  quickfireId: Id<"quickfires">
): Promise<any | null> {
  return await ctx.db.get(quickfireId);
}

/**
 * Get quickfire by ID or throw error if not found
 * @param ctx - Database context
 * @param quickfireId - Quickfire ID to retrieve
 * @returns Quickfire entity
 * @throws Error if quickfire not found
 */
export async function getQuickfireByIdOrThrow(
  ctx: any,
  quickfireId: Id<"quickfires">
): Promise<any> {
  const quickfire = await ctx.db.get(quickfireId);
  if (!quickfire) {
    throw new Error(`Quickfire with ID ${quickfireId} not found`);
  }
  return quickfire;
}

/**
 * Get quickfires by event using optimized index
 * @param ctx - Database context
 * @param eventId - Event ID to filter by
 * @returns Array of quickfires for the event
 */
export async function getQuickfiresByEvent(
  ctx: any,
  eventId: Id<"events">
): Promise<any[]> {
  return await ctx.db
    .query("quickfires")
    .withIndex("by_event", (q: any) => q.eq("eventId", eventId))
    .collect();
}

/**
 * Get quickfires by session using optimized index
 * @param ctx - Database context
 * @param sessionId - Session ID to filter by
 * @returns Array of quickfires for the session
 */
export async function getQuickfiresBySession(
  ctx: any,
  sessionId: Id<"sessions">
): Promise<any[]> {
  return await ctx.db
    .query("quickfires")
    .withIndex("by_session", (q: any) => q.eq("sessionId", sessionId))
    .collect();
}

/**
 * Get quickfires by session ordered by display order
 * @param ctx - Database context
 * @param sessionId - Session ID to filter by
 * @returns Array of quickfires for the session, ordered by display order
 */
export async function getQuickfiresBySessionOrdered(
  ctx: any,
  sessionId: Id<"sessions">
): Promise<any[]> {
  return await ctx.db
    .query("quickfires")
    .withIndex("by_session_order", (q: any) => q.eq("sessionId", sessionId))
    .order("asc") // Ensure lowest order numbers (newest after reset) appear first
    .collect();
}

/**
 * Get active quickfires by session using optimized compound index
 * @param ctx - Database context
 * @param sessionId - Session ID to filter by
 * @returns Array of active quickfires for the session
 */
export async function getActiveQuickfiresBySession(
  ctx: any,
  sessionId: Id<"sessions">
): Promise<any[]> {
  return await ctx.db
    .query("quickfires")
    .withIndex("by_session_voting", (q: any) => 
      q.eq("sessionId", sessionId).eq("votingActive", true)
    )
    .collect();
}

/**
 * Get all active quickfires using optimized index
 * @param ctx - Database context
 * @returns Array of all active quickfires
 */
export async function getActiveQuickfires(
  ctx: any
): Promise<any[]> {
  return await ctx.db
    .query("quickfires")
    .withIndex("by_voting_active", (q: any) => q.eq("votingActive", true))
    .collect();
}

/**
 * Get quickfires by event and session using compound index
 * @param ctx - Database context
 * @param eventId - Event ID to filter by
 * @param sessionId - Session ID to filter by
 * @returns Array of quickfires for the event and session
 */
export async function getQuickfiresByEventAndSession(
  ctx: any,
  eventId: Id<"events">,
  sessionId: Id<"sessions">
): Promise<any[]> {
  return await ctx.db
    .query("quickfires")
    .withIndex("by_event_session", (q: any) => 
      q.eq("eventId", eventId).eq("sessionId", sessionId)
    )
    .collect();
}

/**
 * Get quickfires for active event and session
 * @param ctx - Database context
 * @returns Array of quickfires for the active event and session
 */
export async function getQuickfiresForActiveSession(
  ctx: any
): Promise<any[]> {
  const [activeEvent, activeSession] = await Promise.all([
    getActiveEventOrNull(ctx),
    getActiveSessionForActiveEvent(ctx)
  ]);

  if (!activeEvent || !activeSession) {
    return [];
  }

  return await getQuickfiresByEventAndSession(ctx, activeEvent._id, activeSession._id);
}

/**
 * Get quickfires with enriched data (session, event details)
 * @param ctx - Database context
 * @param quickfires - Array of quickfire objects
 * @returns Array of quickfires with enriched data
 */
export async function enrichQuickfiresWithDetails(
  ctx: any,
  quickfires: any[]
): Promise<any[]> {
  if (quickfires.length === 0) {
    return [];
  }

  // Extract unique IDs for batch loading
  const sessionIds = extractUniqueIds(quickfires, "sessionId");
  const eventIds = extractUniqueIds(quickfires, "eventId");

  // Batch load all related entities
  const [sessions, events] = await Promise.all([
    batchLoadSessions(ctx, sessionIds),
    batchLoadEvents(ctx, eventIds)
  ]);

  // Enrich quickfires with details
  return quickfires.map(quickfire => {
    const session = sessions.get(quickfire.sessionId);
    const event = events.get(quickfire.eventId);

    return {
      ...quickfire,
      sessionName: session?.name || "Unknown Session",
      eventName: event?.name || "Unknown Event"
    };
  });
}

/**
 * Get quickfire statistics for a session
 * @param ctx - Database context
 * @param sessionId - Session ID to get statistics for
 * @returns Object with quickfire statistics
 */
export async function getQuickfireStatsBySession(
  ctx: any,
  sessionId: Id<"sessions">
): Promise<{
  total: number;
  active: number;
  inactive: number;
}> {
  const quickfires = await getQuickfiresBySession(ctx, sessionId);
  
  const active = quickfires.filter(qf => qf.votingActive).length;
  const inactive = quickfires.length - active;
  
  return {
    total: quickfires.length,
    active,
    inactive
  };
}

/**
 * Get quickfire vote statistics
 * @param ctx - Database context
 * @param quickfireId - Quickfire ID to get statistics for
 * @returns Object with vote statistics
 */
export async function getQuickfireVoteStats(
  ctx: any,
  quickfireId: Id<"quickfires">
): Promise<{
  count: number;
  totalScore: number;
  averageScore: number;
  minScore: number;
  maxScore: number;
}> {
  const votes = await ctx.db
    .query("quickfireVotes")
    .withIndex("by_quickfire", (q: any) => q.eq("quickfireId", quickfireId))
    .collect();
  
  if (votes.length === 0) {
    return {
      count: 0,
      totalScore: 0,
      averageScore: 0,
      minScore: 0,
      maxScore: 0
    };
  }
  
  const scores = votes.map((vote: any) => vote.score);
  const totalScore = scores.reduce((sum: number, score: number) => sum + score, 0);
  const averageScore = totalScore / votes.length;
  const minScore = Math.min(...scores);
  const maxScore = Math.max(...scores);
  
  return {
    count: votes.length,
    totalScore,
    averageScore,
    minScore,
    maxScore
  };
}

/**
 * Get quickfires with vote statistics
 * @param ctx - Database context
 * @param eventId - Event ID to get quickfires for
 * @returns Array of quickfires with vote statistics
 */
export async function getQuickfiresWithVoteStats(
  ctx: any,
  eventId: Id<"events">
): Promise<any[]> {
  const quickfires = await getQuickfiresByEvent(ctx, eventId);
  
  return await Promise.all(
    quickfires.map(async (quickfire) => {
      const voteStats = await getQuickfireVoteStats(ctx, quickfire._id);
      return {
        ...quickfire,
        voteCount: voteStats.count,
        totalScore: voteStats.totalScore,
        averageScore: voteStats.averageScore
      };
    })
  );
}

/**
 * Get top quickfires by vote score
 * @param ctx - Database context
 * @param eventId - Event ID to get quickfires for
 * @param limit - Maximum number of quickfires to return
 * @returns Array of top-voted quickfires
 */
export async function getTopQuickfiresByEvent(
  ctx: any,
  eventId: Id<"events">,
  limit: number = 10
): Promise<any[]> {
  const quickfiresWithStats = await getQuickfiresWithVoteStats(ctx, eventId);
  
  return quickfiresWithStats
    .sort((a, b) => b.averageScore - a.averageScore)
    .slice(0, limit);
}

/**
 * Get quickfire votes with enriched data
 * @param ctx - Database context
 * @param quickfireId - Quickfire ID to get votes for
 * @returns Array of votes with enriched user data
 */
export async function getQuickfireVotesWithUserData(
  ctx: any,
  quickfireId: Id<"quickfires">
): Promise<any[]> {
  const votes = await ctx.db
    .query("quickfireVotes")
    .withIndex("by_quickfire", (q: any) => q.eq("quickfireId", quickfireId))
    .collect();
  
  if (votes.length === 0) {
    return [];
  }
  
  // Extract user IDs for batch loading
  const userIds = extractUniqueIds(votes, "userId");
  const users = await batchLoadUsers(ctx, userIds);
  
  // Enrich votes with user data
  return votes.map((vote: any) => {
    const user = users.get(vote.userId);
    return {
      ...vote,
      userName: user?.name || "Unknown User",
      userUsername: user?.username || "Unknown"
    };
  });
}

/**
 * Get quickfire participation summary
 * @param ctx - Database context
 * @param eventId - Event ID to get summary for
 * @returns Object with participation statistics
 */
export async function getQuickfireParticipationSummary(
  ctx: any,
  eventId: Id<"events">
): Promise<{
  totalQuickfires: number;
  totalVotes: number;
  uniqueVoters: number;
  averageScore: number;
  participationRate: number;
}> {
  // Get all quickfires for the event
  const quickfires = await getQuickfiresByEvent(ctx, eventId);
  
  // Get all votes for these quickfires
  const allVotes = await Promise.all(
    quickfires.map(qf => ctx.db
      .query("quickfireVotes")
      .withIndex("by_quickfire", (q: any) => q.eq("quickfireId", qf._id))
      .collect()
    )
  );
  
  const votes = allVotes.flat();
  
  // Get event users for participation rate calculation
  const allUsers = await ctx.db.query("users").collect();
  const eventUsers = allUsers.filter((user: any) => 
    user.events?.some((event: any) => event.eventId === eventId)
  );
  
  const uniqueVoters = new Set(votes.map((vote: any) => vote.userId.toString())).size;
  const totalScore = votes.reduce((sum: number, vote: any) => sum + vote.score, 0);
  const averageScore = votes.length > 0 ? totalScore / votes.length : 0;
  const participationRate = eventUsers.length > 0 ? (uniqueVoters / eventUsers.length) * 100 : 0;
  
  return {
    totalQuickfires: quickfires.length,
    totalVotes: votes.length,
    uniqueVoters,
    averageScore,
    participationRate
  };
}

/**
 * Get user's quickfire votes for a session
 * @param ctx - Database context
 * @param userId - User ID to get votes for
 * @param sessionId - Session ID to filter by
 * @returns Array of user's quickfire votes for the session
 */
export async function getUserQuickfireVotesForSession(
  ctx: any,
  userId: Id<"users">,
  sessionId: Id<"sessions">
): Promise<any[]> {
  // Get quickfires for the session
  const quickfires = await getQuickfiresBySession(ctx, sessionId);
  
  // Get user's votes for these quickfires
  const votes = await Promise.all(
    quickfires.map(async (quickfire) => {
      const vote = await ctx.db
        .query("quickfireVotes")
        .withIndex("by_user_quickfire", (q: any) => 
          q.eq("userId", userId).eq("quickfireId", quickfire._id)
        )
        .first();
      
      return vote ? {
        ...vote,
        quickfireIdea: quickfire.idea,
        quickfireQuestion: quickfire.question
      } : null;
    })
  );
  
  return votes.filter(vote => vote !== null);
}

/**
 * Count quickfires by voting status for analytics
 * @param ctx - Database context
 * @param eventId - Event ID to count quickfires for
 * @returns Object with count statistics
 */
export async function getQuickfireCountByStatus(
  ctx: any,
  eventId: Id<"events">
): Promise<{
  totalQuickfires: number;
  activeQuickfires: number;
  inactiveQuickfires: number;
}> {
  const quickfires = await getQuickfiresByEvent(ctx, eventId);
  
  const activeQuickfires = quickfires.filter(qf => qf.votingActive).length;
  const inactiveQuickfires = quickfires.length - activeQuickfires;
  
  return {
    totalQuickfires: quickfires.length,
    activeQuickfires,
    inactiveQuickfires
  };
}

/**
 * Get all quickfires for analytics/export
 * @param ctx - Database context
 * @returns Array of all quickfires
 */
export async function getAllQuickfires(ctx: any): Promise<any[]> {
  const quickfires = await ctx.db.query("quickfires").collect();
  
  // Log warning for large collections
  if (quickfires.length > 1000) {
    console.warn(`Large quickfires collection: ${quickfires.length} quickfires`);
  }
  
  return quickfires;
}

/**
 * Validate quickfire access for operations
 * @param ctx - Database context
 * @param quickfireId - Quickfire ID to validate
 * @returns Boolean indicating if quickfire exists and is accessible
 */
export async function validateQuickfireAccess(
  ctx: any,
  quickfireId: Id<"quickfires">
): Promise<{
  canAccess: boolean;
  quickfire: any | null;
  reason?: string;
}> {
  const quickfire = await getQuickfireById(ctx, quickfireId);
  
  if (!quickfire) {
    return {
      canAccess: false,
      quickfire: null,
      reason: "Quickfire not found"
    };
  }
  
  return {
    canAccess: true,
    quickfire
  };
}

/**
 * Get quickfire summary for dashboard
 * @param ctx - Database context
 * @param eventId - Event ID to get summary for
 * @returns Object with quickfire summary data
 */
export async function getQuickfireSummary(
  ctx: any,
  eventId: Id<"events">
): Promise<{
  totalQuickfires: number;
  activeQuickfires: number;
  totalVotes: number;
  averageScore: number;
  topQuickfires: Array<{
    idea: string;
    question?: string;
    voteCount: number;
    averageScore: number;
  }>;
}> {
  const quickfires = await getQuickfiresByEvent(ctx, eventId);
  
  const activeQuickfires = quickfires.filter(qf => qf.votingActive).length;
  
  // Get vote statistics
  const quickfiresWithStats = await getQuickfiresWithVoteStats(ctx, eventId);
  
  const totalVotes = quickfiresWithStats.reduce((sum, qf) => sum + qf.voteCount, 0);
  const totalScore = quickfiresWithStats.reduce((sum, qf) => sum + qf.totalScore, 0);
  const averageScore = totalVotes > 0 ? totalScore / totalVotes : 0;
  
  // Get top 3 quickfires
  const topQuickfires = quickfiresWithStats
    .sort((a, b) => b.averageScore - a.averageScore)
    .slice(0, 3)
    .map(qf => ({
      idea: qf.idea,
      question: qf.question,
      voteCount: qf.voteCount,
      averageScore: qf.averageScore
    }));
  
  return {
    totalQuickfires: quickfires.length,
    activeQuickfires,
    totalVotes,
    averageScore,
    topQuickfires
  };
}

/**
 * Batch update quickfire voting status efficiently
 * @param ctx - Database context
 * @param updates - Array of update operations
 * @returns Promise resolving when all updates complete
 */
export async function batchUpdateQuickfireVotingStatus(
  ctx: any,
  updates: Array<{
    quickfireId: Id<"quickfires">;
    votingActive: boolean;
  }>
): Promise<void> {
  if (updates.length === 0) {
    return;
  }
  
  const now = Date.now();
  const updatePromises = updates.map(({ quickfireId, votingActive }) => 
    ctx.db.patch(quickfireId, {
      votingActive,
      updatedAt: now
    })
  );
  
  await Promise.all(updatePromises);
}

/**
 * Get quickfires by multiple session IDs efficiently
 * @param ctx - Database context
 * @param sessionIds - Array of session IDs
 * @returns Map of session ID to quickfires array
 */
export async function batchGetQuickfiresBySessions(
  ctx: any,
  sessionIds: Id<"sessions">[]
): Promise<Map<Id<"sessions">, any[]>> {
  const result = new Map<Id<"sessions">, any[]>();
  
  if (sessionIds.length === 0) {
    return result;
  }
  
  // Get all quickfires and filter by session
  const allQuickfires = await ctx.db.query("quickfires").collect();
  const sessionIdStrings = new Set(sessionIds.map(id => id.toString()));
  
  // Initialize result map
  sessionIds.forEach(id => {
    result.set(id, []);
  });
  
  // Group quickfires by session
  allQuickfires.forEach((quickfire: any) => {
    if (sessionIdStrings.has(quickfire.sessionId.toString())) {
      const quickfires = result.get(quickfire.sessionId);
      if (quickfires) {
        quickfires.push(quickfire);
      }
    }
  });
  
  return result;
}

/**
 * Get quickfire with session and event details efficiently
 * @param ctx - Database context
 * @param quickfireId - Quickfire ID to get details for
 * @returns Quickfire with enriched session and event data
 */
export async function getQuickfireWithDetails(
  ctx: any,
  quickfireId: Id<"quickfires">
): Promise<any | null> {
  const quickfire = await getQuickfireById(ctx, quickfireId);
  
  if (!quickfire) {
    return null;
  }

  // Batch load session and event data
  const [session, event] = await Promise.all([
    ctx.db.get(quickfire.sessionId),
    ctx.db.get(quickfire.eventId)
  ]);

  return {
    ...quickfire,
    sessionName: session?.name || "Unknown Session",
    eventName: event?.name || "Unknown Event",
    sessionType: session?.type || "Unknown",
    sessionActive: session?.active || false
  };
}

/**
 * Get quickfire session data efficiently (optimized for admin operations)
 * @param ctx - Database context
 * @param sessionId - Session ID to get quickfire data for
 * @returns Object with session details and quickfire statistics
 */
export async function getQuickfireSessionData(
  ctx: any,
  sessionId: Id<"sessions">
): Promise<{
  session: any;
  quickfireCount: number;
  activeQuickfireCount: number;
  totalVotes: number;
  averageScore: number;
} | null> {
  const session = await ctx.db.get(sessionId);
  
  if (!session) {
    return null;
  }

  // Get all quickfires for this session
  const quickfires = await getQuickfiresBySession(ctx, sessionId);
  
  if (quickfires.length === 0) {
    return {
      session,
      quickfireCount: 0,
      activeQuickfireCount: 0,
      totalVotes: 0,
      averageScore: 0
    };
  }

  // Count active quickfires
  const activeQuickfireCount = quickfires.filter(qf => qf.votingActive).length;

  // Get vote statistics for all quickfires in parallel
  const voteStats = await Promise.all(
    quickfires.map(async (quickfire) => {
      const votes = await ctx.db
        .query("quickfireVotes")
        .withIndex("by_quickfire", (q: any) => q.eq("quickfireId", quickfire._id))
        .collect();
      
      return {
        count: votes.length,
        totalScore: votes.reduce((sum: number, vote: any) => sum + vote.score, 0)
      };
    })
  );

  // Calculate aggregated statistics
  const totalVotes = voteStats.reduce((sum, stats) => sum + stats.count, 0);
  const totalScore = voteStats.reduce((sum, stats) => sum + stats.totalScore, 0);
  const averageScore = totalVotes > 0 ? totalScore / totalVotes : 0;

  return {
    session,
    quickfireCount: quickfires.length,
    activeQuickfireCount,
    totalVotes,
    averageScore
  };
}

/**
 * Create a new quickfire item
 * @param ctx - Database context
 * @param data - Quickfire creation data
 * @returns Created quickfire ID
 */
export async function createQuickfire(
  ctx: any,
  data: {
    idea: string;
    comments?: string;
    question?: string;
    eventId: Id<"events">;
    sessionId: Id<"sessions">;
    order?: number;
    votingActive?: boolean;
  }
): Promise<Id<"quickfires">> {
  const now = Date.now();
  return await ctx.db.insert("quickfires", {
    idea: data.idea,
    comments: data.comments || "",
    question: data.question || "",
    eventId: data.eventId,
    sessionId: data.sessionId,
    votingActive: data.votingActive || false,
    order: data.order || 0,
    createdAt: now,
  });
}

/**
 * Update an existing quickfire item
 * @param ctx - Database context
 * @param quickfireId - Quickfire ID to update
 * @param updates - Update data
 * @returns Promise resolving when update completes
 */
export async function updateQuickfire(
  ctx: any,
  quickfireId: Id<"quickfires">,
  updates: {
    idea?: string;
    comments?: string;
    question?: string;
    sessionId?: Id<"sessions">;
    order?: number;
    votingActive?: boolean;
  }
): Promise<void> {
  const updateData: any = {
    ...updates,
    updatedAt: Date.now()
  };

  await ctx.db.patch(quickfireId, updateData);
}

/**
 * Delete a quickfire item
 * @param ctx - Database context
 * @param quickfireId - Quickfire ID to delete
 * @returns Promise resolving when deletion completes
 */
export async function deleteQuickfire(
  ctx: any,
  quickfireId: Id<"quickfires">
): Promise<void> {
  await ctx.db.delete(quickfireId);
}

/**
 * Batch update multiple quickfire items
 * @param ctx - Database context
 * @param updates - Array of update operations
 * @returns Promise resolving when all updates complete
 */
export async function batchUpdateQuickfires(
  ctx: any,
  updates: Array<{
    quickfireId: Id<"quickfires">;
    updates: {
      idea?: string;
      comments?: string;
      question?: string;
      sessionId?: Id<"sessions">;
      order?: number;
      votingActive?: boolean;
    };
  }>
): Promise<void> {
  if (updates.length === 0) {
    return;
  }

  const now = Date.now();
  const updatePromises = updates.map(({ quickfireId, updates: updateData }) => 
    ctx.db.patch(quickfireId, {
      ...updateData,
      updatedAt: now
    })
  );

  await Promise.all(updatePromises);
}

/**
 * Batch delete multiple quickfire items
 * @param ctx - Database context
 * @param quickfireIds - Array of quickfire IDs to delete
 * @returns Promise resolving when all deletions complete
 */
export async function batchDeleteQuickfires(
  ctx: any,
  quickfireIds: Id<"quickfires">[]
): Promise<void> {
  if (quickfireIds.length === 0) {
    return;
  }

  const deletePromises = quickfireIds.map(id => ctx.db.delete(id));
  await Promise.all(deletePromises);
}
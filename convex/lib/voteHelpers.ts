/**
 * Vote operation helpers for standardized database access patterns
 * 
 * These helpers provide consistent patterns for vote-related database operations,
 * including both regular votes and quickfire votes, with optimized query patterns.
 */

import { Id } from "../_generated/dataModel";
import { 
  batchLoadUsers, 
  batchLoadIdeas, 
  batchLoadTeams,
  extractUniqueIds
} from "./batchHelpers";
import { getVotingStatus as getVotingStatusSetting } from "./settingsHelpers";

/**
 * Get vote by ID with error handling
 * @param ctx - Database context
 * @param voteId - Vote ID to retrieve
 * @returns Vote entity or null if not found
 */
export async function getVoteById(
  ctx: any,
  voteId: Id<"votes">
): Promise<any | null> {
  return await ctx.db.get(voteId);
}

/**
 * Get vote by ID or throw error if not found
 * @param ctx - Database context
 * @param voteId - Vote ID to retrieve
 * @returns Vote entity
 * @throws Error if vote not found
 */
export async function getVoteByIdOrThrow(
  ctx: any,
  voteId: Id<"votes">
): Promise<any> {
  const vote = await ctx.db.get(voteId);
  if (!vote) {
    throw new Error(`Vote with ID ${voteId} not found`);
  }
  return vote;
}

/**
 * Get votes by idea using optimized index
 * @param ctx - Database context
 * @param ideaId - Idea ID to get votes for
 * @returns Array of votes for the idea
 */
export async function getVotesByIdea(
  ctx: any,
  ideaId: Id<"ideas">
): Promise<any[]> {
  return await ctx.db
    .query("votes")
    .withIndex("by_idea", (q: any) => q.eq("ideaId", ideaId))
    .collect();
}

/**
 * Get votes by user using optimized index
 * @param ctx - Database context
 * @param userId - User ID to get votes for
 * @returns Array of votes by the user
 */
export async function getVotesByUser(
  ctx: any,
  userId: Id<"users">
): Promise<any[]> {
  return await ctx.db
    .query("votes")
    .withIndex("by_user", (q: any) => q.eq("userId", userId))
    .collect();
}

/**
 * Get votes by event using optimized index
 * @param ctx - Database context
 * @param eventId - Event ID to get votes for
 * @returns Array of votes for the event
 */
export async function getVotesByEvent(
  ctx: any,
  eventId: Id<"events">
): Promise<any[]> {
  return await ctx.db
    .query("votes")
    .withIndex("by_event", (q: any) => q.eq("eventId", eventId))
    .collect();
}

/**
 * Get votes by session using optimized index
 * @param ctx - Database context
 * @param sessionId - Session ID to get votes for
 * @returns Array of votes for the session
 */
export async function getVotesBySession(
  ctx: any,
  sessionId: Id<"sessions">
): Promise<any[]> {
  return await ctx.db
    .query("votes")
    .withIndex("by_session", (q: any) => q.eq("sessionId", sessionId))
    .collect();
}

/**
 * Get user's votes for a specific event
 * @param ctx - Database context
 * @param userId - User ID to get votes for
 * @param eventId - Event ID to filter by
 * @returns Array of user's votes for the event
 */
export async function getUserVotesForEvent(
  ctx: any,
  userId: Id<"users">,
  eventId: Id<"events">
): Promise<any[]> {
  return await ctx.db
    .query("votes")
    .withIndex("by_event_user", (q: any) => q.eq("eventId", eventId).eq("userId", userId))
    .collect();
}

/**
 * Get user's votes for a specific session
 * @param ctx - Database context
 * @param userId - User ID to get votes for
 * @param sessionId - Session ID to filter by
 * @returns Array of user's votes for the session
 */
export async function getUserVotesForSession(
  ctx: any,
  userId: Id<"users">,
  sessionId: Id<"sessions">
): Promise<any[]> {
  return await ctx.db
    .query("votes")
    .withIndex("by_session_user", (q: any) => q.eq("sessionId", sessionId).eq("userId", userId))
    .collect();
}

/**
 * Get user's specific vote for an idea
 * @param ctx - Database context
 * @param userId - User ID to get vote for
 * @param ideaId - Idea ID to get vote for
 * @returns User's vote for the idea or null if not found
 */
export async function getUserVoteForIdea(
  ctx: any,
  userId: Id<"users">,
  ideaId: Id<"ideas">
): Promise<any | null> {
  return await ctx.db
    .query("votes")
    .withIndex("by_user_idea", (q: any) => q.eq("userId", userId).eq("ideaId", ideaId))
    .first();
}

/**
 * Check if user has voted for an idea
 * @param ctx - Database context
 * @param userId - User ID to check
 * @param ideaId - Idea ID to check
 * @returns Boolean indicating if user has voted
 */
export async function hasUserVotedForIdea(
  ctx: any,
  userId: Id<"users">,
  ideaId: Id<"ideas">
): Promise<boolean> {
  const vote = await getUserVoteForIdea(ctx, userId, ideaId);
  return vote !== null;
}

/**
 * Get vote statistics for an idea
 * @param ctx - Database context
 * @param ideaId - Idea ID to get statistics for
 * @returns Object with vote statistics
 */
export async function getVoteStatsForIdea(
  ctx: any,
  ideaId: Id<"ideas">
): Promise<{
  count: number;
  totalScore: number;
  averageScore: number;
  minScore: number;
  maxScore: number;
}> {
  const votes = await getVotesByIdea(ctx, ideaId);
  
  if (votes.length === 0) {
    return {
      count: 0,
      totalScore: 0,
      averageScore: 0,
      minScore: 0,
      maxScore: 0
    };
  }
  
  const scores = votes.map(vote => vote.score);
  const totalScore = scores.reduce((sum, score) => sum + score, 0);
  const averageScore = totalScore / votes.length;
  const minScore = Math.min(...scores);
  const maxScore = Math.max(...scores);
  
  return {
    count: votes.length,
    totalScore,
    averageScore,
    minScore,
    maxScore
  };
}

/**
 * Get vote statistics for an event
 * @param ctx - Database context
 * @param eventId - Event ID to get statistics for
 * @returns Object with event vote statistics
 */
export async function getVoteStatsForEvent(
  ctx: any,
  eventId: Id<"events">
): Promise<{
  totalVotes: number;
  uniqueVoters: number;
  averageScore: number;
  ideasVoted: number;
}> {
  const votes = await getVotesByEvent(ctx, eventId);
  
  if (votes.length === 0) {
    return {
      totalVotes: 0,
      uniqueVoters: 0,
      averageScore: 0,
      ideasVoted: 0
    };
  }
  
  const uniqueVoters = new Set(votes.map(vote => vote.userId.toString())).size;
  const uniqueIdeas = new Set(votes.map(vote => vote.ideaId.toString())).size;
  const totalScore = votes.reduce((sum, vote) => sum + vote.score, 0);
  const averageScore = totalScore / votes.length;
  
  return {
    totalVotes: votes.length,
    uniqueVoters,
    averageScore,
    ideasVoted: uniqueIdeas
  };
}

/**
 * Note: Vote upsert functions are not included in query helpers
 * as they require mutation context. These should be implemented
 * directly in mutation files where needed.
 */

/**
 * Upsert a vote (create or update)
 * @param ctx - Database context
 * @param voteData - Vote data to upsert
 * @returns Object with vote ID, score, and whether it was updated
 */
export async function upsertVote(
  ctx: any,
  voteData: {
    userId: Id<"users">;
    ideaId: Id<"ideas">;
    eventId: Id<"events">;
    sessionId: Id<"sessions">;
    score: number;
    comment?: string;
  }
): Promise<{
  voteId: Id<"votes">;
  score: number;
  updated: boolean;
}> {
  // Check if vote already exists
  const existingVote = await getUserVoteForIdea(ctx, voteData.userId, voteData.ideaId);
  const now = Date.now();
  
  if (existingVote) {
    // Update existing vote
    await ctx.db.patch(existingVote._id, {
      score: voteData.score,
      comment: voteData.comment || undefined,
      updatedAt: now,
    });
    return {
      voteId: existingVote._id,
      score: voteData.score,
      updated: true
    };
  } else {
    // Create new vote
    const voteId = await ctx.db.insert("votes", {
      userId: voteData.userId,
      ideaId: voteData.ideaId,
      eventId: voteData.eventId,
      sessionId: voteData.sessionId,
      score: voteData.score,
      comment: voteData.comment,
      createdAt: now,
      updatedAt: now,
    });
    return {
      voteId,
      score: voteData.score,
      updated: false
    };
  }
}

/**
 * Validate vote access for a user
 * @param ctx - Database context
 * @param userId - User ID to validate
 * @param ideaId - Idea ID to validate
 * @returns Boolean indicating if user can vote
 */
export async function validateVoteAccess(
  ctx: any,
  _userId: Id<"users">,
  ideaId: Id<"ideas">
): Promise<{
  canVote: boolean;
  reason?: string;
}> {
  // Get idea and validate it exists
  const idea = await ctx.db.get(ideaId);
  if (!idea) {
    return { canVote: false, reason: "Idea not found" };
  }
  
  // Check if voting is enabled globally
  const votingStarted = await getVotingStatusSetting(ctx);
  if (!votingStarted) {
    return { canVote: false, reason: "Voting has not started yet" };
  }
  
  // Get session and validate voting is enabled
  const session = await ctx.db.get(idea.sessionId);
  if (!session) {
    return { canVote: false, reason: "Session not found" };
  }
  
  if (!session.settings?.votingEnabled) {
    return { canVote: false, reason: "Voting is not enabled for the current session" };
  }
  
  // Get team and validate voting is enabled
  const team = await ctx.db.get(idea.teamId);
  if (!team) {
    return { canVote: false, reason: "Team not found for this idea" };
  }
  
  if (!team.voting) {
    return { canVote: false, reason: "Voting is not enabled for this team" };
  }
  
  return { canVote: true };
}

/**
 * Get top voted ideas for an event
 * @param ctx - Database context
 * @param eventId - Event ID to get top ideas for
 * @param limit - Maximum number of ideas to return
 * @returns Array of top-voted ideas with statistics
 */
export async function getTopVotedIdeasByEvent(
  ctx: any,
  eventId: Id<"events">,
  limit: number = 10
): Promise<Array<{
  idea: any;
  voteCount: number;
  totalScore: number;
  averageScore: number;
}>> {
  // Get all votes for the event
  const votes = await getVotesByEvent(ctx, eventId);
  
  // Group votes by idea
  const votesByIdea = votes.reduce((acc, vote) => {
    const ideaId = vote.ideaId.toString();
    if (!acc[ideaId]) {
      acc[ideaId] = [];
    }
    acc[ideaId].push(vote);
    return acc;
  }, {} as Record<string, any[]>);
  
  // Calculate statistics for each idea
  const ideaStats = await Promise.all(
    Object.entries(votesByIdea).map(async ([ideaId, ideaVotes]) => {
      const idea = await ctx.db.get(ideaId as Id<"ideas">);
      if (!idea) return null;
      
      const totalScore = (ideaVotes as any[]).reduce((sum: number, vote: any) => sum + vote.score, 0);
      const averageScore = totalScore / (ideaVotes as any[]).length;
      
      return {
        idea,
        voteCount: (ideaVotes as any[]).length,
        totalScore,
        averageScore
      };
    })
  );
  
  // Filter out null results and sort by average score
  return ideaStats
    .filter((stat): stat is NonNullable<typeof stat> => stat !== null)
    .sort((a, b) => b.averageScore - a.averageScore)
    .slice(0, limit);
}

/**
 * Get votes with enriched data (user, idea, team details)
 * @param ctx - Database context
 * @param votes - Array of vote objects
 * @returns Array of votes with enriched data
 */
export async function enrichVotesWithDetails(
  ctx: any,
  votes: any[]
): Promise<any[]> {
  if (votes.length === 0) {
    return [];
  }
  
  // Extract unique IDs for batch loading
  const userIds = extractUniqueIds(votes, "userId");
  const ideaIds = extractUniqueIds(votes, "ideaId");
  
  // Batch load users and ideas
  const [users, ideas] = await Promise.all([
    batchLoadUsers(ctx, userIds),
    batchLoadIdeas(ctx, ideaIds)
  ]);
  
  // Get team IDs from ideas for additional enrichment
  const teamIds = Array.from(ideas.values())
    .filter(idea => idea !== null)
    .map(idea => idea.teamId);
  
  const teams = await batchLoadTeams(ctx, teamIds);
  
  // Enrich votes with details
  return votes.map(vote => {
    const user = users.get(vote.userId);
    const idea = ideas.get(vote.ideaId);
    const team = idea ? teams.get(idea.teamId) : null;
    
    return {
      ...vote,
      userName: user?.name || "Unknown",
      userUsername: user?.username || "Unknown",
      ideaName: idea?.name || "Unknown Idea",
      teamName: team?.name || "Unknown Team"
    };
  });
}

/**
 * Get voting summary for analytics
 * @param ctx - Database context
 * @param eventId - Event ID to get summary for
 * @returns Object with comprehensive voting statistics
 */
export async function getVotingSummary(
  ctx: any,
  eventId: Id<"events">
): Promise<{
  totalVotes: number;
  uniqueVoters: number;
  uniqueIdeas: number;
  averageScore: number;
  participationRate: number;
  topIdeas: Array<{
    ideaName: string;
    teamName: string;
    voteCount: number;
    averageScore: number;
  }>;
}> {
  // Get all votes for the event
  const votes = await getVotesByEvent(ctx, eventId);
  
  // Get event users for participation rate calculation
  const allUsers = await ctx.db.query("users").collect();
  const eventUsers = allUsers.filter((user: any) => 
    user.events?.some((event: any) => event.eventId === eventId)
  );
  
  const uniqueVoters = new Set(votes.map(vote => vote.userId.toString())).size;
  const uniqueIdeas = new Set(votes.map(vote => vote.ideaId.toString())).size;
  const totalScore = votes.reduce((sum, vote) => sum + vote.score, 0);
  const averageScore = votes.length > 0 ? totalScore / votes.length : 0;
  const participationRate = eventUsers.length > 0 ? (uniqueVoters / eventUsers.length) * 100 : 0;
  
  // Get top 5 ideas
  const topIdeas = await getTopVotedIdeasByEvent(ctx, eventId, 5);
  const topIdeasWithNames = await Promise.all(
    topIdeas.map(async (item) => {
      const team = await ctx.db.get(item.idea.teamId);
      return {
        ideaName: item.idea.name,
        teamName: team?.name || "Unknown Team",
        voteCount: item.voteCount,
        averageScore: item.averageScore
      };
    })
  );
  
  return {
    totalVotes: votes.length,
    uniqueVoters,
    uniqueIdeas,
    averageScore,
    participationRate,
    topIdeas: topIdeasWithNames
  };
}

/**
 * Get all votes for analytics/export
 * @param ctx - Database context
 * @returns Array of all votes
 */
export async function getAllVotes(ctx: any): Promise<any[]> {
  const votes = await ctx.db.query("votes").collect();
  
  // Log warning for large collections
  if (votes.length > 5000) {
    console.warn(`Large votes collection: ${votes.length} votes`);
  }
  
  return votes;
}

// Quickfire vote helpers

/**
 * Get quickfire vote by user and quickfire
 * @param ctx - Database context
 * @param userId - User ID
 * @param quickfireId - Quickfire ID
 * @returns Quickfire vote or null if not found
 */
export async function getQuickfireVoteByUserAndQuickfire(
  ctx: any,
  userId: Id<"users">,
  quickfireId: Id<"quickfires">
): Promise<any | null> {
  return await ctx.db
    .query("quickfireVotes")
    .withIndex("by_user_quickfire", (q: any) => q.eq("userId", userId).eq("quickfireId", quickfireId))
    .first();
}

/**
 * Get quickfire votes by quickfire
 * @param ctx - Database context
 * @param quickfireId - Quickfire ID
 * @returns Array of quickfire votes
 */
export async function getQuickfireVotesByQuickfire(
  ctx: any,
  quickfireId: Id<"quickfires">
): Promise<any[]> {
  return await ctx.db
    .query("quickfireVotes")
    .withIndex("by_quickfire", (q: any) => q.eq("quickfireId", quickfireId))
    .collect();
}

/**
 * Get quickfire votes by user
 * @param ctx - Database context
 * @param userId - User ID to get votes for
 * @returns Array of quickfire votes by the user
 */
export async function getQuickfireVotesByUser(
  ctx: any,
  userId: Id<"users">
): Promise<any[]> {
  return await ctx.db
    .query("quickfireVotes")
    .withIndex("by_user", (q: any) => q.eq("userId", userId))
    .collect();
}

/**
 * Quickfire Vote Mutation Helpers
 * These functions handle create, update, and upsert operations for quickfire votes
 */

/**
 * Create a new quickfire vote
 * @param ctx - Database context
 * @param args - Vote data for creation
 * @returns Vote ID
 */
export async function createQuickfireVote(
  ctx: any,
  args: {
    userId: Id<"users">;
    quickfireId: Id<"quickfires">;
    score: number;
    voter: string;
  }
): Promise<Id<"quickfireVotes">> {
  return await ctx.db.insert("quickfireVotes", {
    userId: args.userId,
    quickfireId: args.quickfireId,
    score: args.score,
    voter: args.voter,
    createdAt: Date.now(),
  });
}

/**
 * Update an existing quickfire vote
 * @param ctx - Database context
 * @param voteId - Vote ID to update
 * @param args - Update data
 * @returns void
 */
export async function updateQuickfireVote(
  ctx: any,
  voteId: Id<"quickfireVotes">,
  args: {
    score: number;
  }
): Promise<void> {
  await ctx.db.patch(voteId, {
    score: args.score,
    updatedAt: Date.now(),
  });
}

/**
 * Upsert a quickfire vote (create or update)
 * @param ctx - Database context
 * @param args - Vote data to upsert
 * @returns Object with vote ID and whether it was updated
 */
export async function upsertQuickfireVote(
  ctx: any,
  args: {
    userId: Id<"users">;
    quickfireId: Id<"quickfires">;
    score: number;
    voter: string;
  }
): Promise<{
  voteId: Id<"quickfireVotes">;
  updated: boolean;
}> {
  // Check if vote already exists
  const existingVote = await getQuickfireVoteByUserAndQuickfire(ctx, args.userId, args.quickfireId);
  
  if (existingVote) {
    // Update existing vote
    await updateQuickfireVote(ctx, existingVote._id, {
      score: args.score,
    });
    return {
      voteId: existingVote._id,
      updated: true
    };
  } else {
    // Create new vote
    const voteId = await createQuickfireVote(ctx, args);
    return {
      voteId,
      updated: false
    };
  }
}

/**
 * Batch load quickfire votes for multiple quickfires
 * @param ctx - Database context
 * @param quickfireIds - Array of quickfire IDs
 * @returns Map of quickfire ID to array of votes
 */
export async function batchLoadQuickfireVotes(
  ctx: any,
  quickfireIds: Id<"quickfires">[]
): Promise<Map<Id<"quickfires">, any[]>> {
  const result = new Map<Id<"quickfires">, any[]>();
  
  if (quickfireIds.length === 0) {
    return result;
  }
  
  // Get all votes for all quickfires in one query
  const allVotes = await ctx.db.query("quickfireVotes").collect();
  
  // Group votes by quickfire ID
  const quickfireIdStrings = new Set(quickfireIds.map(id => id.toString()));
  const relevantVotes = allVotes.filter((vote: any) => 
    quickfireIdStrings.has(vote.quickfireId.toString())
  );
  
  // Initialize result map
  quickfireIds.forEach(id => {
    result.set(id, []);
  });
  
  // Group votes by quickfire
  relevantVotes.forEach((vote: any) => {
    const votes = result.get(vote.quickfireId);
    if (votes) {
      votes.push(vote);
    }
  });
  
  return result;
}

/**
 * Get user's quickfire votes for multiple quickfires
 * @param ctx - Database context
 * @param userId - User ID
 * @param quickfireIds - Array of quickfire IDs
 * @returns Map of quickfire ID to user's vote
 */
export async function getUserQuickfireVotesForQuickfires(
  ctx: any,
  userId: Id<"users">,
  quickfireIds: Id<"quickfires">[]
): Promise<Map<Id<"quickfires">, any | null>> {
  const result = new Map<Id<"quickfires">, any | null>();
  
  if (quickfireIds.length === 0) {
    return result;
  }
  
  // Get all user votes
  const userVotes = await ctx.db
    .query("quickfireVotes")
    .withIndex("by_user", (q: any) => q.eq("userId", userId))
    .collect();
  
  // Create lookup map
  const votesByQuickfire = new Map<string, any>();
  userVotes.forEach((vote: any) => {
    votesByQuickfire.set(vote.quickfireId.toString(), vote);
  });
  
  // Map results
  quickfireIds.forEach(id => {
    result.set(id, votesByQuickfire.get(id.toString()) || null);
  });
  
  return result;
}

/**
 * Get all quickfire votes for analytics
 * @param ctx - Database context
 * @returns Array of all quickfire votes
 */
export async function getAllQuickfireVotes(ctx: any): Promise<any[]> {
  return await ctx.db.query("quickfireVotes").collect();
}

/**
 * Batch load quickfire votes statistics for multiple quickfires efficiently
 * @param ctx - Database context
 * @param quickfireIds - Array of quickfire IDs
 * @returns Map of quickfire ID to vote statistics
 */
export async function batchLoadQuickfireVoteStats(
  ctx: any,
  quickfireIds: Id<"quickfires">[]
): Promise<Map<Id<"quickfires">, {
  count: number;
  totalScore: number;
  averageScore: number;
  minScore: number;
  maxScore: number;
}>> {
  const result = new Map();
  
  if (quickfireIds.length === 0) {
    return result;
  }
  
  // Get all quickfire votes in one query
  const allVotes = await ctx.db.query("quickfireVotes").collect();
  
  // Group votes by quickfire ID
  const votesByQuickfire = new Map<string, any[]>();
  const quickfireIdStrings = new Set(quickfireIds.map(id => id.toString()));
  
  allVotes.forEach((vote: any) => {
    const quickfireIdStr = vote.quickfireId.toString();
    if (quickfireIdStrings.has(quickfireIdStr)) {
      if (!votesByQuickfire.has(quickfireIdStr)) {
        votesByQuickfire.set(quickfireIdStr, []);
      }
      votesByQuickfire.get(quickfireIdStr)!.push(vote);
    }
  });
  
  // Calculate statistics for each quickfire
  quickfireIds.forEach(id => {
    const votes = votesByQuickfire.get(id.toString()) || [];
    
    if (votes.length === 0) {
      result.set(id, {
        count: 0,
        totalScore: 0,
        averageScore: 0,
        minScore: 0,
        maxScore: 0
      });
    } else {
      const scores = votes.map(vote => vote.score);
      const totalScore = scores.reduce((sum, score) => sum + score, 0);
      const averageScore = totalScore / votes.length;
      const minScore = Math.min(...scores);
      const maxScore = Math.max(...scores);
      
      result.set(id, {
        count: votes.length,
        totalScore,
        averageScore,
        minScore,
        maxScore
      });
    }
  });
  
  return result;
}

/**
 * Batch delete quickfire votes
 * @param ctx - Database context
 * @param voteIds - Array of vote IDs to delete
 * @returns Promise resolving when all deletions complete
 */
export async function batchDeleteQuickfireVotes(
  ctx: any,
  voteIds: Id<"quickfireVotes">[]
): Promise<void> {
  if (voteIds.length === 0) {
    return;
  }

  const deletePromises = voteIds.map(id => ctx.db.delete(id));
  await Promise.all(deletePromises);
}

/**
 * Get quickfire votes with aggregated statistics efficiently
 * @param ctx - Database context
 * @param quickfireId - Quickfire ID to get votes and stats for
 * @returns Object with votes array and aggregated statistics
 */
export async function getQuickfireVotesWithStats(
  ctx: any,
  quickfireId: Id<"quickfires">
): Promise<{
  votes: any[];
  stats: {
    count: number;
    totalScore: number;
    averageScore: number;
    minScore: number;
    maxScore: number;
  };
}> {
  const votes = await getQuickfireVotesByQuickfire(ctx, quickfireId);
  
  if (votes.length === 0) {
    return {
      votes: [],
      stats: {
        count: 0,
        totalScore: 0,
        averageScore: 0,
        minScore: 0,
        maxScore: 0
      }
    };
  }
  
  const scores = votes.map(vote => vote.score);
  const totalScore = scores.reduce((sum, score) => sum + score, 0);
  const averageScore = totalScore / votes.length;
  const minScore = Math.min(...scores);
  const maxScore = Math.max(...scores);
  
  return {
    votes,
    stats: {
      count: votes.length,
      totalScore,
      averageScore,
      minScore,
      maxScore
    }
  };
}

/**
 * Batch load votes for multiple ideas
 * @param ctx - Database context
 * @param ideaIds - Array of idea IDs
 * @returns Map of idea ID to array of votes
 */
export async function batchLoadVotesByIdea(
  ctx: any,
  ideaIds: Id<"ideas">[]
): Promise<Map<Id<"ideas">, any[]>> {
  const result = new Map<Id<"ideas">, any[]>();
  
  if (ideaIds.length === 0) {
    return result;
  }
  
  // Get all votes for all ideas in one query
  const allVotes = await ctx.db.query("votes").collect();
  
  // Group votes by idea ID
  const ideaIdStrings = new Set(ideaIds.map(id => id.toString()));
  const relevantVotes = allVotes.filter((vote: any) => 
    ideaIdStrings.has(vote.ideaId.toString())
  );
  
  // Initialize result map
  ideaIds.forEach(id => {
    result.set(id, []);
  });
  
  // Group votes by idea
  relevantVotes.forEach((vote: any) => {
    const votes = result.get(vote.ideaId);
    if (votes) {
      votes.push(vote);
    }
  });
  
  return result;
}
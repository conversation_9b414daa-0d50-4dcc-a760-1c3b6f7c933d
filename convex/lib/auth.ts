/**
 * Unified Authentication Framework
 * 
 * This module provides a unified authentication system that standardizes
 * authentication patterns across all Convex functions while maintaining
 * intentional error handling distinctions for different contexts.
 */

import { ConvexError } from "convex/values";
import { 
  getUserByUsername, 
  getUserByUsernameOrThrow, 
  getUserById, 
  getUserByIdOrThrow,
  validateAdminUser,
  validateAdminUserWithError,
  validateAdminUserWithConvexError,
  validateAdminOrTeamLeadUser
} from "./userHelpers";

/**
 * Authentication context type for consistent auth data
 */
export type AuthContext = {
  user: any;
  role: "admin" | "teamLead" | "teamMember";
  isAuthenticated: boolean;
  hasAdminAccess: boolean;
  hasTeamLeadAccess: boolean;
};

/**
 * Authentication result type for standardized responses
 */
export type AuthResult<T = any> = {
  success: boolean;
  user?: T;
  error?: string;
  code?: string;
};

/**
 * Authentication error codes
 */
export const AUTH_ERROR_CODES = {
  USER_NOT_FOUND: "USER_NOT_FOUND",
  ACCESS_DENIED: "ACCESS_DENIED",
  INSUFFICIENT_PRIVILEGES: "INSUFFICIENT_PRIVILEGES",
  INVALID_CREDENTIALS: "INVALID_CREDENTIALS",
  AUTHENTICATION_REQUIRED: "AUTHENTICATION_REQUIRED"
} as const;

/**
 * Basic authentication - validates user exists and is approved
 * @param ctx - Convex context object
 * @param username - Username to authenticate
 * @param useConvexError - Whether to use ConvexError (default: true)
 * @returns AuthContext with user information
 */
export async function requireAuth(
  ctx: any, 
  username: string, 
  useConvexError: boolean = true
): Promise<AuthContext> {
  const user = useConvexError 
    ? await getUserByUsernameOrThrow(ctx, username)
    : await getUserByUsername(ctx, username);

  if (!user) {
    if (useConvexError) {
      throw new ConvexError({
        message: "User not found. Please check your login credentials.",
        code: AUTH_ERROR_CODES.USER_NOT_FOUND
      });
    } else {
      throw new Error("User not found");
    }
  }

  if (user.status !== "approved") {
    const message = "Access denied. User account is not approved.";
    if (useConvexError) {
      throw new ConvexError({
        message,
        code: AUTH_ERROR_CODES.ACCESS_DENIED
      });
    } else {
      throw new Error(message);
    }
  }

  return {
    user,
    role: user.role,
    isAuthenticated: true,
    hasAdminAccess: user.role === "admin",
    hasTeamLeadAccess: user.role === "admin" || user.role === "teamLead"
  };
}

/**
 * Admin authentication - validates user has admin privileges
 * @param ctx - Convex context object
 * @param username - Username to authenticate
 * @param useConvexError - Whether to use ConvexError (default: true for user-facing, false for internal)
 * @returns AuthContext with admin user information
 */
export async function requireAdmin(
  ctx: any, 
  username: string, 
  useConvexError: boolean = true
): Promise<AuthContext> {
  const user = useConvexError 
    ? await validateAdminUserWithConvexError(ctx, username)
    : await validateAdminUserWithError(ctx, username);

  return {
    user,
    role: user.role,
    isAuthenticated: true,
    hasAdminAccess: true,
    hasTeamLeadAccess: true
  };
}

/**
 * Role-based authentication - validates user has specific role
 * @param ctx - Convex context object
 * @param username - Username to authenticate
 * @param requiredRole - Required role ("admin", "teamLead", or "teamMember")
 * @param useConvexError - Whether to use ConvexError (default: true)
 * @returns AuthContext with user information
 */
export async function requireRole(
  ctx: any, 
  username: string, 
  requiredRole: "admin" | "teamLead" | "teamMember", 
  useConvexError: boolean = true
): Promise<AuthContext> {
  const authContext = await requireAuth(ctx, username, useConvexError);

  // Admin can access everything
  if (authContext.user.role === "admin") {
    return authContext;
  }

  // Team lead can access team member functions
  if (requiredRole === "teamMember" && authContext.user.role === "teamLead") {
    return authContext;
  }

  // Check exact role match
  if (authContext.user.role !== requiredRole) {
    const message = `Access denied. ${requiredRole} privileges required.`;
    if (useConvexError) {
      throw new ConvexError({
        message,
        code: AUTH_ERROR_CODES.INSUFFICIENT_PRIVILEGES
      });
    } else {
      throw new Error(message);
    }
  }

  return authContext;
}

/**
 * Ownership authentication - validates user owns a resource or has admin access
 * @param ctx - Convex context object
 * @param username - Username to authenticate
 * @param resourceUserId - User ID of resource owner
 * @param useConvexError - Whether to use ConvexError (default: true)
 * @returns AuthContext with user information
 */
export async function requireOwnership(
  ctx: any, 
  username: string, 
  resourceUserId: string, 
  useConvexError: boolean = true
): Promise<AuthContext> {
  const authContext = await requireAuth(ctx, username, useConvexError);

  // Admin can access everything
  if (authContext.user.role === "admin") {
    return authContext;
  }

  // Check ownership
  if (authContext.user._id !== resourceUserId) {
    const message = "Access denied. You can only access your own resources.";
    if (useConvexError) {
      throw new ConvexError({
        message,
        code: AUTH_ERROR_CODES.ACCESS_DENIED
      });
    } else {
      throw new Error(message);
    }
  }

  return authContext;
}

/**
 * Team lead or admin authentication - validates user has team lead or admin privileges
 * @param ctx - Convex context object
 * @param username - Username to authenticate
 * @param useConvexError - Whether to use ConvexError (default: true)
 * @returns AuthContext with user information
 */
export async function requireTeamLeadOrAdmin(
  ctx: any, 
  username: string, 
  useConvexError: boolean = true
): Promise<AuthContext> {
  if (useConvexError) {
    const user = await validateAdminOrTeamLeadUser(ctx, username);
    return {
      user,
      role: user.role,
      isAuthenticated: true,
      hasAdminAccess: user.role === "admin",
      hasTeamLeadAccess: true
    };
  } else {
    const user = await getUserByUsername(ctx, username);
    if (!user) {
      throw new Error("User not found");
    }
    if (user.role !== "admin" && user.role !== "teamLead") {
      throw new Error("Access denied. Administrator or team lead privileges required.");
    }
    return {
      user,
      role: user.role,
      isAuthenticated: true,
      hasAdminAccess: user.role === "admin",
      hasTeamLeadAccess: true
    };
  }
}

/**
 * Safe authentication - returns AuthResult instead of throwing
 * @param ctx - Convex context object
 * @param username - Username to authenticate
 * @returns AuthResult with success status and user data
 */
export async function safeAuth(ctx: any, username: string): Promise<AuthResult> {
  try {
    const authContext = await requireAuth(ctx, username, false);
    return {
      success: true,
      user: authContext.user
    };
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : "Authentication failed",
      code: AUTH_ERROR_CODES.AUTHENTICATION_REQUIRED
    };
  }
}

/**
 * Validates user ID parameter and returns user
 * @param ctx - Convex context object
 * @param userId - User ID to validate
 * @param useConvexError - Whether to use ConvexError (default: true)
 * @returns User object
 */
export async function validateUserId(
  ctx: any, 
  userId: string, 
  useConvexError: boolean = true
) {
  return useConvexError 
    ? await getUserByIdOrThrow(ctx, userId)
    : await getUserById(ctx, userId);
}

/**
 * Helper function to determine appropriate error handling based on context
 * @param context - Context type ("admin-internal", "admin-user", "user")
 * @returns Whether to use ConvexError
 */
export function shouldUseConvexError(context: "admin-internal" | "admin-user" | "user"): boolean {
  return context !== "admin-internal";
}

/**
 * Legacy compatibility wrapper for existing validateAdminUser calls
 * @param ctx - Convex context object
 * @param username - Username to validate
 * @returns User object if admin
 * @throws ConvexError if user is not found or not admin
 */
export async function validateAdminUserLegacy(ctx: any, username: string) {
  return await validateAdminUser(ctx, username);
}

/**
 * Legacy compatibility wrapper for existing validateAdminUserWithError calls
 * @param ctx - Convex context object
 * @param username - Username to validate
 * @returns User object if admin
 * @throws Error if user is not found or not admin
 */
export async function validateAdminUserWithErrorLegacy(ctx: any, username: string) {
  return await validateAdminUserWithError(ctx, username);
}
/**
 * Snippet management helpers for standardized operations and performance optimization
 * 
 * These helpers provide consistent patterns for snippet operations, improve query performance,
 * and reduce code duplication across snippet-related functions.
 */

import { QueryCtx, MutationCtx } from "../_generated/server";
import { Id } from "../_generated/dataModel";
import { 
  QueryResponse, 
  CreateResponse, 
  UpdateResponse, 
  DeleteResponse, 
  ErrorCode 
} from "./responseTypes";
import { 
  createCreateResponse, 
  createUpdateResponse, 
  createDeleteResponse,
  throwConvexError 
} from "./responseUtils";

/**
 * Standard snippet interface
 */
export interface SnippetWithMetadata {
  _id: Id<"snippets">;
  _creationTime: number;
  name: string;
  content: string;
  createdAt: number;
  updatedAt?: number;
  // Additional computed fields
  contentLength?: number;
  contentPreview?: string;
  lastModified?: string;
}

/**
 * Batch load multiple snippets by IDs
 * @param ctx - Database context
 * @param snippetIds - Array of snippet IDs
 * @returns Map of snippet ID to snippet object
 */
export async function batchLoadSnippets(
  ctx: QueryCtx,
  snippetIds: Id<"snippets">[]
): Promise<Map<Id<"snippets">, any | null>> {
  const results = new Map<Id<"snippets">, any | null>();
  
  // Remove duplicates and filter out invalid IDs
  const uniqueIds = Array.from(new Set(snippetIds)).filter(id => id != null);
  
  if (uniqueIds.length === 0) {
    return results;
  }
  
  // Batch load all snippets
  const snippets = await Promise.all(
    uniqueIds.map(id => ctx.db.get(id))
  );
  
  // Map results back to IDs
  uniqueIds.forEach((id, index) => {
    results.set(id, snippets[index]);
  });
  
  return results;
}

/**
 * Get all snippets with enhanced metadata and sorting
 * @param ctx - Database context
 * @returns Array of snippets with metadata
 */
export async function getAllSnippetsWithMetadata(
  ctx: QueryCtx
): Promise<QueryResponse<SnippetWithMetadata[]>> {
  const snippets = await ctx.db
    .query("snippets")
    .order("asc")
    .collect();
  
  // Enrich with metadata
  const enrichedSnippets = snippets.map(snippet => ({
    ...snippet,
    contentLength: snippet.content.length,
    contentPreview: snippet.content.substring(0, 100) + (snippet.content.length > 100 ? '...' : ''),
    lastModified: new Date(snippet.updatedAt || snippet.createdAt).toISOString()
  }));
  
  // Sort by name (case-insensitive)
  return enrichedSnippets.sort((a, b) => a.name.toLowerCase().localeCompare(b.name.toLowerCase()));
}

/**
 * Efficient snippet name validation with caching
 * @param ctx - Database context
 * @param name - Snippet name to validate
 * @param excludeId - ID to exclude from validation (for updates)
 * @returns Whether the name exists
 */
export async function validateSnippetName(
  ctx: QueryCtx,
  name: string,
  excludeId?: Id<"snippets">
): Promise<boolean> {
  const trimmedName = name.trim();
  
  if (!trimmedName) {
    return false; // Empty names are invalid
  }
  
  const existingSnippet = await ctx.db
    .query("snippets")
    .withIndex("by_name", (q) => q.eq("name", trimmedName))
    .first();
  
  if (!existingSnippet) {
    return true; // Name is available
  }
  
  // If excludeId is provided and matches, name is available for update
  if (excludeId && existingSnippet._id === excludeId) {
    return true;
  }
  
  return false; // Name is taken
}

/**
 * Create a new snippet with validation and error handling
 * @param ctx - Database context
 * @param name - Snippet name
 * @param content - Snippet content
 * @returns Create response
 */
export async function createSnippetWithValidation(
  ctx: MutationCtx,
  name: string,
  content: string
): Promise<CreateResponse<"snippets">> {
  const trimmedName = name.trim();
  
  if (!trimmedName) {
    throwConvexError(
      "Snippet name cannot be empty",
      ErrorCode.VALIDATION_ERROR
    );
  }
  
  if (!content.trim()) {
    throwConvexError(
      "Snippet content cannot be empty",
      ErrorCode.VALIDATION_ERROR
    );
  }
  
  // Check for existing snippet with the same name
  const nameAvailable = await validateSnippetName(ctx, trimmedName);
  if (!nameAvailable) {
    throwConvexError(
      `Snippet with name "${trimmedName}" already exists`,
      ErrorCode.DUPLICATE_ENTRY
    );
  }
  
  const now = Date.now();
  const snippetId = await ctx.db.insert("snippets", {
    name: trimmedName,
    content: content,
    createdAt: now,
    updatedAt: now,
  });
  
  return createCreateResponse(snippetId);
}

/**
 * Update a snippet with validation and error handling
 * @param ctx - Database context
 * @param snippetId - Snippet ID to update
 * @param name - New name (optional)
 * @param content - New content (optional)
 * @returns Update response
 */
export async function updateSnippetWithValidation(
  ctx: MutationCtx,
  snippetId: Id<"snippets">,
  name?: string,
  content?: string
): Promise<UpdateResponse> {
  // Get the existing snippet
  const existingSnippet = await ctx.db.get(snippetId);
  if (!existingSnippet) {
    throwConvexError(
      "Snippet not found",
      ErrorCode.RESOURCE_NOT_FOUND
    );
  }
  
  // Validate name if provided
  if (name !== undefined) {
    const trimmedName = name.trim();
    if (!trimmedName) {
      throwConvexError(
        "Snippet name cannot be empty",
        ErrorCode.VALIDATION_ERROR
      );
    }
    
    if (trimmedName !== existingSnippet.name) {
      const nameAvailable = await validateSnippetName(ctx, trimmedName, snippetId);
      if (!nameAvailable) {
        throwConvexError(
          `Another snippet with the name "${trimmedName}" already exists`,
          ErrorCode.DUPLICATE_ENTRY
        );
      }
    }
  }
  
  // Validate content if provided
  if (content !== undefined && !content.trim()) {
    throwConvexError(
      "Snippet content cannot be empty",
      ErrorCode.VALIDATION_ERROR
    );
  }
  
  // Build update object
  const updateData: any = {
    updatedAt: Date.now(),
  };
  
  if (name !== undefined) {
    updateData.name = name.trim();
  }
  if (content !== undefined) {
    updateData.content = content;
  }
  
  await ctx.db.patch(snippetId, updateData);
  return createUpdateResponse(true);
}

/**
 * Delete a snippet with validation
 * @param ctx - Database context
 * @param snippetId - Snippet ID to delete
 * @returns Delete response
 */
export async function deleteSnippetWithValidation(
  ctx: MutationCtx,
  snippetId: Id<"snippets">
): Promise<DeleteResponse> {
  const snippet = await ctx.db.get(snippetId);
  if (!snippet) {
    throwConvexError(
      "Snippet not found",
      ErrorCode.RESOURCE_NOT_FOUND
    );
  }
  
  await ctx.db.delete(snippetId);
  return createDeleteResponse(true);
}

/**
 * Get snippet by ID with enhanced error handling
 * @param ctx - Database context
 * @param snippetId - Snippet ID
 * @returns Snippet object or null
 */
export async function getSnippetByIdWithValidation(
  ctx: QueryCtx,
  snippetId: Id<"snippets">
): Promise<QueryResponse<any | null>> {
  const snippet = await ctx.db.get(snippetId);
  if (!snippet) {
    return null;
  }
  
  return {
    ...snippet,
    contentLength: snippet.content.length,
    contentPreview: snippet.content.substring(0, 100) + (snippet.content.length > 100 ? '...' : ''),
    lastModified: new Date(snippet.updatedAt || snippet.createdAt).toISOString()
  };
}

/**
 * Search snippets by name or content
 * @param ctx - Database context
 * @param searchTerm - Search term
 * @param limit - Maximum results to return
 * @returns Array of matching snippets
 */
export async function searchSnippets(
  ctx: QueryCtx,
  searchTerm: string,
  limit: number = 10
): Promise<QueryResponse<SnippetWithMetadata[]>> {
  const allSnippets = await getAllSnippetsWithMetadata(ctx);
  
  if (!searchTerm.trim()) {
    return allSnippets.slice(0, limit);
  }
  
  const term = searchTerm.toLowerCase();
  const matchingSnippets = allSnippets.filter(snippet => 
    snippet.name.toLowerCase().includes(term) || 
    snippet.content.toLowerCase().includes(term)
  );
  
  return matchingSnippets.slice(0, limit);
}

/**
 * Get snippet statistics
 * @param ctx - Database context
 * @returns Statistics about snippets
 */
export async function getSnippetStatistics(
  ctx: QueryCtx
): Promise<QueryResponse<{
  totalSnippets: number;
  averageContentLength: number;
  longestSnippet: { name: string; length: number } | null;
  shortestSnippet: { name: string; length: number } | null;
  recentlyCreated: any[];
  recentlyUpdated: any[];
}>> {
  const snippets = await ctx.db.query("snippets").collect();
  
  if (snippets.length === 0) {
    return {
      totalSnippets: 0,
      averageContentLength: 0,
      longestSnippet: null,
      shortestSnippet: null,
      recentlyCreated: [],
      recentlyUpdated: []
    };
  }
  
  const contentLengths = snippets.map(s => s.content.length);
  const averageContentLength = contentLengths.reduce((a, b) => a + b, 0) / contentLengths.length;
  
  const longestSnippet = snippets.reduce((longest, current) => 
    current.content.length > longest.content.length ? current : longest
  );
  
  const shortestSnippet = snippets.reduce((shortest, current) => 
    current.content.length < shortest.content.length ? current : shortest
  );
  
  const recentlyCreated = snippets
    .sort((a, b) => b.createdAt - a.createdAt)
    .slice(0, 5);
  
  const recentlyUpdated = snippets
    .filter(s => s.updatedAt)
    .sort((a, b) => (b.updatedAt || 0) - (a.updatedAt || 0))
    .slice(0, 5);
  
  return {
    totalSnippets: snippets.length,
    averageContentLength: Math.round(averageContentLength),
    longestSnippet: { name: longestSnippet.name, length: longestSnippet.content.length },
    shortestSnippet: { name: shortestSnippet.name, length: shortestSnippet.content.length },
    recentlyCreated,
    recentlyUpdated
  };
}

/**
 * Batch create multiple snippets
 * @param ctx - Database context
 * @param snippets - Array of snippet data
 * @returns Array of created snippet IDs
 */
export async function batchCreateSnippets(
  ctx: MutationCtx,
  snippets: Array<{ name: string; content: string }>
): Promise<Id<"snippets">[]> {
  const results: Id<"snippets">[] = [];
  const now = Date.now();
  
  // Validate all snippets first
  for (const snippet of snippets) {
    const trimmedName = snippet.name.trim();
    if (!trimmedName) {
      throwConvexError(
        "All snippet names must be provided",
        ErrorCode.VALIDATION_ERROR
      );
    }
    
    if (!snippet.content.trim()) {
      throwConvexError(
        "All snippet contents must be provided",
        ErrorCode.VALIDATION_ERROR
      );
    }
    
    const nameAvailable = await validateSnippetName(ctx, trimmedName);
    if (!nameAvailable) {
      throwConvexError(
        `Snippet with name "${trimmedName}" already exists`,
        ErrorCode.DUPLICATE_ENTRY
      );
    }
  }
  
  // Create all snippets
  for (const snippet of snippets) {
    const snippetId = await ctx.db.insert("snippets", {
      name: snippet.name.trim(),
      content: snippet.content,
      createdAt: now,
      updatedAt: now,
    });
    results.push(snippetId);
  }
  
  return results;
}

/**
 * Batch delete multiple snippets
 * @param ctx - Database context
 * @param snippetIds - Array of snippet IDs to delete
 * @returns Number of deleted snippets
 */
export async function batchDeleteSnippets(
  ctx: MutationCtx,
  snippetIds: Id<"snippets">[]
): Promise<number> {
  let deletedCount = 0;
  
  // Validate all snippets exist first
  const snippets = await batchLoadSnippets(ctx, snippetIds);
  
  for (const id of snippetIds) {
    const snippet = snippets.get(id);
    if (!snippet) {
      throwConvexError(
        `Snippet with ID ${id} not found`,
        ErrorCode.RESOURCE_NOT_FOUND
      );
    }
  }
  
  // Delete all snippets
  for (const id of snippetIds) {
    await ctx.db.delete(id);
    deletedCount++;
  }
  
  return deletedCount;
}
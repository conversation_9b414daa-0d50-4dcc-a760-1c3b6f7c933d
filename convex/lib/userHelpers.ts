/**
 * Shared User Database Query Utilities
 * 
 * This module provides centralized user lookup and batch loading functions
 * to eliminate code duplication and improve performance across the codebase.
 */

import { ConvexError } from "convex/values";

/**
 * Retrieves a user by username with proper error handling
 * @param ctx - Convex context object
 * @param username - Username to look up
 * @returns User object or null if not found
 */
export async function getUserByUsername(ctx: any, username: string) {
  return await ctx.db
    .query("users")
    .withIndex("by_username", (q: any) => q.eq("username", username))
    .first();
}

/**
 * Retrieves a user by username and throws ConvexError if not found
 * @param ctx - Convex context object
 * @param username - Username to look up
 * @returns User object
 * @throws ConvexError with message "User not found" if user doesn't exist
 */
export async function getUserByUsernameOrThrow(ctx: any, username: string) {
  const user = await getUserByUsername(ctx, username);
  if (!user) {
    throw new ConvexError({
      message: "User not found. Please check your login credentials.",
      code: "USER_NOT_FOUND"
    });
  }
  return user;
}

/**
 * Retrieves a user by ID with null safety
 * @param ctx - Convex context object
 * @param userId - User ID to look up
 * @returns User object or null if not found
 */
export async function getUserById(ctx: any, userId: string) {
  return await ctx.db.get(userId);
}

/**
 * Retrieves a user by ID and throws ConvexError if not found
 * @param ctx - Convex context object
 * @param userId - User ID to look up
 * @returns User object
 * @throws ConvexError with message "User not found" if user doesn't exist
 */
export async function getUserByIdOrThrow(ctx: any, userId: string) {
  const user = await getUserById(ctx, userId);
  if (!user) {
    throw new ConvexError({
      message: "User not found. Please check your login credentials.",
      code: "USER_NOT_FOUND"
    });
  }
  return user;
}

/**
 * Batch loads multiple users by their IDs to avoid N+1 queries
 * @param ctx - Convex context object
 * @param userIds - Array of user IDs to load
 * @returns Map of user ID to user object (null if not found)
 */
export async function batchLoadUsers(ctx: any, userIds: string[]) {
  const uniqueUserIds = Array.from(new Set(userIds));
  const users = await Promise.all(
    uniqueUserIds.map(id => ctx.db.get(id))
  );
  
  const userMap = new Map();
  uniqueUserIds.forEach((id, index) => {
    userMap.set(id, users[index]);
  });
  
  return userMap;
}

/**
 * Retrieves all approved users for a specific event
 * @param ctx - Convex context object
 * @param eventId - Event ID to filter users by
 * @returns Array of approved users registered for the event
 */
export async function getApprovedUsersForEvent(ctx: any, eventId: string) {
  const users = await ctx.db
    .query("users")
    .withIndex("by_status_role", (q: any) => q.eq("status", "approved"))
    .filter((q: any) => q.neq(q.field("role"), "admin"))
    .collect();
    
  return users.filter((user: any) => 
    user.events?.some((e: any) => e.eventId === eventId)
  );
}

/**
 * Validates user admin privileges
 * @param ctx - Convex context object
 * @param username - Username to validate
 * @returns User object if admin
 * @throws ConvexError if user is not found or not admin
 */
export async function validateAdminUser(ctx: any, username: string) {
  const user = await getUserByUsernameOrThrow(ctx, username);
  
  if (user.role !== "admin") {
    throw new ConvexError({
      message: "Access denied. Administrator privileges required.",
      code: "ACCESS_DENIED"
    });
  }
  
  return user;
}

/**
 * INTENTIONAL ERROR PATTERN SEPARATION: Admin authentication contexts
 * 
 * Different admin contexts use different error types intentionally:
 * - Internal admin tooling (quickfire.ts, sparks.ts): Uses standard Error for simplicity
 * - User-facing operations (users.ts): Uses ConvexError for structured client handling
 * 
 * This separation allows frontend to handle errors appropriately based on context.
 * Consolidation would lose important error handling distinctions.
 */

/**
 * Validates admin user privileges with standard Error (for internal admin tooling contexts)
 * Used by: quickfire.ts, sparks.ts - internal admin management functions
 * @param ctx - Convex context object
 * @param username - Username to validate
 * @returns User object if admin
 * @throws Error if user is not found or not admin (for internal tooling simplicity)
 */
export async function validateAdminUserWithError(ctx: any, username: string) {
  const user = await getUserByUsername(ctx, username);
  
  if (!user) {
    throw new Error("User not found");
  }
  
  if (user.role !== "admin") {
    throw new Error("Access denied. Admin privileges required.");
  }
  
  return user;
}

/**
 * Validates admin user privileges with ConvexError (for user-facing operations)
 * Used by: users.ts - user registration, login, and profile management
 * @param ctx - Convex context object
 * @param username - Username to validate
 * @returns User object if admin
 * @throws ConvexError if user is not found or not admin (for structured client handling)
 */
export async function validateAdminUserWithConvexError(ctx: any, username: string) {
  const user = await getUserByUsername(ctx, username);
  
  if (!user) {
    throw new ConvexError({
      message: "User not found. Please check your login credentials.",
      code: "USER_NOT_FOUND"
    });
  }
  
  if (user.role !== "admin") {
    throw new ConvexError({
      message: "Access denied. Administrator privileges required.",
      code: "ACCESS_DENIED"
    });
  }
  
  return user;
}

/**
 * Validates user team lead privileges
 * @param ctx - Convex context object
 * @param username - Username to validate
 * @returns User object if team lead
 * @throws ConvexError if user is not found or not team lead
 */
export async function validateTeamLeadUser(ctx: any, username: string) {
  const user = await getUserByUsernameOrThrow(ctx, username);
  
  if (user.role !== "teamLead") {
    throw new ConvexError({
      message: "Access denied. Team lead privileges required.",
      code: "ACCESS_DENIED"
    });
  }
  
  return user;
}

/**
 * Validates user has admin or team lead privileges
 * @param ctx - Convex context object
 * @param username - Username to validate
 * @returns User object if admin or team lead
 * @throws ConvexError if user is not found or insufficient privileges
 */
export async function validateAdminOrTeamLeadUser(ctx: any, username: string) {
  const user = await getUserByUsernameOrThrow(ctx, username);
  
  if (user.role !== "admin" && user.role !== "teamLead") {
    throw new ConvexError({
      message: "Access denied. Administrator or team lead privileges required.",
      code: "ACCESS_DENIED"
    });
  }
  
  return user;
}

/**
 * Gets user's team information for a specific event
 * @param ctx - Convex context object
 * @param user - User object
 * @param eventId - Event ID to get team info for
 * @returns Object with teamId and teamName, or null if no team assigned
 */
export async function getUserTeamForEvent(ctx: any, user: any, eventId: string) {
  const eventRegistration = user.events?.find(
    (event: any) => event.eventId === eventId
  );
  
  if (!eventRegistration?.teamId) {
    return null;
  }
  
  const team = await ctx.db.get(eventRegistration.teamId);
  return {
    teamId: eventRegistration.teamId,
    teamName: team?.name || null
  };
}

/**
 * Gets user's team information for a specific event using batch loading
 * @param ctx - Convex context object
 * @param user - User object
 * @param eventId - Event ID to get team info for
 * @param teamMap - Optional pre-loaded team map to avoid additional queries
 * @returns Object with teamId and teamName, or null if no team assigned
 */
export async function getUserTeamForEventOptimized(ctx: any, user: any, eventId: string, teamMap?: Map<string, any>) {
  const eventRegistration = user.events?.find(
    (event: any) => event.eventId === eventId
  );
  
  if (!eventRegistration?.teamId) {
    return null;
  }
  
  let team;
  if (teamMap) {
    team = teamMap.get(eventRegistration.teamId);
  } else {
    team = await ctx.db.get(eventRegistration.teamId);
  }
  
  return {
    teamId: eventRegistration.teamId,
    teamName: team?.name || null
  };
}

/**
 * Gets users by status with optional role filtering
 * @param ctx - Convex context object
 * @param status - User status to filter by
 * @param excludeRole - Optional role to exclude from results
 * @returns Array of users matching the criteria
 */
export async function getUsersByStatusWithRoleFilter(ctx: any, status: string, excludeRole?: string) {
  let query = ctx.db
    .query("users")
    .withIndex("by_status_role", (q: any) => q.eq("status", status));
    
  if (excludeRole) {
    query = query.filter((q: any) => q.neq(q.field("role"), excludeRole));
  }
  
  return await query.collect();
}

/**
 * Batch update user events for multiple users
 * @param ctx - Convex context object
 * @param userEventUpdates - Array of objects with userId and events to update
 * @returns Promise that resolves when all updates are complete
 */
export async function batchUpdateUserEvents(ctx: any, userEventUpdates: Array<{ userId: string; events: any[] }>) {
  const now = Date.now();
  const updatePromises = userEventUpdates.map(update => 
    ctx.db.patch(update.userId, {
      events: update.events,
      updatedAt: now
    })
  );
  
  await Promise.all(updatePromises);
}

/**
 * Gets all users with optional pagination and filtering
 * @param ctx - Convex context object
 * @returns Array of all users
 */
export async function getAllUsers(ctx: any) {
  return await ctx.db.query("users").collect();
}

/**
 * Gets users by role using index
 * @param ctx - Convex context object
 * @param role - Role to filter by
 * @returns Array of users with the specified role
 */
export async function getUsersByRole(ctx: any, role: string) {
  return await ctx.db
    .query("users")
    .filter((q: any) => q.eq(q.field("role"), role))
    .collect();
}

/**
 * Gets admin users using role filter
 * @param ctx - Convex context object
 * @returns Array of admin users
 */
export async function getAdminUsers(ctx: any) {
  return await ctx.db
    .query("users")
    .filter((q: any) => q.eq(q.field("role"), "admin"))
    .collect();
}

/**
 * Gets users by status for grouped team display
 * @param ctx - Convex context object
 * @param status - Status to filter by
 * @param excludeRole - Role to exclude from results
 * @returns Array of users with specified status excluding role
 */
export async function getApprovedUsersExcludingRole(ctx: any, status: string, excludeRole: string) {
  return await ctx.db
    .query("users")
    .withIndex("by_status_role", (q: any) => q.eq("status", status))
    .filter((q: any) => q.neq(q.field("role"), excludeRole))
    .collect();
}

/**
 * Gets all user ideas for deletion analysis
 * @param ctx - Convex context object
 * @param userId - User ID to get ideas for
 * @returns Array of user ideas
 */
export async function getUserIdeas(ctx: any, userId: string) {
  return await ctx.db
    .query("ideas")
    .filter((q: any) => q.eq(q.field("userId"), userId))
    .collect();
}

/**
 * Gets user spark submissions for deletion analysis
 * @param ctx - Convex context object
 * @param userId - User ID to get submissions for
 * @returns Array of user spark submissions
 */
export async function getUserSparkSubmissions(ctx: any, userId: string) {
  return await ctx.db
    .query("sparkSubmissions")
    .withIndex("by_user", (q: any) => q.eq("userId", userId))
    .collect();
}

/**
 * Gets user votes for deletion analysis
 * @param ctx - Convex context object
 * @param userId - User ID to get votes for
 * @returns Array of user votes
 */
export async function getUserVotes(ctx: any, userId: string) {
  return await ctx.db
    .query("votes")
    .withIndex("by_user", (q: any) => q.eq("userId", userId))
    .collect();
}

/**
 * Gets user quickfire votes for deletion analysis
 * @param ctx - Convex context object
 * @param userId - User ID to get quickfire votes for
 * @returns Array of user quickfire votes
 */
export async function getUserQuickfireVotesForDeletion(ctx: any, userId: string) {
  return await ctx.db
    .query("quickfireVotes")
    .withIndex("by_user", (q: any) => q.eq("userId", userId))
    .collect();
}

/**
 * Gets all votes for an idea for deletion analysis
 * @param ctx - Convex context object
 * @param ideaId - Idea ID to get votes for
 * @returns Array of votes for the idea
 */
export async function getVotesForIdea(ctx: any, ideaId: string) {
  return await ctx.db
    .query("votes")
    .withIndex("by_idea", (q: any) => q.eq("ideaId", ideaId))
    .collect();
}

/**
 * Updates user autosave setting
 * @param ctx - Convex context object
 * @param userId - User ID to update
 * @param autosave - Boolean autosave preference
 * @returns Promise that resolves when update is complete
 */
export async function updateUserAutosaveSetting(ctx: any, userId: string, autosave: boolean) {
  await ctx.db.patch(userId, {
    autosave,
    updatedAt: Date.now(),
  });
}
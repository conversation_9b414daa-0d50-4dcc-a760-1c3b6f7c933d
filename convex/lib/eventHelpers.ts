/**
 * Shared Active Event Utility
 * 
 * This module provides a centralized function for retrieving the active event
 * across all Convex functions. This eliminates code duplication and ensures
 * consistent active event handling throughout the application.
 */

/**
 * Retrieves the active event from the database
 * @param ctx - Convex context object
 * @returns The active event object or throws an error if no active event is found
 * @throws Error with message "No active event found" if no active event exists
 */
export async function getActiveEvent(ctx: any) {
  const activeEvent = await ctx.db
    .query("events")
    .withIndex("by_active", (q: any) => q.eq("active", true))
    .first();

  if (!activeEvent) {
    throw new Error("No active event found");
  }

  return activeEvent;
}

/**
 * Retrieves the active event from the database, returning null if not found
 * @param ctx - Convex context object
 * @returns The active event object or null if no active event exists
 */
export async function getActiveEventOrNull(ctx: any) {
  return await ctx.db
    .query("events")
    .withIndex("by_active", (q: any) => q.eq("active", true))
    .first();
}

/**
 * Efficiently deactivates all events by using indexed query
 * @param ctx - Convex context object
 * @returns Promise that resolves when all events are deactivated
 */
export async function deactivateAllEvents(ctx: any) {
  const activeEvents = await ctx.db
    .query("events")
    .withIndex("by_active", (q: any) => q.eq("active", true))
    .collect();

  // Batch update all active events
  const updatePromises = activeEvents.map((event: any) => 
    ctx.db.patch(event._id, { active: false, updatedAt: Date.now() })
  );
  
  await Promise.all(updatePromises);
}

/**
 * Batch delete all related entities for an event to optimize cascade deletes
 * @param ctx - Convex context object
 * @param eventId - Event ID to cascade delete from
 * @returns Promise that resolves when all related entities are deleted
 */
export async function cascadeDeleteEventRelations(ctx: any, eventId: string) {
  // Get all related entities in parallel
  const [sessions, ideas, votes, quickfires, teams, sparks] = await Promise.all([
    ctx.db.query("sessions").withIndex("by_event", (q: any) => q.eq("eventId", eventId)).collect(),
    ctx.db.query("ideas").withIndex("by_event", (q: any) => q.eq("eventId", eventId)).collect(),
    ctx.db.query("votes").withIndex("by_event", (q: any) => q.eq("eventId", eventId)).collect(),
    ctx.db.query("quickfires").withIndex("by_event", (q: any) => q.eq("eventId", eventId)).collect(),
    ctx.db.query("teams").withIndex("by_event", (q: any) => q.eq("eventId", eventId)).collect(),
    ctx.db.query("sparks").withIndex("by_event", (q: any) => q.eq("eventId", eventId)).collect()
  ]);

  // Get quickfire votes and spark submissions for all related entities in this event
  const [quickfireVotesArrays, sparkSubmissionsArrays] = await Promise.all([
    Promise.all(quickfires.map((quickfire: any) =>
      ctx.db.query("quickfireVotes").withIndex("by_quickfire", (q: any) => q.eq("quickfireId", quickfire._id)).collect()
    )),
    Promise.all(sparks.map((spark: any) =>
      ctx.db.query("sparkSubmissions").withIndex("by_spark", (q: any) => q.eq("sparkId", spark._id)).collect()
    ))
  ]);
  
  const quickfireVotes = quickfireVotesArrays.flat();
  const sparkSubmissions = sparkSubmissionsArrays.flat();

  // Batch delete all entities
  const deletePromises = [
    // Delete sessions
    ...sessions.map((session: any) => ctx.db.delete(session._id)),
    // Delete ideas  
    ...ideas.map((idea: any) => ctx.db.delete(idea._id)),
    // Delete votes
    ...votes.map((vote: any) => ctx.db.delete(vote._id)),
    // Delete quickfire votes
    ...quickfireVotes.map((vote: any) => ctx.db.delete(vote._id)),
    // Delete quickfires
    ...quickfires.map((quickfire: any) => ctx.db.delete(quickfire._id)),
    // Delete spark submissions
    ...sparkSubmissions.map((submission: any) => ctx.db.delete(submission._id)),
    // Delete sparks
    ...sparks.map((spark: any) => ctx.db.delete(spark._id)),
    // Delete teams
    ...teams.map((team: any) => ctx.db.delete(team._id))
  ];

  await Promise.all(deletePromises);
}

/**
 * Efficiently updates user event associations by only updating affected users
 * @param ctx - Convex context object
 * @param eventId - Event ID to remove from user associations
 * @returns Promise that resolves when all user associations are updated
 */
export async function removeEventFromUserAssociations(ctx: any, eventId: string) {
  // Get only users that have this event association
  const allUsers = await ctx.db.query("users").collect();
  const usersWithEvent = allUsers.filter((user: any) => 
    user.events?.some((e: any) => e.eventId === eventId)
  );

  // Batch update only affected users
  const updatePromises = usersWithEvent.map((user: any) => {
    const updatedEvents = user.events.filter((e: any) => e.eventId !== eventId);
    return ctx.db.patch(user._id, { 
      events: updatedEvents,
      updatedAt: Date.now()
    });
  });

  await Promise.all(updatePromises);
}

/**
 * Get all events with optimized sorting using the indexed query
 * @param ctx - Convex context object
 * @returns Array of events with active events first, then inactive, both sorted by creation date
 */
export async function getAllEventsOptimized(ctx: any) {
  // Use parallel queries with indexed access for better performance
  const [activeEvents, inactiveEvents] = await Promise.all([
    ctx.db
      .query("events")
      .withIndex("by_active_created", (q: any) => q.eq("active", true))
      .order("desc")
      .collect(),
    ctx.db
      .query("events")
      .withIndex("by_active_created", (q: any) => q.eq("active", false))
      .order("desc")
      .collect()
  ]);
  
  // Return active events first, then inactive events, both sorted by creation date (newest first)
  return [...activeEvents, ...inactiveEvents];
}

/**
 * Validates if an event exists and optionally checks if it's active
 * @param ctx - Convex context object
 * @param eventId - Event ID to validate
 * @param requireActive - Whether to require the event to be active
 * @returns The event object if valid, throws error otherwise
 */
export async function validateEventExists(ctx: any, eventId: string, requireActive: boolean = false) {
  const event = await ctx.db.get(eventId);
  
  if (!event) {
    throw new Error(`Event with ID ${eventId} not found`);
  }
  
  if (requireActive && !event.active) {
    throw new Error(`Event with ID ${eventId} is not active`);
  }
  
  return event;
}

/**
 * Efficiently updates event timestamps for batch operations
 * @param ctx - Convex context object
 * @param eventIds - Array of event IDs to update
 * @param updateData - Additional data to update
 * @returns Promise that resolves when all events are updated
 */
export async function batchUpdateEventTimestamps(ctx: any, eventIds: string[], updateData: any = {}) {
  const updatePromises = eventIds.map(eventId => 
    ctx.db.patch(eventId, {
      ...updateData,
      updatedAt: Date.now()
    })
  );
  
  await Promise.all(updatePromises);
}

/**
 * Optimized helper to update a single event with timestamp
 * @param ctx - Convex context object
 * @param eventId - Event ID to update
 * @param updateData - Data to update
 * @returns Promise that resolves when event is updated
 */
export async function updateEventWithTimestamp(ctx: any, eventId: string, updateData: any) {
  return await ctx.db.patch(eventId, {
    ...updateData,
    updatedAt: Date.now()
  });
}

/**
 * Batch validate multiple events exist
 * @param ctx - Convex context object
 * @param eventIds - Array of event IDs to validate
 * @param requireActive - Whether to require events to be active
 * @returns Array of event objects if valid, throws error otherwise
 */
export async function batchValidateEventsExist(ctx: any, eventIds: string[], requireActive: boolean = false) {
  const events = await Promise.all(
    eventIds.map(eventId => ctx.db.get(eventId))
  );
  
  const validationErrors: string[] = [];
  
  events.forEach((event, index) => {
    const eventId = eventIds[index];
    if (!event) {
      validationErrors.push(`Event with ID ${eventId} not found`);
    } else if (requireActive && !event.active) {
      validationErrors.push(`Event with ID ${eventId} is not active`);
    }
  });
  
  if (validationErrors.length > 0) {
    throw new Error(validationErrors.join('; '));
  }
  
  return events;
}
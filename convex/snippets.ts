import { mutation, query } from "./_generated/server";
import { v } from "convex/values";
import { 
  QueryResponse, 
  CreateResponse, 
  UpdateResponse,
  DeleteResponse 
} from "./lib/responseTypes";
import { 
  createCreateResponse, 
  createDeleteResponse
} from "./lib/responseUtils";
import {
  getAllSnippetsWithMetadata,
  getSnippetByIdWithValidation,
  createSnippetWithValidation,
  updateSnippetWithValidation,
  deleteSnippetWithValidation,
  validateSnippetName,
  searchSnippets as searchSnippetsHelper,
  getSnippetStatistics as getSnippetStatisticsHelper,
  batchCreateSnippets as batchCreateSnippetsHelper,
  batchDeleteSnippets as batchDeleteSnippetsHelper
} from "./lib/snippetHelpers";

// Get all snippets with enhanced metadata, sorted by name
export const getAllSnippets = query({
  args: {},
  handler: async (ctx): Promise<QueryResponse<any[]>> => {
    return await getAllSnippetsWithMetadata(ctx);
  },
});

// Get snippet by ID with enhanced metadata
export const getSnippetById = query({
  args: { snippetId: v.id("snippets") },
  handler: async (ctx, args): Promise<QueryResponse<any | null>> => {
    return await getSnippetByIdWithValidation(ctx, args.snippetId);
  },
});

// Create a new snippet with validation
export const createSnippet = mutation({
  args: {
    name: v.string(),
    content: v.string(),
  },
  handler: async (ctx, args): Promise<CreateResponse<"snippets">> => {
    return await createSnippetWithValidation(ctx, args.name, args.content);
  },
});

// Update an existing snippet with validation
export const updateSnippet = mutation({
  args: {
    snippetId: v.id("snippets"),
    name: v.optional(v.string()),
    content: v.optional(v.string()),
  },
  handler: async (ctx, args): Promise<UpdateResponse> => {
    return await updateSnippetWithValidation(ctx, args.snippetId, args.name, args.content);
  },
});

// Delete a snippet with validation
export const deleteSnippet = mutation({
  args: {
    snippetId: v.id("snippets"),
  },
  handler: async (ctx, args): Promise<DeleteResponse> => {
    return await deleteSnippetWithValidation(ctx, args.snippetId);
  },
});

// Check if snippet name exists (for validation)
export const checkSnippetNameExists = query({
  args: {
    name: v.string(),
    excludeId: v.optional(v.id("snippets")),
  },
  handler: async (ctx, args): Promise<QueryResponse<boolean>> => {
    const isAvailable = await validateSnippetName(ctx, args.name, args.excludeId);
    return !isAvailable; // Return true if name exists (inverse of availability)
  },
});

// Search snippets by name or content
export const searchSnippets = query({
  args: {
    searchTerm: v.string(),
    limit: v.optional(v.number()),
  },
  handler: async (ctx, args): Promise<QueryResponse<any[]>> => {
    return await searchSnippetsHelper(ctx, args.searchTerm, args.limit);
  },
});

// Get snippet statistics
export const getSnippetStatistics = query({
  args: {},
  handler: async (ctx): Promise<QueryResponse<any>> => {
    return await getSnippetStatisticsHelper(ctx);
  },
});

// Batch create multiple snippets
export const batchCreateSnippets = mutation({
  args: {
    snippets: v.array(v.object({
      name: v.string(),
      content: v.string(),
    })),
  },
  handler: async (ctx, args): Promise<CreateResponse<"snippets">> => {
    const createdIds = await batchCreateSnippetsHelper(ctx, args.snippets);
    return createCreateResponse(createdIds[0]); // Return first ID for compatibility
  },
});

// Batch delete multiple snippets
export const batchDeleteSnippets = mutation({
  args: {
    snippetIds: v.array(v.id("snippets")),
  },
  handler: async (ctx, args): Promise<DeleteResponse> => {
    const deletedCount = await batchDeleteSnippetsHelper(ctx, args.snippetIds);
    return createDeleteResponse(deletedCount > 0);
  },
});
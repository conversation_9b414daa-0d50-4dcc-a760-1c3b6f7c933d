import { v } from "convex/values";
import { mutation, query } from "./_generated/server";
import { getActiveEventOrNull } from "./lib/eventHelpers";
import { requireAdmin } from "./lib/auth";
import { Doc } from "./_generated/dataModel";
import {
  CreateResponse,
  UpdateResponse,
  BulkResponse,
  QueryResponse,
  ErrorCode
} from "./lib/responseTypes";
import {
  createCreateResponse,
  createUpdateResponse,
  createBulkResponse,
  throwConvexError
} from "./lib/responseUtils";
// Import helper functions for optimization
import {
  getAllTeams as getAllTeamsHelper,
  getTeamsByEvent as getTeamsByEventHelper,
  getTeamById as getTeamByIdHelper,
  getTeamByIdOrThrow,
  getActiveTeamsByEvent,
  getTeamRelatedEntitiesForDeletion,
  getTeamsWithEnrichedMembers,
  getTeamLeads
} from "./lib/teamHelpers";
import {
  batchLoadTeamMembers,
  batchDeleteEntities
} from "./lib/batchHelpers";

export const createTeam = mutation({
  args: {
    username: v.string(),
    name: v.string(),
    eventId: v.id("events"),
    voting: v.optional(v.boolean()),
  },
  handler: async (ctx, args): Promise<CreateResponse<"teams">> => {
    // Authenticate admin user
    await requireAdmin(ctx, args.username, true); // Use ConvexError for admin mutations
    const now = Date.now();
    const teamId = await ctx.db.insert("teams", {
      name: args.name,
      eventId: args.eventId,
      voting: args.voting,
      active: true, // Teams are active by default
      createdAt: now,
    });
    return createCreateResponse(teamId);
  },
});

export const getTeamsByEvent = query({
  args: { eventId: v.id("events") },
  handler: async (ctx, args): Promise<QueryResponse<Doc<"teams">[]>> => {
    return await getTeamsByEventHelper(ctx, args.eventId);
  },
});

export const getAllTeams = query({
  args: {},
  handler: async (ctx): Promise<QueryResponse<Doc<"teams">[]>> => {
    try {
      // PERFORMANCE OPTIMIZATION: Use helper function for consistency
      return await getAllTeamsHelper(ctx);
    } catch (error) {
      console.error("Error fetching all teams:", error);
      throwConvexError("Failed to fetch teams", ErrorCode.DATABASE_ERROR);
    }
  },
});

export const getTeamsByActiveEvent = query({
  args: {},
  handler: async (ctx): Promise<QueryResponse<(Doc<"teams"> & { users: Doc<"users">[] })[]>> => {
    try {
      // Get the active event first using optimized index
      const activeEvent = await getActiveEventOrNull(ctx);
      
      if (!activeEvent) {
        return [];
      }

      // Get teams for the active event using helper
      const teams = await getTeamsByEventHelper(ctx, activeEvent._id);

      // PERFORMANCE OPTIMIZATION: Use batch loading helper for team members
      const teamIds = teams.map((team: any) => team._id);
      const teamMembersMap = await batchLoadTeamMembers(ctx, activeEvent._id, teamIds);

      // Build teams with users efficiently using batch-loaded data
      const teamsWithUsers = teams.map((team: any) => ({
        ...team,
        users: teamMembersMap.get(team._id) || []
      }));

      return teamsWithUsers;
    } catch (error) {
      console.error("Error fetching teams by active event:", error);
      throwConvexError("Failed to fetch teams for active event", ErrorCode.DATABASE_ERROR);
    }
  },
});

export const getTeamById = query({
  args: { teamId: v.id("teams") },
  handler: async (ctx, args): Promise<QueryResponse<Doc<"teams"> | null>> => {
    return await getTeamByIdHelper(ctx, args.teamId);
  },
});

export const updateTeam = mutation({
  args: {
    username: v.string(),
    teamId: v.id("teams"),
    name: v.optional(v.string()),
    lead: v.optional(v.id("users")),
    voting: v.optional(v.boolean()),
    active: v.optional(v.boolean()),
  },
  handler: async (ctx, args): Promise<UpdateResponse> => {
    // Authenticate admin user
    await requireAdmin(ctx, args.username, true); // Use ConvexError for admin mutations
    const updateData: any = {
      updatedAt: Date.now(),
    };

    if (args.name !== undefined) updateData.name = args.name;
    if (args.lead !== undefined) updateData.lead = args.lead;
    if (args.voting !== undefined) updateData.voting = args.voting;
    if (args.active !== undefined) updateData.active = args.active;

    await ctx.db.patch(args.teamId, updateData);
    return createUpdateResponse();
  },
});

export const deleteTeam = mutation({
  args: { username: v.string(), teamId: v.id("teams") },
  handler: async (ctx, args): Promise<BulkResponse> => {
    // Authenticate admin user
    await requireAdmin(ctx, args.username, true); // Use ConvexError for admin mutations
    
    // Verify team exists before proceeding
    await getTeamByIdOrThrow(ctx, args.teamId);
    
    // PERFORMANCE OPTIMIZATION: Use dedicated helper for efficient entity collection
    const relatedEntities = await getTeamRelatedEntitiesForDeletion(ctx, args.teamId);
    
    // Combine all entities to delete
    const entitiesToDelete = [
      ...relatedEntities.teamUsers,
      ...relatedEntities.ideas,
      ...relatedEntities.ideaVotes,
      ...relatedEntities.userVotes,
      ...relatedEntities.userQuickfireVotes,
      ...relatedEntities.sparkSubmissions
    ];
    
    // PERFORMANCE OPTIMIZATION: Use batch deletion helper for better performance
    await batchDeleteEntities(ctx, entitiesToDelete);
    
    // Calculate total deletions count
    const totalDeletions = relatedEntities.totalCount + 1; // +1 for team itself
    
    // PERFORMANCE OPTIMIZATION: Use batch deletion for consistency
    await batchDeleteEntities(ctx, [{ _id: args.teamId }]);
    
    return createBulkResponse(totalDeletions);
  },
});

export const toggleTeamActive = mutation({
  args: { username: v.string(), teamId: v.id("teams") },
  handler: async (ctx, args): Promise<UpdateResponse> => {
    // Authenticate admin user
    await requireAdmin(ctx, args.username, true); // Use ConvexError for admin mutations
    
    // Use helper function with built-in error handling
    const team = await getTeamByIdHelper(ctx, args.teamId);
    if (!team) {
      throwConvexError("Team not found", ErrorCode.RESOURCE_NOT_FOUND);
    }

    // Default to true if active field doesn't exist (for existing teams)
    const currentActive = team.active ?? true;
    
    await ctx.db.patch(args.teamId, {
      active: !currentActive,
      updatedAt: Date.now(),
    });
    
    return createUpdateResponse();
  },
});

export const getActiveTeams = query({
  args: { eventId: v.optional(v.id("events")) },
  handler: async (ctx, args): Promise<QueryResponse<Doc<"teams">[]>> => {
    try {
      if (args.eventId) {
        // PERFORMANCE OPTIMIZATION: Use helper function for event-specific active teams
        return await getActiveTeamsByEvent(ctx, args.eventId);
      }
      
      // PERFORMANCE OPTIMIZATION: When no eventId is provided, use active event helper
      // This avoids scanning all teams across all events
      const activeEvent = await getActiveEventOrNull(ctx);
      if (activeEvent) {
        return await getActiveTeamsByEvent(ctx, activeEvent._id);
      }
      
      // PERFORMANCE OPTIMIZATION: Use helper function for consistency
      const allTeams = await getAllTeamsHelper(ctx);
      return allTeams.filter((team: any) => team.active ?? true);
    } catch (error) {
      console.error("Error fetching active teams:", error);
      throwConvexError("Failed to fetch active teams", ErrorCode.DATABASE_ERROR);
    }
  },
});

export const getTeamsWithMembers = query({
  args: { eventId: v.id("events") },
  handler: async (ctx, args): Promise<QueryResponse<Array<Doc<"teams"> & { members: Doc<"users">[]; memberCount: number }>>> => {
    try {
      // PERFORMANCE OPTIMIZATION: Use new helper function for efficient team loading with members
      return await getTeamsWithEnrichedMembers(ctx, args.eventId);
    } catch (error) {
      console.error("Error fetching teams with members:", error);
      throwConvexError("Failed to fetch teams with members", ErrorCode.DATABASE_ERROR);
    }
  },
});

export const getTeamLeadsForTeam = query({
  args: { 
    teamId: v.id("teams"),
    eventId: v.id("events") 
  },
  handler: async (ctx, args): Promise<QueryResponse<Doc<"users">[]>> => {
    try {
      // PERFORMANCE OPTIMIZATION: Use helper function for getting team leads
      return await getTeamLeads(ctx, args.teamId, args.eventId);
    } catch (error) {
      console.error("Error fetching team leads for team:", error);
      throwConvexError("Failed to fetch team leads", ErrorCode.DATABASE_ERROR);
    }
  },
});
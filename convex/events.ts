import { v } from "convex/values";
import { mutation, query } from "./_generated/server";
import { getActiveEventOrNull, deactivateAllEvents, cascadeDeleteEventRelations, removeEventFromUserAssociations, getAllEventsOptimized, validateEventExists, updateEventWithTimestamp } from "./lib/eventHelpers";
import { requireAdmin } from "./lib/auth";
import { createCreateResponse, createUpdateResponse, createDeleteResponse } from "./lib/responseUtils";
import { CreateResponse, UpdateResponse, DeleteResponse } from "./lib/responseTypes";
import { batchLoadEvents } from "./lib/batchHelpers";
import { enrichEventsWithStatistics } from "./lib/enrichmentHelpers";

export const getActiveEvent = query({
  args: {},
  handler: async (ctx) => {
    return await getActiveEventOrNull(ctx);
  },
});

export const getEventById = query({
  args: { eventId: v.id("events") },
  handler: async (ctx, args) => {
    // Use batch loading helper for consistency and potential caching benefits
    const eventsMap = await batchLoadEvents(ctx, [args.eventId]);
    return eventsMap.get(args.eventId) || null;
  },
});

export const getEventsByIds = query({
  args: { eventIds: v.array(v.id("events")) },
  handler: async (ctx, args) => {
    // Use batch loading helper for multiple events
    const eventsMap = await batchLoadEvents(ctx, args.eventIds);
    return args.eventIds.map(id => eventsMap.get(id)).filter(event => event !== null);
  },
});

export const getAllEvents = query({
  args: { username: v.string() },
  handler: async (ctx, args) => {
    // Authenticate admin user
    await requireAdmin(ctx, args.username, true); // Use ConvexError for admin queries
    // Use optimized helper function for better performance
    return await getAllEventsOptimized(ctx);
  },
});

export const getAllEventsWithStatistics = query({
  args: { username: v.string() },
  handler: async (ctx, args) => {
    // Authenticate admin user
    await requireAdmin(ctx, args.username, true); // Use ConvexError for admin queries
    
    // Get all events using optimized helper
    const events = await getAllEventsOptimized(ctx);
    
    // Enrich events with comprehensive statistics
    return await enrichEventsWithStatistics(ctx, events);
  },
});

export const createEvent = mutation({
  args: {
    username: v.string(),
    name: v.string(),
    description: v.optional(v.string()),
    active: v.boolean(),
    startDate: v.optional(v.number()),
    endDate: v.optional(v.number()),
  },
  handler: async (ctx, args): Promise<CreateResponse<"events">> => {
    // Authenticate admin user
    await requireAdmin(ctx, args.username, true); // Use ConvexError for admin mutations
    // If setting this event as active, deactivate all other events
    if (args.active) {
      await deactivateAllEvents(ctx);
    }

    const now = Date.now();
    const id = await ctx.db.insert("events", {
      name: args.name,
      description: args.description,
      active: args.active,
      startDate: args.startDate,
      endDate: args.endDate,
      createdAt: now,
    });
    
    return createCreateResponse(id);
  },
});

export const activateEvent = mutation({
  args: { username: v.string(), eventId: v.id("events") },
  handler: async (ctx, args): Promise<UpdateResponse> => {
    // Authenticate admin user
    await requireAdmin(ctx, args.username, true); // Use ConvexError for admin mutations
    
    // Validate event exists
    await validateEventExists(ctx, args.eventId);
    
    // Deactivate all events first using optimized helper
    await deactivateAllEvents(ctx);

    // Activate the specified event using helper
    await updateEventWithTimestamp(ctx, args.eventId, {
      active: true,
    });
    
    return createUpdateResponse();
  },
});

export const updateEvent = mutation({
  args: {
    username: v.string(),
    eventId: v.id("events"),
    name: v.optional(v.string()),
    description: v.optional(v.string()),
    startDate: v.optional(v.number()),
    endDate: v.optional(v.number()),
  },
  handler: async (ctx, args): Promise<UpdateResponse> => {
    // Authenticate admin user
    await requireAdmin(ctx, args.username, true); // Use ConvexError for admin mutations
    
    // Validate event exists
    await validateEventExists(ctx, args.eventId);
    
    const updateData: any = {};

    if (args.name !== undefined) updateData.name = args.name;
    if (args.description !== undefined) updateData.description = args.description;
    if (args.startDate !== undefined) updateData.startDate = args.startDate;
    if (args.endDate !== undefined) updateData.endDate = args.endDate;

    await updateEventWithTimestamp(ctx, args.eventId, updateData);
    
    return createUpdateResponse();
  },
});

export const deactivateEvent = mutation({
  args: { username: v.string(), eventId: v.id("events") },
  handler: async (ctx, args): Promise<UpdateResponse> => {
    // Authenticate admin user
    await requireAdmin(ctx, args.username, true); // Use ConvexError for admin mutations
    
    // Validate event exists
    await validateEventExists(ctx, args.eventId);
    
    await updateEventWithTimestamp(ctx, args.eventId, {
      active: false,
    });
    
    return createUpdateResponse();
  },
});

export const deleteEvent = mutation({
  args: { username: v.string(), eventId: v.id("events") },
  handler: async (ctx, args): Promise<DeleteResponse> => {
    // Authenticate admin user
    await requireAdmin(ctx, args.username, true); // Use ConvexError for admin mutations
    
    // Validate event exists
    await validateEventExists(ctx, args.eventId);
    
    // CASCADE DELETE - Delete everything related to this event using optimized batch operations
    
    // Use optimized cascade delete helper
    await cascadeDeleteEventRelations(ctx, args.eventId);
    
    // Remove event association from users using optimized helper
    await removeEventFromUserAssociations(ctx, args.eventId);
    
    // Finally, delete the event itself
    await ctx.db.delete(args.eventId);
    
    return createDeleteResponse();
  },
});
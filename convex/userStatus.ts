import { v } from "convex/values";
import { mutation } from "./_generated/server";
import { getAllUsers as getAllUsersHelper } from "./lib/userHelpers";
import { 
  UpdateResponse, 
  MutationResponse 
} from "./lib/responseTypes";
import { 
  createUpdateResponse, 
  createSuccessResponse 
} from "./lib/responseUtils";

export const updateUserStatus = mutation({
  args: {
    userId: v.id("users"),
    status: v.union(v.literal("pending"), v.literal("approved"), v.literal("rejected")),
  },
  handler: async (ctx, args): Promise<UpdateResponse> => {
    await ctx.db.patch(args.userId, {
      status: args.status,
      updatedAt: Date.now(),
    });
    return createUpdateResponse();
  },
});

// Cleanup function to remove lastSeen field
export const removeLastSeenField = mutation({
  args: {},
  handler: async (ctx): Promise<MutationResponse<{ message: string }>> => {
    const users = await getAllUsersHelper(ctx);
    const updatePromises = [];
    let updatedCount = 0;
    
    for (const user of users) {
      if ("lastSeen" in user) {
        const { lastSeen, ...userWithoutLastSeen } = user;
        updatePromises.push(ctx.db.replace(user._id, userWithoutLastSeen));
        updatedCount++;
      }
    }
    
    // OPTIMIZATION: Execute all updates in parallel
    await Promise.all(updatePromises);
    
    return createSuccessResponse({ message: `Removed lastSeen from ${updatedCount} users` });
  },
});
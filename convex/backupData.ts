import { query, mutation } from "./_generated/server";
import { v } from "convex/values";
import { ConvexError } from "convex/values";
import { Id } from "./_generated/dataModel";
import {
  ErrorCode
} from "./lib/responseTypes";
import {
  throwConvexError
} from "./lib/responseUtils";
import { batchDeleteEntities } from "./lib/batchHelpers";
import { 
  getBackupByIdOrThrow,
  getAllBackups,
  enrichBackups 
} from "./lib/backupHelpers";

// =====================================
// HELPER FUNCTIONS
// =====================================

/**
 * Helper function to get backup by ID with proper error handling
 * @param ctx - Database context
 * @param backupId - Backup ID to retrieve
 * @returns Backup object
 * @throws ConvexError if backup not found
 */
async function getBackupById(ctx: any, backupId: Id<"backups">) {
  const backup = await ctx.db.get(backupId);
  if (!backup) {
    throwConvexError(
      "Backup not found",
      ErrorCode.RESOURCE_NOT_FOUND
    );
  }
  return backup;
}

/**
 * Helper function to get backup by ID with proper error handling
 * @param ctx - Database context
 * @param backupId - Backup ID to retrieve
 * @returns Backup object
 * @throws ConvexError if backup not found
 */
async function getBackupOrThrow(ctx: any, backupId: Id<"backups">) {
  return await getBackupByIdOrThrow(ctx, backupId);
}

/**
 * Helper function to create backup with proper error handling
 * @param ctx - Database context
 * @param backupData - Backup data to insert
 * @returns Backup ID
 * @throws ConvexError if creation fails
 */
async function createBackup(ctx: any, backupData: any) {
  try {
    return await ctx.db.insert("backups", backupData);
  } catch (error) {
    throwConvexError(
      "Failed to create backup",
      ErrorCode.DATABASE_ERROR
    );
  }
}

/**
 * Helper function to update backup with proper error handling
 * @param ctx - Database context
 * @param backupId - Backup ID to update
 * @param updateData - Data to update
 * @throws ConvexError if update fails
 */
async function updateBackup(ctx: any, backupId: Id<"backups">, updateData: any) {
  try {
    await ctx.db.patch(backupId, updateData);
  } catch (error) {
    throwConvexError(
      "Failed to update backup",
      ErrorCode.DATABASE_ERROR
    );
  }
}

/**
 * Helper function to delete backup with proper error handling
 * @param ctx - Database context
 * @param backupId - Backup ID to delete
 * @throws ConvexError if deletion fails
 */
async function deleteBackup(ctx: any, backupId: Id<"backups">) {
  try {
    await ctx.db.delete(backupId);
  } catch (error) {
    throwConvexError(
      "Failed to delete backup",
      ErrorCode.DATABASE_ERROR
    );
  }
}

// =====================================
// QUERIES (Real-time)
// =====================================

/**
 * Get all backups with real-time updates
 * Ordered by creation time (newest first) for better UX
 */
export const getBackupsList = query({
  args: {},
  handler: async (ctx) => {
    const backups = await getAllBackups(ctx);
    
    // Enrich backups with additional metadata
    return await enrichBackups(ctx, backups);
  },
});


/**
 * Get specific backup details
 */
export const getBackupDetails = query({
  args: { backupId: v.id("backups") },
  handler: async (ctx, args) => {
    const backup = await getBackupById(ctx, args.backupId);
    return backup;
  },
});


// =====================================
// MUTATIONS (Metadata Management)
// =====================================

/**
 * Create backup metadata record
 */
export const createBackupMetadata = mutation({
  args: {
    id: v.string(),
    description: v.optional(v.string()),
    fileName: v.string(),
    createdBy: v.string(),
    includeStorage: v.boolean(),
    tablesIncluded: v.array(v.string()),
  },
  handler: async (ctx, args) => {
    try {
      const now = Date.now();
      
      const backupId = await createBackup(ctx, {
        id: args.id,
        timestamp: now,
        description: args.description,
        size: 0, // Will be updated after file creation
        status: "creating",
        createdBy: args.createdBy,
        fileName: args.fileName,
        includeStorage: args.includeStorage,
        tablesIncluded: args.tablesIncluded,
        createdAt: now,
      });
      
      // Return the ID directly for backward compatibility
      return backupId;
    } catch (error) {
      throwConvexError(
        "Failed to create backup metadata",
        ErrorCode.DATABASE_ERROR
      );
    }
  },
});

/**
 * Create backup metadata record with file information (optimized for single transaction)
 */
export const createBackupMetadataComplete = mutation({
  args: {
    id: v.string(),
    description: v.optional(v.string()),
    fileName: v.string(),
    createdBy: v.string(),
    includeStorage: v.boolean(),
    tablesIncluded: v.array(v.string()),
    size: v.number(),
    fileId: v.id("_storage"),
  },
  handler: async (ctx, args) => {
    try {
      const now = Date.now();
      
      const backupId = await createBackup(ctx, {
        id: args.id,
        timestamp: now,
        description: args.description,
        size: args.size,
        status: "completed",
        createdBy: args.createdBy,
        fileName: args.fileName,
        fileId: args.fileId,
        includeStorage: args.includeStorage,
        tablesIncluded: args.tablesIncluded,
        createdAt: now,
      });
      
      // Return the ID directly for backward compatibility
      return backupId;
    } catch (error) {
      throwConvexError(
        "Failed to create backup metadata",
        ErrorCode.DATABASE_ERROR
      );
    }
  },
});

/**
 * Update backup metadata (size, status, etc.)
 */
export const updateBackupMetadata = mutation({
  args: {
    backupId: v.id("backups"),
    size: v.optional(v.number()),
    status: v.optional(v.union(v.literal("completed"), v.literal("failed"), v.literal("creating"))),
    fileId: v.optional(v.id("_storage")),
    fileName: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    try {
      // Check if backup exists using helper function
      await getBackupOrThrow(ctx, args.backupId);

      const updateData: any = {
        updatedAt: Date.now(),
      };
      
      if (args.size !== undefined) updateData.size = args.size;
      if (args.status !== undefined) updateData.status = args.status;
      if (args.fileId !== undefined) updateData.fileId = args.fileId;
      if (args.fileName !== undefined) updateData.fileName = args.fileName;
      
      await updateBackup(ctx, args.backupId, updateData);
      
      // Return void for backward compatibility
      return;
    } catch (error) {
      if (error instanceof ConvexError) {
        throw error;
      }
      throwConvexError(
        "Failed to update backup metadata",
        ErrorCode.DATABASE_ERROR
      );
    }
  },
});

/**
 * Delete backup metadata
 */
export const deleteBackupMetadata = mutation({
  args: { backupId: v.id("backups") },
  handler: async (ctx, args) => {
    try {
      // Check if backup exists using helper function
      await getBackupOrThrow(ctx, args.backupId);

      await deleteBackup(ctx, args.backupId);
      
      // Return void for backward compatibility
      return;
    } catch (error) {
      if (error instanceof ConvexError) {
        throw error;
      }
      throwConvexError(
        "Failed to delete backup metadata",
        ErrorCode.DATABASE_ERROR
      );
    }
  },
});

/**
 * Generate upload URL for backup files
 */
export const generateUploadUrl = mutation({
  args: {},
  handler: async (ctx) => {
    try {
      const uploadUrl = await ctx.storage.generateUploadUrl();
      
      // Return the URL directly for backward compatibility
      return uploadUrl;
    } catch (error) {
      throwConvexError(
        "Failed to generate upload URL",
        ErrorCode.INTERNAL_ERROR
      );
    }
  },
});

/**
 * Helper function to batch delete documents from a table
 * @param ctx - Database context
 * @param tableName - Name of the table to clear
 * @returns Number of documents deleted
 */
async function batchDeleteFromTable(ctx: any, tableName: string): Promise<number> {
  let totalDeleted = 0;
  let cursor = null;
  let result: any;
  
  // Process in chunks to avoid memory issues
  do {
    result = await ctx.db.query(tableName as any).paginate({ 
      cursor, 
      numItems: 1000 
    });
    
    if (result.page.length > 0) {
      // Use optimized batch deletion helper
      await batchDeleteEntities(ctx, result.page);
      totalDeleted += result.page.length;
    }
    
    cursor = result.continueCursor;
  } while (result.isDone === false);
  
  return totalDeleted;
}

/**
 * Helper function to handle user table cleanup with admin preservation
 * @param ctx - Database context
 * @returns Object containing deleted count and preserved admin count
 */
async function cleanupUsersTable(ctx: any): Promise<{ deletedCount: number; preservedAdmins: number }> {
  const allUsers = await ctx.db.query("users").take(10000);
  
  // Separate admin and non-admin users
  const adminUsers = allUsers.filter((user: any) => user.role === "admin");
  const nonAdminUsers = allUsers.filter((user: any) => user.role !== "admin");
  
  // Use optimized batch deletion helper
  await batchDeleteEntities(ctx, nonAdminUsers);
  
  return {
    deletedCount: nonAdminUsers.length,
    preservedAdmins: adminUsers.length
  };
}

/**
 * Clear all Convex data (danger zone functionality)
 */
export const clearAllDataMutation = mutation({
  args: {},
  handler: async (ctx) => {
    try {
      // Get all table names from schema (excluding system tables, backups, and file storage)
      // Note: 'backups' table and '_storage' (file storage) are intentionally excluded to preserve backup functionality
      const tables = [
        "events", "teams", "ideas", "sessions", "votes",
        "quickfires", "quickfireVotes",
        "snippets", "settings", "presence", "sparks", "sparkSubmissions"
      ];

      // Delete all documents from each table in parallel
      const deletionPromises = tables.map(tableName => batchDeleteFromTable(ctx, tableName));
      const deletionResults = await Promise.all(deletionPromises);
      
      // Calculate total deleted documents from all tables
      const totalDeletedFromTables = deletionResults.reduce((sum, count) => sum + count, 0);

      // Handle users table separately - preserve admin users
      const userCleanupResult = await cleanupUsersTable(ctx);
      
      const totalDeleted = totalDeletedFromTables + userCleanupResult.deletedCount;

      // Return the response object directly for backward compatibility
      return {
        cleared: tables.length + 1, // +1 for users table
        deletedRecords: totalDeleted,
        preservedAdmins: userCleanupResult.preservedAdmins,
        message: `All data cleared successfully. Preserved ${userCleanupResult.preservedAdmins} admin user(s), all backups, and file storage.`
      };
    } catch (error) {
      throwConvexError(
        "Failed to clear all data",
        ErrorCode.DATABASE_ERROR
      );
    }
  },
});
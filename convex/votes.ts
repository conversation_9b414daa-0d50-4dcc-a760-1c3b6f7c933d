import { v } from "convex/values";
import { mutation, query } from "./_generated/server";
import { Doc } from "./_generated/dataModel";
import { getActiveEventOrNull } from "./lib/eventHelpers";
import { getUserByUsername } from "./lib/userHelpers";
import { getVotingStatus as getVotingStatusSetting } from "./lib/settingsHelpers";
import { 
  getUserVotesForEvent,
  upsertVote,
  getAllVotes as getAllVotesHelper,
  getAllQuickfireVotes as getAllQuickfireVotesHelper
} from "./lib/voteHelpers";
import { 
  batchLoadIdeas,
  batchLoadTeams,
  batchLoadSessions
} from "./lib/batchHelpers";
import { getSessionsByEvent, getActiveSessionForEvent } from "./lib/sessionHelpers";
import { getTeamsByEvent } from "./lib/teamHelpers";
import { getIdeasBySession } from "./lib/ideaHelpers";
import { 
  QueryResponse,
  VotingResponse,
  ErrorCode
} from "./lib/responseTypes";
import { 
  createVotingResponse,
  throwConvexError
} from "./lib/responseUtils";

// Get user's votes for a specific event
export const getUserVotes = query({
  args: { username: v.string(), eventId: v.id("events") },
  handler: async (ctx, args): Promise<QueryResponse<Array<any>>> => {
    // Find user by username
    const user = await getUserByUsername(ctx, args.username);

    if (!user) {
      return []; // Return empty array instead of throwing error
    }

    // Get all votes for this user in the event using helper
    return await getUserVotesForEvent(ctx, user._id, args.eventId);
  },
});

// Submit or update a vote
export const submitVote = mutation({
  args: {
    username: v.string(),
    ideaId: v.id("ideas"),
    score: v.number(),
    comment: v.optional(v.string()),
  },
  handler: async (ctx, args): Promise<VotingResponse> => {
    // Validate score range first (cheap validation)
    if (args.score < 0 || args.score > 10) {
      throwConvexError("Score must be between 0 and 10", ErrorCode.VALIDATION_ERROR);
    }

    // Parallelize all validation queries to reduce latency
    const [user, votingStarted] = await Promise.all([
      getUserByUsername(ctx, args.username),
      getVotingStatusSetting(ctx)
    ]);

    if (!user) {
      throwConvexError("User not found", ErrorCode.USER_NOT_FOUND);
    }

    if (!votingStarted) {
      throwConvexError("Voting has not started yet", ErrorCode.OPERATION_NOT_ALLOWED);
    }

    // Batch load idea and its related entities
    const ideaMap = await batchLoadIdeas(ctx, [args.ideaId]);
    const idea = ideaMap.get(args.ideaId);

    if (!idea) {
      throwConvexError("Idea not found", ErrorCode.RESOURCE_NOT_FOUND);
    }

    // Batch load session and team using idea's references
    const [sessionMap, teamMap] = await Promise.all([
      batchLoadSessions(ctx, [idea.sessionId]),
      batchLoadTeams(ctx, [idea.teamId])
    ]);

    const session = sessionMap.get(idea.sessionId);
    const team = teamMap.get(idea.teamId);

    if (!session) {
      throwConvexError("Session not found", ErrorCode.RESOURCE_NOT_FOUND);
    }

    if (!team) {
      throwConvexError("Team not found for this idea", ErrorCode.RESOURCE_NOT_FOUND);
    }

    // Check session and team voting settings
    if (!session.settings?.votingEnabled) {
      throwConvexError("Voting is not enabled for the current session", ErrorCode.OPERATION_NOT_ALLOWED);
    }

    if (!team.voting) {
      throwConvexError("Voting is not enabled for this team", ErrorCode.OPERATION_NOT_ALLOWED);
    }

    // Use helper to upsert vote
    const voteResult = await upsertVote(ctx, {
      userId: user._id,
      ideaId: args.ideaId,
      eventId: idea.eventId || session.eventId,
      sessionId: idea.sessionId,
      score: args.score,
      comment: args.comment
    });

    return createVotingResponse(voteResult.voteId, voteResult.score, true);
  },
});

// Get ideas available for voting
export const getVotingIdeas = query({
  args: { username: v.string() },
  handler: async (ctx, _args): Promise<QueryResponse<{ideas: Array<any>, event: any, noTeamSelected?: boolean}>> => {

    // Get active event
    const activeEvent = await getActiveEventOrNull(ctx);

    if (!activeEvent) {
      return { ideas: [], event: null };
    }

    // Check if voting is enabled
    const votingStarted = await getVotingStatusSetting(ctx);

    if (!votingStarted) {
      return { ideas: [], event: activeEvent };
    }

    // Get active session for this event using optimized helper
    const activeSession = await getActiveSessionForEvent(ctx, activeEvent._id);

    if (!activeSession) {
      return { ideas: [], event: activeEvent };
    }

    // Check if voting is enabled in session settings
    if (!activeSession.settings?.votingEnabled) {
      return { ideas: [], event: activeEvent };
    }

    // Get active teams data for active event using helper
    const allTeams = await getTeamsByEvent(ctx, activeEvent._id);
    const teams = allTeams.filter((team: any) => team.active);

    // Get teams with voting: true
    const votingTeams = teams.filter((team: any) => team.voting === true);

    // If no teams selected for voting, show "Get Ready" message
    if (votingTeams.length === 0) {
      return { ideas: [], event: activeEvent, noTeamSelected: true };
    }

    // Get teams that are finished (eligible for voting)
    const finishedTeamIds = activeSession.finishedTeams || [];
    if (finishedTeamIds.length === 0) {
      return { ideas: [], event: activeEvent, noTeamSelected: true };
    }

    // Only show teams that are active, voting:true AND finished
    const teamsToShow = votingTeams.filter((team: any) => 
      finishedTeamIds.includes(team._id)
    );

    if (teamsToShow.length === 0) {
      return { ideas: [], event: activeEvent, noTeamSelected: true };
    }

    // Get submitted ideas from finished teams for the active session using helper
    const allIdeas = await getIdeasBySession(ctx, activeSession._id);
    const ideas = allIdeas.filter((idea: any) => idea.submitted);

    // Filter ideas that belong to teams that should be shown
    const teamsToShowIds = teamsToShow.map((team: any) => team._id);
    const votingIdeas = ideas.filter(idea => 
      teamsToShowIds.includes(idea.teamId)
    );

    // Add team names to ideas
    const ideasWithTeams = await Promise.all(
      votingIdeas.map(async (idea: any) => {
        const team = teams.find((t: any) => t._id === idea.teamId);
        return {
          _id: idea._id,
          title: idea.name,
          description: idea.description || '',
          teamId: idea.teamId,
          teamName: team?.name || 'Unknown Team',
          presenters: idea.presenters || [],
          eventId: idea.eventId || activeEvent._id, // Use eventId from idea or fallback to activeEvent
          sessionId: idea.sessionId,
          userId: idea.userId,
          submitted: idea.submitted,
          createdAt: idea.createdAt,
        };
      })
    );

    // Sort ideas by team and then by title
    ideasWithTeams.sort((a, b) => {
      const teamCompare = a.teamName.localeCompare(b.teamName);
      if (teamCompare !== 0) return teamCompare;
      return a.title.localeCompare(b.title);
    });

    return {
      ideas: ideasWithTeams,
      event: activeEvent,
    };
  },
});

// Get voting status
export const getVotingStatus = query({
  args: {},
  handler: async (ctx): Promise<QueryResponse<{votingStarted: boolean}>> => {
    // Check if voting is enabled
    const votingStarted = await getVotingStatusSetting(ctx);

    return {
      votingStarted,
    };
  },
});

// Get all votes (for analytics/export)
export const getAllVotes = query({
  args: {},
  handler: async (ctx): Promise<QueryResponse<Array<any>>> => {
    return await getAllVotesHelper(ctx);
  },
});

// Get all quickfire votes (for analytics/export)  
export const getAllQuickfireVotes = query({
  args: {},
  handler: async (ctx): Promise<QueryResponse<Array<any>>> => {
    return await getAllQuickfireVotesHelper(ctx);
  },
});

// Phase 2 Optimization: Combined query function for Voting page
export const getVotingPageData = query({
  args: { username: v.string() },
  handler: async (ctx, args): Promise<QueryResponse<{
    votingIdeas: {ideas: Array<any>, event: any, noTeamSelected: boolean},
    votingStatus: {votingStarted: boolean},
    votingTeamStatus: {mode: "none" | "all" | "specific", teams: Array<any>},
    userVotes: Array<any>
  }>> => {
    // Get user and validate session
    const user = await getUserByUsername(ctx, args.username);

    if (!user) {
      return {
        votingIdeas: { ideas: [], event: null, noTeamSelected: false },
        votingStatus: { votingStarted: false },
        votingTeamStatus: { mode: "none", teams: [] },
        userVotes: []
      };
    }

    // Parallel queries for all voting data
    const [activeEvent, votingStarted] = await Promise.all([
      getActiveEventOrNull(ctx),
      getVotingStatusSetting(ctx)
    ]);

    if (!activeEvent) {
      return {
        votingIdeas: { ideas: [], event: null, noTeamSelected: false },
        votingStatus: { votingStarted },
        votingTeamStatus: { mode: "none", teams: [] },
        userVotes: []
      };
    }

    // Get user votes for this event using helper
    const userVotes = votingStarted ? await getUserVotesForEvent(ctx, user._id, activeEvent._id) : [];

    // Get voting ideas data (replicating getVotingIdeas logic)
    let votingIdeasResult = { ideas: [] as any[], event: activeEvent, noTeamSelected: false };

    if (votingStarted) {
      // Get active session and teams in parallel using helpers
      const [allSessions, allTeams] = await Promise.all([
        getSessionsByEvent(ctx, activeEvent._id),
        getTeamsByEvent(ctx, activeEvent._id)
      ]);
      
      const activeSession = allSessions.find((session: any) => session.active);
      const teams = allTeams.filter((team: any) => team.active);

      if (activeSession && activeSession.settings?.votingEnabled) {
        const votingTeams = teams.filter((team: any) => team.voting === true);
        const finishedTeamIds = activeSession.finishedTeams || [];
        const teamsToShow = votingTeams.filter((team: Doc<"teams">) => finishedTeamIds.includes(team._id));

        if (teamsToShow.length > 0) {
          const allIdeas = await getIdeasBySession(ctx, activeSession._id);
          const ideas = allIdeas.filter((idea: any) => idea.submitted);

          const teamsToShowIds = teamsToShow.map((team: any) => team._id);
          const votingIdeas = ideas.filter((idea: any) => teamsToShowIds.includes(idea.teamId));

          // Pre-compute team groupings on server side
          const ideasWithTeams = votingIdeas.map((idea: any) => {
            const team = teams.find((t: any) => t._id === idea.teamId);
            return {
              _id: idea._id,
              title: idea.name,
              description: idea.description || '',
              teamId: idea.teamId,
              teamName: team?.name || 'Unknown Team',
              presenters: idea.presenters || [],
              eventId: idea.eventId || activeEvent._id,
              sessionId: idea.sessionId,
              userId: idea.userId,
              submitted: idea.submitted,
              createdAt: idea.createdAt,
            };
          });

          // Sort ideas by team and then by title
          ideasWithTeams.sort((a, b) => {
            const teamCompare = a.teamName.localeCompare(b.teamName);
            if (teamCompare !== 0) return teamCompare;
            return a.title.localeCompare(b.title);
          });

          votingIdeasResult.ideas = ideasWithTeams;
        } else {
          votingIdeasResult.noTeamSelected = true;
        }
      }
    }

    // Get voting team status using helper
    const allTeams = await getTeamsByEvent(ctx, activeEvent._id);
    const activeTeams = allTeams.filter((team: any) => team.active);

    const votingTeams = activeTeams.filter((team: any) => team.voting === true);
    let mode: "none" | "all" | "specific" = "none";
    if (votingTeams.length === activeTeams.length && votingTeams.length > 0) {
      mode = "all";
    } else if (votingTeams.length === 1) {
      mode = "specific";
    }

    const votingTeamStatus = {
      mode,
      teams: activeTeams.map((team: any) => ({
        _id: team._id,
        name: team.name,
        voting: team.voting || false,
      })),
    };

    return {
      votingIdeas: votingIdeasResult,
      votingStatus: { votingStarted },
      votingTeamStatus,
      userVotes
    };
  },
});
import { v } from "convex/values";
import { action, query } from "./_generated/server";
import { api } from "./_generated/api";
import { getActiveEvent, getActiveEventOrNull } from "./lib/eventHelpers";
import { ConvexError } from "convex/values";
import { ErrorCode } from "./lib/responseTypes";
import { 
  createExportResponse
} from "./lib/responseUtils";
import type { ExportResponse } from "./lib/responseTypes";
import { 
  batchLoadUsers, 
  batchLoadTeams, 
  batchLoadSessions, 
  batchLoadEvents,
  batchLoadSparks,
  extractUniqueIds
} from "./lib/batchHelpers";
import { batchLoadQuickfireVotes, batchLoadVotesByIdea } from "./lib/voteHelpers";
import { getApprovedUsersForEvent } from "./lib/userHelpers";
import { getSubmittedIdeasBySession } from "./lib/ideaHelpers";
import { getQuickfiresBySession } from "./lib/quickfireHelpers";
import { getSessionById } from "./lib/sessionHelpers";

/**
 * Calculate Bayesian Average score for consistent scoring across voting systems
 * @param votes - Array of vote objects with score property
 * @param totalExpectedVoters - Total number of expected voters
 * @returns Calculated Bayesian average score
 */
function calculateBayesianAverage(votes: any[], totalExpectedVoters: number): number {
  if (totalExpectedVoters === 0) {
    return 0;
  }
  
  const actualVotesSum = votes.reduce((acc: number, vote: any) => acc + vote.score, 0);
  const voteCount = votes.length;
  const missingVotes = totalExpectedVoters - voteCount;
  const priorMean = 5; // Neutral score on 0-10 scale
  
  // Bayesian Average: (Sum of Actual Votes + (Missing Votes × Prior Mean)) / Total Expected Voters
  return Math.round(((actualVotesSum + (missingVotes * priorMean)) / totalExpectedVoters) * 10) / 10;
}

// Query to get sessions for active event (replaces /api/leaderboard/sessions)
export const getSessionsForLeaderboard = query({
  args: {},
  handler: async (ctx) => {
    // Get the active event first
    const activeEvent = await getActiveEventOrNull(ctx);
    
    if (!activeEvent) {
      return { sessions: [], event: null };
    }

    // Get sessions for the active event - only Ideas and Quickfire sessions (for leaderboards)
    const sessions = await ctx.db
      .query("sessions")
      .withIndex("by_event", (q: any) => q.eq("eventId", activeEvent._id))
      .order("desc") // Newest first like old implementation
      .take(100);

    // Filter to only Ideas and Quickfire sessions (leaderboard-compatible sessions)
    const leaderboardSessions = sessions.filter(session => 
      session.type === "Ideas" || session.type === "Quickfire"
    );

    // Format sessions to match the expected interface
    const formattedSessions = leaderboardSessions.map(session => ({
      id: session._id,
      name: session.name,
      active: session.active || false,
      createdAt: new Date(session.createdAt).toISOString(),
    }));

    return {
      sessions: formattedSessions,
      event: {
        id: activeEvent._id,
        name: activeEvent.name
      }
    };
  },
});

// Query to get teams list (replaces /api/teams/list)
export const getTeamsForDropdown = query({
  args: {},
  handler: async (ctx) => {
    // Get the active event first
    const activeEvent = await getActiveEventOrNull(ctx);
    
    if (!activeEvent) {
      return [];
    }

    // Get teams for the active event
    const teams = await ctx.db
      .query("teams")
      .withIndex("by_event", (q: any) => q.eq("eventId", activeEvent._id))
      .take(50);

    // Format teams to match the expected interface
    return teams.map(team => ({
      id: team._id,
      name: team.name
    }));
  },
});

// Helper function to process Ideas for leaderboard
async function processIdeasForLeaderboard(
  ctx: any,
  session: any,
  activeEvent: any,
  totalExpectedVoters: number,
  teamLookup: Map<string, any>
) {
  const ideas = await getSubmittedIdeasBySession(ctx, session._id);

  // OPTIMIZED: Batch load all votes for ideas in this session
  const ideaIds = ideas.map(idea => idea._id);
  const votesByIdea = await batchLoadVotesByIdea(ctx, ideaIds);

  const ideasData = [];

  for (const idea of ideas) {
    // OPTIMIZED: Get votes from batch-loaded map instead of individual queries
    const votes = votesByIdea.get(idea._id) || [];
    const voteCount = votes.length;

    // Skip ideas with no votes to avoid showing all ideas with score 5
    // This provides cleaner leaderboard UX - only show ideas that have been voted on
    if (voteCount === 0) {
      continue;
    }

    // Calculate Bayesian Average score
    // 
    // INTENTIONAL DOMAIN SEPARATION: Ideas and Quickfire voting systems are distinct domains
    // that use identical Bayesian formulas for consistency and fairness across voting types.
    // This is NOT code duplication - consolidation would inappropriately couple separate
    // voting systems that serve different purposes and contexts.
    //
    // Ideas voting system: Extended deliberation on team-submitted ideas with detailed feedback
    // Mathematical consistency: Uses same Bayesian parameters as Quickfire for fair comparison
    const totalScore = calculateBayesianAverage(votes, totalExpectedVoters);

    // OPTIMIZED: Use team lookup map instead of individual database calls
    const team = teamLookup.get(idea.teamId);
    const teamName = team?.name || "Unknown Team";

    ideasData.push({
      type: "idea",
      ideaId: idea._id,
      ideaName: idea.name,
      ideaDescription: idea.description || "",
      teamName,
      eventName: activeEvent.name,
      sessionName: session.name,
      totalScore,
      voteCount,
      totalExpectedVoters
    });
  }

  return ideasData;
}

// Helper function to process Quickfire for leaderboard
async function processQuickfireForLeaderboard(
  ctx: any,
  session: any,
  activeEvent: any,
  totalExpectedVoters: number
) {
  const quickfireItems = await getQuickfiresBySession(ctx, session._id);

  // OPTIMIZED: Batch load all votes for quickfires in this session
  const quickfireIds = quickfireItems.map(qf => qf._id);
  const votesByQuickfire = await batchLoadQuickfireVotes(ctx, quickfireIds);

  const quickfireData = [];

  for (const quickfire of quickfireItems) {
    // OPTIMIZED: Get votes from batch-loaded map instead of individual queries
    const votes = votesByQuickfire.get(quickfire._id) || [];
    const voteCount = votes.length;

    // Skip quickfires with no votes to avoid showing all quickfires with score 5
    // This provides cleaner leaderboard UX - only show quickfires that have been voted on
    if (voteCount === 0) {
      continue;
    }

    // Calculate Bayesian Average score
    // 
    // INTENTIONAL DOMAIN SEPARATION: Ideas and Quickfire voting systems are distinct domains
    // that use identical Bayesian formulas for consistency and fairness across voting types.
    // This is NOT code duplication - consolidation would inappropriately couple separate
    // voting systems that serve different purposes and contexts.
    //
    // Quickfire voting system: Rapid voting on admin-submitted questions/ideas
    // Mathematical consistency: Uses same Bayesian parameters as Ideas for fair comparison
    const totalScore = calculateBayesianAverage(votes, totalExpectedVoters);

    quickfireData.push({
      type: "quickfire",
      quickfireId: quickfire._id,
      idea: quickfire.idea,
      comments: quickfire.comments || "",
      question: quickfire.question || "",
      eventName: activeEvent.name,
      sessionName: session.name,
      sessionId: session._id,
      totalScore,
      voteCount,
      totalExpectedVoters
    });
  }

  return quickfireData;
}

// Helper function to build unified leaderboard from Ideas and Quickfire data
function buildLeaderboard(ideasData: any[], quickfireData: any[]): any[] {
  const leaderboardData = [...ideasData, ...quickfireData];
  
  // Sort by totalScore descending (highest first)
  leaderboardData.sort((a, b) => b.totalScore - a.totalScore);

  return leaderboardData;
}

// Main leaderboard query (replaces /api/leaderboard?sessionId=optional)
export const getLeaderboard = query({
  args: { sessionId: v.optional(v.id("sessions")) },
  handler: async (ctx, args) => {
    // Get active event
    const activeEvent = await getActiveEvent(ctx);

    // Get total expected voters (users registered for active event, excluding admins) for Bayesian calculation
    // OPTIMIZED: Use helper function for approved users filtering
    const eventUsers = await getApprovedUsersForEvent(ctx, activeEvent._id);
    const totalExpectedVoters = eventUsers.length;

    let targetSessions;
    if (args.sessionId) {
      // Get specific session
      const session = await getSessionById(ctx, args.sessionId);
      if (!session) {
        throw new ConvexError({
          message: "Session not found",
          code: ErrorCode.RESOURCE_NOT_FOUND
        });
      }
      targetSessions = [session];
    } else {
      // Get all Ideas sessions for active event (all sessions leaderboard excludes Quickfire)
      // Use compound index to eliminate filter operation
      const [ideasSessions, undefinedTypeSessions] = await Promise.all([
        ctx.db
          .query("sessions")
          .withIndex("by_event_type", (q: any) => q.eq("eventId", activeEvent._id).eq("type", "Ideas"))
          .take(100),
        ctx.db
          .query("sessions")
          .withIndex("by_event", (q: any) => q.eq("eventId", activeEvent._id))
          .filter((q: any) => q.eq(q.field("type"), undefined))
          .take(100)
      ]);
      
      // Combine both result sets
      targetSessions = [...ideasSessions, ...undefinedTypeSessions];
    }

    // OPTIMIZATION: Batch load all teams for the active event to eliminate N+1 queries
    const allTeams = await ctx.db
      .query("teams")
      .withIndex("by_event", (q: any) => q.eq("eventId", activeEvent._id))
      .take(100);
    
    // Create team lookup map for O(1) access
    const teamLookup = new Map(allTeams.map(team => [team._id, team]));

    const allIdeasData = [];
    const allQuickfireData = [];

    // Process each session
    for (const session of targetSessions) {
      if (session.type === "Quickfire") {
        const quickfireData = await processQuickfireForLeaderboard(
          ctx,
          session,
          activeEvent,
          totalExpectedVoters
        );
        allQuickfireData.push(...quickfireData);
      } else {
        const ideasData = await processIdeasForLeaderboard(
          ctx,
          session,
          activeEvent,
          totalExpectedVoters,
          teamLookup
        );
        allIdeasData.push(...ideasData);
      }
    }

    return buildLeaderboard(allIdeasData, allQuickfireData);
  },
});

// Action to export ideas data as CSV (replaces /api/sessions/export)
export const exportIdeasData = action({
  args: { username: v.string() },
  handler: async (ctx, args): Promise<ExportResponse> => {
    // Authenticate admin user using query
    await ctx.runQuery(api.users.validateAdminUserQuery, { username: args.username });
    
    // OPTIMIZED: Execute independent operations in parallel after authentication
    const [activeEvent, allIdeas, allVotes, allSessions, allUsers] = await Promise.all([
      ctx.runQuery(api.events.getActiveEvent),
      ctx.runQuery(api.ideas.getAllDataGroupedByTeam, {}),
      ctx.runQuery(api.votes.getAllVotes),
      ctx.runQuery(api.sessions.getSessionsByActiveEvent),
      ctx.runQuery(api.users.getAllUsers)
    ]);
    
    if (!activeEvent) {
      throw new ConvexError({
        message: "No active event found",
        code: ErrorCode.RESOURCE_NOT_FOUND
      });
    }
    
    // Create lookups for efficient access
    const sessionLookup = new Map(allSessions.map(s => [s._id, s]));
    const userLookup = new Map(allUsers.map(u => [u._id, u]));
    
    // Group votes by idea for efficient lookup
    const votesByIdea = new Map<string, any[]>();
    allVotes.forEach((vote: any) => {
      const ideaId = vote.ideaId;
      if (!votesByIdea.has(ideaId)) {
        votesByIdea.set(ideaId, []);
      }
      votesByIdea.get(ideaId)!.push(vote);
    });
    
    // Create CSV header
    let csvContent = "IDEA,SESSION,DAY,SCORE,VOTER,COMMENT\n";
    
    // Process each team's ideas
    for (const teamId in allIdeas) {
      const teamData = allIdeas[teamId];
      
      for (const idea of teamData.ideas) {
        const session = sessionLookup.get(idea.sessionId);
        const sessionName = session?.name || "Unknown Session";
        const dayNumber = session?.day || 1;
        
        // Get votes for this idea
        const ideaVotes = votesByIdea.get(idea._id) || [];
        
        if (ideaVotes.length === 0) {
          // Add row with no votes
          const ideaText = (idea.name || "").replace(/"/g, '""'); // Escape quotes
          csvContent += `"${ideaText}","${sessionName}",${dayNumber},,,""\n`;
        } else {
          for (const vote of ideaVotes) {
            const voter = userLookup.get(vote.userId);
            const voterName = voter?.username || "Unknown";
            const comment = (vote.comment || "").replace(/"/g, '""'); // Escape quotes
            const ideaText = (idea.name || "").replace(/"/g, '""'); // Escape quotes
            
            csvContent += `"${ideaText}","${sessionName}",${dayNumber},${vote.score || ""},"${voterName}","${comment}"\n`;
          }
        }
      }
    }

    // Return CSV content
    return createExportResponse(
      csvContent,
      `ideas-data-${activeEvent.name.replace(/\s+/g, '-')}-${new Date().toISOString().split('T')[0]}.csv`
    );
  },
});

// Action to export teams data as CSV (replaces /api/teams/export)
export const exportTeamsData = action({
  args: { username: v.string() },
  handler: async (ctx, args): Promise<ExportResponse> => {
    // Authenticate admin user using query
    await ctx.runQuery(api.users.validateAdminUserQuery, { username: args.username });
    
    // OPTIMIZED: Execute independent operations in parallel after authentication
    const [activeEvent, teamsWithUsers] = await Promise.all([
      ctx.runQuery(api.events.getActiveEvent),
      ctx.runQuery(api.teams.getTeamsByActiveEvent)
    ]);
    
    if (!activeEvent) {
      throw new ConvexError({
        message: "No active event found",
        code: ErrorCode.RESOURCE_NOT_FOUND
      });
    }
    
    // Create CSV header
    let csvContent = "Team,Team Leads,Team Members\n";
    
    // Process each team
    for (const team of teamsWithUsers) {
      const teamLeads = team.users.filter((user: { role: string }) => user.role === "teamLead").map((user: { username: string }) => user.username);
      const teamMembers = team.users.filter((user: { role: string }) => user.role === "teamMember").map((user: { username: string }) => user.username);
      
      const leadsStr = teamLeads.join('-');
      const membersStr = teamMembers.join('-');
      
      csvContent += `"${team.name}","${leadsStr}","${membersStr}"\n`;
    }

    // Return CSV content
    return createExportResponse(
      csvContent,
      `teams-data-${activeEvent.name.replace(/\s+/g, '-')}-${new Date().toISOString().split('T')[0]}.csv`
    );
  },
});

// Teams leaderboard query - aggregates team scores by summing their ideas' Bayesian averages (Ideas sessions only)
export const getTeamsLeaderboard = query({
  args: { sessionId: v.optional(v.id("sessions")) },
  handler: async (ctx, args) => {
    if (args.sessionId) {
      // Get specific session - must be Ideas type for teams competition
      const session = await getSessionById(ctx, args.sessionId);
      if (!session || session.type !== "Ideas") {
        return []; // No teams competition for non-Ideas sessions
      }
    }

    // OPTIMIZED: Get ideas leaderboard data for Ideas sessions only
    const ideasLeaderboard = await ctx.runQuery(api.analytics.getLeaderboard, args);
    
    // Filter only idea types (should already be Ideas only, but extra safety)
    const ideasOnly = ideasLeaderboard.filter(item => item.type === "idea");
    
    // Group by team and sum scores
    const teamsMap = new Map();
    
    ideasOnly.forEach(idea => {
      const teamName = idea.teamName;
      const current = teamsMap.get(teamName) || { 
        totalScore: 0, 
        ideaCount: 0,
        eventName: idea.eventName,
        sessionName: idea.sessionName 
      };
      
      teamsMap.set(teamName, {
        totalScore: current.totalScore + idea.totalScore,
        ideaCount: current.ideaCount + 1,
        eventName: idea.eventName,
        sessionName: args.sessionId ? idea.sessionName : "Ideas Competition"
      });
    });
    
    // Convert to array and sort by total score descending
    const teamsLeaderboard = Array.from(teamsMap.entries())
      .map(([teamName, data]) => ({
        teamName,
        totalScore: Math.round(data.totalScore * 10) / 10, // Same rounding as ideas
        ideaCount: data.ideaCount,
        eventName: data.eventName,
        sessionName: data.sessionName
      }))
      .sort((a, b) => b.totalScore - a.totalScore);
    
    return teamsLeaderboard;
  },
});


// Print functions - Replace /api/print/* endpoints

// Get ideas for team printing (replaces /api/print/ideas)
export const getPrintIdeasByTeam = query({
  args: { teamId: v.id("teams") },
  handler: async (ctx, args) => {
    // Get ideas for this team (limit to 100 most recent to avoid unbounded queries)
    const ideas = await ctx.db
      .query("ideas")
      .withIndex("by_team", (q) => q.eq("teamId", args.teamId))
      .order("desc") // Newest first like original
      .take(100);

    // OPTIMIZED: Batch load all related entities to eliminate N+1 queries
    const eventIds = extractUniqueIds(ideas, "eventId");
    const sessionIds = extractUniqueIds(ideas, "sessionId");
    const userIds = extractUniqueIds(ideas, "userId");
    const teamIds = extractUniqueIds(ideas, "teamId");

    const [events, sessions, users, teams] = await Promise.all([
      batchLoadEvents(ctx, eventIds),
      batchLoadSessions(ctx, sessionIds),
      batchLoadUsers(ctx, userIds),
      batchLoadTeams(ctx, teamIds)
    ]);

    // Populate related data for each idea
    const populatedIdeas = ideas.map(idea => {
      const event = events.get(idea.eventId);
      const session = sessions.get(idea.sessionId);
      const user = users.get(idea.userId);
      const team = teams.get(idea.teamId);

      return {
        ...idea,
        eventName: event?.name || "Unknown Event",
        sessionName: session?.name || "Unknown Session", 
        userName: user?.name || "Unknown User",
        teamName: team?.name || "Unknown Team",
        // Presenters already populated in ideas table
        presenters: idea.presenters || []
      };
    });

    return populatedIdeas;
  },
});

// Get spark submissions for team printing (replaces /api/print/sparks)
export const getPrintSparkSubmissionsByTeam = query({
  args: { teamId: v.id("teams") },
  handler: async (ctx, args) => {
    // Get all spark submissions for this team
    const sparkSubmissions = await ctx.db
      .query("sparkSubmissions")
      .withIndex("by_team", (q) => q.eq("teamId", args.teamId))
      .order("desc") // Newest first like original
      .take(200);

    // OPTIMIZED: Batch load all related entities to eliminate N+1 queries
    const eventIds = extractUniqueIds(sparkSubmissions, "eventId");
    const sessionIds = extractUniqueIds(sparkSubmissions.filter(s => s.sessionId), "sessionId");
    const userIds = extractUniqueIds(sparkSubmissions, "userId");
    const teamIds = extractUniqueIds(sparkSubmissions, "teamId");
    const sparkIds = extractUniqueIds(sparkSubmissions, "sparkId");

    const [events, sessions, users, teams, sparks] = await Promise.all([
      batchLoadEvents(ctx, eventIds),
      batchLoadSessions(ctx, sessionIds),
      batchLoadUsers(ctx, userIds),
      batchLoadTeams(ctx, teamIds),
      batchLoadSparks(ctx, sparkIds)
    ]);

    // Populate related data for each spark submission
    const populatedSparkSubmissions = sparkSubmissions.map(submission => {
      const event = events.get(submission.eventId);
      const session = submission.sessionId ? sessions.get(submission.sessionId) : null;
      const user = users.get(submission.userId);
      const team = teams.get(submission.teamId);
      const spark = sparks.get(submission.sparkId);

      return {
        ...submission,
        eventName: event?.name || "Unknown Event",
        sessionName: session?.name || "Unknown Session", 
        userName: user?.name || "Unknown User",
        teamName: team?.name || "Unknown Team",
        sparkName: spark?.name || "Unknown Spark",
        sparkColor: spark?.color || "#b96eff",
        sparkFields: spark?.fields || []
      };
    });

    return populatedSparkSubmissions;
  },
});

// Query to get data summary for analytics - checks if there's any voting data available
export const getDataSummaryForAnalytics = query({
  args: {},
  handler: async (ctx) => {
    const activeEvent = await getActiveEventOrNull(ctx);
    if (!activeEvent) {
      return { hasData: false, votesCount: 0, quickfireVotesCount: 0 };
    }
    
    // Check for actual voting data since analytics is for voting analysis
    // Most efficient approach: just check if ANY votes exist, don't even count them all
    const hasVotes = await ctx.db
      .query("votes")
      .withIndex("by_event", (q: any) => q.eq("eventId", activeEvent._id))
      .first() !== null;
    
    // For quickfire votes, use the most efficient approach by checking each quickfire individually
    let hasQuickfireVotes = false;
    if (!hasVotes) { // Only check if we don't already have votes
      // Get quickfires for this event (small, bounded result set)
      const quickfires = await ctx.db
        .query("quickfires")
        .withIndex("by_event", (q: any) => q.eq("eventId", activeEvent._id))
        .take(100);
      
      if (quickfires.length > 0) {
        // Check each quickfire individually - stop at first vote found
        for (const quickfire of quickfires) {
          const hasVoteForQuickfire = await ctx.db
            .query("quickfireVotes")
            .withIndex("by_quickfire", (q: any) => q.eq("quickfireId", quickfire._id))
            .first() !== null;
          
          if (hasVoteForQuickfire) {
            hasQuickfireVotes = true;
            break; // Stop as soon as we find any vote
          }
        }
      }
    }
    
    return {
      hasData: hasVotes || hasQuickfireVotes,
      votesCount: hasVotes ? 1 : 0, // We don't need exact count, just existence
      quickfireVotesCount: hasQuickfireVotes ? 1 : 0 // We don't need exact count, just existence
    };
  }
});



import { v } from "convex/values";
import { mutation, query } from "./_generated/server";
import { getSettingByKey, updateOrCreateSetting, getAllSettings as getAllSettingsHelper, deleteSetting as deleteSettingHelper } from "./lib/settingsHelpers";
import { requireAdmin } from "./lib/auth";
import { batchDeleteStalePresence, getAllPresenceRecords } from "./lib/presenceHelpers";
import { 
  MutationResponse, 
  DeleteResponse,
  QueryResponse 
} from "./lib/responseTypes";
import { 
  createSuccessResponse, 
  createDeleteResponse 
} from "./lib/responseUtils";
import { Id } from "./_generated/dataModel";

export const getSetting = query({
  args: { key: v.string() },
  handler: async (ctx, args): Promise<QueryResponse<any>> => {
    const setting = await getSettingByKey(ctx, args.key);
    return setting?.value || null;
  },
});

export const getAllSettings = query({
  args: {},
  handler: async (ctx): Promise<QueryResponse<any[]>> => {
    const settingsMap = await getAllSettingsHelper(ctx);
    // Convert Map to array format for compatibility
    return Array.from(settingsMap.entries()).map(([key, value]) => ({ key, value }));
  },
});

export const getRestoreStatus = query({
  args: {},
  handler: async (ctx): Promise<QueryResponse<boolean>> => {
    const status = await getSettingByKey(ctx, "restore_in_progress");
    return status?.value || false;
  },
});

export const setSetting = mutation({
  args: {
    username: v.string(),
    key: v.string(),
    value: v.any(),
  },
  handler: async (ctx, args): Promise<MutationResponse<{ id: Id<"settings"> }>> => {
    // Authenticate admin user
    await requireAdmin(ctx, args.username, true); // Use ConvexError for admin mutations
    const settingId = await updateOrCreateSetting(ctx, args.key, args.value);
    return createSuccessResponse({ id: settingId });
  },
});

export const deleteSetting = mutation({
  args: { username: v.string(), key: v.string() },
  handler: async (ctx, args): Promise<DeleteResponse> => {
    // Authenticate admin user
    await requireAdmin(ctx, args.username, true); // Use ConvexError for admin mutations
    const deleted = await deleteSettingHelper(ctx, args.key);
    return createDeleteResponse(deleted);
  },
});

// Commonly used settings helpers
export const getAutoApprovalSetting = query({
  args: {},
  handler: async (ctx): Promise<QueryResponse<boolean>> => {
    const setting = await getSettingByKey(ctx, "auto-approval");
    return setting?.value || false;
  },
});

export const setAutoApprovalSetting = mutation({
  args: { username: v.string(), enabled: v.boolean() },
  handler: async (ctx, args): Promise<MutationResponse<{ id: Id<"settings"> }>> => {
    // Authenticate admin user
    await requireAdmin(ctx, args.username, true); // Use ConvexError for admin mutations
    const settingId = await updateOrCreateSetting(ctx, "auto-approval", args.enabled);
    return createSuccessResponse({ id: settingId });
  },
});

export const getTeamSelectionSetting = query({
  args: {},
  handler: async (ctx): Promise<QueryResponse<boolean>> => {
    const setting = await getSettingByKey(ctx, "team-selection");
    // If no setting exists, default to true (enabled)
    // If setting exists, use its value
    return setting?.value ?? true;
  },
});

export const setTeamSelectionSetting = mutation({
  args: { username: v.string(), enabled: v.boolean() },
  handler: async (ctx, args): Promise<MutationResponse<{ id: Id<"settings"> }>> => {
    // Authenticate admin user
    await requireAdmin(ctx, args.username, true); // Use ConvexError for admin mutations
    const settingId = await updateOrCreateSetting(ctx, "team-selection", args.enabled);
    return createSuccessResponse({ id: settingId });
  },
});

export const getVotingSetting = query({
  args: {},
  handler: async (ctx): Promise<QueryResponse<boolean>> => {
    const setting = await getSettingByKey(ctx, "voting");
    return setting?.value || false; // Default to disabled
  },
});

export const setVotingSetting = mutation({
  args: { username: v.string(), value: v.boolean() },
  handler: async (ctx, args): Promise<MutationResponse<{ id: Id<"settings"> }>> => {
    // Authenticate admin user
    await requireAdmin(ctx, args.username, true); // Use ConvexError for admin mutations
    const settingId = await updateOrCreateSetting(ctx, "voting", args.value);
    return createSuccessResponse({ id: settingId });
  },
});

export const getOnlineUsersSetting = query({
  args: {},
  handler: async (ctx): Promise<QueryResponse<boolean>> => {
    const setting = await getSettingByKey(ctx, "online-users-enabled");
    return setting?.value || false; // Default to disabled for performance
  },
});

export const setOnlineUsersSetting = mutation({
  args: { username: v.string(), enabled: v.boolean() },
  handler: async (ctx, args): Promise<MutationResponse<{ id: Id<"settings">; presenceRecordsCleared?: number }>> => {
    // Authenticate admin user
    await requireAdmin(ctx, args.username, true); // Use ConvexError for admin mutations
    const settingId = await updateOrCreateSetting(ctx, "online-users-enabled", args.enabled);

    let presenceRecordsCleared = 0;
    // If disabling, clean up all presence records using batch deletion
    if (!args.enabled) {
      const allPresence = await getAllPresenceRecords(ctx);
      if (allPresence.length > 0) {
        await batchDeleteStalePresence(ctx, allPresence);
        presenceRecordsCleared = allPresence.length;
      }
    }

    return createSuccessResponse({ 
      id: settingId, 
      ...(presenceRecordsCleared > 0 && { presenceRecordsCleared })
    });
  },
});
import { v } from "convex/values";
import { mutation, query } from "./_generated/server";
import { getUserByIdOrThrow } from "./lib/userHelpers";
import { getSessionByIdOrThrow } from "./lib/sessionHelpers";
import { createCreateResponse, createUpdateResponse, createDeleteResponse, throwConvexError } from "./lib/responseUtils";
import { CreateResponse, UpdateResponse, DeleteResponse, ErrorCode } from "./lib/responseTypes";
import { 
  getSparkSubmissionsBySession, 
  getSparkSubmissionsByUser as getSparkSubmissionsByUserHelper,
  getUserSparkSubmissionForSession,
  getSparkSubmissionByIdOrThrow
} from "./lib/sparkHelpers";
import { enrichSparkSubmissionsWithComprehensiveData } from "./lib/enrichmentHelpers";

// Submit spark form data
export const submitSparkData = mutation({
  args: {
    sessionId: v.id("sessions"),
    sparkId: v.id("sparks"),
    userId: v.id("users"),
    data: v.any(), // JSON object with field values
  },
  handler: async (ctx, args): Promise<CreateResponse<"sparkSubmissions">> => {
    // Get user
    const user = await getUserByIdOrThrow(ctx, args.userId);

    // Get session to validate and get team/event info
    const session = await getSessionByIdOrThrow(ctx, args.sessionId);

    // Validate that this is a Sparks session with the correct sparkId
    if (session.type !== 'Sparks' || session.sparkId !== args.sparkId) {
      throwConvexError("Invalid session or spark configuration", ErrorCode.VALIDATION_ERROR);
    }

    // Get user's team from the active event
    const userEvent = user.events.find((e: any) => e.eventId === session.eventId);
    if (!userEvent || !userEvent.teamId) {
      throwConvexError("User not assigned to a team for this event", ErrorCode.OPERATION_NOT_ALLOWED);
    }

    // Always create new submission - allow multiple submissions per user per session
    const now = Date.now();
    const id = await ctx.db.insert("sparkSubmissions", {
      sessionId: args.sessionId,
      userId: args.userId,
      sparkId: args.sparkId,
      teamId: userEvent.teamId,
      eventId: session.eventId,
      data: args.data,
      submittedAt: now,
      updatedAt: now,
    });
    
    return createCreateResponse(id);
  },
});

// Get spark submissions for a session
export const getSparkSubmissions = query({
  args: {
    sessionId: v.id("sessions"),
  },
  handler: async (ctx, args) => {
    const submissions = await getSparkSubmissionsBySession(ctx, args.sessionId);
    
    // Use comprehensive enrichment for better performance and consistency
    return await enrichSparkSubmissionsWithComprehensiveData(ctx, submissions);
  },
});

// Get spark submissions by current user
export const getSparkSubmissionsByUser = query({
  args: {
    userId: v.id("users"),
    sessionId: v.optional(v.id("sessions")),
  },
  handler: async (ctx, args) => {
    let submissions;
    
    if (args.sessionId) {
      // Use optimized index for user+session lookup
      const submission = await getUserSparkSubmissionForSession(ctx, args.userId, args.sessionId);
      submissions = submission ? [submission] : [];
    } else {
      // Use helper for all user submissions
      submissions = await getSparkSubmissionsByUserHelper(ctx, args.userId);
    }
    
    // Enrich with comprehensive data
    return await enrichSparkSubmissionsWithComprehensiveData(ctx, submissions);
  },
});

// Update spark submission
export const updateSparkSubmission = mutation({
  args: {
    submissionId: v.id("sparkSubmissions"),
    userId: v.id("users"),
    data: v.any(),
  },
  handler: async (ctx, args): Promise<UpdateResponse> => {
    // Get submission and verify ownership
    const submission = await getSparkSubmissionByIdOrThrow(ctx, args.submissionId);

    if (submission.userId !== args.userId) {
      throwConvexError("Not authorized to update this submission", ErrorCode.FORBIDDEN);
    }

    await ctx.db.patch(args.submissionId, {
      data: args.data,
      updatedAt: Date.now(),
    });
    
    return createUpdateResponse();
  },
});

// Delete spark submission
export const deleteSparkSubmission = mutation({
  args: {
    submissionId: v.id("sparkSubmissions"),
    userId: v.id("users"),
  },
  handler: async (ctx, args): Promise<DeleteResponse> => {
    // Get submission and verify ownership
    const submission = await getSparkSubmissionByIdOrThrow(ctx, args.submissionId);

    if (submission.userId !== args.userId) {
      throwConvexError("Not authorized to delete this submission", ErrorCode.FORBIDDEN);
    }

    await ctx.db.delete(args.submissionId);
    
    return createDeleteResponse();
  },
});
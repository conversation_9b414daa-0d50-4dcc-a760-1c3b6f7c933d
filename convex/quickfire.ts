import { v } from "convex/values";
import { mutation, query, internalMutation } from "./_generated/server";
import { internal } from "./_generated/api";
import { getActiveEvent, getActiveEventOrNull } from "./lib/eventHelpers";
import { getUserByUsername } from "./lib/userHelpers";
import { getActiveSessionForEvent, getActiveSessionForActiveEvent, getSessionsByTypeForActiveEvent, getSessionByIdOrThrow } from "./lib/sessionHelpers";
import { requireAdmin } from "./lib/auth";
import {
  getQuickfiresBySessionOrdered,
  getActiveQuickfiresBySession,
  getQuickfiresByEvent,
  enrichQuickfiresWithDetails,
  getQuickfireById,
  getQuickfireSessionData,
  createQuickfire,
  updateQuickfire,
  deleteQuickfire,
  batchUpdateQuickfires
} from "./lib/quickfireHelpers";
import {
  getQuickfireVotesByQuickfire,
  getQuickfireVotesByUser,
  getUserQuickfireVotesForQuickfires,
  batchLoadQuickfireVoteStats,
  batchDeleteQuickfireVotes,
  upsertQuickfireVote
} from "./lib/voteHelpers";
import { 
  MutationResponse,
  CreateResponse,
  UpdateResponse,
  DeleteResponse,
  ErrorCode
} from "./lib/responseTypes";
import {
  createCreateResponse,
  createUpdateResponse,
  createDeleteResponse,
  createSuccessResponse,
  throwConvexError
} from "./lib/responseUtils";





// Get quickfire items for voting
export const getQuickfireItemsForVoting = query({
  args: { username: v.string() },
  handler: async (ctx, args) => {
    // Get user by username
    const user = await getUserByUsername(ctx, args.username);

    if (!user) {
      throwConvexError("User not found", ErrorCode.USER_NOT_FOUND);
    }

    // Get active event
    const activeEvent = await getActiveEventOrNull(ctx);

    if (!activeEvent) {
      return [];
    }

    // Get active session for this event
    const activeSession = await getActiveSessionForEvent(ctx, activeEvent._id);

    if (!activeSession) {
      return [];
    }

    // Check if session type is Quickfire
    if (activeSession.type !== "Quickfire") {
      return [];
    }

    // Get quickfire items with votingActive=true for the active session
    const quickfireItems = await getActiveQuickfiresBySession(ctx, activeSession._id);

    return quickfireItems;
  },
});

// Get quickfire votes for current user
export const getQuickfireVotesByUserForUser = query({
  args: { username: v.string() },
  handler: async (ctx, args) => {
    // Get user by username
    const user = await getUserByUsername(ctx, args.username);

    if (!user) {
      throwConvexError("User not found", ErrorCode.USER_NOT_FOUND);
    }

    // Get all votes by this user using optimized helper
    const votes = await getQuickfireVotesByUser(ctx, user._id);

    // Transform to match the expected format
    const votesMap: { [key: string]: { quickfireId: string; score: number; voter: string } } = {};
    votes.forEach(vote => {
      votesMap[vote.quickfireId] = {
        quickfireId: vote.quickfireId,
        score: vote.score,
        voter: vote.voter
      };
    });

    return votesMap;
  },
});

// Submit quickfire vote
export const submitQuickfireVote = mutation({
  args: {
    username: v.string(),
    quickfireId: v.id("quickfires"),
    score: v.number(),
  },
  handler: async (ctx, args): Promise<CreateResponse<"quickfireVotes">> => {
    // Get user by username
    const user = await getUserByUsername(ctx, args.username);

    if (!user) {
      throwConvexError("User not found", ErrorCode.USER_NOT_FOUND);
    }

    // Use helper function to create or update quickfire vote
    const result = await upsertQuickfireVote(ctx, {
      userId: user._id,
      quickfireId: args.quickfireId,
      score: args.score,
      voter: user.username, // Store username for compatibility
    });

    return createCreateResponse(result.voteId);
  },
});

// Get active session for quickfire checking
export const getActiveSession = query({
  args: {},
  handler: async (ctx) => {
    // Get active event
    const activeEvent = await getActiveEventOrNull(ctx);

    if (!activeEvent) {
      return null;
    }

    // Get active session for this event
    const activeSession = await getActiveSessionForActiveEvent(ctx);

    if (!activeSession) {
      return null;
    }

    return {
      _id: activeSession._id,
      name: activeSession.name,
      type: activeSession.type,
      active: activeSession.active,
    };
  },
});



// ADMIN FUNCTIONS FOR QUICKFIRE MANAGEMENT (quickfires table - admin-created voting items)

// Get all quickfire items for admin management
export const getAllQuickfireItems = query({
  args: { username: v.string() },
  handler: async (ctx, args) => {
    // Admin authorization check
    await requireAdmin(ctx, args.username, false); // Use Error for admin internal tooling
    // Get active event
    const activeEvent = await getActiveEvent(ctx);

    // Get all quickfire items for the active event with enriched data
    const quickfireItems = await getQuickfiresByEvent(ctx, activeEvent._id);
    const itemsWithSessionName = await enrichQuickfiresWithDetails(ctx, quickfireItems);

    return itemsWithSessionName.sort((a, b) => (b.order || 0) - (a.order || 0));
  },
});

// Get quickfire items for active session only
export const getQuickfireItemsForActiveSession = query({
  args: { username: v.string() },
  handler: async (ctx, args) => {
    // Admin authorization check
    await requireAdmin(ctx, args.username, false); // Use Error for admin internal tooling
    // Get active event
    const activeEvent = await getActiveEventOrNull(ctx);

    if (!activeEvent) {
      return { error: "No active event found", items: [] };
    }

    // Get active session
    const activeSession = await getActiveSessionForActiveEvent(ctx);

    if (!activeSession) {
      return { error: "No active session found", items: [] };
    }

    // Check if the session type is Quickfire
    if (activeSession.type !== "Quickfire") {
      return { error: "No Active Quickfire Session", items: [] };
    }

    // Get quickfire items for the active session
    const quickfireItems = await getQuickfiresBySessionOrdered(ctx, activeSession._id);

    const items = quickfireItems.map(item => ({
      ...item,
      sessionName: activeSession.name,
      eventName: activeEvent.name
    })).sort((a, b) => (b.order || 0) - (a.order || 0));

    return { error: null, items };
  },
});

// Create quickfire item (admin)
export const createQuickfireItem = mutation({
  args: {
    username: v.string(),
    idea: v.string(),
    comments: v.optional(v.string()),
    question: v.optional(v.string()),
    sessionId: v.optional(v.id("sessions")),
  },
  handler: async (ctx, args): Promise<CreateResponse<"quickfires">> => {
    // Admin authorization check
    await requireAdmin(ctx, args.username, false); // Use Error for admin internal tooling
    // Get active event
    const activeEvent = await getActiveEvent(ctx);

    let targetSession;
    
    if (args.sessionId) {
      // Use provided session with validation using helper
      targetSession = await getSessionByIdOrThrow(ctx, args.sessionId);
    } else {
      // Fallback to active session
      targetSession = await getActiveSessionForActiveEvent(ctx);

      if (!targetSession) {
        throwConvexError("No active session found", ErrorCode.RESOURCE_NOT_FOUND);
      }
    }

    // Check if the session type is Quickfire
    if (targetSession.type !== "Quickfire") {
      throwConvexError("Target session is not a Quickfire session", ErrorCode.OPERATION_NOT_ALLOWED);
    }

    // Get the current max order for this session
    const existingItems = await getQuickfiresBySessionOrdered(ctx, targetSession._id);
    
    const maxOrder = existingItems.length > 0 
      ? Math.max(...existingItems.map(item => item.order || 0)) 
      : 0;

    // Create the quickfire item with higher order number (newest first)
    const quickfireId = await createQuickfire(ctx, {
      idea: args.idea,
      comments: args.comments || "",
      question: args.question || "",
      eventId: activeEvent._id,
      sessionId: targetSession._id,
      votingActive: false,
      order: maxOrder + 1,
    });

    return createCreateResponse(quickfireId);
  },
});

// Update quickfire item
export const updateQuickfireItem = mutation({
  args: {
    username: v.string(),
    quickfireId: v.id("quickfires"),
    idea: v.string(),
    comments: v.optional(v.string()),
    question: v.optional(v.string()),
    sessionId: v.optional(v.id("sessions")),
  },
  handler: async (ctx, args): Promise<UpdateResponse> => {
    // Admin authorization check
    await requireAdmin(ctx, args.username, false); // Use Error for admin internal tooling
    
    const updateData: any = {
      idea: args.idea,
      comments: args.comments || "",
      question: args.question || "",
    };

    // If sessionId is provided, update it
    if (args.sessionId) {
      updateData.sessionId = args.sessionId;
    }

    await updateQuickfire(ctx, args.quickfireId, updateData);
    return createUpdateResponse();
  },
});

// Delete quickfire item with batch operations
export const deleteQuickfireItem = mutation({
  args: {
    username: v.string(),
    quickfireId: v.id("quickfires"),
  },
  handler: async (ctx, args): Promise<DeleteResponse> => {
    // Admin authorization check
    await requireAdmin(ctx, args.username, false); // Use Error for admin internal tooling
    
    // Get all votes for this quickfire item
    const votes = await getQuickfireVotesByQuickfire(ctx, args.quickfireId);

    // Batch delete all votes in parallel
    const voteIds = votes.map(vote => vote._id);
    await batchDeleteQuickfireVotes(ctx, voteIds);

    // Then delete the quickfire item itself
    await deleteQuickfire(ctx, args.quickfireId);
    return createDeleteResponse();
  },
});

// Internal mutation to atomically update quickfire voting status
export const _atomicToggleQuickfireVoting = internalMutation({
  args: {
    quickfireId: v.id("quickfires"),
    votingActive: v.boolean(),
  },
  handler: async (ctx, args) => {
    // Get the quickfire item to find its session
    const quickfireItem = await getQuickfireById(ctx, args.quickfireId);
    if (!quickfireItem) {
      throwConvexError("Quickfire item not found", ErrorCode.RESOURCE_NOT_FOUND);
    }

    // Get all quickfire items in the same session
    const allItems = await getQuickfiresBySessionOrdered(ctx, quickfireItem.sessionId);

    // Atomically update all items in the session with batch operations
    const updates = allItems
      .map(item => {
        const shouldBeActive = item._id === args.quickfireId && args.votingActive;
        
        // Only update if the status needs to change
        if (item.votingActive !== shouldBeActive) {
          return {
            quickfireId: item._id,
            updates: {
              votingActive: shouldBeActive,
            }
          };
        }
        return null;
      })
      .filter(update => update !== null);
    
    await batchUpdateQuickfires(ctx, updates);

    return args.quickfireId;
  },
});

// Toggle voting status for quickfire item
export const toggleQuickfireVoting = mutation({
  args: {
    username: v.string(),
    quickfireId: v.id("quickfires"),
    votingActive: v.boolean(),
  },
  handler: async (ctx, args): Promise<UpdateResponse> => {
    // Admin authorization check
    await requireAdmin(ctx, args.username, false); // Use Error for admin internal tooling

    // Use internal mutation for atomic updates
    await ctx.runMutation(internal.quickfire._atomicToggleQuickfireVoting, {
      quickfireId: args.quickfireId,
      votingActive: args.votingActive,
    });
    
    return createUpdateResponse();
  },
});

// Get sessions of type "Quickfire" for session change modal
export const getQuickfireSessions = query({
  args: {},
  handler: async (ctx) => {
    // Get all sessions of type "Quickfire" for the active event using helper
    const sessions = await getSessionsByTypeForActiveEvent(ctx, "Quickfire");

    return sessions;
  },
});

// Reorder quickfire items within a session
export const reorderQuickfireItems = mutation({
  args: {
    username: v.string(),
    sessionId: v.id("sessions"),
    itemId: v.id("quickfires"),
    newOrder: v.number(),
  },
  handler: async (ctx, args): Promise<UpdateResponse> => {
    // Admin authorization check
    await requireAdmin(ctx, args.username, false); // Use Error for admin internal tooling

    // Get the item to reorder using helper
    const item = await getQuickfireById(ctx, args.itemId);
    if (!item) {
      throwConvexError("Item not found", ErrorCode.RESOURCE_NOT_FOUND);
    }

    // Get all items in the session
    const allItems = await getQuickfiresBySessionOrdered(ctx, args.sessionId);

    // Sort by current order (ascending to match display order)
    const sortedItems = allItems.sort((a, b) => (a.order || 0) - (b.order || 0));
    
    // Find the item to move
    const currentIndex = sortedItems.findIndex(i => i._id === args.itemId);
    if (currentIndex === -1) {
      throwConvexError("Item not found in session", ErrorCode.RESOURCE_NOT_FOUND);
    }

    // Remove the item from its current position
    const [movedItem] = sortedItems.splice(currentIndex, 1);
    
    // Insert it at the new position
    const newIndex = Math.max(0, Math.min(args.newOrder, sortedItems.length));
    sortedItems.splice(newIndex, 0, movedItem);

    // Update all items with their new order (higher index gets higher order number)
    const updates = sortedItems.map((item, index) => ({
      quickfireId: item._id,
      updates: {
        order: index + 1, // Ascending order: first item gets order 1
      }
    }));

    await batchUpdateQuickfires(ctx, updates);
    
    return createUpdateResponse();
  },
});

// Reset order based on createdAt (chronological creation order)
export const resetOrderByCreatedAt = mutation({
  args: {
    username: v.string(),
    sessionId: v.id("sessions"),
  },
  handler: async (ctx, args): Promise<MutationResponse<{message: string; count: number}>> => {
    // Admin authorization check
    await requireAdmin(ctx, args.username, false); // Use Error for admin internal tooling

    // Get all items in the session
    const allItems = await getQuickfiresBySessionOrdered(ctx, args.sessionId);

    if (allItems.length === 0) {
      return createSuccessResponse({ message: "No items found in session", count: 0 });
    }

    // Sort by createdAt (newest first to match display order)
    const sortedItems = allItems.sort((a, b) => b.createdAt - a.createdAt);

    // Update all items with their new order (newest gets lowest order number to appear first)
    const updates = sortedItems.map((item, index) => ({
      quickfireId: item._id,
      updates: {
        order: index + 1,
      }
    }));

    await batchUpdateQuickfires(ctx, updates);
    
    return createSuccessResponse({ message: `Reset order for ${allItems.length} items`, count: allItems.length });
  },
});

// Get quickfire items by session ID (for dropdown selection)
export const getQuickfireItemsBySession = query({
  args: { sessionId: v.optional(v.id("sessions")) },
  handler: async (ctx, args) => {
    // Get active event
    const activeEvent = await getActiveEventOrNull(ctx);

    if (!activeEvent) {
      return { error: "No active event found", items: [] };
    }

    let quickfireItems;
    let sessionName = "";

    if (args.sessionId) {
      // Get items for specific session with validation using helper
      try {
        const session = await getSessionByIdOrThrow(ctx, args.sessionId);
        
        sessionName = session.name;
        
        quickfireItems = await getQuickfiresBySessionOrdered(ctx, args.sessionId!);
      } catch (error) {
        return { error: "Session not found", items: [] };
      }
    } else {
      // Get active session items (fallback behavior)
      const activeSession = await getActiveSessionForActiveEvent(ctx);

      if (!activeSession) {
        return { error: "No active session found", items: [] };
      }

      if (activeSession.type !== "Quickfire") {
        return { error: "No Active Quickfire Session", items: [] };
      }

      sessionName = activeSession.name;
      
      quickfireItems = await getQuickfiresBySessionOrdered(ctx, activeSession._id);
    }

    const items = quickfireItems.map(item => ({
      ...item,
      sessionName,
      eventName: activeEvent.name
    }));

    return { error: null, items };
  },
});

// Phase 2 Optimization: Combined query function for Quickfire page with batch loading
export const getQuickfirePageData = query({
  args: { username: v.string() },
  handler: async (ctx, args) => {
    // Parallel queries for quickfire data
    const [user, activeEvent] = await Promise.all([
      getUserByUsername(ctx, args.username),
      getActiveEventOrNull(ctx)
    ]);

    if (!user || !activeEvent) {
      return {
        quickfireItems: [],
        userVotes: {}
      };
    }

    // Get active session
    const activeSession = await getActiveSessionForActiveEvent(ctx);

    if (!activeSession || activeSession.type !== "Quickfire") {
      return {
        quickfireItems: [],
        userVotes: {}
      };
    }

    // Get active quickfire items
    const quickfireItems = await getActiveQuickfiresBySession(ctx, activeSession._id);

    if (quickfireItems.length === 0) {
      return {
        quickfireItems: [],
        userVotes: {}
      };
    }

    // Batch load user votes for all quickfires
    const quickfireIds = quickfireItems.map(item => item._id);
    const userVotesMap = await getUserQuickfireVotesForQuickfires(ctx, user._id, quickfireIds);

    // Transform to expected format
    const userVotes: { [key: string]: { quickfireId: string; score: number; voter: string } } = {};
    Array.from(userVotesMap.entries()).forEach(([quickfireId, vote]) => {
      if (vote) {
        userVotes[quickfireId.toString()] = {
          quickfireId: quickfireId.toString(),
          score: vote.score,
          voter: vote.voter
        };
      }
    });

    return {
      quickfireItems,
      userVotes
    };
  },
});

// Optimized admin query for quickfire management with batch statistics
export const getQuickfireAdminData = query({
  args: { username: v.string() },
  handler: async (ctx, args) => {
    // Admin authorization check
    await requireAdmin(ctx, args.username, false);
    
    // Get active event
    const activeEvent = await getActiveEvent(ctx);

    // Get all quickfire items for the active event
    const quickfireItems = await getQuickfiresByEvent(ctx, activeEvent._id);
    
    if (quickfireItems.length === 0) {
      return {
        quickfireItems: [],
        voteStats: new Map(),
        sessionSummary: {}
      };
    }

    // Batch load vote statistics for all quickfires
    const quickfireIds = quickfireItems.map(item => item._id);
    const voteStatsMap = await batchLoadQuickfireVoteStats(ctx, quickfireIds);

    // Get session data for summary
    const sessionIds = [...new Set(quickfireItems.map(item => item.sessionId))];
    const sessionSummaries = await Promise.all(
      sessionIds.map(sessionId => getQuickfireSessionData(ctx, sessionId))
    );

    // Create session summary map
    const sessionSummary: { [key: string]: any } = {};
    sessionSummaries.forEach(summary => {
      if (summary) {
        sessionSummary[summary.session._id] = summary;
      }
    });

    // Enrich quickfires with details
    const itemsWithDetails = await enrichQuickfiresWithDetails(ctx, quickfireItems);

    return {
      quickfireItems: itemsWithDetails.sort((a, b) => (b.order || 0) - (a.order || 0)),
      voteStats: Object.fromEntries(voteStatsMap),
      sessionSummary
    };
  },
});


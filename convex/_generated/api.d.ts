/* eslint-disable */
/**
 * Generated `api` utility.
 *
 * THIS CODE IS AUTOMATICALLY GENERATED.
 *
 * To regenerate, run `npx convex dev`.
 * @module
 */

import type {
  ApiFromModules,
  FilterApi,
  FunctionReference,
} from "convex/server";
import type * as analytics from "../analytics.js";
import type * as backupActions from "../backupActions.js";
import type * as backupData from "../backupData.js";
import type * as crons from "../crons.js";
import type * as events from "../events.js";
import type * as ideas from "../ideas.js";
import type * as lib_auth from "../lib/auth.js";
import type * as lib_backupHelpers from "../lib/backupHelpers.js";
import type * as lib_batchHelpers from "../lib/batchHelpers.js";
import type * as lib_enrichmentHelpers from "../lib/enrichmentHelpers.js";
import type * as lib_eventHelpers from "../lib/eventHelpers.js";
import type * as lib_ideaHelpers from "../lib/ideaHelpers.js";
import type * as lib_presenceHelpers from "../lib/presenceHelpers.js";
import type * as lib_quickfireHelpers from "../lib/quickfireHelpers.js";
import type * as lib_responseTypes from "../lib/responseTypes.js";
import type * as lib_responseUtils from "../lib/responseUtils.js";
import type * as lib_sessionHelpers from "../lib/sessionHelpers.js";
import type * as lib_settingsHelpers from "../lib/settingsHelpers.js";
import type * as lib_snippetHelpers from "../lib/snippetHelpers.js";
import type * as lib_sparkHelpers from "../lib/sparkHelpers.js";
import type * as lib_teamHelpers from "../lib/teamHelpers.js";
import type * as lib_userHelpers from "../lib/userHelpers.js";
import type * as lib_voteHelpers from "../lib/voteHelpers.js";
import type * as presence from "../presence.js";
import type * as quickfire from "../quickfire.js";
import type * as sessions from "../sessions.js";
import type * as settings from "../settings.js";
import type * as snippets from "../snippets.js";
import type * as sparkSubmissions from "../sparkSubmissions.js";
import type * as sparks from "../sparks.js";
import type * as teams from "../teams.js";
import type * as userActions from "../userActions.js";
import type * as userAdmin from "../userAdmin.js";
import type * as userAuth from "../userAuth.js";
import type * as userContent from "../userContent.js";
import type * as userProfile from "../userProfile.js";
import type * as userRegistration from "../userRegistration.js";
import type * as userStatus from "../userStatus.js";
import type * as userTeams from "../userTeams.js";
import type * as users from "../users.js";
import type * as votes from "../votes.js";
import type * as voting from "../voting.js";

/**
 * A utility for referencing Convex functions in your app's API.
 *
 * Usage:
 * ```js
 * const myFunctionReference = api.myModule.myFunction;
 * ```
 */
declare const fullApi: ApiFromModules<{
  analytics: typeof analytics;
  backupActions: typeof backupActions;
  backupData: typeof backupData;
  crons: typeof crons;
  events: typeof events;
  ideas: typeof ideas;
  "lib/auth": typeof lib_auth;
  "lib/backupHelpers": typeof lib_backupHelpers;
  "lib/batchHelpers": typeof lib_batchHelpers;
  "lib/enrichmentHelpers": typeof lib_enrichmentHelpers;
  "lib/eventHelpers": typeof lib_eventHelpers;
  "lib/ideaHelpers": typeof lib_ideaHelpers;
  "lib/presenceHelpers": typeof lib_presenceHelpers;
  "lib/quickfireHelpers": typeof lib_quickfireHelpers;
  "lib/responseTypes": typeof lib_responseTypes;
  "lib/responseUtils": typeof lib_responseUtils;
  "lib/sessionHelpers": typeof lib_sessionHelpers;
  "lib/settingsHelpers": typeof lib_settingsHelpers;
  "lib/snippetHelpers": typeof lib_snippetHelpers;
  "lib/sparkHelpers": typeof lib_sparkHelpers;
  "lib/teamHelpers": typeof lib_teamHelpers;
  "lib/userHelpers": typeof lib_userHelpers;
  "lib/voteHelpers": typeof lib_voteHelpers;
  presence: typeof presence;
  quickfire: typeof quickfire;
  sessions: typeof sessions;
  settings: typeof settings;
  snippets: typeof snippets;
  sparkSubmissions: typeof sparkSubmissions;
  sparks: typeof sparks;
  teams: typeof teams;
  userActions: typeof userActions;
  userAdmin: typeof userAdmin;
  userAuth: typeof userAuth;
  userContent: typeof userContent;
  userProfile: typeof userProfile;
  userRegistration: typeof userRegistration;
  userStatus: typeof userStatus;
  userTeams: typeof userTeams;
  users: typeof users;
  votes: typeof votes;
  voting: typeof voting;
}>;
export declare const api: FilterApi<
  typeof fullApi,
  FunctionReference<any, "public">
>;
export declare const internal: FilterApi<
  typeof fullApi,
  FunctionReference<any, "internal">
>;

import { v } from "convex/values";
import { mutation, query } from "./_generated/server";
import { Doc } from "./_generated/dataModel";
import { getUserByUsername as getUserByUsernameHelper, getAdminUsers as getAdminUsersHelper } from "./lib/userHelpers";
import { 
  DeleteResponse, 
  MutationResponse,
  ErrorCode 
} from "./lib/responseTypes";
import { 
  createDeleteResponse, 
  createSuccessResponse,
  throwConvexError 
} from "./lib/responseUtils";

// Admin Management Functions for AdminsManagement component

export const getAdminUsers = query({
  args: {},
  handler: async (ctx): Promise<Array<{
    id: string;
    username: string;
    role: string;
  }>> => {
    const admins = await getAdminUsersHelper(ctx) as Doc<"users">[];
    
    return admins.map((admin: Doc<"users">) => ({
      id: admin._id.toString(),
      username: admin.username,
      role: admin.role
    }));
  },
});

export const getAdminUsersForManagement = query({
  args: {},
  handler: async (ctx): Promise<Array<{
    id: string;
    username: string;
    role: string;
  }>> => {
    const admins = await getAdminUsersHelper(ctx);
    
    return admins.map((admin: Doc<"users">) => ({
      id: admin._id.toString(),
      username: admin.username,
      role: admin.role
    }));
  },
});

export const createAdminUserWithHashedPassword = mutation({
  args: {
    username: v.string(),
    hashedPassword: v.string(), // already hashed password
  },
  handler: async (ctx, args): Promise<MutationResponse<{
    id: string;
    username: string;
    role: string;
  }>> => {
    // Check if username already exists
    const existingUser = await getUserByUsernameHelper(ctx, args.username);
    
    if (existingUser) {
      throwConvexError("Username already exists. Please choose a different username.", ErrorCode.DUPLICATE_ENTRY);
    }

    const now = Date.now();
    const adminId = await ctx.db.insert("users", {
      username: args.username,
      name: args.username, // Default name to username for admins
      password: args.hashedPassword,
      role: "admin",
      status: "approved", // Admins are auto-approved
      events: [], // Admins don't participate in events
      autosave: true, // Default autosave to enabled for new users
      createdAt: now,
    });

    // Return admin data in expected format
    return createSuccessResponse({
      id: adminId.toString(),
      username: args.username,
      role: "admin"
    });
  },
});

export const updateAdminUsername = mutation({
  args: {
    adminId: v.id("users"),
    username: v.string(),
  },
  handler: async (ctx, args): Promise<MutationResponse<{
    id: string;
    username: string;
    role: string;
  }>> => {
    // Check if new username already exists (excluding current admin)
    const existingUser = await getUserByUsernameHelper(ctx, args.username);
    
    if (existingUser && existingUser._id !== args.adminId) {
      throwConvexError("Username already exists. Please choose a different username.", ErrorCode.DUPLICATE_ENTRY);
    }

    // Verify the user being updated is actually an admin
    const admin = await ctx.db.get(args.adminId);
    if (!admin || admin.role !== "admin") {
      throwConvexError("Access denied. Administrator privileges required.", ErrorCode.FORBIDDEN);
    }

    await ctx.db.patch(args.adminId, {
      username: args.username,
      name: args.username, // Update name to match username for admins
      updatedAt: Date.now(),
    });

    return createSuccessResponse({
      id: args.adminId.toString(),
      username: args.username,
      role: "admin"
    });
  },
});

export const updateAdminPasswordWithHashedPassword = mutation({
  args: {
    adminId: v.id("users"),
    hashedPassword: v.string(), // already hashed password
  },
  handler: async (ctx, args): Promise<MutationResponse<{ success: boolean }>> => {
    // Verify the user being updated is actually an admin
    const admin = await ctx.db.get(args.adminId);
    if (!admin || admin.role !== "admin") {
      throwConvexError("Access denied. Administrator privileges required.", ErrorCode.FORBIDDEN);
    }

    await ctx.db.patch(args.adminId, {
      password: args.hashedPassword,
      updatedAt: Date.now(),
    });

    return createSuccessResponse({ success: true });
  },
});

export const deleteAdminUser = mutation({
  args: {
    adminId: v.id("users"),
  },
  handler: async (ctx, args): Promise<DeleteResponse> => {
    // Verify the user being deleted is actually an admin
    const admin = await ctx.db.get(args.adminId);
    if (!admin || admin.role !== "admin") {
      throwConvexError("Access denied. Administrator privileges required.", ErrorCode.FORBIDDEN);
    }

    // Delete the admin user
    await ctx.db.delete(args.adminId);

    return createDeleteResponse();
  },
});
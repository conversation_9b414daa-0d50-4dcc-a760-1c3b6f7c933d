import { v } from "convex/values";
import { mutation, query, action } from "./_generated/server";
import { api } from "./_generated/api";
import { getActiveEventOrNull } from "./lib/eventHelpers";
import { getActiveSessionForActiveEvent, getAllActiveSessions, getSessionsByEvent as getSessionsByEventHelper, getSessionById as getSessionByIdHelper, batchDeactivateSessions, createSession as createSessionHelper, updateSession as updateSessionHelper, deleteSession as deleteSessionHelper, getAllSessions as getAllSessionsHelper } from "./lib/sessionHelpers";
import { getSparkById, getSparkSubmissionsBySession, getSparkSubmissionsBySpark } from "./lib/sparkHelpers";
import { batchDeleteEntities, extractUniqueIds, batchUpdateEntities } from "./lib/batchHelpers";
import { getIdeasBySession } from "./lib/ideaHelpers";
import { getVotesByIdea, getVotesBySession, getQuickfireVotesByQuickfire } from "./lib/voteHelpers";
import { getQuickfiresBySession } from "./lib/quickfireHelpers";
import { enrichWithUserAndTeamInfo, enrichWithSessionAndEventContext, enrichSessionsWithSparkInfo } from "./lib/enrichmentHelpers";
import { requireAdmin } from "./lib/auth";
import { 
  CreateResponse, 
  UpdateResponse, 
  DeleteResponse, 
  ActionResponse, 
  ErrorCode 
} from "./lib/responseTypes";
import { 
  createCreateResponse, 
  createUpdateResponse, 
  createDeleteResponse, 
  createActionSuccess, 
  createActionError, 
  throwConvexError 
} from "./lib/responseUtils";

export const createSession = mutation({
  args: {
    username: v.string(),
    name: v.string(),
    type: v.string(),
    eventId: v.id("events"),
    day: v.optional(v.number()),
    active: v.optional(v.boolean()),
    startTime: v.optional(v.number()),
    endTime: v.optional(v.number()),
    settings: v.optional(v.object({
      votingEnabled: v.optional(v.boolean()),
      submissionEnabled: v.optional(v.boolean()),
    })),
    sparkId: v.optional(v.id("sparks")),
  },
  handler: async (ctx, args): Promise<CreateResponse<"sessions">> => {
    // Authenticate admin user
    await requireAdmin(ctx, args.username, true); // Use ConvexError for admin mutations
    // Validation: Sparks-type sessions MUST have a sparkId
    if (args.type === 'Sparks' && !args.sparkId) {
      throwConvexError("Sparks-type sessions require a spark configuration to be selected", ErrorCode.VALIDATION_ERROR);
    }
    
    // Validation: Non-Sparks sessions MUST NOT have a sparkId
    if (args.type !== 'Sparks' && args.sparkId) {
      throwConvexError("Only Sparks-type sessions can have a spark configuration", ErrorCode.VALIDATION_ERROR);
    }
    
    // Validate that the spark exists if provided
    if (args.sparkId) {
      const spark = await getSparkById(ctx, args.sparkId);
      if (!spark) {
        throwConvexError("Selected spark configuration not found", ErrorCode.RESOURCE_NOT_FOUND);
      }
    }

    // If setting this session as active, deactivate all other sessions
    if (args.active) {
      const activeSessions = await getAllActiveSessions(ctx);
      const sessionIdsToDeactivate = activeSessions.map((session: any) => session._id);
      
      await batchDeactivateSessions(ctx, sessionIdsToDeactivate);
    }

    const sessionId = await createSessionHelper(ctx, {
      name: args.name,
      type: args.type,
      eventId: args.eventId,
      day: args.day || 1,
      active: args.active || false,
      startTime: args.startTime,
      endTime: args.endTime,
      settings: args.settings,
      sparkId: args.sparkId,
    });
    
    return createCreateResponse(sessionId);
  },
});

export const getActiveSession = query({
  args: {},
  handler: async (ctx): Promise<any> => {
    return await getActiveSessionForActiveEvent(ctx);
  },
});

export const getSessionsByEvent = query({
  args: { eventId: v.id("events") },
  handler: async (ctx, args): Promise<any[]> => {
    return await getSessionsByEventHelper(ctx, args.eventId);
  },
});

export const getAllSessions = query({
  args: {},
  handler: async (ctx): Promise<any[]> => {
    const sessions = await getAllSessionsHelper(ctx);
    return await enrichWithSessionAndEventContext(ctx, sessions);
  },
});

export const getSessionsByActiveEvent = query({
  args: {},
  handler: async (ctx): Promise<any[]> => {
    // Get the active event first
    const activeEvent = await getActiveEventOrNull(ctx);
    
    if (!activeEvent) {
      return [];
    }

    // Get sessions for the active event with enrichment
    const sessions = await getSessionsByEventHelper(ctx, activeEvent._id);
    const enrichedSessions = await enrichSessionsWithSparkInfo(ctx, sessions);
    
    return enrichedSessions;
  },
});

export const getSessionsByActiveEventExcludingQuickfire = query({
  args: {},
  handler: async (ctx): Promise<any[]> => {
    // Get the active event first
    const activeEvent = await getActiveEventOrNull(ctx);
    
    if (!activeEvent) {
      return [];
    }

    // Get sessions for the active event, excluding Quickfire sessions
    const sessions = await getSessionsByEventHelper(ctx, activeEvent._id);
    const filteredSessions = sessions.filter((session: any) => session.type !== "Quickfire");
    
    // Enrich with spark information
    return await enrichSessionsWithSparkInfo(ctx, filteredSessions);
  },
});

export const getSessionById = query({
  args: { sessionId: v.id("sessions") },
  handler: async (ctx, args): Promise<any> => {
    return await getSessionByIdHelper(ctx, args.sessionId);
  },
});

export const getSessionByIdWithEnrichment = query({
  args: { sessionId: v.id("sessions") },
  handler: async (ctx, args): Promise<any> => {
    const session = await getSessionByIdHelper(ctx, args.sessionId);
    if (!session) {
      return null;
    }
    
    // Enrich with spark and event information
    const enrichedSessions = await enrichWithSessionAndEventContext(ctx, [session]);
    const enrichedWithSpark = await enrichSessionsWithSparkInfo(ctx, enrichedSessions);
    
    return enrichedWithSpark[0];
  },
});

export const activateSession = mutation({
  args: { username: v.string(), sessionId: v.id("sessions") },
  handler: async (ctx, args): Promise<UpdateResponse> => {
    // Authenticate admin user
    await requireAdmin(ctx, args.username, true); // Use ConvexError for admin mutations
    
    // Use the optimized batch deactivation helper
    await deactivateOtherSessions(ctx, args.sessionId);

    // Activate the specified session
    await updateSessionHelper(ctx, args.sessionId, {
      active: true,
    });
    
    return createUpdateResponse();
  },
});

// Helper function to validate session update parameters
async function validateSessionUpdate(ctx: any, args: any, currentSession: any): Promise<{ finalType: string; finalSparkId: any }> {
  // Determine the final type (use new type if provided, otherwise current)
  const finalType = args.type !== undefined ? args.type : currentSession.type;
  
  // Handle sparkId logic
  let finalSparkId;
  if (args.clearSparkId) {
    finalSparkId = undefined;
  } else if (args.sparkId !== undefined) {
    finalSparkId = args.sparkId;
  } else {
    finalSparkId = currentSession.sparkId;
  }
  
  // Validation: Sparks-type sessions MUST have a sparkId
  if (finalType === 'Sparks' && !finalSparkId) {
    throwConvexError("Sparks-type sessions require a spark configuration to be selected", ErrorCode.VALIDATION_ERROR);
  }
  
  // Validation: Non-Sparks sessions MUST NOT have a sparkId
  if (finalType !== 'Sparks' && finalSparkId) {
    throwConvexError("Only Sparks-type sessions can have a spark configuration", ErrorCode.VALIDATION_ERROR);
  }
  
  // Validate that the spark exists if provided
  if (finalSparkId) {
    const spark = await getSparkById(ctx, finalSparkId);
    if (!spark) {
      throwConvexError("Selected spark configuration not found", ErrorCode.RESOURCE_NOT_FOUND);
    }
  }
  
  return { finalType, finalSparkId };
}

// Helper function to handle session deactivation when setting active=true with batch processing
async function deactivateOtherSessions(ctx: any, sessionId: string) {
  const allActiveSessions = await getAllActiveSessions(ctx);
  const sessionIdsToDeactivate = allActiveSessions
    .filter((session: any) => session._id !== sessionId)
    .map((session: any) => session._id);
  
  await batchDeactivateSessions(ctx, sessionIdsToDeactivate);
}

// Helper function to build update data object
function buildUpdateData(args: any) {
  const updateData: Record<string, any> = {};

  if (args.name !== undefined) updateData.name = args.name;
  if (args.type !== undefined) updateData.type = args.type;
  if (args.day !== undefined) updateData.day = args.day;
  if (args.active !== undefined) updateData.active = args.active;
  if (args.startTime !== undefined) updateData.startTime = args.startTime;
  if (args.endTime !== undefined) updateData.endTime = args.endTime;
  if (args.settings !== undefined) updateData.settings = args.settings;
  
  return updateData;
}

// Helper function to handle spark submission synchronization with batch processing
async function handleSparkSubmissionSync(ctx: any, args: any, currentSession: any) {
  const typeChangedFromSparks = args.type !== undefined && currentSession.type === 'Sparks' && args.type !== 'Sparks';
  
  // If changing away from Sparks or clearing sparkId, clear sessionId from all submissions of the current spark
  if ((typeChangedFromSparks || args.clearSparkId) && currentSession.sparkId) {
    const sparkSubmissions = await getSparkSubmissionsBySpark(ctx, currentSession.sparkId);
    
    // Use batch helper for better performance
    const clearUpdates = sparkSubmissions.map((submission: any) => ({
      id: submission._id,
      updateData: { sessionId: undefined }
    }));
    
    await batchUpdateEntities(ctx, clearUpdates);
  }
  
  // If assigning a new spark, update all submissions of that spark to have this sessionId
  if (args.sparkId !== undefined && args.sparkId !== currentSession.sparkId) {
    const sparkSubmissions = await getSparkSubmissionsBySpark(ctx, args.sparkId);
    
    // Use batch helper for better performance
    const sessionUpdates = sparkSubmissions.map((submission: any) => ({
      id: submission._id,
      updateData: { sessionId: args.sessionId }
    }));
    
    await batchUpdateEntities(ctx, sessionUpdates);
  }
}


export const updateSession = mutation({
  args: {
    username: v.string(),
    sessionId: v.id("sessions"),
    name: v.optional(v.string()),
    type: v.optional(v.string()),
    day: v.optional(v.number()),
    active: v.optional(v.boolean()),
    startTime: v.optional(v.number()),
    endTime: v.optional(v.number()),
    settings: v.optional(v.object({
      votingEnabled: v.optional(v.boolean()),
      submissionEnabled: v.optional(v.boolean()),
    })),
    sparkId: v.optional(v.id("sparks")),
    clearSparkId: v.optional(v.boolean()), // Flag to clear sparkId
  },
  handler: async (ctx, args): Promise<UpdateResponse> => {
    // Authenticate admin user
    await requireAdmin(ctx, args.username, true); // Use ConvexError for admin mutations
    // Get current session for validation
    const currentSession = await getSessionByIdHelper(ctx, args.sessionId);
    if (!currentSession) {
      throwConvexError("Session not found", ErrorCode.RESOURCE_NOT_FOUND);
    }
    
    // Step 1: Validate session update parameters
    const { finalSparkId } = await validateSessionUpdate(ctx, args, currentSession);
    
    // Step 2: Handle session deactivation if setting active=true
    if (args.active === true) {
      await deactivateOtherSessions(ctx, args.sessionId);
    }
    
    // Step 3: Handle spark submission synchronization
    await handleSparkSubmissionSync(ctx, args, currentSession);
    
    // Step 4: Build update data object
    const updateData = buildUpdateData(args);
    
    // Step 5: Handle sparkId field updates using validated finalSparkId
    if (args.clearSparkId) {
      updateData.sparkId = undefined; // This removes the field
    } else if (args.sparkId !== undefined) {
      updateData.sparkId = finalSparkId;
    }
    
    // Step 6: Apply the update to the database
    await updateSessionHelper(ctx, args.sessionId, updateData);
    
    return createUpdateResponse();
  },
});

export const deactivateSession = mutation({
  args: { username: v.string(), sessionId: v.id("sessions") },
  handler: async (ctx, args): Promise<UpdateResponse> => {
    // Authenticate admin user
    await requireAdmin(ctx, args.username, true); // Use ConvexError for admin mutations
    await updateSessionHelper(ctx, args.sessionId, {
      active: false,
    });
    
    return createUpdateResponse();
  },
});

export const deleteSession = mutation({
  args: { username: v.string(), sessionId: v.id("sessions") },
  handler: async (ctx, args): Promise<DeleteResponse> => {
    // Authenticate admin user
    await requireAdmin(ctx, args.username, true); // Use ConvexError for admin mutations
    // CASCADE DELETE - Delete session and all related data based on session type
    
    // Get the session to check its type
    const session = await getSessionByIdHelper(ctx, args.sessionId);
    if (!session) {
      throwConvexError("Session not found", ErrorCode.RESOURCE_NOT_FOUND);
    }
    
    // Delete content based on session type
    if (session.type === 'Ideas') {
      // Delete all ideas for this session with optimized batch loading
      const ideas = await getIdeasBySession(ctx, args.sessionId);
      
      // Use batch loading to get all votes for all ideas efficiently
      const ideaIds = extractUniqueIds(ideas, "_id");
      const voteBatches = await Promise.all(
        ideaIds.map(ideaId => getVotesByIdea(ctx, ideaId))
      );
      
      const allVotes = voteBatches.flat();
      
      // Use batch delete helpers for efficient deletion
      await Promise.all([
        batchDeleteEntities(ctx, allVotes),
        batchDeleteEntities(ctx, ideas)
      ]);
      
    } else if (session.type === 'Quickfire') {
      // Delete all quickfire items for this session with optimized batch loading
      const quickfires = await getQuickfiresBySession(ctx, args.sessionId);
      
      // Use batch loading to get all votes for all quickfires efficiently
      const quickfireIds = extractUniqueIds(quickfires, "_id");
      const quickfireVoteBatches = await Promise.all(
        quickfireIds.map(quickfireId => getQuickfireVotesByQuickfire(ctx, quickfireId))
      );
      
      const allQuickfireVotes = quickfireVoteBatches.flat();
      
      // Use batch delete helpers for efficient deletion
      await Promise.all([
        batchDeleteEntities(ctx, allQuickfireVotes),
        batchDeleteEntities(ctx, quickfires)
      ]);
    } else if (session.type === 'Sparks') {
      // Delete all spark submissions for this session using batch helper
      const sparkSubmissions = await getSparkSubmissionsBySession(ctx, args.sessionId);
      
      await batchDeleteEntities(ctx, sparkSubmissions);
      
      // Note: The spark configuration itself remains intact for reuse
      // Only the session reference and submissions are deleted
    }
    
    // Delete any votes that reference this session directly using batch helper
    const sessionVotes = await getVotesBySession(ctx, args.sessionId);
    
    await batchDeleteEntities(ctx, sessionVotes);
    
    // Finally, delete the session itself
    await deleteSessionHelper(ctx, args.sessionId);
    
    return createDeleteResponse();
  },
});

// Utility functions for session-spark relationship management

export const getSparkSubmissionCountForSession = query({
  args: { sessionId: v.id("sessions") },
  handler: async (ctx, args): Promise<number> => {
    const sparkSubmissions = await getSparkSubmissionsBySession(ctx, args.sessionId);
    
    return sparkSubmissions.length;
  },
});

export const getSparkSubmissionsForSession = query({
  args: { sessionId: v.id("sessions") },
  handler: async (ctx, args): Promise<any[]> => {
    const submissions = await getSparkSubmissionsBySession(ctx, args.sessionId);
    return await enrichWithUserAndTeamInfo(ctx, submissions);
  },
});

export const checkSessionHasSparkSubmissions = query({
  args: { sessionId: v.id("sessions") },
  handler: async (ctx, args): Promise<boolean> => {
    const sparkSubmissions = await getSparkSubmissionsBySession(ctx, args.sessionId);
    
    return sparkSubmissions.length > 0;
  },
});

export const validateSessionSparkRelationship = action({
  args: { sessionId: v.id("sessions") },
  handler: async (ctx, args): Promise<ActionResponse<{ valid: boolean; error?: string }>> => {
    const session = await ctx.runQuery(api.sessions.getSessionById, { sessionId: args.sessionId });
    if (!session) {
      return createActionError("Session not found", "Session not found");
    }

    // Use helper function for validation
    const validationResult = await validateSessionSparkRelationshipHelper(ctx, session);
    
    if (!validationResult.valid) {
      return createActionError(validationResult.error!, "Validation failed");
    }

    return createActionSuccess({ valid: true }, "Session spark relationship is valid");
  },
});

// Helper function for session-spark relationship validation
async function validateSessionSparkRelationshipHelper(ctx: any, session: any): Promise<{ valid: boolean; error?: string }> {
  // Check if session type and sparkId are consistent
  if (session.type === 'Sparks' && !session.sparkId) {
    return { valid: false, error: "Sparks session must have a sparkId" };
  }

  if (session.type !== 'Sparks' && session.sparkId) {
    return { valid: false, error: "Non-Sparks session cannot have a sparkId" };
  }

  // Check if sparkId exists if provided
  if (session.sparkId) {
    const spark = await getSparkById(ctx, session.sparkId);
    if (!spark) {
      return { valid: false, error: "Referenced spark not found" };
    }
  }

  return { valid: true };
}

// Additional validation helpers

export const checkCanChangeSessionType = action({
  args: { 
    sessionId: v.id("sessions"), 
    newType: v.string() 
  },
  handler: async (ctx, args): Promise<ActionResponse<{ canChange: boolean; error?: string; requiresSparkSelection?: boolean; message?: string }>> => {
    // OPTIMIZED: Execute independent operations in parallel
    const [session, hasSubmissions] = await Promise.all([
      ctx.runQuery(api.sessions.getSessionById, { sessionId: args.sessionId }),
      ctx.runQuery(api.sessions.checkSessionHasSparkSubmissions, { sessionId: args.sessionId })
    ]);

    if (!session) {
      return createActionError("Session not found", "Session not found");
    }

    // If changing from Sparks type, check if there are submissions
    if (session.type === 'Sparks' && args.newType !== 'Sparks') {
      if (hasSubmissions) {
        return createActionError(
          "Cannot change session type from Sparks when there are existing submissions. Delete submissions first or create a new session.",
          "Type change validation failed"
        );
      }
    }

    // If changing to Sparks type, validate that sparkId will be provided
    if (session.type !== 'Sparks' && args.newType === 'Sparks' && !session.sparkId) {
      return createActionSuccess({
        canChange: true, 
        requiresSparkSelection: true,
        message: "Changing to Sparks type requires selecting a spark configuration"
      }, "Type change allowed with spark selection requirement");
    }

    return createActionSuccess({ canChange: true }, "Type change is allowed");
  },
});

export const checkCanDeleteSession = action({
  args: { sessionId: v.id("sessions") },
  handler: async (ctx, args): Promise<ActionResponse<{ canDelete: boolean; error?: string; itemCount?: number; itemType?: string; warning?: string }>> => {
    // OPTIMIZED: Execute independent operations in parallel
    const [session, sparkSubmissions, sparkSubmissionCount] = await Promise.all([
      ctx.runQuery(api.sessions.getSessionById, { sessionId: args.sessionId }),
      ctx.runQuery(api.sessions.getSparkSubmissionsForSession, { sessionId: args.sessionId }),
      ctx.runQuery(api.sessions.getSparkSubmissionCountForSession, { sessionId: args.sessionId })
    ]);

    if (!session) {
      return createActionError("Session not found", "Session not found");
    }

    // Active sessions should be deactivated first
    if (session.active) {
      return createActionError(
        "Cannot delete active session. Deactivate it first.",
        "Delete validation failed"
      );
    }

    // Get counts of data that will be deleted with optimized counting
    let itemCount = 0;
    let itemType = "";

    if (session.type === 'Ideas') {
      // Use pre-fetched sparkSubmissions data
      itemCount = sparkSubmissions.length;
      itemType = "ideas and votes";
    } else if (session.type === 'Quickfire') {
      itemType = "quickfire items and votes";
    } else if (session.type === 'Sparks') {
      // Use pre-fetched sparkSubmissionCount data
      itemCount = sparkSubmissionCount;
      itemType = "spark submissions";
    }

    return createActionSuccess({
      canDelete: true, 
      itemCount,
      itemType,
      warning: itemCount > 0 ? `This will permanently delete ${itemCount} ${itemType}` : undefined
    }, "Delete is allowed");
  },
});
import { v } from "convex/values";
import { mutation, action, internalMutation } from "./_generated/server";
import { internal } from "./_generated/api";
import bcrypt from "bcryptjs";
import { 
  getUserByUsername as getUserByUsernameHelper,
  updateUserAutosaveSetting as updateUserAutosaveSettingHelper
} from "./lib/userHelpers";
import { 
  UpdateResponse, 
  ActionResponse, 
  ErrorCode 
} from "./lib/responseTypes";
import { 
  createUpdateResponse, 
  createActionSuccess, 
  createActionError,
  throwConvexError 
} from "./lib/responseUtils";

export const updateUserProfileMutation = internalMutation({
  args: {
    userId: v.id("users"),
    name: v.optional(v.string()),
    username: v.optional(v.string()),
    hashedPassword: v.optional(v.string()), // already hashed password
    autosave: v.optional(v.boolean()),
  },
  handler: async (ctx, args): Promise<UpdateResponse> => {
    // Check if username is being updated and if it already exists
    if (args.username) {
      const existingUser = await getUserByUsernameHelper(ctx, args.username!);
      
      if (existingUser && existingUser._id !== args.userId) {
        throwConvexError("Username already exists. Please choose a different username.", ErrorCode.DUPLICATE_ENTRY);
      }
    }

    const updateData: any = {
      updatedAt: Date.now(),
    };

    if (args.name !== undefined) {
      updateData.name = args.name;
    }
    if (args.username !== undefined) {
      updateData.username = args.username;
    }
    if (args.hashedPassword !== undefined) {
      updateData.password = args.hashedPassword;
    }
    if (args.autosave !== undefined) {
      updateData.autosave = args.autosave;
    }

    await ctx.db.patch(args.userId, updateData);
    return createUpdateResponse();
  },
});

export const updateUserProfile = action({
  args: {
    userId: v.id("users"),
    name: v.optional(v.string()),
    username: v.optional(v.string()),
    password: v.optional(v.string()), // raw password - will be hashed
  },
  handler: async (ctx, args): Promise<ActionResponse<void>> => {
    try {
      let hashedPassword: string | undefined;
      
      // Hash password in action if provided
      if (args.password !== undefined) {
        hashedPassword = await bcrypt.hash(args.password, 10);
      }

      // Call internal mutation with hashed password
      await ctx.runMutation(internal.users.updateUserProfileMutation, {
        userId: args.userId,
        name: args.name,
        username: args.username,
        hashedPassword,
      });
      
      return createActionSuccess(undefined, "User profile updated successfully");
    } catch (error) {
      return createActionError(error instanceof Error ? error.message : "Failed to update user profile");
    }
  },
});

export const updateUserProfileWithHashedPassword = mutation({
  args: {
    userId: v.id("users"),
    name: v.optional(v.string()),
    username: v.optional(v.string()),
    hashedPassword: v.optional(v.string()), // already hashed password
  },
  handler: async (ctx, args): Promise<UpdateResponse> => {
    // Check if username is being updated and if it already exists
    if (args.username) {
      const existingUser = await getUserByUsernameHelper(ctx, args.username!);
      
      if (existingUser && existingUser._id !== args.userId) {
        throwConvexError("Username already exists. Please choose a different username.", ErrorCode.DUPLICATE_ENTRY);
      }
    }

    const updateData: any = {
      updatedAt: Date.now(),
    };

    if (args.name !== undefined) {
      updateData.name = args.name;
    }
    if (args.username !== undefined) {
      updateData.username = args.username;
    }
    if (args.hashedPassword !== undefined) {
      updateData.password = args.hashedPassword;
    }

    await ctx.db.patch(args.userId, updateData);
    return createUpdateResponse();
  },
});

export const updateUserName = mutation({
  args: {
    userId: v.id("users"),
    name: v.string(),
  },
  handler: async (ctx, args): Promise<UpdateResponse> => {
    await ctx.db.patch(args.userId, {
      name: args.name,
      updatedAt: Date.now(),
    });
    return createUpdateResponse();
  },
});

export const updateUserRole = mutation({
  args: {
    userId: v.id("users"),
    role: v.union(v.literal("teamLead"), v.literal("teamMember")),
  },
  handler: async (ctx, args): Promise<UpdateResponse> => {
    await ctx.db.patch(args.userId, {
      role: args.role,
      updatedAt: Date.now(),
    });
    return createUpdateResponse();
  },
});

export const updateUserPassword = mutation({
  args: {
    userId: v.id("users"),
    password: v.string(), // raw password - will be hashed
  },
  handler: async (ctx, args): Promise<UpdateResponse> => {
    const hashedPassword = await bcrypt.hash(args.password, 10);
    await ctx.db.patch(args.userId, {
      password: hashedPassword,
      updatedAt: Date.now(),
    });
    return createUpdateResponse();
  },
});

export const updateUserHashedPassword = mutation({
  args: {
    userId: v.id("users"),
    hashedPassword: v.string(), // already hashed password
  },
  handler: async (ctx, args): Promise<UpdateResponse> => {
    await ctx.db.patch(args.userId, {
      password: args.hashedPassword,
      updatedAt: Date.now(),
    });
    return createUpdateResponse();
  },
});

export const updateUserAutosaveSetting = mutation({
  args: {
    userId: v.id("users"),
    autosave: v.boolean(),
  },
  handler: async (ctx, args): Promise<UpdateResponse> => {
    await updateUserAutosaveSettingHelper(ctx, args.userId, args.autosave);
    return createUpdateResponse();
  },
});
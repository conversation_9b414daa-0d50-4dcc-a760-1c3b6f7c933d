import { v } from "convex/values";
import { mutation, query, action } from "./_generated/server";
import { api } from "./_generated/api";
import { requireAdmin } from "./lib/auth";
import {
  getAllSparks,
  getSparkById as getSparkByIdHelper,
  getSparkByName,
  canDeleteSpark as canDeleteSparkHelper,
  canEditSpark as canEditSparkHelper,
  getSparkSubmissionCount as getSparkSubmissionCountHelper,
  deleteSparkWithCascade,
  getSparkNamesOptimized,
  validateSparkFields,
  batchCheckSparkUsability,
  createSpark as createSparkHelper,
  updateSpark as updateSparkHelper
} from "./lib/sparkHelpers";
import { 
  CreateResponse, 
  UpdateResponse, 
  DeleteResponse, 
  ActionResponse, 
  ErrorCode 
} from "./lib/responseTypes";
import { 
  createCreateResponse, 
  createUpdateResponse, 
  createDeleteResponse, 
  createActionSuccess, 
  createActionError, 
  throwConvexError 
} from "./lib/responseUtils";

// Field configuration schema for validation
const sparkFieldSchema = v.object({
  type: v.union(
    v.literal("text"),
    v.literal("richtext"), 
    v.literal("dropdown"),
    v.literal("radio"),
    v.literal("checkbox")
  ),
  label: v.string(),
  placeholder: v.optional(v.string()),
  options: v.optional(v.array(v.string())),
  required: v.optional(v.boolean()),
});

// Get all sparks for an event
export const getSparks = query({
  args: { eventId: v.id("events") },
  handler: async (ctx, args) => {
    return await getAllSparks(ctx, args.eventId);
  },
});

// Get spark by ID
export const getSparkById = query({
  args: { sparkId: v.id("sparks") },
  handler: async (ctx, args) => {
    return await getSparkByIdHelper(ctx, args.sparkId);
  },
});

// Create new spark
export const createSpark = mutation({
  args: {
    username: v.string(),
    eventId: v.id("events"),
    name: v.string(),
    description: v.optional(v.string()),
    fields: v.array(sparkFieldSchema),
  },
  handler: async (ctx, args): Promise<CreateResponse<"sparks">> => {
    // Authenticate admin user
    await requireAdmin(ctx, args.username, true); // Use ConvexError for admin mutations
    // Validate name is not empty
    if (!args.name.trim()) {
      throwConvexError("Spark name cannot be empty", ErrorCode.VALIDATION_ERROR);
    }

    // Check if spark with same name already exists in this event
    const existingSpark = await getSparkByName(ctx, args.name.trim(), args.eventId);

    if (existingSpark) {
      throwConvexError("A spark with this name already exists in this event", ErrorCode.DUPLICATE_ENTRY);
    }

    // Validate fields using helper function
    const fieldErrors = validateSparkFields(args.fields);
    if (fieldErrors.length > 0) {
      throwConvexError(fieldErrors[0], ErrorCode.VALIDATION_ERROR);
    }

    const sparkId = await createSparkHelper(ctx, {
      name: args.name,
      description: args.description,
      eventId: args.eventId,
      fields: args.fields,
    });
    
    return createCreateResponse(sparkId);
  },
});

// Update existing spark
export const updateSpark = mutation({
  args: {
    username: v.string(),
    sparkId: v.id("sparks"),
    name: v.optional(v.string()),
    description: v.optional(v.string()),
    color: v.optional(v.string()),
    fields: v.optional(v.array(sparkFieldSchema)),
  },
  handler: async (ctx, args): Promise<UpdateResponse> => {
    // Authenticate admin user
    await requireAdmin(ctx, args.username, true); // Use ConvexError for admin mutations
    const spark = await getSparkByIdHelper(ctx, args.sparkId);
    if (!spark) {
      throwConvexError("Spark not found", ErrorCode.RESOURCE_NOT_FOUND);
    }

    // If updating name, check for duplicates
    if (args.name !== undefined) {
      if (!args.name.trim()) {
        throwConvexError("Spark name cannot be empty", ErrorCode.VALIDATION_ERROR);
      }

      const spark = await getSparkByIdHelper(ctx, args.sparkId);
      if (!spark) {
        throwConvexError("Spark not found", ErrorCode.RESOURCE_NOT_FOUND);
      }

      const existingSpark = await getSparkByName(ctx, args.name!.trim(), spark.eventId);

      if (existingSpark && existingSpark._id !== args.sparkId) {
        throwConvexError("A spark with this name already exists in this event", ErrorCode.DUPLICATE_ENTRY);
      }
    }

    // If updating fields, validate them using helper function
    if (args.fields !== undefined) {
      const fieldErrors = validateSparkFields(args.fields);
      if (fieldErrors.length > 0) {
        throwConvexError(fieldErrors[0], ErrorCode.VALIDATION_ERROR);
      }
    }

    await updateSparkHelper(ctx, args.sparkId, {
      name: args.name,
      description: args.description,
      color: args.color,
      fields: args.fields,
    });
    return createUpdateResponse();
  },
});

// Delete spark with cascade delete of submissions
export const deleteSpark = mutation({
  args: { username: v.string(), sparkId: v.id("sparks") },
  handler: async (ctx, args): Promise<DeleteResponse> => {
    // Authenticate admin user
    await requireAdmin(ctx, args.username, true); // Use ConvexError for admin mutations
    const spark = await getSparkByIdHelper(ctx, args.sparkId);
    if (!spark) {
      throwConvexError("Spark not found", ErrorCode.RESOURCE_NOT_FOUND);
    }

    // Check if spark is being used by any sessions
    const canDelete = await canDeleteSparkHelper(ctx, args.sparkId);

    if (!canDelete) {
      throwConvexError(`SPARK_IN_USE:${spark.name}`, ErrorCode.RESOURCE_LOCKED);
    }

    // Use optimized cascade deletion
    await deleteSparkWithCascade(ctx, args.sparkId);
    return createDeleteResponse();
  },
});

// Check if spark can be deleted (returns true if can delete, false if in use)
export const canDeleteSpark = query({
  args: { sparkId: v.id("sparks") },
  handler: async (ctx, args) => {
    return await canDeleteSparkHelper(ctx, args.sparkId);
  },
});

// Get submission count for a spark (for delete confirmation)
export const getSparkSubmissionCount = query({
  args: { sparkId: v.id("sparks") },
  handler: async (ctx, args) => {
    return await getSparkSubmissionCountHelper(ctx, args.sparkId);
  },
});

// Check if spark can be edited (returns true if can edit, false if has submissions)
export const canEditSpark = query({
  args: { sparkId: v.id("sparks") },
  handler: async (ctx, args) => {
    return await canEditSparkHelper(ctx, args.sparkId);
  },
});

// OPTIMIZED: Batch action to check spark usability (delete, edit, submission count)
export const checkSparkUsabilityAction = action({
  args: { sparkId: v.id("sparks") },
  handler: async (ctx, args): Promise<ActionResponse<{
    canDelete: boolean;
    canEdit: boolean;
    submissionCount: number;
  }>> => {
    try {
      // OPTIMIZED: Execute all three checks in parallel instead of individual sequential calls
      const [canDelete, canEdit, submissionCount] = await Promise.all([
        ctx.runQuery(api.sparks.canDeleteSpark, args),
        ctx.runQuery(api.sparks.canEditSpark, args),
        ctx.runQuery(api.sparks.getSparkSubmissionCount, args)
      ]);
      
      return createActionSuccess({
        canDelete,
        canEdit,
        submissionCount
      });
    } catch (error) {
      return createActionError(error instanceof Error ? error.message : "Failed to check spark usability");
    }
  },
});

// Individual action wrappers for backward compatibility (deprecated - use checkSparkUsabilityAction)
export const checkCanDeleteSpark = action({
  args: { sparkId: v.id("sparks") },
  handler: async (ctx, args): Promise<ActionResponse<boolean>> => {
    try {
      const canDelete = await ctx.runQuery(api.sparks.canDeleteSpark, args);
      return createActionSuccess(canDelete);
    } catch (error) {
      return createActionError(error instanceof Error ? error.message : "Failed to check if spark can be deleted");
    }
  },
});

export const checkCanEditSpark = action({
  args: { sparkId: v.id("sparks") },
  handler: async (ctx, args): Promise<ActionResponse<boolean>> => {
    try {
      const canEdit = await ctx.runQuery(api.sparks.canEditSpark, args);
      return createActionSuccess(canEdit);
    } catch (error) {
      return createActionError(error instanceof Error ? error.message : "Failed to check if spark can be edited");
    }
  },
});

export const getSparkSubmissionCountAction = action({
  args: { sparkId: v.id("sparks") },
  handler: async (ctx, args): Promise<ActionResponse<number>> => {
    try {
      const submissionCount = await ctx.runQuery(api.sparks.getSparkSubmissionCount, args);
      return createActionSuccess(submissionCount);
    } catch (error) {
      return createActionError(error instanceof Error ? error.message : "Failed to get spark submission count");
    }
  },
});

// Get spark names for dropdown selection (useful for session management)
export const getSparkNames = query({
  args: { eventId: v.id("events") },
  handler: async (ctx, args) => {
    return await getSparkNamesOptimized(ctx, args.eventId);
  },
});

// Batch check spark usability (can delete, can edit, submission count)
export const batchCheckSparksUsability = query({
  args: { sparkIds: v.array(v.id("sparks")) },
  handler: async (ctx, args) => {
    return await batchCheckSparkUsability(ctx, args.sparkIds);
  },
});

// Action to batch check spark usability (for frontend usage)
export const batchCheckSparksUsabilityAction = action({
  args: { sparkIds: v.array(v.id("sparks")) },
  handler: async (ctx, args): Promise<ActionResponse<Map<string, {
    canDelete: boolean;
    canEdit: boolean;
    submissionCount: number;
  }>>> => {
    try {
      const usabilityMap = await ctx.runQuery(api.sparks.batchCheckSparksUsability, args);
      
      // Convert Map to plain object for serialization
      const result = new Map<string, {
        canDelete: boolean;
        canEdit: boolean;
        submissionCount: number;
      }>();
      
      usabilityMap.forEach((value, key) => {
        result.set(key.toString(), value);
      });
      
      return createActionSuccess(result);
    } catch (error) {
      return createActionError(error instanceof Error ? error.message : "Failed to batch check spark usability");
    }
  },
});
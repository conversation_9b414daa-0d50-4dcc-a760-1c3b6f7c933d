import { v } from "convex/values";
import { mutation, query } from "./_generated/server";
import { 
  getUserByIdOrThrow,
  getUserIdeas as getUserIdeasHelper,
  getUserSparkSubmissions,
  getUserVotes,
  getUserQuickfireVotesForDeletion,
  getVotesForIdea
} from "./lib/userHelpers";
import { batchDeleteEntities } from "./lib/batchHelpers";
import { 
  DeleteResponse, 
  MutationResponse 
} from "./lib/responseTypes";
import { 
  createDeleteResponse, 
  createSuccessResponse 
} from "./lib/responseUtils";

export const getUserIdeas = query({
  args: { userId: v.id("users") },
  handler: async (ctx, args) => {
    return await getUserIdeasHelper(ctx, args.userId.toString());
  },
});

export const getUserIdeasForDeletion = query({
  args: { userId: v.id("users") },
  handler: async (ctx, args): Promise<{
    userRole: string;
    hasIdeas: boolean;
    hasSparks: boolean;
    hasVotes: boolean;
    hasQuickfireVotes: boolean;
    hasContent: boolean;
    hasAnyData: boolean;
    counts: {
      ideas: number;
      sparkSubmissions: number;
      votes: number;
      quickfireVotes: number;
    };
  }> => {
    // Get user to check their role
    const user = await getUserByIdOrThrow(ctx, args.userId);

    // OPTIMIZATION: Batch load all user-related data in parallel
    const [ideas, sparkSubmissions, votes, quickfireVotes] = await Promise.all([
      getUserIdeasHelper(ctx, args.userId),
      getUserSparkSubmissions(ctx, args.userId),
      getUserVotes(ctx, args.userId),
      getUserQuickfireVotesForDeletion(ctx, args.userId)
    ]);

    return {
      userRole: user.role,
      hasIdeas: ideas.length > 0,
      hasSparks: sparkSubmissions.length > 0,
      hasVotes: votes.length > 0,
      hasQuickfireVotes: quickfireVotes.length > 0,
      hasContent: ideas.length > 0 || sparkSubmissions.length > 0,
      hasAnyData: ideas.length > 0 || sparkSubmissions.length > 0 || votes.length > 0 || quickfireVotes.length > 0,
      counts: {
        ideas: ideas.length,
        sparkSubmissions: sparkSubmissions.length,
        votes: votes.length,
        quickfireVotes: quickfireVotes.length
      }
    };
  },
});

export const transferUserContent = mutation({
  args: {
    fromUserId: v.id("users"),
    toUserId: v.id("users"),
  },
  handler: async (ctx, args): Promise<MutationResponse<{
    ideasTransferred: number;
    sparkSubmissionsTransferred: number;
  }>> => {
    // OPTIMIZATION: Batch load all content to transfer in parallel
    const [ideas, sparkSubmissions] = await Promise.all([
      getUserIdeasHelper(ctx, args.fromUserId),
      getUserSparkSubmissions(ctx, args.fromUserId)
    ]);

    const now = Date.now();
    const transferPromises = [];

    // Transfer ideas
    for (const idea of ideas) {
      transferPromises.push(
        ctx.db.patch(idea._id, {
          userId: args.toUserId,
          updatedAt: now,
        })
      );
    }

    // Transfer spark submissions
    for (const sparkSubmission of sparkSubmissions) {
      transferPromises.push(
        ctx.db.patch(sparkSubmission._id, {
          userId: args.toUserId,
          updatedAt: now,
        })
      );
    }

    // Execute all transfers in parallel
    await Promise.all(transferPromises);

    return createSuccessResponse({
      ideasTransferred: ideas.length,
      sparkSubmissionsTransferred: sparkSubmissions.length
    });
  },
});

export const deleteUser = mutation({
  args: { 
    userId: v.id("users"),
    cascadeDelete: v.optional(v.boolean()) // Kept for backward compatibility, but always cascade deletes
  },
  handler: async (ctx, args): Promise<DeleteResponse> => {
    // Always cascade delete user's content for data consistency
    // Note: cascadeDelete parameter is maintained for backward compatibility but ignored
    
    // OPTIMIZATION: Batch load all related data to minimize queries
    const [ideas, sparkSubmissions, userVotes, userQuickfireVotes] = await Promise.all([
      getUserIdeasHelper(ctx, args.userId),
      getUserSparkSubmissions(ctx, args.userId),
      getUserVotes(ctx, args.userId),
      getUserQuickfireVotesForDeletion(ctx, args.userId)
    ]);
    
    // OPTIMIZATION: Batch load all votes for user's ideas
    const allIdeaVotes = await Promise.all(
      ideas.map((idea: any) => getVotesForIdea(ctx, idea._id))
    );
    
    // Flatten all idea votes into a single array
    const ideaVotes = allIdeaVotes.flat();
    
    // OPTIMIZATION: Use batch delete helper for efficient deletion
    const allEntitiesToDelete = [
      ...ideaVotes,
      ...ideas,
      ...sparkSubmissions,
      ...userVotes,
      ...userQuickfireVotes
    ];
    
    // Execute all deletions using the batch helper
    await batchDeleteEntities(ctx, allEntitiesToDelete);
    
    // Delete the user
    await ctx.db.delete(args.userId);
    
    return createDeleteResponse();
  },
});
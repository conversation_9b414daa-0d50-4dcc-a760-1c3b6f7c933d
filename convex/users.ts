import { v } from "convex/values";
import { query } from "./_generated/server";
import { Doc } from "./_generated/dataModel";
import { getActiveEventOrNull } from "./lib/eventHelpers";
import { 
  getUserByUsername as getUserByUsernameHelper,
  getUserByIdOrThrow,
  getUsersByStatusWithRoleFilter,
  getUsersByRole as getUsersByRoleHelper,
  getApprovedUsersExcludingRole,
  getAllUsers as getAllUsersHelper
} from "./lib/userHelpers";
import { batchLoadTeams } from "./lib/batchHelpers";
import { getTeamsByEvent } from "./lib/teamHelpers";

// Re-export functions from specialized modules for backward compatibility
export { validateAdminUserQuery } from "./userAuth";
export { 
  updateUserProfile, 
  updateUserProfileMutation,
  updateUserProfileWithHashedPassword,
  updateUserName, 
  updateUserRole,
  updateUserPassword,
  updateUserHashedPassword
} from "./userProfile";
export { 
  createUser,
  createUserWithTeam,
  createUserWithHashedPassword,
  registerUser,
  registerUserMutation
} from "./userRegistration";
export { 
  updateUserEvents,
  joinTeam,
  updateUserTeam,
  bulkUpdateUserTeam
} from "./userTeams";
export { 
  getAdminUsers,
  getAdminUsersForManagement,
  createAdminUserWithHashedPassword,
  updateAdminUsername,
  updateAdminPasswordWithHashedPassword,
  deleteAdminUser
} from "./userAdmin";
export { 
  getUserIdeas,
  getUserIdeasForDeletion,
  transferUserContent,
  deleteUser
} from "./userContent";
export { 
  updateUserStatus,
  removeLastSeenField
} from "./userStatus";

// Core user query functions that remain in this file
export const getUserByUsername = query({
  args: { username: v.string() },
  handler: async (ctx, args): Promise<any> => {
    const user = await getUserByUsernameHelper(ctx, args.username);
    return user;
  },
});

export const getUserById = query({
  args: { userId: v.id("users") },
  handler: async (ctx, args): Promise<any> => {
    return await getUserByIdOrThrow(ctx, args.userId);
  },
});

export const checkUserAdmin = query({
  args: { username: v.string() },
  handler: async (ctx, args): Promise<{ isAdmin: boolean }> => {
    const user = await getUserByUsernameHelper(ctx, args.username);
    
    return { isAdmin: user?.role === "admin" };
  },
});

export const getAllUsers = query({
  args: {},
  handler: async (ctx): Promise<any[]> => {
    const users = await getAllUsersHelper(ctx);
    
    // Optional: Log metrics for monitoring (in production)
    if (users.length > 1000) {
      console.warn(`Large user collection: ${users.length} users`);
    }
    
    return users;
  },
});

export const getUsersByStatus = query({
  args: { status: v.union(v.literal("pending"), v.literal("approved"), v.literal("rejected")) },
  handler: async (ctx, args): Promise<any[]> => {
    return await getUsersByStatusWithRoleFilter(ctx, args.status);
  },
});

export const getUsersByRole = query({
  args: { role: v.union(v.literal("admin"), v.literal("teamLead"), v.literal("teamMember")) },
  handler: async (ctx, args): Promise<any[]> => {
    return await getUsersByRoleHelper(ctx, args.role);
  },
});

export const getCurrentUser = query({
  args: { username: v.string(), activeEventId: v.optional(v.id("events")) },
  handler: async (ctx, args): Promise<{
    id: string;
    name: string;
    username: string;
    role: string;
    teamName: string | null;
    teamId: string | null;
    autosave: boolean | undefined;
  } | null> => {
    const user = await getUserByUsernameHelper(ctx, args.username);
    
    if (!user) {
      return null; // Return null instead of throwing error
    }

    if (!args.activeEventId) {
      return {
        id: user._id.toString(),
        name: user.name,
        username: user.username,
        role: user.role,
        teamName: null,
        teamId: null,
        autosave: user.autosave,
      };
    }

    // Find the active event registration
    const activeEventRegistration = user.events?.find(
      (event: any) => event.eventId === args.activeEventId
    );

    let teamName = null;
    if (activeEventRegistration?.teamId) {
      const teams = await batchLoadTeams(ctx, [activeEventRegistration.teamId]);
      teamName = teams.get(activeEventRegistration.teamId)?.name || null;
    }

    return {
      id: user._id.toString(),
      name: user.name,
      username: user.username,
      role: user.role,
      teamName,
      teamId: activeEventRegistration?.teamId?.toString() || null,
      autosave: user.autosave,
    };
  },
});

export const getUserApprovalStatus = query({
  args: { username: v.string() },
  handler: async (ctx, args): Promise<{ status: string; role: string } | null> => {
    const user = await getUserByUsernameHelper(ctx, args.username);
    
    if (!user) {
      return null;
    }
    
    return {
      status: user.status,
      role: user.role
    };
  },
});

// UserManagement component functions - OPTIMIZED VERSION
export const getUsersGroupedByTeam = query({
  args: {},
  handler: async (ctx): Promise<{
    teams: Array<{
      _id: any;
      name: string;
      users: any[];
    }>;
    noTeam: any[];
  }> => {
    // Get active event using shared utility
    const activeEvent = await getActiveEventOrNull(ctx);

    if (!activeEvent) {
      return { teams: [], noTeam: [] }; // Return consistent structure when no active event
    }

    // OPTIMIZATION: Batch load approved users and teams in parallel
    const [users, teams] = await Promise.all([
      getApprovedUsersExcludingRole(ctx, "approved", "admin"),
      getTeamsByEvent(ctx, activeEvent._id)
    ]);

    // Create a map for faster team lookups
    const teamMap = new Map(teams.map((team: any) => [team._id.toString(), team]));

    // Pre-filter users by active event to avoid repeated filtering
    const eventUsers = users.filter((user: any) => 
      user.events?.some((e: any) => e.eventId === activeEvent._id)
    );

    // Group users efficiently using a single pass
    const teamUsers = new Map<string, any[]>();
    const noTeamUsers: any[] = [];

    for (const user of eventUsers) {
      const eventRegistration = user.events.find((e: any) => e.eventId === activeEvent._id);
      
      if (eventRegistration?.teamId) {
        const teamIdStr = eventRegistration.teamId.toString();
        const team: any = teamMap.get(teamIdStr);
        
        if (team) {
          if (!teamUsers.has(teamIdStr)) {
            teamUsers.set(teamIdStr, []);
          }
          
          teamUsers.get(teamIdStr)!.push({
            _id: user._id,
            username: user.username,
            name: user.name,
            role: user.role,
            teamId: team._id,
            teamName: team.name
          });
        }
      } else {
        noTeamUsers.push({
          _id: user._id,
          username: user.username,
          name: user.name,
          role: user.role
        });
      }
    }

    // Build final result efficiently
    const groupedUsers = {
      teams: teams.map((team: Doc<"teams">) => ({
        _id: team._id,
        name: team.name,
        users: teamUsers.get(team._id.toString()) || []
      })),
      noTeam: noTeamUsers
    };

    return groupedUsers;
  },
});
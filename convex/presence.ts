import { v } from "convex/values";
import { mutation, query, internalMutation } from "./_generated/server";
import { getActiveEventOrNull } from "./lib/eventHelpers";
import { getOnlineUsersStatus } from "./lib/settingsHelpers";
import { batchLoadUsers, batchLoadUserTeams } from "./lib/batchHelpers";
import { getUserById } from "./lib/userHelpers";
import { 
  getUserSessionPresence, 
  getUserPresenceRecords, 
  getOnlinePresenceRecords,
  getAllPresenceRecords,
  createPresenceRecord,
  updatePresenceRecord,
  filterRecentPresence,
  getUniqueUsersFromPresence,
  batchSetPresenceOffline,
  batchDeleteStalePresence,
  PRESENCE_CONSTANTS 
} from "./lib/presenceHelpers";
import { 
  MutationResponse, 
  QueryResponse, 
  ErrorCode 
} from "./lib/responseTypes";
import { 
  createSuccessResponse, 
  createErrorResponse
} from "./lib/responseUtils";

// =====================================
// PRESENCE-SPECIFIC RESPONSE TYPES
// =====================================

/**
 * Response for presence operations
 */
export type PresenceResponse = MutationResponse<{
  isOnline?: boolean;
  lastSeen?: number;
  reason?: string;
}>;

/**
 * Response for presence statistics
 */
export type PresenceStatsResponse = QueryResponse<{
  enabled: boolean;
  totalPresenceRecords: number;
  onlineUsers: number;
  uniqueUsers: number;
}>;

/**
 * Response for online users query
 */
export type OnlineUsersResponse = QueryResponse<Array<{
  id: string;
  username: string;
  name: string;
  role: string;
  teamId: string | null;
  lastSeen: number;
}>>;

/**
 * Response for cleanup operations
 */
export type CleanupResponse = MutationResponse<{
  cleaned: boolean;
  count?: number;
}>;

// Helper function to check if online users feature is enabled
async function isOnlineUsersEnabled(ctx: any): Promise<boolean> {
  return await getOnlineUsersStatus(ctx);
}

// Update or create presence record for a user
export const updatePresence = mutation({
  args: {
    userId: v.string(), // Accept any string, validate in handler
    sessionId: v.string(),
  },
  handler: async (ctx, args): Promise<PresenceResponse> => {
    try {
      // Check if online users feature is enabled
      if (!(await isOnlineUsersEnabled(ctx))) {
        return createErrorResponse("Online users feature is disabled", ErrorCode.OPERATION_NOT_ALLOWED);
      }

      // Validate userId format and that the user exists
      let userId;
      try {
        userId = args.userId as any; // Try to use as ID
        const user = await getUserById(ctx, userId);
        if (!user) {
          return createErrorResponse("User not found", ErrorCode.USER_NOT_FOUND);
        }
      } catch (error) {
        return createErrorResponse("User not found", ErrorCode.USER_NOT_FOUND);
      }

      const now = Date.now();

      // Find existing presence record for this user and session
      const existingPresence = await getUserSessionPresence(ctx, userId, args.sessionId);

      if (existingPresence) {
        // Update existing presence using helper
        await updatePresenceRecord(ctx, existingPresence._id, {
          lastSeen: now,
          isOnline: true,
        });
      } else {
        // Create new presence record using helper
        await createPresenceRecord(ctx, userId, args.sessionId, true);
      }

      return createSuccessResponse({
        isOnline: true,
        lastSeen: now
      });
    } catch (error) {
      return createErrorResponse(
        error instanceof Error ? error.message : "Failed to update presence",
        ErrorCode.INTERNAL_ERROR
      );
    }
  },
});

// Set user offline (when they explicitly disconnect)
export const setOffline = mutation({
  args: {
    userId: v.string(), // Accept any string, validate in handler
    sessionId: v.optional(v.string()), // Optional - if not provided, sets all sessions offline
  },
  handler: async (ctx, args): Promise<PresenceResponse> => {
    try {
      // Check if online users feature is enabled
      if (!(await isOnlineUsersEnabled(ctx))) {
        return createErrorResponse("Online users feature is disabled", ErrorCode.OPERATION_NOT_ALLOWED);
      }

      // Validate userId format and that the user exists
      let userId;
      try {
        userId = args.userId as any; // Try to use as ID
        const user = await getUserById(ctx, userId);
        if (!user) {
          return createErrorResponse("User not found", ErrorCode.USER_NOT_FOUND);
        }
      } catch (error) {
        return createErrorResponse("User not found", ErrorCode.USER_NOT_FOUND);
      }

      const now = Date.now();

      if (args.sessionId) {
        // Set specific session offline
        const presence = await getUserSessionPresence(ctx, userId, args.sessionId);

        if (presence) {
          await updatePresenceRecord(ctx, presence._id, {
            isOnline: false,
            lastSeen: now,
          });
        }
      } else {
        // Set all sessions for this user offline
        const userPresences = await getUserPresenceRecords(ctx, userId);
        await batchSetPresenceOffline(ctx, userPresences);
      }

      return createSuccessResponse({
        isOnline: false,
        lastSeen: now
      });
    } catch (error) {
      return createErrorResponse(
        error instanceof Error ? error.message : "Failed to set user offline",
        ErrorCode.INTERNAL_ERROR
      );
    }
  },
});

// Get all online users with their details
export const getOnlineUsers = query({
  args: {},
  handler: async (ctx): Promise<OnlineUsersResponse> => {
    // Check if online users feature is enabled
    if (!(await isOnlineUsersEnabled(ctx))) {
      return [];
    }

    // Get all presence records that are marked online
    const presenceRecords = await getOnlinePresenceRecords(ctx);

    // Filter out stale records and get unique users
    const recentPresence = filterRecentPresence(presenceRecords, PRESENCE_CONSTANTS.OFFLINE_THRESHOLD);
    const activeUsers = getUniqueUsersFromPresence(recentPresence);

    // Early return if no active users
    if (activeUsers.size === 0) {
      return [];
    }

    // Batch load all users and their team data
    const userIds = Array.from(activeUsers.keys());
    const activeEvent = await getActiveEventOrNull(ctx);
    
    // Batch load users and teams in parallel
    const [users, userTeams] = await Promise.all([
      batchLoadUsers(ctx, userIds),
      activeEvent 
        ? batchLoadUserTeams(ctx, activeEvent._id, userIds)
        : Promise.resolve(new Map())
    ]);
    
    // Build final response with enriched user data
    const enrichedUsers = [];
    for (const [userId, presenceData] of Array.from(activeUsers.entries())) {
      const user = users.get(userId);
      if (!user) continue; // Skip if user not found
      
      const team = userTeams.get(userId);
      enrichedUsers.push({
        id: user._id,
        username: user.username,
        name: user.name,
        role: user.role,
        teamId: team?._id || null,
        lastSeen: presenceData.lastSeen,
      });
    }
    
    return enrichedUsers;
  },
});

// Background cleanup job for stale presence records
export const cleanupStalePresence = internalMutation({
  args: {},
  handler: async (ctx): Promise<CleanupResponse> => {
    try {
      const now = Date.now();
      
      // Find all presence records older than threshold using helper
      const allPresence = await getAllPresenceRecords(ctx);
      const staleRecords = allPresence.filter((presence: any) => 
        (now - presence.lastSeen) > PRESENCE_CONSTANTS.CLEANUP_THRESHOLD
      );
      
      // Use batch delete helper for efficient deletion
      await batchDeleteStalePresence(ctx, staleRecords);
      const cleanedCount = staleRecords.length;

      return createSuccessResponse({
        cleaned: true,
        count: cleanedCount
      });
    } catch (error) {
      return createErrorResponse(
        error instanceof Error ? error.message : "Failed to cleanup stale presence records",
        ErrorCode.INTERNAL_ERROR
      );
    }
  },
});

// Get presence statistics
export const getPresenceStats = query({
  args: {},
  handler: async (ctx): Promise<PresenceStatsResponse> => {
    // Check if online users feature is enabled
    if (!(await isOnlineUsersEnabled(ctx))) {
      return {
        enabled: false,
        totalPresenceRecords: 0,
        onlineUsers: 0,
        uniqueUsers: 0,
      };
    }

    // Use helper functions instead of direct database queries
    const [allPresence, onlinePresence] = await Promise.all([
      getAllPresenceRecords(ctx),
      getOnlinePresenceRecords(ctx)
    ]);
    
    const recentOnlinePresence = filterRecentPresence(onlinePresence, PRESENCE_CONSTANTS.OFFLINE_THRESHOLD);
    const uniqueOnlineUsers = new Set(recentOnlinePresence.map(p => p.userId)).size;

    return {
      enabled: true,
      totalPresenceRecords: allPresence.length,
      onlineUsers: recentOnlinePresence.length,
      uniqueUsers: uniqueOnlineUsers,
    };
  },
});
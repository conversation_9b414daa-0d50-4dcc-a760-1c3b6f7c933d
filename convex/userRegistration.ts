import { v } from "convex/values";
import { mutation, action, internalMutation } from "./_generated/server";
import { internal } from "./_generated/api";
import bcrypt from "bcryptjs";
import { getActiveEvent } from "./lib/eventHelpers";
import { getUserByUsername as getUserByUsernameHelper } from "./lib/userHelpers";
import { getSettingByKey } from "./lib/settingsHelpers";
import { batchLoadTeams } from "./lib/batchHelpers";
import { 
  CreateResponse, 
  ActionResponse, 
  MutationResponse,
  ErrorCode 
} from "./lib/responseTypes";
import { 
  createCreateResponse, 
  createActionSuccess, 
  createActionError, 
  createSuccessResponse,
  throwConvexError 
} from "./lib/responseUtils";

export const createUser = mutation({
  args: {
    username: v.string(),
    name: v.string(),
    password: v.string(), // bcrypt hashed
    role: v.union(v.literal("admin"), v.literal("teamLead"), v.literal("teamMember")),
    status: v.union(v.literal("pending"), v.literal("approved"), v.literal("rejected")),
    events: v.optional(v.array(v.object({
      eventId: v.id("events"),
      teamId: v.optional(v.id("teams"))
    }))),
  },
  handler: async (ctx, args): Promise<CreateResponse<"users">> => {
    const existingUser = await getUserByUsernameHelper(ctx, args.username);
    
    if (existingUser) {
      throwConvexError("Username already exists. Please choose a different username.", ErrorCode.DUPLICATE_ENTRY);
    }

    const now = Date.now();
    const id = await ctx.db.insert("users", {
      username: args.username,
      name: args.name,
      password: args.password,
      role: args.role,
      status: args.status,
      events: args.events || [],
      autosave: true, // Default autosave to enabled for new users
      createdAt: now,
    });
    
    return createCreateResponse(id);
  },
});

export const createUserWithTeam = mutation({
  args: {
    username: v.string(),
    name: v.string(),
    password: v.string(), // raw password - will be hashed
    role: v.union(v.literal("teamLead"), v.literal("teamMember")),
    teamId: v.optional(v.id("teams")),
  },
  handler: async (ctx, args): Promise<MutationResponse<{
    user: {
      id: string;
      _id: string;
      username: string;
      name: string;
      role: string;
      teamId?: string;
      teamName?: string;
    };
  }>> => {
    // Check if username already exists
    const existingUser = await getUserByUsernameHelper(ctx, args.username);
    
    if (existingUser) {
      throwConvexError("Username already exists. Please choose a different username.", ErrorCode.DUPLICATE_ENTRY);
    }

    // Get active event
    const activeEvent = await getActiveEvent(ctx);

    // Hash password
    const hashedPassword = await bcrypt.hash(args.password, 10);

    // Create user with event registration
    const events = args.teamId ? [{
      eventId: activeEvent._id,
      teamId: args.teamId
    }] : [{
      eventId: activeEvent._id
    }];

    const now = Date.now();
    const userId = await ctx.db.insert("users", {
      username: args.username,
      name: args.name,
      password: hashedPassword,
      role: args.role,
      status: "approved", // Auto-approve created users
      events,
      autosave: true, // Default autosave to enabled for new users
      createdAt: now,
    });

    // Get team data if needed using batch loading
    let teamName = undefined;
    if (args.teamId) {
      const teams = await batchLoadTeams(ctx, [args.teamId]);
      teamName = teams.get(args.teamId)?.name;
    }

    // Return user data in the format expected by the UI
    const user = await ctx.db.get(userId);
    return createSuccessResponse({
      user: {
        id: userId.toString(),
        _id: userId.toString(),
        username: user!.username,
        name: user!.name,
        role: user!.role,
        teamId: args.teamId?.toString(),
        teamName
      }
    });
  },
});

export const createUserWithHashedPassword = mutation({
  args: {
    username: v.string(),
    name: v.string(),
    hashedPassword: v.string(), // already hashed password
    role: v.union(v.literal("teamLead"), v.literal("teamMember")),
    teamId: v.optional(v.id("teams")),
  },
  handler: async (ctx, args): Promise<MutationResponse<{
    user: {
      id: string;
      _id: string;
      username: string;
      name: string;
      role: string;
      teamId?: string;
      teamName?: string;
    };
  }>> => {
    // Check if username already exists
    const existingUser = await getUserByUsernameHelper(ctx, args.username);
    
    if (existingUser) {
      throwConvexError("Username already exists. Please choose a different username.", ErrorCode.DUPLICATE_ENTRY);
    }

    // Get active event
    const activeEvent = await getActiveEvent(ctx);

    // Create user with event registration
    const events = args.teamId ? [{
      eventId: activeEvent._id,
      teamId: args.teamId
    }] : [{
      eventId: activeEvent._id
    }];

    const now = Date.now();
    const userId = await ctx.db.insert("users", {
      username: args.username,
      name: args.name,
      password: args.hashedPassword,
      role: args.role,
      status: "approved", // Auto-approve created users
      events,
      autosave: true, // Default autosave to enabled for new users
      createdAt: now,
    });

    // Get team data if needed using batch loading
    let teamName = undefined;
    if (args.teamId) {
      const teams = await batchLoadTeams(ctx, [args.teamId]);
      teamName = teams.get(args.teamId)?.name;
    }

    // Return user data in the format expected by the UI
    const user = await ctx.db.get(userId);
    return createSuccessResponse({
      user: {
        id: userId.toString(),
        _id: userId.toString(),
        username: user!.username,
        name: user!.name,
        role: user!.role,
        teamId: args.teamId?.toString(),
        teamName
      }
    });
  },
});

export const registerUserMutation = internalMutation({
  args: {
    username: v.string(),
    hashedPassword: v.string(), // already hashed password
    name: v.string(),
    eventId: v.optional(v.id("events")), // Optional eventId for explicit registration
  },
  handler: async (ctx, args): Promise<MutationResponse<{
    user: {
      id: string;
      username: string;
      name: string;
      role: string;
      status: string;
      eventId: string;
      autoApproved: boolean;
      existingUser: boolean;
    };
  }>> => {
    // OPTIMIZED: Execute independent operations in parallel
    const [activeEvent, existingUser, autoApprovalSetting] = await Promise.all([
      getActiveEvent(ctx),
      getUserByUsernameHelper(ctx, args.username),
      getSettingByKey(ctx, "auto-approval")
    ]);

    // If user exists, check if they're already registered for this event
    if (existingUser) {
      const existingEventReg = existingUser.events.find((e: any) => e.eventId === activeEvent._id);
      if (existingEventReg) {
        throwConvexError("You are already registered for this event. Please login instead.", ErrorCode.DUPLICATE_ENTRY);
      }
      
      // Add registration for new event
      const updatedEvents = [...existingUser.events, { eventId: activeEvent._id }];
      await ctx.db.patch(existingUser._id, {
        events: updatedEvents,
        updatedAt: Date.now(),
      });
      
      return createSuccessResponse({
        user: {
          id: existingUser._id.toString(),
          username: existingUser.username,
          name: existingUser.name,
          role: existingUser.role,
          status: existingUser.status,
          eventId: activeEvent._id.toString(),
          autoApproved: true,
          existingUser: true // Add flag for existing user
        }
      });
    }

    // Use pre-fetched auto-approval setting
    const autoApproval = autoApprovalSetting?.value || false;

    // Create new user with event registration
    const now = Date.now();
    const userId = await ctx.db.insert("users", {
      username: args.username,
      password: args.hashedPassword,
      name: args.name,
      role: 'teamMember',
      status: autoApproval ? 'approved' : 'pending',
      events: [{ eventId: activeEvent._id }],
      autosave: true, // Default autosave to enabled for new users
      createdAt: now,
    });

    // Return user object without password
    return createSuccessResponse({
      user: {
        id: userId.toString(),
        username: args.username,
        name: args.name,
        role: 'teamMember',
        status: autoApproval ? 'approved' : 'pending',
        eventId: activeEvent._id.toString(),
        autoApproved: autoApproval, // Add this flag for the client to handle redirects
        existingUser: false // Add flag for new user
      }
    });
  },
});

export const registerUser = action({
  args: {
    username: v.string(),
    password: v.string(), // raw password - will be hashed
    name: v.string(),
    eventId: v.optional(v.id("events")), // Optional eventId for explicit registration
  },
  handler: async (ctx, args): Promise<ActionResponse<{
    user: {
      id: string;
      username: string;
      name: string;
      role: string;
      status: string;
      eventId: string;
      autoApproved: boolean;
      existingUser: boolean;
    };
  }>> => {
    try {
      // Validate required fields
      if (!args.username || !args.password || !args.name) {
        throwConvexError("All fields are required. Please fill in all information.", ErrorCode.REQUIRED_FIELD_MISSING);
      }

      // Hash password in action (allows setTimeout)
      const hashedPassword = await bcrypt.hash(args.password, 10);

      // Call mutation with hashed password
      const result = await ctx.runMutation(internal.users.registerUserMutation, {
        username: args.username,
        hashedPassword,
        name: args.name,
        eventId: args.eventId,
      });
      
      return createActionSuccess(result.data!, "User registered successfully");
    } catch (error) {
      return createActionError(error instanceof Error ? error.message : "Failed to register user");
    }
  },
});
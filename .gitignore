# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
Context/screenshots/
Context/Specs/**
/node_modules
/.pnp
.pnp.*
.yarn/*
!.yarn/patches
!.yarn/plugins
!.yarn/releases
!.yarn/versions
/design-ref
/technical-ref
# testing
/coverage
/resources
/references
/mongodb
/minio-data
# next.js
/.next/
/out/
apprefresher.example
/mongo
# production
/build
.windsurf*
/.cursor
# misc
.DS_Store
*.pem

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*

# env files (can opt-in for committing if needed)
.env*

# vercel
.vercel

# typescript
*.tsbuildinfo
next-env.d.ts
testbackup/
backups/
# Sentry Config File
.env.sentry-build-plugin

# Sentry Config File
.sentryclirc
logs/
build-and-push.sh



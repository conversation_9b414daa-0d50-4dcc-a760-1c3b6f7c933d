{"name": "LionX", "short_name": "LionX", "description": "LionX", "start_url": "/", "scope": "/", "icons": [{"src": "/favicons/favicon-96x96.png", "sizes": "96x96", "type": "image/png"}, {"src": "/favicons/apple-touch-icon.png", "sizes": "180x180", "type": "image/png"}, {"src": "/favicons/web-app-manifest-192x192.png", "sizes": "192x192", "type": "image/png"}, {"src": "/favicons/web-app-manifest-512x512.png", "sizes": "512x512", "type": "image/png"}], "theme_color": "#000000", "background_color": "#000000", "display": "standalone"}
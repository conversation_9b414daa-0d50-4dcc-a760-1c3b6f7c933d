import 'next-auth';

declare module 'next-auth' {
  interface User {
    id: string;
    username: string;
    name: string;
    role: 'admin' | 'teamLead' | 'teamMember';
    status: 'pending' | 'approved' | 'rejected';
    teamId: string | null;
    eventId: string | null;
  }

  interface Session {
    user: {
      id: string;
      username: string;
      name: string;
      role: 'admin' | 'teamLead' | 'teamMember';
      status: 'pending' | 'approved' | 'rejected';
      teamId: string | null;
      eventId: string | null;
    };
  }
}

declare module 'next-auth/jwt' {
  interface JWT {
    id: string;
    username: string;
    name: string;
    role: 'admin' | 'teamLead' | 'teamMember';
    status: 'pending' | 'approved' | 'rejected';
    teamId: string | null;
    eventId: string | null;
  }
}
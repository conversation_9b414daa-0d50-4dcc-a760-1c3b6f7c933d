"use client";

import { lazy, Suspense } from 'react';
import { useSearchParams } from 'next/navigation';
import { StateCard } from '@/components/ui/state-card';

// Lazy load components
const Settings = lazy(() => import('./Settings'));
const UserApprovals = lazy(() => import('./UserApprovals'));
const TeamsManagement = lazy(() => import('./TeamsManagement'));
const EventsManagement = lazy(() => import('./EventsManagement'));
const UserManagement = lazy(() => import('./UserManagement'));
const SessionsManagement = lazy(() => import('./SessionsManagement'));
const IdeasManagement = lazy(() => import('./IdeasManagement'));
const QuickfireManagement = lazy(() => import('./QuickfireManagement'));
const VotingManagement = lazy(() => import('./VotingManagement'));
const SparksManagement = lazy(() => import('./SparksManagement'));
const SnippetsManagement = lazy(() => import('./SnippetsManagement'));
const AdminsManagement = lazy(() => import('./AdminsManagement'));
const DataAnalytics = lazy(() => import('./DataAnalytics'));
const BackupsManagement = lazy(() => import('./BackupsManagement'));
const OnlineUsers = lazy(() => import('./OnlineUsers'));

type AdminSection = 'events' | 'approvals' | 'teams' | 'users' | 'sessions' | 'ideas' | 'quickfire' | 'voting' | 'sparks' | 'snippets' | 'data' | 'backups' | 'admins' | 'settings' | 'online';

const SectionPlaceholder = ({ title }: { title: string }) => (
    <div className="space-y-4">
        <h2 className="text-2xl font-bold text-primary">{title}</h2>
        <div className="flex justify-center items-center min-h-[400px]">
            <StateCard state="info" title="Under Development" message="This section is under development" />
        </div>
    </div>
);

const LoadingFallback = () => (
    <div className="flex justify-center items-center min-h-[400px]">
        <StateCard state="loading" title="Loading section..." />
    </div>
);

export default function AdminDashboard() {
    const searchParams = useSearchParams();
    const activeSection = (searchParams.get('section') as AdminSection) || 'events';

    const renderSection = () => {
        switch (activeSection) {
            case 'events':
                return <EventsManagement />;
            case 'approvals':
                return <UserApprovals />;
            case 'teams':
                return <TeamsManagement />;
            case 'users':
                return <UserManagement />;
            case 'sessions':
                return <SessionsManagement />;
            case 'ideas':
                return <IdeasManagement />;
            case 'quickfire':
                return <QuickfireManagement />;
            case 'voting':
                return <VotingManagement />;
            case 'sparks':
                return <SparksManagement />;
            case 'snippets':
                return <SnippetsManagement />;
            case 'data':
                return <DataAnalytics />;
            case 'backups':
                return <BackupsManagement />;
            case 'admins':
                return <AdminsManagement />;
            case 'settings':
                return <Settings />;
            case 'online':
                return <OnlineUsers />;
            default:
                return <SectionPlaceholder title={`${activeSection} Management`} />;
        }
    };

    return (
        <Suspense fallback={<LoadingFallback />}>
            {renderSection()}
        </Suspense>
    );
}
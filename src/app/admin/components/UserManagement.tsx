"use client";

import { useState, useMemo, useCallback, memo } from 'react';
import { useQuery } from "convex/react";
import { ConvexError } from 'convex/values';
import { api } from "@/../convex/_generated/api";
import { Id } from "@/../convex/_generated/dataModel";
import UserHeader from './users/UserHeader';
import UserList from './users/UserList';
import NoActiveEventMessage from './ideas/components/NoActiveEventMessage';
import { StateCard } from '@/components/ui/state-card';
import AdminErrorBoundary from './AdminErrorBoundary';
import {
    RoleChangePopup,
    EditUserPopup,
    TeamChangePopup,
    DeleteConfirmPopup,
    TeamLeadActionsPopup,
    AddUserPopup,
    IdeaStatsPopup
} from './users/UserPopups';
import { useUserManagement } from '@/app/admin/hooks/useUserManagement';
import { useUserFilters } from '@/app/admin/hooks/useUserFilters';
import { useUserModals } from '@/app/admin/hooks/useUserModals';
import { useUserContent } from '@/app/admin/hooks/useUserContent';
import { 
    getCurrentUserRole, 
    getAvailableTeamLeads, 
    startBulkContentCheck, 
    hasTeamLeadsInSelection,
    getTotalUserCount
} from '@/app/admin/utils/userUtils';

// Memoized components for better performance
const UserHeaderMemo = memo(UserHeader);
const UserListMemo = memo(UserList);




function UserManagementCore() {
    // Use extracted hooks for cleaner organization
    const { 
        activeEvent, 
        groupedUsers, 
        error, 
        handleEditUser, 
        handleTeamChange, 
        handleBulkTeamChange, 
        handleRoleChange, 
        handleDeleteUser, 
        handleAddUser, 
        handleMakePending, 
        handleTransferContent,
        setError
    } = useUserManagement();
    
    const { 
        searchTerm, 
        setSearchTerm, 
        filteredGroupedUsers, 
        allUsers 
    } = useUserFilters(groupedUsers);
    
    // UI state management
    const [selectedUsers, setSelectedUsers] = useState<Set<string>>(new Set());
    
    // Use extracted hooks for cleaner organization
    const { modals, openModal, closeModal } = useUserModals();
    
    // Content checking and transfer logic
    const {
        checkingContentUserId,
        bulkActionBlocked,
        userContentData,
        setCheckingContentUserId,
        setContentTransferIntent,
        setCheckingBulkContent,
        setBulkActionBlocked,
        handleContentTransferWithCallback,
        handlePopupTransferConfirm
    } = useUserContent(
        allUsers,
        setError,
        (state) => openModal('showRoleConfirm', state),
        (state) => openModal('showTeamLeadActions', state),
        (state) => openModal('showDeleteConfirm', state),
        handleTransferContent
    );

    // Query vote counts for delete confirmation
    const userVoteData = useQuery(
        api.users.getUserIdeasForDeletion,
        (modals.showDeleteConfirm && modals.showDeleteConfirm !== 'bulk') ? { userId: modals.showDeleteConfirm as Id<"users"> } : "skip"
    );


    // Optimized total users calculation using memoized allUsers
    const totalUsers = useMemo(() => getTotalUserCount(allUsers), [allUsers]);

    // Unified handler for bulk content checks
    const handleBulkContentCheck = useCallback((type: 'delete' | 'role') => {
        startBulkContentCheck(
            allUsers,
            selectedUsers,
            type,
            setCheckingBulkContent,
            setCheckingContentUserId,
            () => {
                if (type === 'delete') {
                    openModal('showDeleteConfirm', 'bulk');
                } else {
                    openModal('showRoleConfirm', 'bulk');
                }
            }
        );
    }, [allUsers, selectedUsers, setCheckingBulkContent, setCheckingContentUserId, openModal]);

    // Memoized event handlers
    const handleUserTeamChange = useCallback(async (userId: string) => {
        // Check if user is a team lead - handle content checking immediately
        const user = allUsers.find(u => u._id === userId);
        
        if (user?.role === 'teamLead') {
            // For team leads, use unified content transfer system
            handleContentTransferWithCallback(
                userId, 
                user.name, 
                user.teamId,
                'teamChange',
                (userId) => openModal('showTeamChange', userId)
            );
            return;
        }
        
        // For regular users, show team selection popup directly
        openModal('showTeamChange', userId);
    }, [allUsers, handleContentTransferWithCallback, openModal]);

    const handleUserPasswordChange = useCallback((userId: string) => openModal('showEditUser', userId), [openModal]);
    
    const handleUserIdeaStatsClick = useCallback((userId: string) => openModal('showIdeaStats', userId), [openModal]);
    
    const handleUserDeleteClick = useCallback(async (userId: string) => {
        // Check if user is a team lead - optimized with memoized allUsers
        const user = allUsers.find(u => u._id === userId);
        
        if (user?.role === 'teamLead') {
            // For team leads, use unified content transfer system
            handleContentTransferWithCallback(
                userId, 
                user.name, 
                user.teamId,
                'delete',
                (userId) => openModal('showDeleteConfirm', userId)
            );
            return;
        }
        // For regular users, show delete confirmation
        openModal('showDeleteConfirm', userId);
    }, [allUsers, handleContentTransferWithCallback, openModal]);
    
    const handleUserRoleClick = useCallback(async (userId: string, username: string) => {
        // Get user details - optimized with memoized allUsers
        const user = allUsers.find(u => u._id === userId);
        
        if (user?.role === 'teamLead') {
            // For team leads, use unified content transfer system
            handleContentTransferWithCallback(
                userId, 
                user.name, 
                user.teamId,
                'roleChange',
                async (userId) => {
                    // For team leads without content, show role confirmation dialog
                    openModal('showRoleConfirm', { userId, username: user.name });
                }
            );
            return;
        } else {
            // For team members, go directly to role change confirmation
            // Votes don't matter for role changes
            openModal('showRoleConfirm', { userId, username });
        }
    }, [allUsers, handleContentTransferWithCallback, openModal]);

    // Modal event handlers
    const handleRoleConfirmCancel = useCallback(() => {
        closeModal('showRoleConfirm');
        setBulkActionBlocked(null);
    }, [closeModal, setBulkActionBlocked]);

    const handleBulkRoleChange = useCallback(async (newRole: 'teamLead' | 'teamMember') => {
        const userIds = Array.from(selectedUsers);
        try {
            for (const userId of userIds) {
                await handleRoleChange(userId, newRole);
            }
            setSelectedUsers(new Set());
            closeModal('showRoleConfirm');
            setBulkActionBlocked(null);
        } catch (error) {
            if (error instanceof ConvexError) {
                const errorData = error.data as { message: string };
                setError(errorData.message);
            } else {
                setError(error instanceof Error ? error.message : 'Failed to update roles');
            }
        }
    }, [selectedUsers, handleRoleChange, setSelectedUsers, closeModal, setBulkActionBlocked, setError]);

    // Edit user handler with modal close
    const handleEditUserWithClose = useCallback(async (userId: string, data: { name: string; username: string; password?: string }) => {
        try {
            await handleEditUser(userId, data);
            closeModal('showEditUser');
        } catch {
            // Error handling is already done in handleEditUser
            // The error state will be set and the modal will remain open
        }
    }, [handleEditUser, closeModal]);



    // Loading and error states
    if (activeEvent === undefined || groupedUsers === undefined) {
        return (
            <div className="flex justify-center items-center min-h-[400px]">
                <StateCard state="loading" title="Loading..." />
            </div>
        );
    }

    // No active event
    if (activeEvent === null) {
        return <NoActiveEventMessage />;
    }

    if (error) {
        return (
            <div className="flex justify-center items-center min-h-[400px]">
                <StateCard 
                    state="error"
                    title="Error"
                    message={error}
                />
            </div>
        );
    }

    if (!groupedUsers || !filteredGroupedUsers) {
        return (
            <div className="flex justify-center items-center min-h-[400px]">
                <StateCard 
                    state="empty"
                    title="No Users Found"
                    message="No approved users found."
                />
            </div>
        );
    }

    const hasUsers = totalUsers > 0;

    return (
        <div className="space-y-6">
            {/* Show header when there's an active event (users exist OR no users but valid groupedUsers structure) */}
            {(hasUsers || (groupedUsers && groupedUsers.teams)) && (
                <UserHeaderMemo
                    totalUsers={totalUsers}
                    searchTerm={searchTerm}
                    selectedUsers={selectedUsers}
                    setSearchTerm={setSearchTerm}
                    onTeamChange={() => openModal('showTeamChange', 'bulk')}
                    onRoleChange={() => handleBulkContentCheck('role')}
                    onDelete={() => handleBulkContentCheck('delete')}
                    onAddUser={() => openModal('showAddUser', true)}
                />
            )}

            <UserListMemo
                groupedUsers={filteredGroupedUsers}
                selectedUsers={selectedUsers}
                setSelectedUsers={setSelectedUsers}
                onTeamChange={handleUserTeamChange}
                onPasswordChange={handleUserPasswordChange}
                onIdeaStatsClick={handleUserIdeaStatsClick}
                onDeleteClick={handleUserDeleteClick}
                onRoleClick={handleUserRoleClick}
                onMakePending={handleMakePending}
            />

            {/* Popups - same exact UI, but connected to clean Convex handlers */}
            <RoleChangePopup
                showRoleConfirm={modals.showRoleConfirm}
                selectedUsers={selectedUsers}
                onCancel={handleRoleConfirmCancel}
                onBulkRoleChange={handleBulkRoleChange}
                onSingleRoleChange={handleRoleChange}
                currentUserRole={modals.showRoleConfirm && typeof modals.showRoleConfirm !== 'string'
                    ? getCurrentUserRole(allUsers, modals.showRoleConfirm.userId)
                    : undefined}
                hasIdeas={modals.showRoleConfirm && typeof modals.showRoleConfirm !== 'string'
                    ? (modals.showRoleConfirm as { hasIdeas?: boolean }).hasIdeas
                    : undefined}
                availableTeamLeads={modals.showRoleConfirm && typeof modals.showRoleConfirm !== 'string'
                    ? getAvailableTeamLeads(
                        allUsers,
                        (modals.showRoleConfirm as { teamId?: string }).teamId,
                        (modals.showRoleConfirm as { userId?: string }).userId
                    )
                    : []}
                bulkActionBlocked={bulkActionBlocked?.type === 'role' ? bulkActionBlocked : null}
            />

            <EditUserPopup
                showEditUser={modals.showEditUser}
                users={allUsers}
                onCancel={() => closeModal('showEditUser')}
                onConfirm={handleEditUserWithClose}
            />

            <TeamChangePopup
                showTeamChange={modals.showTeamChange}
                teams={groupedUsers.teams}
                selectedUsers={selectedUsers}
                onCancel={() => closeModal('showTeamChange')}
                onTeamChange={handleTeamChange}
                onBulkTeamChange={handleBulkTeamChange}
                hasTeamLeadsInSelection={hasTeamLeadsInSelection(allUsers, selectedUsers)}
                availableTeamLeads={modals.showTeamChange && modals.showTeamChange !== 'bulk' ? (() => {
                    const user = allUsers.find(u => u._id === modals.showTeamChange);
                    return user?.teamId ? getAvailableTeamLeads(allUsers, user.teamId, modals.showTeamChange) : [];
                })() : []}
                userRole={modals.showTeamChange && modals.showTeamChange !== 'bulk' ? getCurrentUserRole(allUsers, modals.showTeamChange) : undefined}
                hasContent={(() => {
                    // Only check content for individual team lead changes (not bulk)
                    if (modals.showTeamChange === 'bulk' || !modals.showTeamChange) return false;
                    
                    const user = allUsers.find(u => u._id === modals.showTeamChange);
                    if (user?.role !== 'teamLead') return false;
                    
                    // For team leads being changed, check if they have content
                    // This uses the same userContentData query that's triggered by content checking
                    return checkingContentUserId === modals.showTeamChange && userContentData?.hasContent === true;
                })()}
                currentUserTeamId={modals.showTeamChange && modals.showTeamChange !== 'bulk' ? (() => {
                    const user = allUsers.find(u => u._id === modals.showTeamChange);
                    return user?.teamId;
                })() : undefined}
            />

            <DeleteConfirmPopup
                showDeleteConfirm={modals.showDeleteConfirm}
                selectedUsers={selectedUsers}
                onCancel={() => {
                    closeModal('showDeleteConfirm');
                    setBulkActionBlocked(null);
                }}
                bulkActionBlocked={bulkActionBlocked?.type === 'delete' ? bulkActionBlocked : null}
                userHasVotes={userVoteData ? (userVoteData.counts.votes > 0 || userVoteData.counts.quickfireVotes > 0) : false}
                onConfirm={async (userId: string, isBulk = false) => {
                    if (isBulk) {
                        const userIds = Array.from(selectedUsers);
                        try {
                            for (const id of userIds) {
                                await handleDeleteUser(id);
                            }
                            setSelectedUsers(new Set());
                            setBulkActionBlocked(null);
                        } catch (error) {
                            if (error instanceof ConvexError) {
                                const errorData = error.data as { message: string };
                                setError(errorData.message);
                            } else {
                                setError(error instanceof Error ? error.message : 'Failed to delete users');
                            }
                        }
                    } else {
                        await handleDeleteUser(userId);
                    }
                }}
            />

            {/* Team Lead Actions Popup */}
            <TeamLeadActionsPopup
                showTeamLeadActions={modals.showTeamLeadActions}
                availableTeamLeads={modals.showTeamLeadActions ? getAvailableTeamLeads(allUsers, modals.showTeamLeadActions.teamId, modals.showTeamLeadActions.userId) : []}
                userHasVotes={userContentData ? (userContentData.counts.votes > 0 || userContentData.counts.quickfireVotes > 0) : false}
                onCancel={() => {
                    closeModal('showTeamLeadActions');
                    setContentTransferIntent(null); // Clear intent on cancel
                }}
                onConfirmTransfer={async (newTeamLeadId) => {
                    if (modals.showTeamLeadActions) {
                        await handlePopupTransferConfirm(modals.showTeamLeadActions.userId, newTeamLeadId);
                        setContentTransferIntent(null); // Clear intent after transfer
                    }
                }}
                onConfirmDelete={async (userId) => {
                    await handleDeleteUser(userId);
                    closeModal('showTeamLeadActions');
                    setContentTransferIntent(null); // Clear intent after delete
                }}
            />

            {/* Add User Popup */}
            <AddUserPopup
                showAddUser={modals.showAddUser}
                teams={groupedUsers.teams}
                onCancel={() => closeModal('showAddUser')}
                onAddUser={handleAddUser}
            />
            
            {/* Idea Stats Popup */}
            <IdeaStatsPopup
                showIdeaStats={modals.showIdeaStats}
                onClose={() => closeModal('showIdeaStats')}
            />
        </div>
    );
}

export default function UserManagement() {
    return (
        <AdminErrorBoundary 
            fallbackTitle="User Management Error"
            fallbackMessage="There was an error loading the user management interface."
        >
            <UserManagementCore />
        </AdminErrorBoundary>
    );
}
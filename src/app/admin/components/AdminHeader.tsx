"use client";

import { useState, useEffect } from 'react';
import { useTheme } from 'next-themes';
import { Button } from "@/components/ui/button";
import { SidebarTrigger } from "@/components/ui/sidebar";
import { Sun, Moon } from "lucide-react";

interface AdminHeaderProps {
  activeSection: string;
}

export default function AdminHeader({ activeSection }: AdminHeaderProps) {
  const { theme, setTheme, resolvedTheme } = useTheme();
  const [mounted, setMounted] = useState(false);

  // Navigation items for section titles
  const navigationItems = [
    { id: 'events', label: 'Events' },
    { id: 'sessions', label: 'Sessions' },
    { id: 'teams', label: 'Teams' },
    { id: 'users', label: 'Users' },
    { id: 'ideas', label: 'Ideas' },
    { id: 'quickfire', label: 'Quickfire' },
    { id: 'voting', label: 'Voting' },
    { id: 'sparks', label: 'Sparks' },
    { id: 'snippets', label: 'Snippets' },
    { id: 'data', label: 'Data' },
    { id: 'backups', label: 'Backups' },
    { id: 'online', label: 'Online Users' },
    { id: 'admins', label: 'Admins' },
    { id: 'approvals', label: 'Approvals' },
    { id: 'settings', label: 'Settings' },
  ];

  useEffect(() => {
    setMounted(true);
  }, []);

  const toggleTheme = () => {
    setTheme(theme === 'dark' ? 'light' : 'dark');
  };

  const getSectionTitle = (section: string) => {
    const item = navigationItems.find(item => item.id === section);
    return item ? `${item.label} Management` : '';
  };

  return (
    <header className="h-20 bg-background border-b border-border p-4 flex-shrink-0 flex items-center">
      <div className="w-full flex items-center justify-between">
        <div className="flex items-center gap-4">
          <SidebarTrigger className="size-9 text-foreground hover:text-accent hover:!bg-transparent" />
          <div className="flex flex-col min-w-0">
            <h1 className="text-sm md:text-base font-bold text-foreground truncate">Admin Panel</h1>
            <h2 className="text-xs md:text-sm text-accent truncate">{getSectionTitle(activeSection)}</h2>
          </div>
        </div>
        
        <Button
          onClick={toggleTheme}
          variant="ghost"
          size="icon"
          className="rounded-md hover:bg-accent/10 hover:text-accent"
          title={mounted && resolvedTheme === 'dark' ? 'Switch to Light Mode' : 'Switch to Dark Mode'}
        >
          {mounted && resolvedTheme === 'light' ? (
            <Moon className="h-5 w-5" />
          ) : (
            <Sun className="h-5 w-5" />
          )}
        </Button>
      </div>
    </header>
  );
}
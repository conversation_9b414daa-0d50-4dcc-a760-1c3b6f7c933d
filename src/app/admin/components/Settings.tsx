"use client";

import { useState } from 'react';
import { useQuery, useMutation } from "convex/react";
import { useSession } from 'next-auth/react';
import { api } from "@/../convex/_generated/api";
import { Checkbox } from '@/components/ui/checkbox';
import { debugError } from '@/lib/utils';
import { StateCard } from '@/components/ui/state-card';
import AdminErrorBoundary from './AdminErrorBoundary';
import { Label } from '@/components/ui/label';

function SettingsCore() {
    const { data: sessionData } = useSession();
    
    // Real-time queries from Convex
    const autoApproval = useQuery(api.settings.getAutoApprovalSetting);
    const teamSelection = useQuery(api.settings.getTeamSelectionSetting);
    const onlineUsersEnabled = useQuery(api.settings.getOnlineUsersSetting);
    
    // Mutations for updating settings
    const setAutoApprovalMutation = useMutation(api.settings.setAutoApprovalSetting);
    const setTeamSelectionMutation = useMutation(api.settings.setTeamSelectionSetting);
    const setOnlineUsersMutation = useMutation(api.settings.setOnlineUsersSetting);
    
    const [isUpdating, setIsUpdating] = useState(false);

    const handleAutoApprovalChange = async (checked: boolean) => {
        try {
            setIsUpdating(true);
            await setAutoApprovalMutation({ 
                username: sessionData?.user?.username || '',
                enabled: checked 
            });
            // No need to update local state - Convex will handle real-time updates
        } catch (error) {
            debugError('Error updating auto-approval setting:', error);
        } finally {
            setIsUpdating(false);
        }
    };

    const handleTeamSelectionChange = async (enabled: boolean) => {
        try {
            setIsUpdating(true);
            await setTeamSelectionMutation({ 
                username: sessionData?.user?.username || '',
                enabled 
            });
            // No need to update local state - Convex will handle real-time updates
        } catch (error) {
            debugError('Error updating team selection setting:', error);
        } finally {
            setIsUpdating(false);
        }
    };

    const handleOnlineUsersChange = async (checked: boolean) => {
        try {
            setIsUpdating(true);
            await setOnlineUsersMutation({ 
                username: sessionData?.user?.username || '',
                enabled: checked 
            });
            // No need to update local state - Convex will handle real-time updates
        } catch (error) {
            debugError('Error updating online users setting:', error);
        } finally {
            setIsUpdating(false);
        }
    };

    // Show loading state while queries are loading
    if (autoApproval === undefined || teamSelection === undefined || onlineUsersEnabled === undefined) {
        return (
            <div className="flex justify-center items-center min-h-[400px]">
                <StateCard state="loading" title="Loading settings..." />
            </div>
        );
    }

    return (
        <div className="space-y-6">
            <h2 className="text-2xl font-bold text-primary">Settings</h2>
            
            <div className="space-y-6">
                <div className={`space-y-3 p-4 border border-border rounded-md ${!autoApproval ? 'opacity-50' : ''}`}>
                    <div className="flex items-center space-x-2">
                        <Checkbox
                            id="auto-approval"
                            checked={autoApproval}
                            onCheckedChange={handleAutoApprovalChange}
                            disabled={isUpdating}
                        />
                        <Label 
                            htmlFor="auto-approval" 
                            
                        >
                            Auto approval mode
                        </Label>
                    </div>
                    <p className="text-sm text-muted-foreground">
                        When enabled, new user registrations will be automatically approved without admin review.
                    </p>
                </div>
                
                <div className={`space-y-3 p-4 border border-border rounded-md ${!teamSelection ? 'opacity-50' : ''}`}>
                    <div className="flex items-center space-x-2">
                        <Checkbox
                            id="team-selection"
                            checked={teamSelection}
                            onCheckedChange={handleTeamSelectionChange}
                            disabled={isUpdating}
                        />
                        <Label 
                            htmlFor="team-selection" 
                            
                        >
                            Team selection enabled
                        </Label>
                    </div>
                    <p className="text-sm text-muted-foreground">
                        When enabled, users can select teams during registration. When disabled, users are assigned to no team group.
                    </p>
                </div>
                
                <div className={`space-y-3 p-4 border border-border rounded-md ${!onlineUsersEnabled ? 'opacity-50' : ''}`}>
                    <div className="flex items-center space-x-2">
                        <Checkbox
                            id="online-users"
                            checked={onlineUsersEnabled}
                            onCheckedChange={handleOnlineUsersChange}
                            disabled={isUpdating}
                        />
                        <Label 
                            htmlFor="online-users" 
                            
                        >
                            Enable Online Users Tracking
                        </Label>
                    </div>
                    <p className="text-sm text-muted-foreground">
                        When enabled, tracks and displays online users in real-time. Disable to reduce database overhead and improve performance.
                    </p>
                </div>
            </div>
        </div>
    );
}

export default function Settings() {
    return (
        <AdminErrorBoundary 
            fallbackTitle="Settings Error"
            fallbackMessage="There was an error loading the settings interface."
        >
            <SettingsCore />
        </AdminErrorBoundary>
    );
}
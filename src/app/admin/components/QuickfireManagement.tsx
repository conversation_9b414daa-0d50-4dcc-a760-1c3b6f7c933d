"use client";

import { useState, useC<PERSON>back, useMemo, useEffect, useRef } from 'react';
import { useQuery, useMutation } from "convex/react";
import { api } from "@/../convex/_generated/api";
import { Id } from "@/../convex/_generated/dataModel";
import { useSession } from 'next-auth/react';
import { debugError } from '@/lib/utils';
import { toast } from 'sonner';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { StateCard } from '@/components/ui/state-card';
import AdminErrorBoundary from './AdminErrorBoundary';
import { 
    Select, 
    SelectContent, 
    SelectItem, 
    SelectTrigger, 
    SelectValue 
} from '@/components/ui/select';
import {
    AlertDialog,
    AlertDialogAction,
    AlertDialogCancel,
    AlertDialogContent,
    AlertDialogDescription,
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>er,
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    AlertDialog<PERSON><PERSON>le,
} from '@/components/ui/alert-dialog';
import { Trash2, Edit, RotateCcw, Play, Pause, ChevronUp, ChevronDown } from 'lucide-react';
import NoActiveEventMessage from './ideas/components/NoActiveEventMessage';
import { Label } from '@/components/ui/label';

interface QuickfireFormData {
    idea: string;
    comments?: string;
    question?: string;
}

interface Session {
    _id: Id<"sessions">;
    name: string;
    type: string;
    eventId: Id<"events">;
    active: boolean;
}

interface QuickfireItem {
    _id: Id<"quickfires">;
    idea: string;
    comments?: string;
    question?: string;
    sessionId: Id<"sessions">;
    votingActive: boolean;
    order?: number;
    createdAt: number;
    updatedAt?: number;
}

function QuickfireManagementCore() {
    const { data: sessionData } = useSession();
    
    // Real-time data from Convex - automatically updates
    const activeEvent = useQuery(api.events.getActiveEvent);
    const quickfireSessions = useQuery(api.quickfire.getQuickfireSessions);
    const [selectedSession, setSelectedSession] = useState<string>('');

    // Get session data based on selection
    const sessionId = selectedSession === '' 
        ? undefined 
        : selectedSession as Id<"sessions">;
    
    const quickfireData = useQuery(api.quickfire.getQuickfireItemsBySession, 
        sessionId ? { sessionId } : "skip"
    );

    // UI state only - no complex data management needed
    const [formData, setFormData] = useState<QuickfireFormData>({
        idea: '',
        comments: '',
        question: ''
    });
    const [submitting, setSubmitting] = useState(false);
    const [error, setError] = useState<string | null>(null);
    const [editingItem, setEditingItem] = useState<string | null>(null);
    const [showDeleteConfirm, setShowDeleteConfirm] = useState<string | null>(null);
    const [showSessionModal, setShowSessionModal] = useState<string | null>(null);
    const [selectedSessionId, setSelectedSessionId] = useState<string>('');
    const [changingSession, setChangingSession] = useState(false);
    const [flashingItemId, setFlashingItemId] = useState<string | null>(null);
    const [localItems, setLocalItems] = useState<QuickfireItem[]>([]);

    // Extract items and error from the data with memoization
    const serverItems = useMemo(() => quickfireData?.items || [], [quickfireData?.items]);
    const quickfireError = quickfireData?.error || null;
    
    // Use local items for current session (includes empty arrays)
    const quickfireItems = useMemo(() => {
        return localItems;
    }, [localItems]);
    
    // Clear local items when session changes to prevent showing stale data
    useEffect(() => {
        setLocalItems([]);
    }, [selectedSession]);
    
    // Sync local items with server items when server data changes
    useEffect(() => {
        // Always sync local items with server items, including when empty
        setLocalItems(serverItems);
    }, [serverItems]);
    
    // Convex mutations - automatic optimistic updates
    const createQuickfire = useMutation(api.quickfire.createQuickfireItem);
    const updateQuickfire = useMutation(api.quickfire.updateQuickfireItem);
    const deleteQuickfire = useMutation(api.quickfire.deleteQuickfireItem);
    const toggleVoting = useMutation(api.quickfire.toggleQuickfireVoting);
    const reorderItems = useMutation(api.quickfire.reorderQuickfireItems);
    const resetOrder = useMutation(api.quickfire.resetOrderByCreatedAt);
    
    // Debounced reorder function
    const debounceRef = useRef<NodeJS.Timeout | null>(null);
    const debouncedReorder = useCallback(async (itemId: string, newOrder: number) => {
        if (debounceRef.current) {
            clearTimeout(debounceRef.current);
        }
        
        debounceRef.current = setTimeout(async () => {
            try {
                await reorderItems({
                    username: sessionData?.user?.username || '',
                    sessionId: selectedSession as Id<"sessions">,
                    itemId: itemId as Id<"quickfires">,
                    newOrder: newOrder
                });
            } catch (error) {
                setError(error instanceof Error ? error.message : 'Failed to reorder items');
                debugError('Error reordering items:', error);
            }
        }, 300);
    }, [reorderItems, selectedSession, sessionData?.user?.username]);

    // Cleanup debounce timeout on component unmount
    useEffect(() => {
        return () => {
            if (debounceRef.current) {
                clearTimeout(debounceRef.current);
            }
        };
    }, []);

    // Session options for dropdown
    const sessionOptions = useMemo(() => {
        if (!quickfireSessions) return [];
        
        const options: { value: string; label: string }[] = [];
        
        // Add individual Quickfire sessions
        quickfireSessions.forEach((session: Session) => {
            options.push({
                value: session._id,
                label: session.name
            });
        });
        
        return options;
    }, [quickfireSessions]);

    // Get selected session object
    const selectedSessionObj = useMemo(() => {
        if (!quickfireSessions || selectedSession === '') {
            return null;
        }
        return quickfireSessions.find((s: Session) => s._id === selectedSession);
    }, [quickfireSessions, selectedSession]);

    // Set active session as default when sessions load
    useEffect(() => {
        const activeQuickfireSession = quickfireSessions?.find((s: Session) => s.active);
        if (activeQuickfireSession && selectedSession === '') {
            setSelectedSession(activeQuickfireSession._id);
        } else if (quickfireSessions && quickfireSessions.length > 0 && selectedSession === '' && !activeQuickfireSession) {
            // If no active session, default to first Quickfire session
            setSelectedSession(quickfireSessions[0]._id);
        }
    }, [quickfireSessions, selectedSession]);

    // Check if current session is active (for button enabling/disabling)
    const isCurrentSessionActive = selectedSessionObj?.active || false;

    const truncateText = (text: string) => text.length > 68 ? `${text.slice(0, 68)}...` : text;

    const handleSubmit = useCallback(async (e: React.FormEvent) => {
        e.preventDefault();
        setError(null);
        setSubmitting(true);
        
        try {
            if (editingItem) {
                await updateQuickfire({
                    username: sessionData?.user?.username || '',
                    quickfireId: editingItem as Id<"quickfires">,
                    idea: formData.idea,
                    comments: formData.comments,
                    question: formData.question
                });
                toast.success('Quickfire item updated successfully!');
                setEditingItem(null);
            } else {
                await createQuickfire({
                    username: sessionData?.user?.username || '',
                    idea: formData.idea,
                    comments: formData.comments,
                    question: formData.question,
                    sessionId: selectedSession ? selectedSession as Id<"sessions"> : undefined
                });
                toast.success('Quickfire item added successfully!');
            }
            
            setFormData({
                idea: '',
                comments: '',
                question: ''
            });
        } catch (error) {
            setError(error instanceof Error ? error.message : 'Failed to save quickfire item');
            debugError('Error saving quickfire item:', error);
        } finally {
            setSubmitting(false);
        }
    }, [formData, editingItem, createQuickfire, updateQuickfire, selectedSession, sessionData?.user?.username]);

    const handleEdit = useCallback((itemId: string) => {
        const item = quickfireItems?.find((i: QuickfireItem) => i._id === itemId);
        if (item) {
            setFormData({
                idea: item.idea,
                comments: item.comments || '',
                question: item.question || ''
            });
            setEditingItem(itemId);
            
            // Scroll to the top of the page
            window.scrollTo({
                top: 0,
                behavior: 'smooth'
            });
        }
    }, [quickfireItems]);

    const handleDelete = useCallback(async (itemId: string) => {
        try {
            await deleteQuickfire({ 
                username: sessionData?.user?.username || '',
                quickfireId: itemId as Id<"quickfires"> 
            });
            toast.success('Quickfire item deleted successfully!');
        } catch (error) {
            setError(error instanceof Error ? error.message : 'Failed to delete quickfire item');
            debugError('Error deleting quickfire item:', error);
        } finally {
            setShowDeleteConfirm(null);
        }
    }, [deleteQuickfire, sessionData?.user?.username]);

    const handleChangeSession = useCallback((itemId: string) => {
        const item = quickfireItems?.find((i: QuickfireItem) => i._id === itemId);
        if (item) {
            setShowSessionModal(itemId);
            setSelectedSessionId(item.sessionId);
        }
    }, [quickfireItems]);

    const handleSessionUpdate = useCallback(async () => {
        if (!showSessionModal || !selectedSessionId) return;
        
        setError(null);
        setChangingSession(true);
        
        try {
            const item = quickfireItems?.find((i: QuickfireItem) => i._id === showSessionModal);
            if (!item) throw new Error('Item not found');
            
            await updateQuickfire({
                username: sessionData?.user?.username || '',
                quickfireId: showSessionModal as Id<"quickfires">,
                idea: item.idea,
                comments: item.comments || '',
                question: item.question || '',
                sessionId: selectedSessionId as Id<"sessions">
            });

            toast.success('Session updated successfully!');
            setShowSessionModal(null);
        } catch (error) {
            setError(error instanceof Error ? error.message : 'Failed to update session');
            debugError('Error updating session:', error);
        } finally {
            setChangingSession(false);
        }
    }, [showSessionModal, selectedSessionId, quickfireItems, updateQuickfire, sessionData?.user?.username]);

    const handleToggleVoting = useCallback(async (itemId: string, currentStatus: boolean) => {
        try {
            setError(null);
            
            await toggleVoting({
                username: sessionData?.user?.username || '',
                quickfireId: itemId as Id<"quickfires">,
                votingActive: !currentStatus
            });
        } catch (error) {
            setError(error instanceof Error ? error.message : 'Failed to toggle voting status');
            debugError('Error toggling voting status:', error);
        }
    }, [toggleVoting, sessionData?.user?.username]);

    const handleMoveUp = useCallback(async (itemId: string, currentIndex: number) => {
        if (currentIndex === 0) return;
        
        setError(null);
        
        // Immediately update local UI
        const newItems = [...localItems];
        const [movedItem] = newItems.splice(currentIndex, 1);
        newItems.splice(currentIndex - 1, 0, movedItem);
        setLocalItems(newItems);
        
        // Trigger flash effect
        setFlashingItemId(itemId);
        setTimeout(() => setFlashingItemId(null), 1000);
        
        // Debounced server update
        debouncedReorder(itemId, currentIndex - 1);
    }, [debouncedReorder, localItems]);

    const handleMoveDown = useCallback(async (itemId: string, currentIndex: number) => {
        if (currentIndex >= localItems.length - 1) return;
        
        setError(null);
        
        // Immediately update local UI
        const newItems = [...localItems];
        const [movedItem] = newItems.splice(currentIndex, 1);
        newItems.splice(currentIndex + 1, 0, movedItem);
        setLocalItems(newItems);
        
        // Trigger flash effect
        setFlashingItemId(itemId);
        setTimeout(() => setFlashingItemId(null), 1000);
        
        // Debounced server update
        debouncedReorder(itemId, currentIndex + 1);
    }, [debouncedReorder, localItems]);

    const handleResetOrder = useCallback(async () => {
        try {
            setError(null);
            
            const result = await resetOrder({
                username: sessionData?.user?.username || '',
                sessionId: selectedSession as Id<"sessions">
            });
            
            // Clear local items to force a refresh from server data
            // This ensures the UI immediately reflects the server's new order
            setLocalItems([]);
            
            toast.success(result.data?.message || 'Order reset successfully!');
        } catch (error) {
            setError(error instanceof Error ? error.message : 'Failed to reset order');
            debugError('Error resetting order:', error);
        }
    }, [resetOrder, selectedSession, sessionData?.user?.username]);


    // Check if there's no active event first
    if (activeEvent === null) {
        return <NoActiveEventMessage />;
    }
    
    // Check if sessions data was cleared (empty array) but there's an active event
    if (Array.isArray(quickfireSessions) && quickfireSessions.length === 0) {
        return (
            <div className="flex justify-center items-center min-h-[400px]">
                <StateCard 
                    state="empty" 
                    title="No Quickfire Sessions Found" 
                    message="No quickfire sessions found for this event. Sessions need to be created first."
                />
            </div>
        );
    }

    // Don't render content if selectedSession is still empty or no sessions available
    if (selectedSession === '' || !quickfireSessions) {
        return (
            <div className="flex justify-center items-center min-h-[400px]">
                <StateCard state="loading" title="Loading..." />
            </div>
        );
    }

    // Error states - handle gracefully based on the error message
    if (quickfireError) {
        // Check if there's an active event to differentiate between no event vs no data
        if (activeEvent) {
            return (
                <div className="flex justify-center items-center min-h-[400px]">
                    <StateCard 
                        state="empty" 
                        title="No Quickfire Items Found" 
                        message="No quickfire items found for this session. Create your first quickfire item to get started."
                    />
                </div>
            );
        } else {
            return <NoActiveEventMessage />;
        }
    }

    return (
        <div className="space-y-6">
            {/* Header Section */}
            <div className="space-y-4">
                <div className="space-y-4 p-4 border border-border">
                    {/* Session Filter */}
                    <div className="w-full">
                        <Select value={selectedSession} onValueChange={setSelectedSession}>
                            <SelectTrigger className="w-full focus:ring-0 focus:ring-offset-0 min-h-[3.5rem]">
                                <SelectValue placeholder="Select Quickfire Session" />
                            </SelectTrigger>
                            <SelectContent>
                                {sessionOptions.map((option) => (
                                    <SelectItem key={option.value} value={option.value}>
                                        {option.label}
                                    </SelectItem>
                                ))}
                            </SelectContent>
                        </Select>
                    </div>

                    {/* Session Status */}
                    {!isCurrentSessionActive && (
                        <div className="w-full p-3 border border-destructive/30 bg-destructive/10 text-destructive text-sm font-medium">
                            Session is not active - Voting controls are disabled
                        </div>
                    )}
                </div>
            </div>

            {/* Form Section */}
            <div className="space-y-4">
                <h2 className="text-2xl font-bold text-primary">Quickfire Management</h2>
                
                {/* Error Message */}
                {error && (
                    <div className="text-destructive text-sm bg-destructive/10 border border-destructive/20 p-3">
                        {error}
                    </div>
                )}
                
                <form onSubmit={handleSubmit} className="space-y-6 p-4 border border-border">
                    <div className="space-y-2">
                        <Label htmlFor="idea">
                            Idea
                        </Label>
                        <Input
                            type="text"
                            id="idea"
                            value={formData.idea}
                            onChange={(e) => setFormData(prev => ({ ...prev, idea: e.target.value }))}
                            required
                            disabled={submitting}
                            className="shadow-none focus-visible:ring-0 focus-visible:ring-offset-0"
                        />
                    </div>
                    <div className="space-y-3">
                        <Label htmlFor="question">
                            Question (Optional)
                        </Label>
                        <Input
                            type="text"
                            id="question"
                            value={formData.question || ''}
                            onChange={(e) => setFormData(prev => ({ ...prev, question: e.target.value }))}
                            disabled={submitting}
                            className="shadow-none focus-visible:ring-0 focus-visible:ring-offset-0"
                        />
                    </div>
                    <div className="space-y-3">
                        <Label htmlFor="comments">
                            Comments (Optional)
                        </Label>
                        <Textarea
                            id="comments"
                            value={formData.comments || ''}
                            onChange={(e) => setFormData(prev => ({ ...prev, comments: e.target.value }))}
                            rows={4}
                            disabled={submitting}
                            className="shadow-none focus-visible:ring-0 focus-visible:ring-offset-0"
                        />
                    </div>
                    <div className="flex items-center gap-3 flex-wrap sm:flex-nowrap">
                        <Button 
                            type="submit" 
                            disabled={submitting}
                        >
                            {submitting ? 'Processing...' : editingItem ? 'Update Item' : 'Add Item'}
                        </Button>
                        {editingItem && (
                            <Button
                                type="button"
                                variant="secondary"
                                onClick={() => {
                                    setEditingItem(null);
                                    setFormData({
                                        idea: '',
                                        comments: '',
                                        question: ''
                                    });
                                }}
                            >
                                Cancel Edit
                            </Button>
                        )}
                    </div>
                </form>
            </div>

            {/* Quickfire Items List */}
            {quickfireItems && quickfireItems.length > 0 ? (
                <div className="space-y-4">
                    <div className="flex items-center justify-between">
                        <h2 className="text-2xl font-bold text-primary">Quickfire Items</h2>
                        <Button
                            variant="outline"
                            size="sm"
                            onClick={handleResetOrder}
                            className="gap-2"
                        >
                            Reset Order
                        </Button>
                    </div>
                    <div className="space-y-4">
                        {quickfireItems.map((item: QuickfireItem, index: number) => (
                            <div 
                                key={item._id} 
                                className={`flex flex-col sm:flex-row sm:items-start gap-4 p-4 border transition-colors duration-100 hover:bg-muted/50 ${
                                    flashingItemId === item._id 
                                        ? 'border-primary' 
                                        : 'border-border'
                                }`}
                            >
                                {/* Content Section */}
                                <div className="flex-1 min-w-0 space-y-2">
                                    <h4 className="text-primary font-medium text-lg break-words">{item.idea}</h4>
                                    {item.question && (
                                        <div className="text-sm">
                                            <div className="font-bold text-foreground">Question:</div>
                                            <div className="text-muted-foreground">{truncateText(item.question)}</div>
                                        </div>
                                    )}
                                    {item.comments && (
                                        <div className="text-sm">
                                            <div className="font-bold text-foreground">Comments:</div>
                                            <pre className="whitespace-pre-wrap font-sans text-muted-foreground">{truncateText(item.comments)}</pre>
                                        </div>
                                    )}
                                </div>

                                {/* Action Buttons */}
                                <div className="flex items-center gap-3 flex-wrap sm:flex-nowrap">
                                    <Button
                                        variant="ghost"
                                        size="sm"
                                        onClick={() => handleMoveUp(item._id, index)}
                                        className="shrink-0 p-0 hover:!bg-transparent hover:text-primary"
                                        title="Move up"
                                        disabled={index === 0}
                                    >
                                        <ChevronUp />
                                    </Button>
                                    <Button
                                        variant="ghost"
                                        size="sm"
                                        onClick={() => handleMoveDown(item._id, index)}
                                        className="shrink-0 p-0 hover:!bg-transparent hover:text-primary"
                                        title="Move down"
                                        disabled={index === quickfireItems.length - 1}
                                    >
                                        <ChevronDown />
                                    </Button>
                                    <Button
                                        variant="ghost"
                                        size="sm"
                                        onClick={() => handleEdit(item._id)}
                                        className="shrink-0 p-0 hover:!bg-transparent hover:text-primary"
                                        title="Edit item"
                                    >
                                        <Edit />
                                    </Button>
                                    <Button
                                        variant="ghost"
                                        size="sm"
                                        onClick={() => handleChangeSession(item._id)}
                                        className="shrink-0 p-0 hover:!bg-transparent hover:text-primary"
                                        title={item.votingActive ? "Cannot change session while voting is active" : "Change Session"}
                                        disabled={item.votingActive}
                                    >
                                        <RotateCcw />
                                    </Button>
                                    <Button
                                        variant="ghost"
                                        size="sm"
                                        onClick={() => setShowDeleteConfirm(item._id)}
                                        className="shrink-0 p-0 hover:!bg-transparent hover:text-primary"
                                        title={item.votingActive ? "Cannot delete while voting is active" : "Delete item"}
                                        disabled={item.votingActive}
                                    >
                                        <Trash2 />
                                    </Button>
                                    <Button
                                        variant="ghost"
                                        size="sm"
                                        onClick={() => handleToggleVoting(item._id, !!item.votingActive)}
                                        className={`shrink-0 p-0 hover:!bg-transparent ${
                                            item.votingActive 
                                                ? 'text-destructive hover:text-destructive' 
                                                : 'text-muted-foreground hover:text-primary'
                                        }`}
                                        title={item.votingActive ? 'Deactivate Voting' : 'Activate Voting'}
                                        disabled={!isCurrentSessionActive}
                                    >
                                        {item.votingActive ? <Pause /> : <Play />}
                                    </Button>
                                </div>
                            </div>
                        ))}
                    </div>
                </div>
            ) : (
                quickfireData !== undefined && selectedSession && (
                    <div className="flex justify-center items-center min-h-[200px]">
                        <StateCard 
                            state="empty" 
                            title="No Quickfire Items Found" 
                            message="No quickfire items found for this session. Create your first quickfire item to get started."
                        />
                    </div>
                )
            )}

            {/* Change Session Modal */}
            <AlertDialog open={!!showSessionModal} onOpenChange={(open) => !open && setShowSessionModal(null)}>
                <AlertDialogContent>
                    <AlertDialogHeader>
                        <AlertDialogTitle>Change Session</AlertDialogTitle>
                        <AlertDialogDescription>
                            Select a new session for this quickfire item:
                        </AlertDialogDescription>
                    </AlertDialogHeader>
                    <div className="py-4">
                        <div className="space-y-2">
                            <Label>Session:</Label>
                            <Select 
                                value={selectedSessionId} 
                                onValueChange={setSelectedSessionId}
                                disabled={changingSession}
                            >
                                <SelectTrigger>
                                    <SelectValue placeholder="Select a session..." />
                                </SelectTrigger>
                                <SelectContent>
                                    {quickfireSessions?.filter((session: Session) => session.type === 'Quickfire')
                                        .map((session: Session) => (
                                            <SelectItem key={session._id} value={session._id}>
                                                {session.name}
                                            </SelectItem>
                                        ))
                                    }
                                </SelectContent>
                            </Select>
                        </div>
                    </div>
                    <AlertDialogFooter>
                        <AlertDialogCancel 
                            disabled={changingSession}
                        >
                            Cancel
                        </AlertDialogCancel>
                        <AlertDialogAction
                            onClick={handleSessionUpdate}
                            disabled={changingSession}
                        >
                            {changingSession ? 'Saving...' : 'Save'}
                        </AlertDialogAction>
                    </AlertDialogFooter>
                </AlertDialogContent>
            </AlertDialog>

            {/* Delete Confirmation Modal */}
            <AlertDialog open={!!showDeleteConfirm} onOpenChange={(open) => !open && setShowDeleteConfirm(null)}>
                <AlertDialogContent>
                    <AlertDialogHeader>
                        <AlertDialogTitle>Delete Quickfire Item</AlertDialogTitle>
                        <AlertDialogDescription>
                            Are you sure you want to delete this quickfire item? This action cannot be undone.
                        </AlertDialogDescription>
                    </AlertDialogHeader>
                    <AlertDialogFooter>
                        <AlertDialogCancel>
                            Cancel
                        </AlertDialogCancel>
                        <AlertDialogAction
                            onClick={() => showDeleteConfirm && handleDelete(showDeleteConfirm)}
                            className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
                        >
                            Delete
                        </AlertDialogAction>
                    </AlertDialogFooter>
                </AlertDialogContent>
            </AlertDialog>
        </div>
    );
}

export default function QuickfireManagement() {
    return (
        <AdminErrorBoundary 
            fallbackTitle="Quickfire Management Error"
            fallbackMessage="There was an error loading the quickfire management interface."
        >
            <QuickfireManagementCore />
        </AdminErrorBoundary>
    );
}
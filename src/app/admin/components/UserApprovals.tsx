"use client";

import { useState } from 'react';
import Image from 'next/image';
import { useQuery, useMutation } from "convex/react";
import { ConvexError } from 'convex/values';
import { api } from "@/../convex/_generated/api";
import { Id } from "@/../convex/_generated/dataModel";
import { debugError } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import { StateCard } from '@/components/ui/state-card';
import AdminErrorBoundary from './AdminErrorBoundary';
import {
    AlertDialog,
    AlertDialogAction,
    AlertDialogCancel,
    AlertDialogContent,
    AlertDialogDescription,
    AlertDialogFooter,
    AlertDialogHeader,
    AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import { Trash2, UserCheck, UserX } from 'lucide-react';

function UserApprovalsCore() {
    // Use Convex queries for real-time pending and rejected users
    const pendingUsers = useQuery(api.users.getUsersByStatus, { status: "pending" });
    const rejectedUsers = useQuery(api.users.getUsersByStatus, { status: "rejected" });
    
    // Convex mutations for real-time updates
    const updateUserStatus = useMutation(api.users.updateUserStatus);
    const deleteUser = useMutation(api.users.deleteUser);
    
    const [error, setError] = useState<string | null>(null);
    const [bulkLoading, setBulkLoading] = useState(false);
    const [processedCount, setProcessedCount] = useState(0);
    const [totalToProcess, setTotalToProcess] = useState(0);
    
    // Confirmation dialog states
    const [showBulkApprovalConfirm, setShowBulkApprovalConfirm] = useState(false);
    const [showBulkRejectionConfirm, setShowBulkRejectionConfirm] = useState(false);
    const [showDeleteConfirm, setShowDeleteConfirm] = useState<string | null>(null);
    const [isDeleting, setIsDeleting] = useState(false);

    // Real-time updates are handled automatically by Convex

    const handleDeleteUser = async (userId: string) => {
        setIsDeleting(true);
        try {
            await deleteUser({ userId: userId as Id<"users"> });
            setShowDeleteConfirm(null);
            setError(null);
        } catch (error) {
            if (error instanceof ConvexError) {
                const errorData = error.data as { message: string };
                setError(errorData.message);
            } else {
                setError(error instanceof Error ? error.message : 'Failed to delete user');
            }
            debugError('Error deleting user:', error);
        } finally {
            setIsDeleting(false);
        }
    };

    const handleStatusUpdate = async (userId: string, status: 'approved' | 'rejected') => {
        try {
            await updateUserStatus({ 
                userId: userId as Id<"users">, 
                status: status 
            });
            setError(null);
        } catch (error) {
            if (error instanceof ConvexError) {
                const errorData = error.data as { message: string };
                setError(errorData.message);
            } else {
                setError(error instanceof Error ? error.message : 'Failed to update user status');
            }
            debugError('Error updating user status:', error);
        }
    };

    const handleBulkStatusUpdate = async (status: 'approved' | 'rejected') => {
        const pendingOnly = pendingUsers?.filter(user => user.status === 'pending') || [];
        setBulkLoading(true);
        setError(null);
        setProcessedCount(0);
        setTotalToProcess(pendingOnly.length);
        
        try {
            let failedCount = 0;
            
            // Process users sequentially using Convex mutations
            for (const user of pendingOnly) {
                try {
                    await updateUserStatus({ 
                        userId: user._id as Id<"users">, 
                        status: status 
                    });
                    
                    setProcessedCount(prev => prev + 1);
                } catch (error) {
                    failedCount++;
                    setProcessedCount(prev => prev + 1);
                    debugError('Error updating user status:', error);
                }
            }

            if (failedCount > 0) {
                setError(`Failed to update ${failedCount} user${failedCount > 1 ? 's' : ''}`);
            } else {
                setError(null);
            }
        } catch (error) {
            if (error instanceof ConvexError) {
                const errorData = error.data as { message: string };
                setError(errorData.message);
            } else {
                setError(error instanceof Error ? error.message : 'Failed to update user statuses');
            }
            debugError('Error updating user statuses:', error);
        } finally {
            setBulkLoading(false);
            setProcessedCount(0);
            setTotalToProcess(0);
            setShowBulkApprovalConfirm(false);
            setShowBulkRejectionConfirm(false);
        }
    };

    // Confirmation handlers
    const handleBulkApproval = () => setShowBulkApprovalConfirm(true);
    const handleBulkRejection = () => setShowBulkRejectionConfirm(true);
    const handleDeleteClick = (userId: string) => setShowDeleteConfirm(userId);

    const confirmBulkApproval = () => handleBulkStatusUpdate('approved');
    const confirmBulkRejection = () => handleBulkStatusUpdate('rejected');
    const confirmDelete = () => {
        if (showDeleteConfirm) {
            handleDeleteUser(showDeleteConfirm);
        }
    };

    if (pendingUsers === undefined || rejectedUsers === undefined) {
        return (
            <div className="flex justify-center items-center min-h-[400px]">
                <StateCard state="loading" title="Loading user approvals..." />
            </div>
        );
    }

    // Combine pending and rejected users
    const allUsers = [...(pendingUsers || []), ...(rejectedUsers || [])];

    if (allUsers.length === 0) {
        return (
            <div className="flex justify-center items-center min-h-[400px]">
                <StateCard state="empty" title="No Pending Registrations" message="No pending or rejected registrations found." />
            </div>
        );
    }

    return (
        <div className="space-y-6">
            {/* Bulk Actions */}
            {pendingUsers && pendingUsers.length > 0 && (
                <div className="flex gap-4">
                    <Button
                        onClick={handleBulkApproval}
                        disabled={bulkLoading}
                    >
                        <UserCheck className="h-4 w-4 mr-2" />
                        {bulkLoading ? `Processing ${processedCount}/${totalToProcess}...` : `Approve All (${pendingUsers.length})`}
                    </Button>
                    <Button
                        onClick={handleBulkRejection}
                        disabled={bulkLoading}
                        variant="outline"
                        
                    >
                        <UserX className="h-4 w-4 mr-2" />
                        {bulkLoading ? `Processing ${processedCount}/${totalToProcess}...` : `Reject All (${pendingUsers.length})`}
                    </Button>
                </div>
            )}

            {/* Progress Bar */}
            {bulkLoading && (
                <div className="space-y-2">
                    <div className="w-full bg-muted rounded-full h-2">
                        <div
                            className="bg-primary h-2 rounded-full transition-all duration-300"
                            style={{
                                width: `${(processedCount / totalToProcess) * 100}%`
                            }}
                        />
                    </div>
                    <div className="text-sm text-muted-foreground text-center">
                        Processing {processedCount} of {totalToProcess} users...
                    </div>
                </div>
            )}

            {/* Error Message */}
            {error && (
                <div className="error-display">
                    {error}
                </div>
            )}

            {/* Users List */}
            <div className="space-y-4">
                {allUsers.sort((a, b) => {
                    // Sort by status (pending first, then rejected)
                    if (a.status === b.status) return 0;
                    return a.status === 'pending' ? -1 : 1;
                }).map((user) => (
                    <div 
                        key={user._id}
                        className={`admin-card ${
                            user.status === 'rejected' ? 'bg-destructive/5 border-destructive/20' : ''
                        }`}
                    >
                        {/* User Info Section */}
                        <div className="flex-1 min-w-0">
                            <div className="flex items-center gap-4 mb-3">
                                <div className="flex items-center gap-2">
                                    <Image
                                        src={user.role === 'teamLead' ? '/svgs/Asset 3.svg' : '/svgs/Asset 2.svg'}
                                        alt={user.role === 'teamLead' ? 'Facilitator Icon' : 'Member Icon'}
                                        width={32}
                                        height={32}
                                        className="shrink-0"
                                    />
                                    <div>
                                        <h3 className="font-semibold text-foreground">{user.name}</h3>
                                        <div className={`text-xs px-2 py-1 rounded-md inline-block ${
                                            user.status === 'rejected' 
                                                ? 'bg-destructive/10 text-destructive' 
                                                : 'bg-yellow-100 text-yellow-800'
                                        }`}>
                                            {user.status === 'rejected' ? 'Rejected' : 'Pending'}
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <div className="grid grid-cols-1 sm:grid-cols-3 gap-2 text-sm">
                                <div className="flex justify-between sm:block">
                                    <span className="text-muted-foreground">Username:</span>
                                    <span className="font-medium">{user.username}</span>
                                </div>
                                <div className="flex justify-between sm:block">
                                    <span className="text-muted-foreground">Role:</span>
                                    <span className="font-medium">{user.role === 'teamLead' ? 'Facilitator' : 'Member'}</span>
                                </div>
                                <div className="flex justify-between sm:block">
                                    <span className="text-muted-foreground">Registered:</span>
                                    <span className="font-medium">{new Date(user.createdAt).toLocaleDateString()}</span>
                                </div>
                            </div>
                        </div>

                        {/* Action Buttons */}
                        <div className="flex items-center gap-3 flex-wrap sm:flex-nowrap">
                            {user.status === 'pending' ? (
                                <>
                                    <Button
                                        onClick={() => handleStatusUpdate(user._id, 'approved')}
                                                        size="sm"
                                    >
                                        <UserCheck className="h-4 w-4 mr-1" />
                                        Approve
                                    </Button>
                                    <Button
                                        onClick={() => handleStatusUpdate(user._id, 'rejected')}
                                        variant="outline"
                                        
                                        size="sm"
                                    >
                                        <UserX className="h-4 w-4 mr-1" />
                                        Reject
                                    </Button>
                                </>
                            ) : (
                                <>
                                    <Button
                                        onClick={() => handleStatusUpdate(user._id, 'approved')}
                                                        size="sm"
                                    >
                                        <UserCheck className="h-4 w-4 mr-1" />
                                        Approve
                                    </Button>
                                    <Button
                                        onClick={() => handleDeleteClick(user._id)}
                                        variant="outline"
                                        
                                        size="sm"
                                    >
                                        <Trash2 className="h-4 w-4 mr-1" />
                                        Delete
                                    </Button>
                                </>
                            )}
                        </div>
                    </div>
                ))}
            </div>

            {/* Bulk Approval Confirmation Dialog */}
            <AlertDialog open={showBulkApprovalConfirm} onOpenChange={setShowBulkApprovalConfirm}>
                <AlertDialogContent>
                    <AlertDialogHeader>
                        <AlertDialogTitle>Approve All Users</AlertDialogTitle>
                        <AlertDialogDescription>
                            Are you sure you want to approve all {pendingUsers?.length || 0} pending users? This action cannot be undone.
                        </AlertDialogDescription>
                    </AlertDialogHeader>
                    <AlertDialogFooter>
                        <AlertDialogCancel 
                            disabled={bulkLoading}
                        >
                            Cancel
                        </AlertDialogCancel>
                        <AlertDialogAction
                            onClick={confirmBulkApproval}
                            disabled={bulkLoading}
                            >
                            {bulkLoading ? 'Approving...' : 'Approve All'}
                        </AlertDialogAction>
                    </AlertDialogFooter>
                </AlertDialogContent>
            </AlertDialog>

            {/* Bulk Rejection Confirmation Dialog */}
            <AlertDialog open={showBulkRejectionConfirm} onOpenChange={setShowBulkRejectionConfirm}>
                <AlertDialogContent>
                    <AlertDialogHeader>
                        <AlertDialogTitle>Reject All Users</AlertDialogTitle>
                        <AlertDialogDescription>
                            Are you sure you want to reject all {pendingUsers?.length || 0} pending users? This action cannot be undone.
                        </AlertDialogDescription>
                    </AlertDialogHeader>
                    <AlertDialogFooter>
                        <AlertDialogCancel 
                            disabled={bulkLoading}
                        >
                            Cancel
                        </AlertDialogCancel>
                        <AlertDialogAction
                            onClick={confirmBulkRejection}
                            disabled={bulkLoading}
                            
                        >
                            {bulkLoading ? 'Rejecting...' : 'Reject All'}
                        </AlertDialogAction>
                    </AlertDialogFooter>
                </AlertDialogContent>
            </AlertDialog>

            {/* Delete Confirmation Dialog */}
            <AlertDialog open={!!showDeleteConfirm} onOpenChange={(open) => !open && setShowDeleteConfirm(null)}>
                <AlertDialogContent>
                    <AlertDialogHeader>
                        <AlertDialogTitle>Delete User</AlertDialogTitle>
                        <AlertDialogDescription>
                            Are you sure you want to permanently delete this user? This action cannot be undone.
                        </AlertDialogDescription>
                    </AlertDialogHeader>
                    <AlertDialogFooter>
                        <AlertDialogCancel 
                            disabled={isDeleting}
                        >
                            Cancel
                        </AlertDialogCancel>
                        <AlertDialogAction
                            onClick={confirmDelete}
                            disabled={isDeleting}
                        >
                            {isDeleting ? 'Deleting...' : 'Delete'}
                        </AlertDialogAction>
                    </AlertDialogFooter>
                </AlertDialogContent>
            </AlertDialog>
        </div>
    );
}

export default function UserApprovals() {
    return (
        <AdminErrorBoundary 
            fallbackTitle="User Approvals Error"
            fallbackMessage="There was an error loading the user approvals interface."
        >
            <UserApprovalsCore />
        </AdminErrorBoundary>
    );
}
"use client";

import { useState, useRef } from 'react';
import { useQuery, useMutation } from "convex/react";
import { api } from "@/../convex/_generated/api";
import { Id } from "@/../convex/_generated/dataModel";
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { StateCard } from '@/components/ui/state-card';
import {
    AlertDialog,
    AlertDialogAction,
    AlertDialogCancel,
    AlertDialogContent,
    AlertDialogDescription,
    AlertDialogFooter,
    AlertDialogHeader,
    AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import TiptapEditor, { TiptapEditorHandle } from '@/components/editors/TiptapEditor';
import TiptapContentDisplay from '@/components/editors/TiptapContentDisplay';
import { debug } from '@/lib/utils';
import { toast } from 'sonner';
import { Plus, Edit, Trash2 } from 'lucide-react';
import { Label } from '@/components/ui/label';

// Type for Convex snippet (matches schema)
type ConvexSnippet = {
    _id: Id<"snippets">;
    name: string;
    content: string;
    createdAt: number;
    updatedAt?: number;
};

export default function SnippetsManagement() {
    // Convex real-time data
    const snippets = useQuery(api.snippets.getAllSnippets);
    const createSnippet = useMutation(api.snippets.createSnippet);
    const updateSnippet = useMutation(api.snippets.updateSnippet);
    const deleteSnippet = useMutation(api.snippets.deleteSnippet);

    // UI state
    const [showAddModal, setShowAddModal] = useState(false);
    const [showEditModal, setShowEditModal] = useState<ConvexSnippet | null>(null);
    const [showDeleteConfirm, setShowDeleteConfirm] = useState<ConvexSnippet | null>(null);
    const [snippetName, setSnippetName] = useState('');
    const [submitting, setSubmitting] = useState(false);
    const [deleting, setDeleting] = useState(false);
    const [editorContent, setEditorContent] = useState('');
    
    // Ref for TiptapEditor
    const editorRef = useRef<TiptapEditorHandle>(null);

    const loading = snippets === undefined;
    const error = null; // Convex handles errors differently


    const handleAddSnippet = () => {
        setSnippetName('');
        setEditorContent('');
        setShowAddModal(true);
    };

    const handleEditSnippet = (snippet: ConvexSnippet) => {
        setSnippetName(snippet.name);
        setEditorContent(snippet.content);
        setShowEditModal(snippet);
    };

    const handleSaveSnippet = async () => {
        if (!snippetName.trim()) {
            toast.error('Snippet name is required.');
            return;
        }
        if (!editorContent.trim()) {
            toast.error('Snippet content cannot be empty.');
            return;
        }

        setSubmitting(true);
        try {
            await createSnippet({
                name: snippetName.trim(),
                content: editorContent,
            });
            
            toast.success(`Snippet "${snippetName.trim()}" created.`);
            setShowAddModal(false);
            setSnippetName('');
            setEditorContent('');
        } catch (err) {
            debug('Error creating snippet:', err);
            toast.error(err instanceof Error ? err.message : 'Failed to create snippet.');
        } finally {
            setSubmitting(false);
        }
    };

    const handleUpdateSnippet = async () => {
        if (!showEditModal) return;
        if (!snippetName.trim()) {
            toast.error('Snippet name is required.');
            return;
        }
        if (!editorContent.trim()) {
            toast.error('Snippet content cannot be empty.');
            return;
        }

        setSubmitting(true);
        try {
            await updateSnippet({
                snippetId: showEditModal._id,
                name: snippetName.trim(),
                content: editorContent,
            });
            
            toast.success(`Snippet "${snippetName.trim()}" updated.`);
            setShowEditModal(null);
            setSnippetName('');
            setEditorContent('');
        } catch (err) {
            debug('Error updating snippet:', err);
            toast.error(err instanceof Error ? err.message : 'Failed to update snippet.');
        } finally {
            setSubmitting(false);
        }
    };

    const handleDeleteSnippet = async () => {
        if (!showDeleteConfirm) return;

        setDeleting(true);
        try {
            await deleteSnippet({
                snippetId: showDeleteConfirm._id,
            });
            
            toast.success(`Snippet "${showDeleteConfirm.name}" deleted.`);
            setShowDeleteConfirm(null);
        } catch (err) {
            debug('Error deleting snippet:', err);
            toast.error(err instanceof Error ? err.message : 'Failed to delete snippet.');
        } finally {
            setDeleting(false);
        }
    };

    const closeModals = () => {
        setShowAddModal(false);
        setShowEditModal(null);
        setShowDeleteConfirm(null);
        setSnippetName('');
        setEditorContent('');
    };


    if (loading) {
        return (
            <div className="flex justify-center items-center min-h-[400px]">
                <StateCard state="loading" title="Loading snippets..." />
            </div>
        );
    }

    return (
        <div className="space-y-6">
            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
                <h2 className="text-2xl font-bold text-primary">Snippets Management</h2>
                <Button 
                    onClick={handleAddSnippet}
                    className="w-full sm:w-auto px-6 py-3 uppercase"
                >
                    <Plus className="mr-2" />
                    Add New Snippet
                </Button>
            </div>

            {error && (
                <div className="text-destructive text-sm bg-destructive/10 border border-destructive/20 p-3">
                    {error}
                </div>
            )}
            
            {snippets && snippets.length === 0 && !error && (
                <div className="flex justify-center items-center min-h-[400px]">
                    <StateCard 
                        state="empty" 
                        title="No Snippets Found" 
                        message="No snippets available. Create your first snippet to get started."
                    />
                </div>
            )}
            
            {snippets && snippets.length > 0 && (
                <div className="space-y-8">
                        {snippets.map((snippet) => (
                            <div key={snippet._id} className="flex flex-col gap-6 p-6 border-2 border-secondary hover:bg-muted/50 transition-colors">
                                <div className="flex flex-col-reverse sm:flex-row sm:items-start sm:justify-between gap-4">
                                    <h2 className="text-accent font-black text-lg break-words flex-1">{snippet.name}</h2>
                                    <div className="flex items-center gap-3 flex-shrink-0 justify-end">
                                        <Button
                                            variant="ghost"
                                            size="sm"
                                            onClick={() => handleEditSnippet(snippet)}
                                            className="shrink-0 p-0 hover:!bg-transparent hover:text-primary"
                                            title="Edit snippet"
                                        >
                                            <Edit />
                                        </Button>
                                        <Button
                                            variant="ghost"
                                            size="sm"
                                            onClick={() => setShowDeleteConfirm(snippet)}
                                            className="shrink-0 p-0 hover:!bg-transparent hover:text-primary"
                                            title="Delete snippet"
                                        >
                                            <Trash2 />
                                        </Button>
                                    </div>
                                </div>
                                {/* Render snippet content using the Tiptap function */}
                                <TiptapContentDisplay content={snippet.content} />
                                
                                <div className="text-xs text-muted-foreground border-t border-secondary pt-4 flex flex-col sm:flex-row sm:items-center gap-12">
                                    <div className="flex flex-col gap-1">
                                        <span className="font-medium text-accent uppercase">CREATED</span>
                                        <span className="text-white">{new Date(snippet.createdAt).toLocaleString()}</span>
                                    </div>
                                    {snippet.updatedAt && (
                                        <div className="flex flex-col gap-1">
                                            <span className="font-medium text-accent uppercase">UPDATED</span>
                                            <span className="text-white">{new Date(snippet.updatedAt).toLocaleString()}</span>
                                        </div>
                                    )}
                                </div>
                            </div>
                        ))}
                </div>
            )}

            {/* Add/Edit Snippet Modal */}
            <AlertDialog open={showAddModal || !!showEditModal} onOpenChange={(open) => !open && closeModals()}>
                <AlertDialogContent className="sm:max-w-4xl">
                    <AlertDialogHeader>
                        <AlertDialogTitle className="text-primary">
                            {showAddModal ? 'Add New Snippet' : 'Edit Snippet'}
                        </AlertDialogTitle>
                        <AlertDialogDescription>
                            {showAddModal ? 'Create a new reusable snippet with rich text content.' : 'Edit the selected snippet content and settings.'}
                        </AlertDialogDescription>
                    </AlertDialogHeader>
                    
                    <div className="flex-1 overflow-y-auto space-y-4 py-4">
                        <div className="space-y-6">
                            <Label htmlFor="snippet-name">
                                Snippet Name
                            </Label>
                            <Input
                                id="snippet-name"
                                type="text"
                                value={snippetName}
                                onChange={(e) => setSnippetName(e.target.value)}
                                placeholder="Enter snippet name..."
                                className="shadow-none focus-visible:ring-0 focus-visible:ring-offset-0"
                                disabled={submitting}
                            />
                        </div>
                        
                        <div className="space-y-6">
                            <Label>
                                Snippet Content
                            </Label>
                            <TiptapEditor
                                ref={editorRef}
                                content={editorContent}
                                onChange={setEditorContent}
                                placeholder="Enter snippet content..."
                                editable={!submitting}
                            />
                        </div>
                    </div>
                    
                    <AlertDialogFooter>
                        <AlertDialogCancel 
                            disabled={submitting}
                        >
                            Cancel
                        </AlertDialogCancel>
                        <AlertDialogAction
                            onClick={showAddModal ? handleSaveSnippet : handleUpdateSnippet}
                            disabled={submitting}
                        >
                            {submitting ? (showAddModal ? 'Creating...' : 'Updating...') : (showAddModal ? 'Create Snippet' : 'Update Snippet')}
                        </AlertDialogAction>
                    </AlertDialogFooter>
                </AlertDialogContent>
            </AlertDialog>

            {/* Delete Confirmation Modal */}
            <AlertDialog open={!!showDeleteConfirm} onOpenChange={(open) => !open && setShowDeleteConfirm(null)}>
                <AlertDialogContent>
                    <AlertDialogHeader>
                        <AlertDialogTitle>Delete Snippet</AlertDialogTitle>
                        <AlertDialogDescription>
                            Are you sure you want to delete the snippet &quot;{showDeleteConfirm?.name}&quot;?
                            <br />
                            This action cannot be undone.
                        </AlertDialogDescription>
                    </AlertDialogHeader>
                    <AlertDialogFooter>
                        <AlertDialogCancel 
                            disabled={deleting}
                        >
                            Cancel
                        </AlertDialogCancel>
                        <AlertDialogAction
                            onClick={handleDeleteSnippet}
                            disabled={deleting}
                            className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
                        >
                            {deleting ? 'Deleting...' : 'Delete'}
                        </AlertDialogAction>
                    </AlertDialogFooter>
                </AlertDialogContent>
            </AlertDialog>
        </div>
    );
}
"use client";

import { useState, useEffect } from 'react';
import { useMutation } from "convex/react";
import { useSession } from 'next-auth/react';
import { api } from "@/../convex/_generated/api";
import { Id } from "@/../convex/_generated/dataModel";
import { debugError } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Checkbox } from '@/components/ui/checkbox';
import { 
    Select, 
    SelectContent, 
    SelectItem, 
    SelectTrigger, 
    SelectValue 
} from '@/components/ui/select';
import { 
    Dialog, 
    DialogContent, 
    DialogDescription,
    DialogFooter, 
    DialogHeader, 
    DialogTitle 
} from '@/components/ui/dialog';
import { ChevronUp, ChevronDown, X, Plus } from 'lucide-react';

interface SparkField {
    type: "text" | "richtext" | "dropdown" | "radio" | "checkbox";
    label: string;
    placeholder?: string;
    options?: string[];
    required?: boolean;
}

interface Spark {
    _id: string;
    name: string;
    description?: string;
    fields: SparkField[];
    createdAt: number;
    updatedAt: number;
}

interface SparkConfigEditorProps {
    spark?: Spark | null;
    eventId: Id<"events">;
    open: boolean;
    onClose: () => void;
    onSuccess: (sparkId: string) => void;
    onError: (error: string) => void;
}

const FIELD_TYPE_OPTIONS = [
    { value: 'text', label: 'Text Field' },
    { value: 'richtext', label: 'Rich Text Editor' },
    { value: 'dropdown', label: 'Dropdown Select' },
    { value: 'radio', label: 'Radio Group' },
    { value: 'checkbox', label: 'Checkbox' },
];

export default function SparkConfigEditor({ spark, eventId, open, onClose, onSuccess, onError }: SparkConfigEditorProps) {
    // Get session data for username
    const { data: sessionData } = useSession();
    
    // Convex mutations
    const createSpark = useMutation(api.sparks.createSpark);
    const updateSpark = useMutation(api.sparks.updateSpark);

    // Form state
    const [name, setName] = useState('');
    const [description, setDescription] = useState('');
    const [fields, setFields] = useState<SparkField[]>([]);
    const [saving, setSaving] = useState(false);

    // Initialize form for editing
    useEffect(() => {
        if (open) {
            if (spark) {
                setName(spark.name);
                setDescription(spark.description || '');
                setFields([...spark.fields]);
            } else {
                setName('');
                setDescription('');
                setFields([]);
            }
        }
    }, [spark, open]);

    // Add new field
    const handleAddField = () => {
        const newField: SparkField = {
            type: 'text',
            label: '',
            required: false,
        };
        setFields([...fields, newField]);
    };

    // Remove field
    const handleRemoveField = (index: number) => {
        const newFields = fields.filter((_, i) => i !== index);
        setFields(newFields);
    };

    // Move field up
    const handleMoveFieldUp = (index: number) => {
        if (index > 0) {
            const newFields = [...fields];
            [newFields[index - 1], newFields[index]] = [newFields[index], newFields[index - 1]];
            setFields(newFields);
        }
    };

    // Move field down
    const handleMoveFieldDown = (index: number) => {
        if (index < fields.length - 1) {
            const newFields = [...fields];
            [newFields[index], newFields[index + 1]] = [newFields[index + 1], newFields[index]];
            setFields(newFields);
        }
    };

    // Update field property
    const handleFieldChange = (index: number, property: keyof SparkField, value: string | boolean | string[]) => {
        const newFields = [...fields];
        newFields[index] = { ...newFields[index], [property]: value };
        
        // Clear incompatible properties when changing field type
        if (property === 'type') {
            if (value === 'text' || value === 'richtext' || value === 'checkbox') {
                // These types don't use options
                delete newFields[index].options;
            } else if (value === 'dropdown' || value === 'radio') {
                // These types require options
                newFields[index].options = newFields[index].options || [''];
                // These types don't use placeholder
                delete newFields[index].placeholder;
            }
            
            if (value === 'checkbox') {
                // Checkboxes don't use placeholder
                delete newFields[index].placeholder;
            }
        }
        
        setFields(newFields);
    };

    // Add option to dropdown/radio field
    const handleAddOption = (fieldIndex: number) => {
        const newFields = [...fields];
        if (!newFields[fieldIndex].options) {
            newFields[fieldIndex].options = [];
        }
        newFields[fieldIndex].options!.push('');
        setFields(newFields);
    };

    // Remove option from dropdown/radio field
    const handleRemoveOption = (fieldIndex: number, optionIndex: number) => {
        const newFields = [...fields];
        if (newFields[fieldIndex].options) {
            newFields[fieldIndex].options = newFields[fieldIndex].options!.filter((_, i) => i !== optionIndex);
        }
        setFields(newFields);
    };

    // Update option value
    const handleOptionChange = (fieldIndex: number, optionIndex: number, value: string) => {
        const newFields = [...fields];
        if (newFields[fieldIndex].options) {
            newFields[fieldIndex].options![optionIndex] = value;
        }
        setFields(newFields);
    };

    // Validate form
    const validateForm = (): string | null => {
        if (!name.trim()) {
            return 'Spark name is required';
        }

        if (fields.length === 0) {
            return 'At least one field is required';
        }

        for (let i = 0; i < fields.length; i++) {
            const field = fields[i];
            
            if (!field.label.trim()) {
                return `Field ${i + 1}: Label is required`;
            }

            if ((field.type === 'dropdown' || field.type === 'radio') && 
                (!field.options || field.options.length === 0)) {
                return `Field ${i + 1}: ${field.type} fields must have at least one option`;
            }

            if (field.options) {
                const validOptions = field.options.filter(opt => opt.trim());
                if (validOptions.length === 0) {
                    return `Field ${i + 1}: ${field.type} fields must have at least one non-empty option`;
                }
            }
        }

        return null;
    };

    // Handle save
    const handleSave = async () => {
        const validationError = validateForm();
        if (validationError) {
            onError(validationError);
            return;
        }

        setSaving(true);

        try {
            // Clean up fields before saving
            const cleanFields = fields.map(field => {
                const cleanField: SparkField = {
                    type: field.type,
                    label: field.label.trim(),
                    required: field.required || false,
                };

                if (field.placeholder && (field.type === 'text' || field.type === 'richtext')) {
                    cleanField.placeholder = field.placeholder.trim();
                }

                if (field.options && (field.type === 'dropdown' || field.type === 'radio')) {
                    cleanField.options = field.options.filter(opt => opt.trim()).map(opt => opt.trim());
                }

                return cleanField;
            });

            let sparkId: string;

            if (spark) {
                // Update existing spark
                await updateSpark({
                    username: sessionData?.user?.username || '',
                    sparkId: spark._id as Id<"sparks">,
                    name: name.trim(),
                    description: description.trim() || undefined,
                    fields: cleanFields,
                });
                sparkId = spark._id;
            } else {
                // Create new spark
                const result = await createSpark({
                    username: sessionData?.user?.username || '',
                    eventId: eventId,
                    name: name.trim(),
                    description: description.trim() || undefined,
                    fields: cleanFields,
                });
                sparkId = result.data?.id || '';
            }

            onSuccess(sparkId);
        } catch (error) {
            onError(error instanceof Error ? error.message : 'Failed to save spark');
            debugError('Error saving spark:', error);
        } finally {
            setSaving(false);
        }
    };

    // Handle cancel
    const handleCancel = () => {
        onClose();
    };

    return (
        <Dialog open={open} onOpenChange={(open) => !open && onClose()}>
            <DialogContent className="sm:max-w-6xl max-h-[90vh] overflow-y-auto rounded-none">
                <DialogHeader className="text-left">
                    <DialogTitle>{spark ? 'Edit Spark' : 'Create New Spark'}</DialogTitle>
                    <DialogDescription>
                        {spark ? 'Modify the spark configuration and field settings.' : 'Create a new spark template with custom fields for dynamic forms.'}
                    </DialogDescription>
                </DialogHeader>

                <div className="space-y-6 py-4">
                    {/* Basic Information */}
                    <div className="space-y-4">
                        <div className="space-y-2">
                            <Label>
                                Name *
                            </Label>
                            <Input
                                id="spark-name"
                                type="text"
                                value={name}
                                onChange={(e) => setName(e.target.value)}
                                placeholder="Enter spark name"
                                disabled={saving}
                                className="shadow-none focus-visible:ring-0 focus-visible:ring-offset-0"
                            />
                        </div>

                        <div className="space-y-2">
                            <Label>
                                Description
                            </Label>
                            <Textarea
                                id="spark-description"
                                value={description}
                                onChange={(e) => setDescription(e.target.value)}
                                placeholder="Optional description"
                                disabled={saving}
                                className="shadow-none focus-visible:ring-0 focus-visible:ring-offset-0"
                                rows={3}
                            />
                        </div>
                    </div>

                    {/* Fields Configuration */}
                    <div className="space-y-4">
                        <div className="flex items-center justify-between">
                            <h3 className="text-lg font-semibold">Fields Configuration</h3>
                            <Button
                                type="button"
                                onClick={handleAddField}
                                disabled={saving}
                                className="gap-2"
                            >
                                <Plus className="h-4 w-4" />
                                Add Field
                            </Button>
                        </div>

                        {fields.length === 0 ? (
                            <div className="empty-state">
                                No fields configured. Click &quot;Add Field&quot; to start building your form.
                            </div>
                        ) : (
                            <div className="space-y-4">
                                {fields.map((field, index) => (
                                    <div key={index} className="p-4 border border-border rounded-md space-y-4">
                                        <div className="flex items-center justify-between">
                                            <span className="text-sm font-medium text-muted-foreground">
                                                {FIELD_TYPE_OPTIONS.find(opt => opt.value === field.type)?.label}
                                            </span>
                                            <div className="flex items-center gap-2">
                                                <Button
                                                    type="button"
                                                    variant="ghost"
                                                    size="sm"
                                                    onClick={() => handleMoveFieldUp(index)}
                                                    disabled={saving || index === 0}
                                                    className="h-8 w-8 p-0 hover:!bg-transparent hover:text-primary"
                                                    title="Move up"
                                                >
                                                    <ChevronUp className="h-4 w-4" />
                                                </Button>
                                                <Button
                                                    type="button"
                                                    variant="ghost"
                                                    size="sm"
                                                    onClick={() => handleMoveFieldDown(index)}
                                                    disabled={saving || index === fields.length - 1}
                                                    className="h-8 w-8 p-0 hover:!bg-transparent hover:text-primary"
                                                    title="Move down"
                                                >
                                                    <ChevronDown className="h-4 w-4" />
                                                </Button>
                                                <Button
                                                    type="button"
                                                    variant="ghost"
                                                    size="sm"
                                                    onClick={() => handleRemoveField(index)}
                                                    disabled={saving}
                                                    className="h-8 w-8 p-0 hover:!bg-transparent hover:text-primary"
                                                    title="Remove field"
                                                >
                                                    <X className="h-4 w-4" />
                                                </Button>
                                            </div>
                                        </div>

                                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                            {/* Field Type */}
                                            <div className="space-y-2">
                                                <Label>Type</Label>
                                                <Select
                                                    value={field.type}
                                                    onValueChange={(value) => handleFieldChange(index, 'type', value)}
                                                    disabled={saving}
                                                >
                                                    <SelectTrigger className="h-10">
                                                        <SelectValue />
                                                    </SelectTrigger>
                                                    <SelectContent>
                                                        {FIELD_TYPE_OPTIONS.map(option => (
                                                            <SelectItem key={option.value} value={option.value}>
                                                                {option.label}
                                                            </SelectItem>
                                                        ))}
                                                    </SelectContent>
                                                </Select>
                                            </div>

                                            {/* Required Checkbox */}
                                            <div className="flex items-center space-x-2 pt-6">
                                                <Checkbox
                                                    id={`required-${index}`}
                                                    checked={field.required || false}
                                                    onCheckedChange={(checked) => handleFieldChange(index, 'required', checked)}
                                                    disabled={saving}
                                                />
                                                <Label 
                                                    htmlFor={`required-${index}`}
                                                    className="cursor-pointer"
                                                >
                                                    Required Field
                                                </Label>
                                            </div>
                                        </div>

                                        {/* Field Label */}
                                        <div className="space-y-2 mt-6">
                                            <Label>Label *</Label>
                                            <Input
                                                type="text"
                                                value={field.label}
                                                onChange={(e) => handleFieldChange(index, 'label', e.target.value)}
                                                placeholder="Enter field label"
                                                disabled={saving}
                                                className="shadow-none focus-visible:ring-0 focus-visible:ring-offset-0"
                                            />
                                        </div>

                                        {/* Placeholder (for text and richtext) */}
                                        {(field.type === 'text' || field.type === 'richtext') && (
                                            <div className="space-y-2 mt-6">
                                                <Label>Placeholder</Label>
                                                <Input
                                                    type="text"
                                                    value={field.placeholder || ''}
                                                    onChange={(e) => handleFieldChange(index, 'placeholder', e.target.value)}
                                                    placeholder="Enter placeholder text"
                                                    disabled={saving}
                                                    className="shadow-none focus-visible:ring-0 focus-visible:ring-offset-0"
                                                />
                                            </div>
                                        )}

                                        {/* Options (for dropdown and radio) */}
                                        {(field.type === 'dropdown' || field.type === 'radio') && (
                                            <div className="space-y-2 mt-6">
                                                <div className="flex items-center justify-between">
                                                    <Label>Options *</Label>
                                                    <Button
                                                        type="button"
                                                        variant="outline"
                                                        size="sm"
                                                        onClick={() => handleAddOption(index)}
                                                        disabled={saving}
                                                        className="gap-2"
                                                    >
                                                        <Plus className="h-3 w-3" />
                                                        Add Option
                                                    </Button>
                                                </div>
                                                <div className="space-y-2">
                                                    {field.options && field.options.map((option, optionIndex) => (
                                                        <div key={optionIndex} className="flex gap-2">
                                                            <Input
                                                                type="text"
                                                                value={option}
                                                                onChange={(e) => handleOptionChange(index, optionIndex, e.target.value)}
                                                                placeholder={`Option ${optionIndex + 1}`}
                                                                disabled={saving}
                                                                className="flex-1 shadow-none focus-visible:ring-0 focus-visible:ring-offset-0"
                                                            />
                                                            <Button
                                                                type="button"
                                                                variant="ghost"
                                                                size="sm"
                                                                onClick={() => handleRemoveOption(index, optionIndex)}
                                                                disabled={saving || (field.options && field.options.length <= 1)}
                                                                className="h-10 w-10 p-0 hover:!bg-transparent hover:text-primary shrink-0"
                                                            >
                                                                <X className="h-4 w-4" />
                                                            </Button>
                                                        </div>
                                                    ))}
                                                </div>
                                            </div>
                                        )}
                                    </div>
                                ))}
                            </div>
                        )}
                    </div>
                </div>

                <DialogFooter className="gap-3">
                    <Button
                        variant="outline"
                        onClick={handleCancel}
                        disabled={saving}
                    >
                        Cancel
                    </Button>
                    <Button
                        onClick={handleSave}
                        disabled={saving}
                    >
                        {saving ? 'Saving...' : (spark ? 'Update Spark' : 'Create Spark')}
                    </Button>
                </DialogFooter>
            </DialogContent>
        </Dialog>
    );
}
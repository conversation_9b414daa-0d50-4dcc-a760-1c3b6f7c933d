"use client";

import { useQuery } from "convex/react";
import { api } from "@/../convex/_generated/api";
import { Users } from 'lucide-react';
import { Badge } from '@/components/ui/badge';

const getRoleVariant = (role: string) => {
    if (role === 'admin') return 'default';
    if (role === 'teamLead') return 'secondary';
    return 'outline';
};

export default function OnlineUsers() {
    // Real-time queries from Convex
    const onlineUsersEnabled = useQuery(api.settings.getOnlineUsersSetting);
    const onlineUsers = useQuery(api.presence.getOnlineUsers);
    const teams = useQuery(api.teams.getTeamsByActiveEvent);

    // Create teams map for display
    const teamsMap = teams ? new Map(teams.map(team => [team._id, team.name])) : new Map();

    // Handle different states - wait for all data to load to prevent hydration mismatch
    if (onlineUsersEnabled === undefined || onlineUsers === undefined || teams === undefined) {
        return (
            <div className="space-y-6">
                <div className="flex items-center gap-2">
                    <Users className="h-6 w-6 text-muted-foreground" />
                    <h2 className="text-2xl font-bold text-primary">Online Users (Loading...)</h2>
                </div>
            </div>
        );
    }

    if (!onlineUsersEnabled) {
        return (
            <div className="space-y-6">
                <div className="flex items-center gap-2">
                    <Users className="h-6 w-6 text-muted-foreground" />
                    <h2 className="text-2xl font-bold text-primary">Online Users (Disabled)</h2>
                </div>
                <div className="text-center py-8 bg-muted/30 border border-border rounded-md">
                    <Users className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                    <p className="text-muted-foreground mb-2">Online users tracking is currently disabled.</p>
                    <p className="text-muted-foreground">Enable it in Settings to see who&apos;s online.</p>
                </div>
            </div>
        );
    }

    return (
        <div className="space-y-6">
            <div className="flex items-center gap-2">
                <Users className="h-6 w-6 text-primary" />
                <h2 className="text-2xl font-bold text-primary">Online Users ({onlineUsers.length})</h2>
            </div>
            
            {onlineUsers.length === 0 ? (
                <div className="text-center py-8 bg-muted/30 border border-border rounded-md">
                    <Users className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                    <p className="text-muted-foreground">No users are currently online.</p>
                </div>
            ) : (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    {onlineUsers.map(onlineUser => (
                        <div key={onlineUser.id} className="p-4 border border-border rounded-md bg-muted/20 hover:bg-muted/30 transition-colors">
                            <div className="space-y-2">
                                <div className="flex items-center gap-2">
                                    <div className="flex-1">
                                        <span className="font-medium text-foreground">
                                            {onlineUser.name || onlineUser.username}
                                        </span>
                                        <span className="text-muted-foreground text-sm ml-2">
                                            @{onlineUser.username}
                                        </span>
                                    </div>
                                </div>
                                
                                <div className="flex items-center justify-between">
                                    <Badge variant={getRoleVariant(onlineUser.role)}>
                                        {onlineUser.role === 'teamLead' ? 'Team Lead' : 
                                         onlineUser.role === 'teamMember' ? 'Team Member' : 'Admin'}
                                    </Badge>
                                    
                                    <span className="text-xs text-muted-foreground">
                                        {onlineUser.teamId ? (
                                            teamsMap.get(onlineUser.teamId) || 'Loading team...'
                                        ) : onlineUser.role === 'admin' ? (
                                            ''
                                        ) : (
                                            'No Team'
                                        )}
                                    </span>
                                </div>
                            </div>
                        </div>
                    ))}
                </div>
            )}
        </div>
    );
}

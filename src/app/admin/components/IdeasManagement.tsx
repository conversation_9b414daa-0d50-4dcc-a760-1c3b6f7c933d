"use client";

import { useState, useMemo, useCallback, useEffect, memo } from 'react';
import { useQuery, useMutation } from "convex/react";
import { api } from "@/../convex/_generated/api";
import { Id } from "@/../convex/_generated/dataModel";
import { 
    Select, 
    SelectContent, 
    SelectItem, 
    SelectTrigger, 
    SelectValue 
} from '@/components/ui/select';
import { TeamLeadWithdrawalModal } from './modals/TeamLeadWithdrawalModal';
import TeamGroup from './ideas/components/TeamGroup';
import { GroupedIdeas } from './ideas/types/ideas-management';
import NoActiveEventMessage from './ideas/components/NoActiveEventMessage';
import CountDisplay from './ideas/components/CountDisplay';
import { SESSION_VALUES, SESSION_TYPES, ERROR_MESSAGES } from './ideas/constants/session-constants';
import AdminErrorBoundary from './AdminErrorBoundary';
import { parseError, getErrorMessage, ErrorType, type StructuredError } from './ideas/utils/error-utils';
import { StateCard } from '@/components/ui/state-card';
import { Skeleton } from '@/components/ui/skeleton';

// Memoized TeamGroup for better performance
const TeamGroupMemo = memo(TeamGroup);

function IdeasManagementCore() {
    // All hooks must be at the top, before any conditional logic
    
    // Real-time data from Convex - automatically updates (excludes Quickfire sessions)
    const activeEvent = useQuery(api.events.getActiveEvent);
    const sessions = useQuery(api.sessions.getSessionsByActiveEventExcludingQuickfire);
    const [selectedSession, setSelectedSession] = useState<string>('');
    
    // Get session data based on selection - optimized to avoid conditional queries
    const sessionId = selectedSession === SESSION_VALUES.ALL || selectedSession === SESSION_VALUES.EMPTY 
        ? undefined 
        : selectedSession as Id<"sessions">;
    
    // UI state only - must be declared before queries that use it
    const [withdrawingTeam, setWithdrawingTeam] = useState<string | null>(null);
    const [showWithdrawModal, setShowWithdrawModal] = useState<{
        teamId: string;
        teamName: string;
        sessionId: string;
        eventId: string;
    } | null>(null);
    
    const groupedIdeas = useQuery(api.ideas.getAllDataGroupedByTeam, {
        sessionId
    });

    // Get team leads for the withdrawal modal
    const teamLeads = useQuery(api.teams.getTeamLeadsForTeam, 
        showWithdrawModal ? {
            teamId: showWithdrawModal.teamId as Id<"teams">,
            eventId: showWithdrawModal.eventId as Id<"events">
        } : "skip"
    );

    // Convex mutations
    const withdrawAllTeamData = useMutation(api.ideas.withdrawAllTeamData);
    const withdrawTeamLeadIdeas = useMutation(api.ideas.withdrawTeamLeadIdeas);
    const [error, setError] = useState<StructuredError | null>(null);
    const [showEmptyState, setShowEmptyState] = useState(false);
    

    // Session options for dropdown - optimized
    const sessionOptions = useMemo(() => {
        if (!sessions?.length) return [];
        
        return [
            { value: SESSION_VALUES.ALL, label: 'All Sessions' },
            ...sessions.map(session => ({
                value: session._id,
                label: session.name
            }))
        ];
    }, [sessions]);

    // Get selected session object - optimized
    const selectedSessionObj = useMemo(() => {
        if (!sessions?.length || selectedSession === SESSION_VALUES.ALL) {
            return null;
        }
        return sessions.find(s => s._id === selectedSession) || null;
    }, [sessions, selectedSession]);

    // Calculate total counts - optimized with reduce
    const totalCounts = useMemo(() => {
        if (!groupedIdeas) return { ideas: 0, sparkSubmissions: 0 };
        
        return Object.values(groupedIdeas as GroupedIdeas).reduce(
            (acc, teamGroup) => ({
                ideas: acc.ideas + (teamGroup.ideas?.length || 0),
                sparkSubmissions: acc.sparkSubmissions + (teamGroup.sparkSubmissions?.length || 0)
            }),
            { ideas: 0, sparkSubmissions: 0 }
        );
    }, [groupedIdeas]);

    // Auto-select active session when sessions load - only on initial load
    useEffect(() => {
        if (sessions?.length && selectedSession === SESSION_VALUES.EMPTY) {
            const activeSession = sessions.find(s => s.active);
            
            // If active session is Ideas or Sparks, show that session
            if (activeSession && (activeSession.type === SESSION_TYPES.IDEAS || activeSession.type === SESSION_TYPES.SPARKS)) {
                setSelectedSession(activeSession._id);
            } else {
                // Otherwise, show all sessions (Quickfire active, no active session, etc.)
                setSelectedSession(SESSION_VALUES.ALL);
            }
        }
    }, [sessions, selectedSession]);

    // Withdraw handlers - All hooks must be called before any early returns
    const initiateWithdraw = useCallback((teamId: string, teamName: string) => {
        let sessionIdForWithdraw: string;
        
        if (selectedSession === SESSION_VALUES.ALL) {
            setError(parseError(new Error(ERROR_MESSAGES.CANNOT_WITHDRAW_ALL_SESSIONS)));
            return;
        } else if (selectedSessionObj) {
            sessionIdForWithdraw = selectedSessionObj._id;
        } else {
            setError(parseError(new Error(ERROR_MESSAGES.NO_SESSION_SELECTED)));
            return;
        }
        
        if (!activeEvent) {
            setError(parseError(new Error("No active event found")));
            return;
        }
        
        setShowWithdrawModal({
            teamId,
            teamName,
            sessionId: sessionIdForWithdraw,
            eventId: activeEvent._id
        });
    }, [selectedSession, selectedSessionObj, activeEvent]);

    const cancelWithdraw = useCallback(() => {
        setShowWithdrawModal(null);
        setWithdrawingTeam(null);
    }, []);

    const handleWithdrawAll = useCallback(async () => {
        if (!showWithdrawModal || withdrawingTeam) {
            return; // Prevent multiple simultaneous calls
        }

        const { teamId, sessionId } = showWithdrawModal;
        setWithdrawingTeam(teamId);
        
        try {
            await withdrawAllTeamData({
                teamId: teamId as Id<"teams">,
                sessionId: sessionId as Id<"sessions">
            });
            // Clear states on success
            setShowWithdrawModal(null);
            setWithdrawingTeam(null);
        } catch (error) {
            setError(parseError(error));
            setWithdrawingTeam(null); // Clear withdrawing state on error
        }
    }, [withdrawAllTeamData, showWithdrawModal, withdrawingTeam]);

    const handleWithdrawTeamLead = useCallback(async (teamLeadId: string) => {
        if (!showWithdrawModal || withdrawingTeam) {
            return; // Prevent multiple simultaneous calls
        }

        const { teamId, sessionId } = showWithdrawModal;
        setWithdrawingTeam(teamId);
        
        try {
            await withdrawTeamLeadIdeas({
                teamId: teamId as Id<"teams">,
                sessionId: sessionId as Id<"sessions">,
                userId: teamLeadId as Id<"users">
            });
            // Clear states on success
            setShowWithdrawModal(null);
            setWithdrawingTeam(null);
        } catch (error) {
            setError(parseError(error));
            setWithdrawingTeam(null); // Clear withdrawing state on error
        }
    }, [withdrawTeamLeadIdeas, showWithdrawModal, withdrawingTeam]);

    // Memoized team sorting - CRITICAL PERFORMANCE OPTIMIZATION
    const sortedTeams = useMemo(() => {
        if (!groupedIdeas) return [];
        return Object.entries(groupedIdeas as GroupedIdeas)
            .sort(([, a], [, b]) => (b as GroupedIdeas[string]).lastUpdate - (a as GroupedIdeas[string]).lastUpdate);
    }, [groupedIdeas]);

    // Delay showing empty state to prevent flash during fast session switches
    useEffect(() => {
        if (sortedTeams.length === 0 && groupedIdeas !== undefined) {
            const timer = setTimeout(() => {
                setShowEmptyState(true);
            }, 150); // 150ms delay
            
            return () => clearTimeout(timer);
        } else {
            setShowEmptyState(false);
        }
    }, [sortedTeams.length, groupedIdeas]);
    
    // Early returns for various states
    
    // No active event (check this first, before loading state)
    if (activeEvent === null || error?.type === ErrorType.NO_ACTIVE_EVENT) {
        return <NoActiveEventMessage />;
    }
    
    // Loading state (only if we have an active event but other data is still loading)
    if (activeEvent === undefined || sessions === undefined ) {
        return (
            <div className="space-y-6">
                {/* Header Section Skeleton */}
                <div className="space-y-4">
                    <div className="flex flex-col sm:flex-row sm:items-center gap-4 p-4 border border-skeleton">
                        {/* Session Filter Skeleton */}
                        <div className="flex-1 min-w-0">
                            <Skeleton className="w-full min-h-[3.5rem]" />
                        </div>

                        {/* Count Display Skeletons */}
                        <div className="flex items-center gap-4 flex-wrap sm:flex-nowrap">
                            <Skeleton className="h-8 w-24" />
                            <Skeleton className="h-8 w-24" />
                        </div>
                    </div>
                </div>

                {/* Team Cards Skeleton */}
                <div className="space-y-6">
                    {Array.from({ length: 3 }).map((_, index) => (
                        <div key={index} className="border border-skeleton p-6 space-y-4">
                            {/* Team Header */}
                            <div className="flex items-center justify-between">
                                <Skeleton className="h-6 w-32" />
                                <Skeleton className="h-8 w-20" />
                            </div>
                            
                            {/* Ideas/Sparks Content */}
                            <div className="space-y-4">
                                <Skeleton className="h-5 w-16" />
                                <div className="space-y-4">
                                    {Array.from({ length: 2 }).map((_, ideaIndex) => (
                                        <div key={ideaIndex} className="border-2 border-skeleton p-4 space-y-4">
                                            {/* Idea Header */}
                                            <div className="flex flex-col sm:flex-row sm:items-center gap-3">
                                                <Skeleton className="h-6 w-48 flex-1" />
                                                <Skeleton className="h-6 w-20" />
                                            </div>
                                            
                                            {/* Description */}
                                            <div className="space-y-2">
                                                <Skeleton className="h-4 w-full" />
                                                <Skeleton className="h-4 w-3/4" />
                                                <Skeleton className="h-4 w-1/2" />
                                            </div>
                                            
                                            {/* Metadata */}
                                            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3 pt-4 border-t border-skeleton">
                                                <div className="space-y-1">
                                                    <Skeleton className="h-3 w-20" />
                                                    <Skeleton className="h-4 w-24" />
                                                </div>
                                                <div className="space-y-1">
                                                    <Skeleton className="h-3 w-16" />
                                                    <Skeleton className="h-4 w-20" />
                                                </div>
                                                <div className="space-y-1">
                                                    <Skeleton className="h-3 w-12" />
                                                    <Skeleton className="h-4 w-28" />
                                                </div>
                                                <div className="space-y-1">
                                                    <Skeleton className="h-3 w-10" />
                                                    <Skeleton className="h-4 w-32" />
                                                </div>
                                                <div className="space-y-1 sm:col-span-2">
                                                    <Skeleton className="h-3 w-20" />
                                                    <Skeleton className="h-4 w-40" />
                                                </div>
                                            </div>
                                        </div>
                                    ))}
                                </div>
                            </div>
                        </div>
                    ))}
                </div>
            </div>
        );
    }

    // Error state
    if (error) {
        return (
            <div className="flex justify-center items-center min-h-[400px]">
                <StateCard state="error" title="Error" message={getErrorMessage(error)} />
            </div>
        );
    }

    // No sessions available
    if (Array.isArray(sessions) && sessions.length === 0) {
        return (
            <div className="flex justify-center items-center min-h-[400px]">
                <StateCard state="info" title="No Data Available" message="No sessions found." />
            </div>
        );
    }


    // Determine if we have data to show
    const hasData = groupedIdeas && Object.keys(groupedIdeas).length > 0;

    return (
        <div className="space-y-6">
            {/* Header Section */}
            <div className="space-y-4">
                <div className="flex flex-col sm:flex-row sm:items-center gap-4 p-4 border border-border">
                    {/* Session Filter */}
                    <div className="flex-1 min-w-0">
                        <Select value={selectedSession} onValueChange={setSelectedSession}>
                            <SelectTrigger className="w-full focus:ring-0 focus:ring-offset-0">
                                <SelectValue placeholder="Select Session" />
                            </SelectTrigger>
                            <SelectContent>
                                {sessionOptions.map((option) => (
                                    <SelectItem key={option.value} value={option.value}>
                                        {option.label}
                                    </SelectItem>
                                ))}
                            </SelectContent>
                        </Select>
                    </div>

                    {/* Total Counts */}
                    <div className="flex items-center gap-4 flex-wrap sm:flex-nowrap">
                        {selectedSession === SESSION_VALUES.ALL ? (
                            <>
                                <CountDisplay label="Total Ideas" count={totalCounts.ideas} />
                                <CountDisplay label="Total Sparks" count={totalCounts.sparkSubmissions} />
                            </>
                        ) : selectedSessionObj && selectedSessionObj.type === SESSION_TYPES.SPARKS ? (
                            <CountDisplay label="Total Sparks" count={totalCounts.sparkSubmissions} />
                        ) : (
                            <CountDisplay label="Total Ideas" count={totalCounts.ideas} />
                        )}
                    </div>
                </div>
            </div>

            {/* Teams Content */}
            {hasData && sortedTeams.length > 0 ? (
                <div className="space-y-6">
                    {sortedTeams.map(([teamId, teamGroup]) => {
                        const typedTeamGroup = teamGroup as GroupedIdeas[string];
                        return (
                        <TeamGroupMemo
                            key={teamId}
                            teamId={teamId}
                            teamName={typedTeamGroup.teamName}
                            ideas={typedTeamGroup.ideas || []}
                            sparkSubmissions={typedTeamGroup.sparkSubmissions || []}
                            withdrawingTeam={withdrawingTeam}
                            onWithdraw={initiateWithdraw}
                        />
                        );
                    })}
                </div>
            ) : groupedIdeas !== undefined ? (
                <div className="flex justify-center items-center min-h-[400px]">
                    <StateCard state="empty" title="No Data Available" message="No ideas found in the selected session." />
                </div>
            ) : showEmptyState ? (
                <div className="flex justify-center items-center min-h-[400px]">
                    <StateCard state="empty" title="NO CONTENT FOUND" message="No ideas found in the selected session." />
                </div>
            ) : null}

            {/* Team Lead Withdrawal Modal */}
            <TeamLeadWithdrawalModal
                showWithdrawModal={showWithdrawModal}
                teamLeads={teamLeads || []}
                isLoadingTeamLeads={teamLeads === undefined}
                onCancel={cancelWithdraw}
                onConfirmWithdrawAll={handleWithdrawAll}
                onConfirmWithdrawTeamLead={handleWithdrawTeamLead}
                isWithdrawing={!!withdrawingTeam}
            />
        </div>
    );
}

export default function IdeasManagement() {
    return (
        <AdminErrorBoundary 
            fallbackTitle="Ideas Management Error"
            fallbackMessage="There was an error loading the ideas management interface."
        >
            <IdeasManagementCore />
        </AdminErrorBoundary>
    );
}

"use client";

import { useState, useMemo, useCallback } from 'react';
import { useQuery, useMutation } from "convex/react";
import { api } from "@/../convex/_generated/api";
import { Id } from "@/../convex/_generated/dataModel";
import { useSession } from 'next-auth/react';
import { Button } from '@/components/ui/button';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Label } from '@/components/ui/label';
import { StateCard } from '@/components/ui/state-card';
import AdminErrorBoundary from './AdminErrorBoundary';

interface Team {
    _id: string;
    name: string;
    active: boolean;
    hasSubmitted: boolean;
    createdAt: number;
}

interface VotingStatusData {
    canStart: boolean;
    reason: string;
    unfinishedTeams?: { id: string; name: string; }[];
    activeTeams?: { id: string; name: string; }[];
    finishedTeams?: number;
}

function VotingManagementCore() {
    const { data: session } = useSession();
    // Real-time Convex queries
    const votingStatus = useQuery(api.voting.getVotingStatus);
    const canStartVotingData = useQuery(api.voting.canStartVoting) as VotingStatusData | undefined;
    const teamsVotingStatus = useQuery(api.voting.getTeamsVotingStatus) as Team[] | undefined;
    const votingTeamStatus = useQuery(api.voting.getVotingTeamStatus);
    
    // Convex mutations
    const toggleVoting = useMutation(api.voting.toggleVoting);
    const setTeamVoting = useMutation(api.voting.setTeamVoting);
    
    // UI state
    const [error, setError] = useState<string | null>(null);
    const [isToggling, setIsToggling] = useState(false);
    const [isUpdatingSelection, setIsUpdatingSelection] = useState(false);

    // Derived state
    const loading = votingStatus === undefined || canStartVotingData === undefined || teamsVotingStatus === undefined || votingTeamStatus === undefined;
    const votingStarted = votingStatus?.votingStarted || false;
    const canStartVoting = canStartVotingData?.canStart || false;
    const currentMode = votingTeamStatus?.mode || "none";
    
    // Get only teams that are active AND have submitted (finished teams)
    const finishedTeams = teamsVotingStatus?.filter(team => team.hasSubmitted && team.active) || [];

    // Team statistics
    const teamStats = useMemo(() => {
        if (!teamsVotingStatus) return { total: 0, active: 0, submitted: 0 };
        
        return {
            total: teamsVotingStatus.length,
            active: teamsVotingStatus.filter(team => team.active).length,
            submitted: teamsVotingStatus.filter(team => team.hasSubmitted).length,
        };
    }, [teamsVotingStatus]);

    const handleToggleVoting = async () => {
        if (isToggling) return;
        
        setIsToggling(true);
        setError(null);
        
        try {
            const result = await toggleVoting({ 
                username: session?.user?.username || ''
            });
            if (!result.success) {
                setError('Failed to toggle voting status');
            }
        } catch (error) {
            setError(error instanceof Error ? error.message : 'Failed to toggle voting');
        } finally {
            setIsToggling(false);
        }
    };

    const handleTeamVotingChange = useCallback(async (mode: "none" | "all" | "specific", teamId?: string) => {
        if (isUpdatingSelection) return;
        
        setIsUpdatingSelection(true);
        setError(null);
        
        try {
            await setTeamVoting({
                username: session?.user?.username || '',
                mode,
                teamId: teamId as Id<"teams">,
            });
        } catch (error) {
            setError(error instanceof Error ? error.message : 'Failed to update team voting');
        } finally {
            setIsUpdatingSelection(false);
        }
    }, [isUpdatingSelection, setTeamVoting, session?.user?.username]);

    const handleRadioChange = useCallback((value: string) => {
        if (value === 'none') {
            handleTeamVotingChange('none');
        } else if (value === 'all') {
            handleTeamVotingChange('all');
        } else {
            handleTeamVotingChange('specific', value);
        }
    }, [handleTeamVotingChange]);

    if (loading) {
        return (
            <div className="flex justify-center items-center min-h-[400px]">
                <StateCard state="loading" title="Loading voting management..." />
            </div>
        );
    }

    return (
        <div className="space-y-6">
            <div className="flex items-center justify-between">
                <h2 className="text-3xl font-bold text-primary">Voting Management</h2>
                <div className="flex items-center gap-2">
                    <span className={`inline-flex items-center gap-2 px-3 py-1 rounded-md text-sm font-medium border ${
                        votingStarted 
                            ? 'status-badge-active' 
                            : 'status-badge-inactive'
                    }`}>
                        <span className={`w-2 h-2 rounded-full ${votingStarted ? 'bg-accent' : 'bg-primary'}`}></span>
                        {votingStarted ? 'Voting Active' : 'Voting Inactive'}
                    </span>
                </div>
            </div>

            {error && (
                <div className="text-destructive text-base bg-destructive/10 border border-destructive/20 rounded-md p-3">
                    {error}
                </div>
            )}

            {/* Voting Controls */}
            <div className="space-y-4">
                {!votingStarted ? (
                    <Button
                        onClick={handleToggleVoting}
                        disabled={!canStartVoting || isToggling}
                        className="w-full"
                    >
                        {isToggling ? 'Starting...' : 'Start Voting'}
                    </Button>
                ) : (
                    <Button
                        onClick={handleToggleVoting}
                        disabled={isToggling}
                        variant="destructive"
                        className="w-full"
                    >
                        {isToggling ? 'Stopping...' : 'Stop Voting'}
                    </Button>
                )}
            </div>

            {/* Team Selection - Only show when voting is started */}
            {votingStarted && (
                <div className="space-y-4 p-4 border border-secondary rounded-md">
                    <h3 className="text-xl font-semibold text-secondary">Team Selection for Voting</h3>
                    <RadioGroup
                        value={
                            currentMode === 'none' ? 'none' 
                            : currentMode === 'all' ? 'all'
                            : votingTeamStatus?.teams?.find(t => t.voting === true)?._id || 'none'
                        }
                        onValueChange={handleRadioChange}
                        
                    >
                        <Label 
                            htmlFor="none" 
                            className={`interactive-option cursor-pointer flex items-center gap-3 ${
                                currentMode === 'none' ? 'border-primary' : 'border-popover'
                            }`}
                        >
                            <RadioGroupItem value="none" id="none" />
                            <span className="text-sm font-medium flex-1">
                                No Teams (Show &quot;Get Ready&quot; message)
                            </span>
                        </Label>
                        <Label 
                            htmlFor="all" 
                            className={`interactive-option cursor-pointer flex items-center gap-3 ${
                                currentMode === 'all' ? 'border-primary' : 'border-popover'
                            }`}
                        >
                            <RadioGroupItem value="all" id="all" />
                            <span className="text-sm font-medium flex-1">
                                All Teams ({finishedTeams.length} available)
                            </span>
                        </Label>
                        {finishedTeams.map(team => (
                            <Label 
                                key={team._id} 
                                htmlFor={team._id} 
                                className={`interactive-option cursor-pointer flex items-center gap-3 ${
                                    currentMode === 'specific' && votingTeamStatus?.teams?.find(t => t.voting === true)?._id === team._id ? 'border-primary' : 'border-popover'
                                }`}
                            >
                                <RadioGroupItem value={team._id} id={team._id} />
                                <span className="text-sm font-medium flex-1">
                                    {team.name}
                                </span>
                            </Label>
                        ))}
                    </RadioGroup>

                    {finishedTeams.length === 0 && (
                        <div className="text-muted-foreground text-base bg-muted/50 border border-border rounded-md p-3">
                            <p>No teams have submitted ideas yet. Teams must submit ideas before they can be selected for voting.</p>
                        </div>
                    )}
                </div>
            )}

            {/* Team Statistics */}
            <div className="grid grid-cols-3 gap-4">
                <div className="p-4 border border-border rounded-md bg-muted/20">
                    <div className="text-base text-muted-foreground">Total Teams</div>
                    <div className="text-3xl font-bold text-primary">{teamStats.total}</div>
                </div>
                <div className="p-4 border border-border rounded-md bg-muted/20">
                    <div className="text-base text-muted-foreground">Active Teams</div>
                    <div className="text-3xl font-bold text-primary">{teamStats.active}</div>
                </div>
                <div className="p-4 border border-border rounded-md bg-muted/20">
                    <div className="text-base text-muted-foreground">Submitted Teams</div>
                    <div className="text-3xl font-bold text-primary">{teamStats.submitted}</div>
                </div>
            </div>

            {/* Teams List */}
            {teamsVotingStatus && teamsVotingStatus.length > 0 && (
                <div className="space-y-4">
                    <h3 className="text-xl font-semibold text-primary">Teams Status</h3>
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                        {teamsVotingStatus.map(team => (
                            <div 
                                key={team._id} 
                                className={`p-4 border rounded-md transition-colors ${
                                    team.active 
                                        ? 'border-accent' 
                                        : 'border-primary'
                                }`}
                            >
                                <div className="flex items-center justify-between mb-2">
                                    <span className="font-medium text-foreground truncate text-base">{team.name}</span>
                                    <span className={`inline-flex items-center px-2 py-1 rounded-md text-sm font-medium border ${
                                        team.active 
                                            ? 'status-badge-active' 
                                            : 'status-badge-inactive'
                                    }`}>
                                        {team.active ? 'Active' : 'Inactive'}
                                    </span>
                                </div>
                                <div className="flex items-center">
                                    <span className={`inline-flex items-center gap-1 text-sm ${
                                        team.hasSubmitted 
                                            ? 'text-accent' 
                                            : 'text-muted-foreground'
                                    }`}>
                                        <span className={`w-2 h-2 rounded-full ${
                                            team.hasSubmitted ? 'bg-accent' : 'bg-muted-foreground'
                                        }`}></span>
                                        {team.hasSubmitted ? 'Submitted' : 'Pending'}
                                    </span>
                                </div>
                            </div>
                        ))}
                    </div>
                </div>
            )}
        </div>
    );
}

export default function VotingManagement() {
    return (
        <AdminErrorBoundary 
            fallbackTitle="Voting Management Error"
            fallbackMessage="There was an error loading the voting management interface."
        >
            <VotingManagementCore />
        </AdminErrorBoundary>
    );
}
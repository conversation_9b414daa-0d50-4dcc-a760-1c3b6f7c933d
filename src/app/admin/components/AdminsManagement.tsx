"use client";

import { useState, useCallback, useMemo } from 'react';
import { useQuery, useMutation, useAction } from "convex/react";
import { ConvexError } from 'convex/values';
import { api } from "@/../convex/_generated/api";
import { Id } from "@/../convex/_generated/dataModel";
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
    AlertDialog,
    AlertDialogAction,
    AlertDialogCancel,
    AlertDialogContent,
    AlertDialogDescription,
    AlertDialogFooter,
    AlertDialogHeader,
    AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import { useSession } from 'next-auth/react';
import { Eye, EyeOff, Key, Trash2 } from 'lucide-react';
import { debugError } from '@/lib/utils';

interface Admin {
    id: string;
    username: string;
    role: string;
}

export default function AdminsManagement() {
    const { data: session } = useSession();
    
    // Real-time Convex queries, mutations, and actions
    const admins = useQuery(api.users.getAdminUsers);
    const createAdmin = useAction(api.userActions.createAdminUserAction);
    const updateAdminUsername = useMutation(api.users.updateAdminUsername);
    const updateAdminPassword = useAction(api.userActions.updateAdminPasswordAction);
    const deleteAdmin = useMutation(api.users.deleteAdminUser);
    
    // Local component state
    const [username, setUsername] = useState('');
    const [password, setPassword] = useState('');
    const [showPassword, setShowPassword] = useState(false);
    const [showNewPassword, setShowNewPassword] = useState(false);
    const [error, setError] = useState<string | null>(null);
    const [editingAdmin, setEditingAdmin] = useState<Admin | null>(null);
    const [editSuccessId, setEditSuccessId] = useState<string | null>(null);
    const [editingUsername, setEditingUsername] = useState('');
    const [showPasswordModal, setShowPasswordModal] = useState(false);
    const [selectedAdmin, setSelectedAdmin] = useState<Admin | null>(null);
    const [newPassword, setNewPassword] = useState('');

    // Handle form submissions with proper error handling
    const clearError = () => setError(null);

    const handleAddAdmin = async () => {
        if (!username.trim() || !password.trim()) {
            setError('Username and password are required');
            return;
        }
        clearError();

        try {
            const result = await createAdmin({
                username: username.trim(),
                password: password.trim()
            });
            
            if (!result.success) {
                setError(result.message || 'Failed to add admin');
                return;
            }
            
            // Clear form on success
            setUsername('');
            setPassword('');
        } catch (error) {
            if (error instanceof ConvexError) {
                const errorData = error.data as { message: string };
                setError(errorData.message);
            } else {
                setError(error instanceof Error ? error.message : 'Failed to add admin');
            }
            debugError('Error adding admin:', error);
        }
    };

    const startEditing = useCallback((admin: Admin) => {
        setEditingAdmin(admin);
        setEditingUsername(admin.username);
    }, []);

    const handleEditAdmin = useCallback(async (admin: Admin, newUsername: string) => {
        if (!newUsername.trim() || newUsername === admin.username) {
            return setEditingAdmin(null);
        }

        try {
            await updateAdminUsername({
                adminId: admin.id as Id<"users">,
                username: newUsername.trim()
            });
            
            setEditingAdmin(null);
            setEditSuccessId(admin.id);
            setTimeout(() => {
                setEditSuccessId(null);
            }, 600);
            clearError();
        } catch (error) {
            if (error instanceof ConvexError) {
                const errorData = error.data as { message: string };
                setError(errorData.message);
            } else {
                setError(error instanceof Error ? error.message : 'Failed to update admin');
            }
            debugError('Error updating admin:', error);
        }
    }, [updateAdminUsername]);

    const handleChangePassword = async () => {
        if (!selectedAdmin || !newPassword.trim()) {
            setError('Password is required');
            return;
        }

        try {
            const result = await updateAdminPassword({
                adminId: selectedAdmin.id as Id<"users">,
                password: newPassword.trim()
            });
            
            if (!result.success) {
                setError(result.message || 'Failed to update password');
                return;
            }

            setShowPasswordModal(false);
            setSelectedAdmin(null);
            setNewPassword('');
            setEditSuccessId(selectedAdmin.id);
            setTimeout(() => {
                setEditSuccessId(null);
            }, 600);
            clearError();
        } catch (error) {
            if (error instanceof ConvexError) {
                const errorData = error.data as { message: string };
                setError(errorData.message);
            } else {
                setError(error instanceof Error ? error.message : 'Failed to update password');
            }
            debugError('Error updating password:', error);
        }
    };

    const handleDeleteAdmin = async (adminId: string) => {
        if (session?.user?.id === adminId) {
            setError("You cannot delete your own admin account");
            return;
        }

        try {
            await deleteAdmin({
                adminId: adminId as Id<"users">
            });
            
            clearError();
        } catch (error) {
            if (error instanceof ConvexError) {
                const errorData = error.data as { message: string };
                setError(errorData.message);
            } else {
                setError(error instanceof Error ? error.message : 'Failed to delete admin');
            }
            debugError('Error deleting admin:', error);
        }
    };

    const handleKeyPress = useCallback((e: React.KeyboardEvent, admin: Admin) => {
        if (e.key === 'Enter') {
            handleEditAdmin(admin, editingUsername);
        } else if (e.key === 'Escape') {
            setEditingAdmin(null);
        }
    }, [editingUsername, handleEditAdmin]);

    const skeletonItems = useMemo(() => [1, 2, 3], []);
    
    // Loading state for Convex query
    const loading = admins === undefined;

    return (
        <div className="space-y-6">
            <h2 className="text-2xl font-bold text-primary">Admin Management</h2>
            
            <div className="space-y-4 p-4 border border-border rounded-md">
                <h3 className="text-lg font-semibold text-primary">Add New Admin</h3>
                <div className="space-y-4">
                    <Input
                        type="text"
                        placeholder="Enter username"
                        value={username}
                        onChange={(e) => setUsername(e.target.value)}
                        className="shadow-none focus-visible:ring-0 focus-visible:ring-offset-0"
                    />
                    <div className="relative">
                        <Input
                            type={showPassword ? "text" : "password"}
                            placeholder="Enter password"
                            value={password}
                            onChange={(e) => setPassword(e.target.value)}
                            className="shadow-none focus-visible:ring-0 focus-visible:ring-offset-0 pr-10"
                        />
                        <Button
                            variant="ghost"
                            size="sm"
                            className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                            onClick={() => setShowPassword(!showPassword)}
                            type="button"
                        >
                            {showPassword ? (
                                <EyeOff className="h-4 w-4 text-muted-foreground" />
                            ) : (
                                <Eye className="h-4 w-4 text-muted-foreground" />
                            )}
                        </Button>
                    </div>
                    <Button 
                        onClick={handleAddAdmin}
                        className="w-full"
                    >
                        ADD ADMIN
                    </Button>
                </div>
            </div>

            {error && (
                <div className="error-display">
                    {error}
                </div>
            )}
            
            {loading ? (
                <div className="space-y-3">
                    {skeletonItems.map((_, index) => (
                        <div 
                            key={index}
                            className="flex items-center gap-4 p-4 border border-border rounded-md"
                        >
                            <div className="flex-1 h-4 bg-muted rounded animate-pulse"></div>
                            <div className="w-20 h-8 bg-muted rounded animate-pulse"></div>
                        </div>
                    ))}
                </div>
            ) : (
                <>
                    {admins && admins.length === 0 && !error ? (
                        <div className="empty-state">No admin users found</div>
                    ) : (
                        <div className="space-y-3">
                            {admins && admins.map((admin: Admin) => (
                                <div 
                                    key={admin.id}
                                    className={`flex items-center gap-4 p-4 border border-border rounded-md hover:bg-muted/50 transition-colors ${
                                        editingAdmin?.id === admin.id ? 'bg-muted/50' : ''
                                    } ${editSuccessId === admin.id ? 'border-green-500 bg-green-50' : ''}`}
                                >
                                    <div className="flex-1">
                                        {editingAdmin?.id === admin.id ? (
                                            <Input
                                                type="text"
                                                value={editingUsername}
                                                onChange={(e) => setEditingUsername(e.target.value)}
                                                onKeyDown={(e) => handleKeyPress(e, admin)}
                                                onBlur={() => handleEditAdmin(admin, editingUsername)}
                                                autoFocus
                                                className="shadow-none focus-visible:ring-0 focus-visible:ring-offset-0"
                                            />
                                        ) : (
                                            <div
                                                className="text-foreground hover:text-primary transition-colors cursor-text font-medium"
                                                onClick={() => startEditing(admin)}
                                            >
                                                {admin.username}
                                            </div>
                                        )}
                                    </div>
                                    <div className="flex items-center gap-2">
                                        <Button
                                            variant="ghost"
                                            size="sm"
                                            onClick={() => {
                                                setSelectedAdmin(admin);
                                                setShowPasswordModal(true);
                                            }}
                                            className="h-8 w-8 p-0 hover:!bg-transparent hover:text-primary"
                                            title="Change password"
                                        >
                                            <Key className="h-4 w-4" />
                                        </Button>
                                        <Button
                                            variant="ghost"
                                            size="sm"
                                            onClick={() => handleDeleteAdmin(admin.id)}
                                            disabled={session?.user?.id === admin.id}
                                            className="h-8 w-8 p-0 hover:!bg-transparent hover:text-primary disabled:opacity-50"
                                            title={session?.user?.id === admin.id ? 'Cannot delete your own account' : 'Delete admin'}
                                        >
                                            <Trash2 className="h-4 w-4" />
                                        </Button>
                                    </div>
                                </div>
                            ))}
                        </div>
                    )}
                </>
            )}

            <AlertDialog open={showPasswordModal} onOpenChange={setShowPasswordModal}>
                <AlertDialogContent>
                    <AlertDialogHeader>
                        <AlertDialogTitle>Change Password</AlertDialogTitle>
                        <AlertDialogDescription>
                            Change password for {selectedAdmin?.username}
                        </AlertDialogDescription>
                    </AlertDialogHeader>
                    <div className="py-4">
                        <div className="relative">
                            <Input
                                type={showNewPassword ? "text" : "password"}
                                placeholder="Enter new password"
                                value={newPassword}
                                onChange={(e) => setNewPassword(e.target.value)}
                                className="shadow-none focus-visible:ring-0 focus-visible:ring-offset-0 pr-10"
                            />
                            <Button
                                variant="ghost"
                                size="sm"
                                className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                                onClick={() => setShowNewPassword(!showNewPassword)}
                                type="button"
                            >
                                {showNewPassword ? (
                                    <EyeOff className="h-4 w-4 text-muted-foreground" />
                                ) : (
                                    <Eye className="h-4 w-4 text-muted-foreground" />
                                )}
                            </Button>
                        </div>
                    </div>
                    <AlertDialogFooter>
                        <AlertDialogCancel 
                            onClick={() => {
                                setShowPasswordModal(false);
                                setSelectedAdmin(null);
                                setNewPassword('');
                            }}
                        >
                            Cancel
                        </AlertDialogCancel>
                        <AlertDialogAction
                            onClick={handleChangePassword}
                        >
                            Save Password
                        </AlertDialogAction>
                    </AlertDialogFooter>
                </AlertDialogContent>
            </AlertDialog>
        </div>
    );
}
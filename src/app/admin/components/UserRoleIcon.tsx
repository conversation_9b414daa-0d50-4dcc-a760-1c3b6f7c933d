interface UserRoleIconProps {
  role: 'teamLead' | 'teamMember';
  className?: string;
  onClick?: () => void;
}

export default function UserRoleIcon({ role, className, onClick }: UserRoleIconProps) {
  if (role === 'teamLead') {
    // Team Lead Icon (crown/diamond shape)
    return (
      <button 
        onClick={onClick}
        className={`${className} ${onClick ? 'cursor-pointer hover:scale-105 transition-transform' : ''}`}
        title="Team Lead - Click to change role"
      >
        <svg 
          className="w-full h-full text-primary"
          viewBox="0 0 714.48 667.43" 
          xmlns="http://www.w3.org/2000/svg"
          fill="currentColor"
        >
          <polygon points="614.5 592.44 507.19 667.43 396.95 602.74 396.95 542.47 493.97 504.27 493.97 461.6 358.7 513.4 358.7 514.53 357.24 513.96 355.78 514.53 355.78 513.4 220.52 461.6 220.52 504.27 317.58 542.47 317.58 602.74 207.3 667.43 99.98 592.44 127.93 389.56 0 280.79 86.76 207.3 133.81 221.98 138.18 273.45 226.4 314.62 226.4 382.22 274.91 382.22 263.14 236.7 95.56 163.17 92.64 77.91 213.18 0 357.24 42.2 501.31 0 621.89 77.91 618.93 163.17 451.34 236.7 439.58 382.22 488.08 382.22 488.08 314.62 576.3 273.45 580.72 221.98 627.77 207.3 714.48 280.79 586.6 389.56 614.5 592.44"/>
        </svg>
      </button>
    );
  } else {
    // Team Member Icon (circle)
    return (
      <button 
        onClick={onClick}
        className={`${className} ${onClick ? 'cursor-pointer hover:scale-105 transition-transform' : ''}`}
        title="Team Member - Click to change role"
      >
        <svg 
          className="w-full h-full text-secondary"
          viewBox="0 0 24 24" 
          xmlns="http://www.w3.org/2000/svg"
          fill="currentColor"
        >
          <circle cx="12" cy="12" r="10"/>
        </svg>
      </button>
    );
  }
}
"use client";

import { StateCard } from '@/components/ui/state-card';
import { Button } from '@/components/ui/button';

interface BackupErrorBoundaryProps {
    error: Error;
    resetErrorBoundary: () => void;
    title?: string;
    message?: string;
}

export default function BackupErrorBoundary({ 
    error, 
    resetErrorBoundary, 
    title = "Backup Section Error",
    message 
}: BackupErrorBoundaryProps) {
    const errorMessage = message || `Error loading this section: ${error.message}`;

    return (
        <div className="flex justify-center items-center min-h-[300px] p-6">
            <div className="space-y-4 w-full max-w-md">
                <StateCard
                    state="error"
                    title={title}
                    message={errorMessage}
                />
                <div className="flex justify-center">
                    <Button 
                        onClick={resetErrorBoundary}
                    >
                        Try Again
                    </Button>
                </div>
                {process.env.NODE_ENV === 'development' && (
                    <div className="mt-4 p-4 bg-destructive/10 border border-destructive/20 text-left">
                        <div className="text-sm font-mono text-destructive">
                            {error.message}
                        </div>
                        {error.stack && (
                            <div className="text-xs font-mono text-destructive mt-2 opacity-70">
                                {error.stack}
                            </div>
                        )}
                    </div>
                )}
            </div>
        </div>
    );
}
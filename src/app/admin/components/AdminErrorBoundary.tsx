"use client";

import { Component, ReactNode } from 'react';
import { StateCard } from '@/components/ui/state-card';
import { Button } from '@/components/ui/button';
import { signOut } from 'next-auth/react';

interface AdminErrorBoundaryProps {
    children: ReactNode;
    fallbackTitle?: string;
    fallbackMessage?: string;
}

interface AdminErrorBoundaryState {
    hasError: boolean;
    error?: Error;
}

export default class AdminErrorBoundary extends Component<
    AdminErrorBoundaryProps,
    AdminErrorBoundaryState
> {
    constructor(props: AdminErrorBoundaryProps) {
        super(props);
        this.state = { hasError: false };
    }

    static getDerivedStateFromError(error: Error): AdminErrorBoundaryState {
        return { hasError: true, error };
    }

    componentDidUpdate(_prevProps: AdminErrorBoundaryProps, prevState: AdminErrorBoundaryState) {
        // Auto-redirect when auth error state is first set
        if (!prevState.hasError && this.state.hasError && this.state.error) {
            if (this.isAuthError(this.state.error)) {
                // Immediate redirect for auth errors
                this.handleSignOut();
            }
        }
    }

    componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
        console.error('Admin Error Boundary:', error, errorInfo);
        
        // Auto-redirect on authentication errors
        if (this.isAuthError(error)) {
            this.handleSignOut();
        }
    }

    private isAuthError(error: Error): boolean {
        // Check if it's a ConvexError and extract the correct error message
        let errorMessage = error.message;
        
        // For ConvexError, check the data property
        if (error.name === 'ConvexError' && (error as { data?: unknown }).data) {
            const errorData = (error as unknown as { data: { message?: string; code?: string } }).data;
            errorMessage = errorData.message || errorMessage;
            
            // Also check the code property
            if (errorData.code) {
                return errorData.code === 'USER_NOT_FOUND' ||
                       errorData.code === 'UNAUTHORIZED' ||
                       errorData.code === 'ACCESS_DENIED' ||
                       errorData.code === 'AUTHENTICATION_REQUIRED';
            }
        }
        
        return errorMessage.includes('User not found') || 
               errorMessage.includes('USER_NOT_FOUND') ||
               errorMessage.includes('UNAUTHORIZED') ||
               errorMessage.includes('ACCESS_DENIED') ||
               errorMessage.includes('AUTHENTICATION_REQUIRED') ||
               errorMessage.includes('Please check your login credentials');
    }

    private handleSignOut = async () => {
        try {
            await signOut({ callbackUrl: '/login' });
        } catch (error) {
            console.error('Error during sign out:', error);
            // Fallback: redirect directly to login
            window.location.href = '/login';
        }
    };

    render() {
        if (this.state.hasError) {
            const isAuthError = this.state.error && this.isAuthError(this.state.error);
            
            // Don't render anything for auth errors - just redirect
            if (isAuthError) {
                return null;
            }
            
            return (
                <div className="flex justify-center items-center min-h-[400px]">
                    <div className="space-y-4">
                        <StateCard
                            state="error"
                            title={this.props.fallbackTitle || "Something went wrong"}
                            message={this.props.fallbackMessage || "There was an error loading this section."}
                        />
                        <div className="flex justify-center gap-3">
                            <Button
                                onClick={() => this.setState({ hasError: false })}
                            >
                                Try Again
                            </Button>
                        </div>
                        {process.env.NODE_ENV === 'development' && this.state.error && (
                            <div className="mt-4 p-4 bg-destructive/10 border border-destructive/20 text-left max-w-[600px]">
                                <div className="text-sm font-mono text-destructive">
                                    {this.state.error.message}
                                </div>
                            </div>
                        )}
                    </div>
                </div>
            );
        }

        return this.props.children;
    }
}
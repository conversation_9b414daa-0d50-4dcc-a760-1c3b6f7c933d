"use client";

import { useRouter } from 'next/navigation';
import { signOut } from 'next-auth/react';
import { debugError } from '@/lib/utils';
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
} from "@/components/ui/sidebar";
import { Button } from "@/components/ui/button";
import { 
  Settings,
  Users,
  Calendar,
  FileText,
  Zap,
  Target,
  Vote,
  Sparkles,
  Code,
  BarChart3,
  Archive,
  Shield,
  UsersRound,
  CheckCircle,
} from "lucide-react";

interface AdminSidebarProps {
  activeSection: string;
  onSectionChange: (section: string) => void;
}

export default function AdminSidebar({ activeSection, onSectionChange }: AdminSidebarProps) {
  const router = useRouter();

  // Navigation items with icons
  const navigationItems = [
    { id: 'events', label: 'Events', icon: Calendar },
    { id: 'sessions', label: 'Sessions', icon: FileText },
    { id: 'teams', label: 'Teams', icon: UsersRound },
    { id: 'users', label: 'Users', icon: Users },
    { id: 'ideas', label: 'Ideas', icon: Target },
    { id: 'quickfire', label: 'Quickfire', icon: Zap },
    { id: 'voting', label: 'Voting', icon: Vote },
    { id: 'sparks', label: 'Sparks', icon: Sparkles },
    { id: 'snippets', label: 'Snippets', icon: Code },
    { id: 'data', label: 'Data', icon: BarChart3 },
    { id: 'backups', label: 'Backups', icon: Archive },
    { id: 'online', label: 'Online Users', icon: Users },
    { id: 'admins', label: 'Admins', icon: Shield },
    { id: 'approvals', label: 'Approvals', icon: CheckCircle },
    { id: 'settings', label: 'Settings', icon: Settings },
  ];

  const handleNavigation = (section: string) => {
    onSectionChange(section);
    router.replace(`/admin?section=${section}`, { scroll: false });
  };

  const handleLogout = async () => {
    try {
      await signOut({ redirect: false });
      router.push('/login');
    } catch (error) {
      debugError('Logout error:', error);
    }
  };

  return (
    <Sidebar className="bg-sidebar border-r border-border text-sidebar-foreground">
      {/* Sidebar Header */}
      <SidebarHeader className="h-20 p-4 gap-0 border-b border-border flex-shrink-0">
        <div className="flex items-center gap-4">
          <svg 
            className="w-12 h-12 text-primary" 
            viewBox="0 0 714.48 667.43"
            fill="currentColor"
          >
            <polygon points="614.5 592.44 507.19 667.43 396.95 602.74 396.95 542.47 493.97 504.27 493.97 461.6 358.7 513.4 358.7 514.53 357.24 513.96 355.78 514.53 355.78 513.4 220.52 461.6 220.52 504.27 317.58 542.47 317.58 602.74 207.3 667.43 99.98 592.44 127.93 389.56 0 280.79 86.76 207.3 133.81 221.98 138.18 273.45 226.4 314.62 226.4 382.22 274.91 382.22 263.14 236.7 95.56 163.17 92.64 77.91 213.18 0 357.24 42.2 501.31 0 621.89 77.91 618.93 163.17 451.34 236.7 439.58 382.22 488.08 382.22 488.08 314.62 576.3 273.45 580.72 221.98 627.77 207.3 714.48 280.79 586.6 389.56 614.5 592.44"/>
          </svg>
          <div className="flex flex-col">
            <span className="text-sidebar-foreground font-medium text-sm">Admin</span>
            <span className="text-accent text-xs">Administrator</span>
          </div>
        </div>
      </SidebarHeader>

      {/* Sidebar Content */}
      <SidebarContent className="px-0 py-0">
        <SidebarMenu>
          {navigationItems.map((item) => {
            const Icon = item.icon;
            return (
              <SidebarMenuItem key={item.id}>
                <SidebarMenuButton
                  onClick={() => handleNavigation(item.id)}
                  isActive={activeSection === item.id}
                  className={`w-full justify-start text-left font-medium transition-colors ${
                    activeSection === item.id
                      ? 'bg-primary text-primary-foreground hover:bg-primary hover:text-primary-foreground'
                      : 'text-sidebar-foreground hover:bg-sidebar-accent hover:text-sidebar-accent-foreground'
                  }`}
                >
                  <Icon className="h-5 w-5 mr-4" />
                  {item.label}
                </SidebarMenuButton>
              </SidebarMenuItem>
            );
          })}
        </SidebarMenu>
      </SidebarContent>

      {/* Sidebar Footer */}
      <SidebarFooter className="p-4 border-t border-border">
        <Button
          onClick={handleLogout}
          className="w-full"
        >
          Logout
        </Button>
      </SidebarFooter>
    </Sidebar>
  );
}
"use client";

import { <PERSON><PERSON> } from '@/components/ui/button';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';
import { ChevronDown } from 'lucide-react';
import IdeasList from './IdeasList';
import SparkSubmissionsList from './SparkSubmissionsList';
import { Idea, SparkSubmission } from '../types/ideas-management';

interface TeamGroupProps {
    teamId: string;
    teamName: string;
    ideas: Idea[];
    sparkSubmissions: SparkSubmission[];
    withdrawingTeam: string | null;
    onWithdraw: (teamId: string, teamName: string) => void;
}

export default function TeamGroup({
    teamId,
    teamName,
    ideas,
    sparkSubmissions,
    withdrawingTeam,
    onWithdraw
}: TeamGroupProps) {
    const hasSubmittedIdeas = ideas.some(idea => idea.submitted);
    
    // Calculate total count of all content types for this team
    const totalCount = ideas.length + sparkSubmissions.length;

    return (
        <Collapsible defaultOpen={true} className="space-y-6">
            {/* Team Header - Collapsible trigger */}
            <div className="flex flex-col sm:flex-row sm:items-center gap-4">
                <CollapsibleTrigger className="flex items-center gap-2 group flex-1 min-w-0">
                    <ChevronDown className="h-6 w-6 transition-transform duration-200 group-data-[state=open]:rotate-180 text-primary shrink-0" />
                    <h1 className="text-4xl text-primary truncate">{teamName}</h1>
                </CollapsibleTrigger>
                
                <div className="flex items-center gap-4">
                    <div className="flex items-center justify-center w-10 h-10 border-2 border-secondary text-secondary text-lg font-bold">
                        {totalCount}
                    </div>
                    
                    {hasSubmittedIdeas && (
                        <Button
                            onClick={() => onWithdraw(teamId, teamName)}
                            disabled={withdrawingTeam === teamId}
                            className="shrink-0 h-10"
                        >
                            {withdrawingTeam === teamId ? 'Withdrawing...' : 'Withdraw'}
                        </Button>
                    )}
                </div>
            </div>

            {/* Content Lists - Collapsible content */}
            <CollapsibleContent className="space-y-6">
                <IdeasList ideas={ideas} />
                <SparkSubmissionsList sparkSubmissions={sparkSubmissions} />
            </CollapsibleContent>
        </Collapsible>
    );
}

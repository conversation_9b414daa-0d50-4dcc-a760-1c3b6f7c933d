"use client";

import { Card, CardContent } from '@/components/ui/card';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';
import { ChevronDown } from 'lucide-react';
import { Idea } from '../types/ideas-management';
import TiptapContentDisplay from '@/components/editors/TiptapContentDisplay';

interface IdeasListProps {
    ideas: Idea[];
}

export default function IdeasList({ ideas }: IdeasListProps) {
    if (ideas.length === 0) {
        return null;
    }

    return (
        <div className="space-y-4">
            <h3 className="text-lg font-semibold text-primary">Ideas</h3>
            <div className="space-y-4">
                {ideas.map((idea) => (
                    <Card
                        key={idea._id}
                        className={`transition-all duration-200 rounded-none ${
                            idea.submitted 
                                ? 'border-2 border-[#15B14B]' 
                                : 'border-2 border-[#FED107]'
                        }`}
                    >
                        <CardContent className="space-y-4">
                            {/* Header */}
                            <div className="flex flex-col sm:flex-row sm:items-center gap-3">
                                <h4 className="text-xl font-ultrabold text-secondary flex-1 min-w-0 truncate">
                                    {idea.name}
                                </h4>
                                <div className={`px-2 py-1 text-xs font-medium border-2 shrink-0 self-start sm:self-auto ${
                                    idea.submitted 
                                        ? 'border-[#15B14B] text-[#15B14B]' 
                                        : 'border-[#FED107] text-[#FED107]'
                                }`}>
                                    {idea.submitted ? 'Submitted' : 'Added'}
                                </div>
                            </div>
                            
                            {/* Description */}
                            <Collapsible defaultOpen={false} className="mt-2">
                                <CollapsibleTrigger className="flex items-center gap-2 text-base text-secondary hover:text-accent transition-colors group">
                                    <ChevronDown className="h-5 w-5 transition-transform duration-200 group-data-[state=open]:rotate-180 text-accent" />
                                    <span className="group-data-[state=open]:hidden font-mono">See more</span>
                                    <span className="hidden group-data-[state=open]:block">See less</span>
                                </CollapsibleTrigger>
                                <CollapsibleContent className="mt-3">
                                    <TiptapContentDisplay content={idea.description} />
                                </CollapsibleContent>
                            </Collapsible>
                            
                            {/* Metadata */}
                            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3 pt-4 border-t border-primary/20 text-sm">
                                <div className="flex flex-col">
                                    <span className="text-secondary text-xs uppercase">Submitted by:</span>
                                    <span className="font-medium text-foreground">{idea.userName || 'Unknown User'}</span>
                                </div>
                                <div className="flex flex-col">
                                    <span className="text-secondary text-xs uppercase">Session:</span>
                                    <span className="font-medium text-foreground">{idea.sessionName || 'Unknown Session'}</span>
                                </div>
                                <div className="flex flex-col">
                                    <span className="text-secondary text-xs uppercase">Event:</span>
                                    <span className="font-medium text-foreground">{idea.eventName || 'Unknown Event'}</span>
                                </div>
                                <div className="flex flex-col">
                                    <span className="text-secondary text-xs uppercase">Last Updated:</span>
                                    <span className="font-medium text-foreground">
                                        {new Date(idea.updatedAt || idea.createdAt).toLocaleString()}
                                    </span>
                                </div>
                                <div className="flex flex-col sm:col-span-2">
                                    <span className="text-secondary text-xs uppercase">Presenters:</span>
                                    <span className="font-medium text-foreground">
                                        {idea.presenters && idea.presenters.length > 0
                                            ? idea.presenters.map(p => p.name).join(', ')
                                            : 'No presenters assigned'}
                                    </span>
                                </div>
                            </div>
                        </CardContent>
                    </Card>
                ))}
            </div>
        </div>
    );
}

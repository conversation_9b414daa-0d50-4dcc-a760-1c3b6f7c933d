"use client";

interface CountDisplayProps {
    label: string;
    count: number;
}

export default function CountDisplay({ label, count }: CountDisplayProps) {
    return (
        <div className="flex items-stretch border-2 border-primary min-h-[var(--select-height-default)]">
            <div className="flex items-center px-4 text-sm font-medium text-[var(--accent)]">
                {label}
            </div>
            <div className="flex items-center justify-center px-6 text-xl font-bold text-primary bg-card border-l-2 border-primary min-w-[80px]">
                {count}
            </div>
        </div>
    );
}
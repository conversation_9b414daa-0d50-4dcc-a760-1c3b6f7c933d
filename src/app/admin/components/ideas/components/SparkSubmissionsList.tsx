"use client";

import { Card, CardContent } from '@/components/ui/card';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';
import { ChevronDown } from 'lucide-react';
import { SparkSubmission } from '../types/ideas-management';
import TiptapContentDisplay from '@/components/editors/TiptapContentDisplay';

interface SparkSubmissionsListProps {
    sparkSubmissions: SparkSubmission[];
}

export default function SparkSubmissionsList({ sparkSubmissions }: SparkSubmissionsListProps) {
    if (sparkSubmissions.length === 0) {
        return null;
    }

    return (
        <div className="space-y-4">
            <h3 
                className="text-lg font-semibold"
                style={{ color: sparkSubmissions[0]?.sparkColor || '#0ee0d8' }}
            >
                {sparkSubmissions[0]?.sparkName || 'Spark Submissions'}
            </h3>
            <div className="space-y-4">
                {sparkSubmissions.map((submission) => (
                    <Card
                        key={submission._id}
                        className="transition-all duration-200 rounded-none border-2"
                        style={{ borderColor: submission.sparkColor || '#b96eff' }}
                    >
                        <CardContent className="space-y-4">
                            {/* Header */}
                            <div className="flex items-center justify-between">
                                <div 
                                    className="px-2 py-1 text-xs font-medium border-2"
                                    style={{ 
                                        borderColor: submission.sparkColor || '#b96eff',
                                        color: submission.sparkColor || '#b96eff'
                                    }}
                                >
                                    {submission.sparkName}
                                </div>
                            </div>
                            
                            {/* Dynamic Fields */}
                            <div className="space-y-3">
                                {submission.sparkFields.map((field, index) => {
                                    const fieldId = `field_${index}`;
                                    const value = submission.data?.[fieldId];
                                    
                                    const isEmpty =
                                        value === undefined || value === null ||
                                        (typeof value === 'string' && value.trim() === '');
                                    if (isEmpty && field.type !== 'checkbox') return null;
                                    
                                    return (
                                        <div key={fieldId} className="space-y-1">
                                            <div 
                                                className="text-sm font-medium"
                                                style={{ color: submission.sparkColor || '#0ee0d8' }}
                                            >
                                                {field.label}:
                                            </div>
                                            {field.type === 'richtext' ? (
                                                <Collapsible defaultOpen={false} className="mt-1">
                                                    <CollapsibleTrigger className="flex items-center gap-2 text-sm text-secondary hover:text-accent transition-colors group">
                                                        <ChevronDown className="h-5 w-5 transition-transform duration-200 group-data-[state=open]:rotate-180 text-accent" />
                                                        <span className="group-data-[state=open]:hidden font-mono">See more</span>
                                                        <span className="hidden group-data-[state=open]:block text-accent font-mono">See less</span>
                                                    </CollapsibleTrigger>
                                                    <CollapsibleContent className="mt-2">
                                                        <TiptapContentDisplay content={value as string} />
                                                    </CollapsibleContent>
                                                </Collapsible>
                                            ) : field.type === 'checkbox' ? (
                                                <div className="text-sm text-foreground font-medium">
                                                    {value ? 'Yes' : 'No'}
                                                </div>
                                            ) : (
                                                <div className="text-sm text-foreground font-medium">{value as string}</div>
                                            )}
                                        </div>
                                    );
                                })}
                            </div>
                            
                            {/* Metadata */}
                            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3 pt-4 border-t border-primary/20 text-sm">
                                <div className="flex flex-col">
                                    <span className="text-secondary text-xs uppercase">Submitted by:</span>
                                    <span className="font-medium text-foreground">{submission.userName}</span>
                                </div>
                                <div className="flex flex-col">
                                    <span className="text-secondary text-xs uppercase">Session:</span>
                                    <span className="font-medium text-foreground">{submission.sessionName}</span>
                                </div>
                                <div className="flex flex-col">
                                    <span className="text-secondary text-xs uppercase">Event:</span>
                                    <span className="font-medium text-foreground">{submission.eventName}</span>
                                </div>
                                <div className="flex flex-col">
                                    <span className="text-secondary text-xs uppercase">Last Updated:</span>
                                    <span className="font-medium text-foreground">
                                        {new Date(submission.updatedAt || submission.submittedAt).toLocaleString()}
                                    </span>
                                </div>
                            </div>
                        </CardContent>
                    </Card>
                ))}
            </div>
        </div>
    );
}
// Error types for better error handling
export enum ErrorType {
    NO_ACTIVE_EVENT = 'NO_ACTIVE_EVENT',
    NETWORK_ERROR = 'NETWORK_ERROR',
    VALIDATION_ERROR = 'VALIDATION_ERROR',
    PERMISSION_ERROR = 'PERMISSION_ERROR',
    UNKNOWN_ERROR = 'UNKNOWN_ERROR'
}

export interface StructuredError {
    type: ErrorType;
    message: string;
    code?: string;
    details?: Record<string, unknown>;
}

export class IdeasManagementError extends Error implements StructuredError {
    public type: ErrorType;
    public code?: string;
    public details?: Record<string, unknown>;

    constructor(type: ErrorType, message: string, code?: string, details?: Record<string, unknown>) {
        super(message);
        this.name = 'IdeasManagementError';
        this.type = type;
        this.code = code;
        this.details = details;
    }
}

export function createError(type: ErrorType, message: string, code?: string, details?: Record<string, unknown>): IdeasManagementError {
    return new IdeasManagementError(type, message, code, details);
}

export function parseError(error: unknown): StructuredError {
    if (error instanceof IdeasManagementError) {
        return error;
    }

    if (error instanceof Error) {
        // Check for specific error patterns
        if (error.message.includes('No active event')) {
            return createError(ErrorType.NO_ACTIVE_EVENT, error.message);
        }
        
        if (error.message.includes('network') || error.message.includes('fetch')) {
            return createError(ErrorType.NETWORK_ERROR, 'Network connection error. Please check your connection and try again.');
        }

        return createError(ErrorType.UNKNOWN_ERROR, error.message);
    }

    return createError(ErrorType.UNKNOWN_ERROR, 'An unexpected error occurred');
}

export function getErrorMessage(error: StructuredError): string {
    switch (error.type) {
        case ErrorType.NO_ACTIVE_EVENT:
            return 'No active event found. Please activate an event first.';
        case ErrorType.NETWORK_ERROR:
            return 'Connection error. Please check your network and try again.';
        case ErrorType.VALIDATION_ERROR:
            return error.message || 'Please check your input and try again.';
        case ErrorType.PERMISSION_ERROR:
            return 'You do not have permission to perform this action.';
        default:
            return error.message || 'An unexpected error occurred. Please try again.';
    }
}
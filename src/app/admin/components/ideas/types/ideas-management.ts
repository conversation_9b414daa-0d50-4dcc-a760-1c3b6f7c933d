export interface Idea {
    _id: string;
    name: string;
    description: string;
    eventId: string;
    sessionId: string;
    userId: string;
    teamId: string;
    createdAt: number;
    updatedAt?: number;
    submitted: boolean;
    eventName?: string;
    sessionName?: string;
    userName?: string;
    teamName?: string;
    presenters?: { id: string; name: string }[];
}



export interface SparkSubmission {
    _id: string;
    sessionId: string;
    sessionName: string;
    userId: string;
    userName: string;
    teamId: string;
    teamName: string;
    sparkId: string;
    sparkName: string;
    sparkColor: string;
    eventId: string;
    eventName: string;
    data: Record<string, string | boolean>;
    submittedAt: number;
    updatedAt?: number;
    // Spark field configuration for rendering
    sparkFields: Array<{
        type: 'text' | 'richtext' | 'dropdown' | 'radio' | 'checkbox';
        label: string;
        placeholder?: string;
        options?: string[];
        required?: boolean;
    }>;
}

export interface TeamGroup {
    teamName: string;
    ideas: Idea[];
    sparkSubmissions?: SparkSubmission[];
    lastUpdate: number;
}

export interface GroupedIdeas {
    [teamId: string]: TeamGroup;
}

export interface Session {
    id: string;
    name: string;
    active: boolean;
    eventId: string;
    type: string;
    createdAt: string;
}

export interface WithdrawConfirmData {
    teamId: string;
    teamName: string;
    sessionId: string;
}

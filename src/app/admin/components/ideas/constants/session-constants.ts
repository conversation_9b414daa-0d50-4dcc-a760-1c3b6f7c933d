// Session selection constants
export const SESSION_VALUES = {
    ALL: 'all',
    EMPTY: ''
} as const;

// Session type constants
export const SESSION_TYPES = {
    SPARKS: 'Sparks',
    IDEAS: 'Ideas',
    QUICKFIRE: 'Quickfire'
} as const;

// Error message constants
export const ERROR_MESSAGES = {
    NO_ACTIVE_EVENT: 'No active event found. Please activate an event first.',
    CANNOT_WITHDRAW_ALL_SESSIONS: 'Cannot withdraw from all sessions. Please select a specific session.',
    NO_SESSION_SELECTED: 'No session selected for withdrawal',
    WITHDRAW_FAILED: 'Failed to withdraw team data'
} as const;

export type SessionValue = typeof SESSION_VALUES[keyof typeof SESSION_VALUES];
export type SessionType = typeof SESSION_TYPES[keyof typeof SESSION_TYPES];
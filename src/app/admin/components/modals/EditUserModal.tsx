import { useState, useEffect } from 'react';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import {
    Dialog,
    DialogContent,
    DialogDescription,
    DialogFooter,
    DialogHeader,
    DialogTitle,
} from '@/components/ui/dialog';
import { Label } from '@/components/ui/label';

interface User {
    _id: string;
    name: string;
    username: string;
    role: 'admin' | 'teamLead' | 'teamMember';
    teamId?: string;
    teamName?: string;
}

interface EditUserModalProps {
    showEditUser: string | null;
    users: User[];
    onCancel: () => void;
    onConfirm: (userId: string, data: { name: string; username: string; password?: string }) => Promise<void>;
}

export function EditUserModal({
    showEditUser,
    users,
    onCancel,
    onConfirm
}: EditUserModalProps) {
    const [name, setName] = useState('');
    const [username, setUsername] = useState('');
    const [password, setPassword] = useState('');

    const currentUser = users.find(u => u._id === showEditUser);

    // Reset form when user changes
    useEffect(() => {
        if (currentUser) {
            setName(currentUser.name);
            setUsername(currentUser.username);
            setPassword('');
        }
    }, [currentUser]);

    const handleSubmit = async () => {
        if (!showEditUser || !name.trim() || !username.trim()) return;
        
        const data: { name: string; username: string; password?: string } = {
            name: name.trim(),
            username: username.trim()
        };
        
        if (password.trim()) {
            data.password = password;
        }
        
        await onConfirm(showEditUser, data);
        setName('');
        setUsername('');
        setPassword('');
    };

    return (
        <Dialog open={!!showEditUser} onOpenChange={(open) => !open && onCancel()}>
            <DialogContent>
                <DialogHeader>
                    <DialogTitle>Edit User</DialogTitle>
                    <DialogDescription>
                        Update user information. Leave password empty to keep current password.
                    </DialogDescription>
                </DialogHeader>
                
                <div className="space-y-4">
                    <div>
                        <Label>Name</Label>
                        <Input
                            placeholder="Full name"
                            value={name}
                            onChange={(e) => setName(e.target.value)}
                        />
                    </div>
                    
                    <div>
                        <Label>Username</Label>
                        <Input
                            placeholder="Username"
                            value={username}
                            onChange={(e) => setUsername(e.target.value)}
                        />
                    </div>
                    
                    <div>
                        <Label className="text-sm font-medium text-foreground mb-2 block">New Password (optional)</Label>
                        <Input
                            type="password"
                            placeholder="Leave empty to keep current password"
                            value={password}
                            onChange={(e) => setPassword(e.target.value)}
                        />
                    </div>
                </div>
                
                <DialogFooter>
                    <Button
                        variant="outline"
                        onClick={onCancel}
                    >
                        Cancel
                    </Button>
                    <Button
                        onClick={handleSubmit}
                        disabled={!name.trim() || !username.trim()}
                    >
                        Save Changes
                    </Button>
                </DialogFooter>
            </DialogContent>
        </Dialog>
    );
}
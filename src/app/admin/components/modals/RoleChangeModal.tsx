import { useState } from 'react';
import { Button } from '@/components/ui/button';
import {
    AlertDialog,
    AlertDialogAction,
    AlertDialogCancel,
    AlertDialogContent,
    AlertDialogDescription,
    AlertDialogFooter,
    AlertDialogHeader,
    AlertDialogTitle,
} from '@/components/ui/alert-dialog';

interface User {
    _id: string;
    name: string;
    username: string;
    role: 'admin' | 'teamLead' | 'teamMember';
    teamId?: string;
    teamName?: string;
}

type RoleConfirmState = null | string | {
    userId: string;
    username: string;
    hasIdeas?: boolean;
    teamId?: string;
};

interface RoleChangeModalProps {
    showRoleConfirm: RoleConfirmState;
    selectedUsers: Set<string>;
    onCancel: () => void;
    onBulkRoleChange: (role: 'teamLead' | 'teamMember') => Promise<void>;
    availableTeamLeads?: User[];
    onSingleRoleChange: (userId: string, currentRole: 'teamLead' | 'teamMember', newTeamLeadId?: string) => Promise<void>;
    currentUserRole?: 'teamLead' | 'teamMember';
    hasIdeas?: boolean;
    hasContent?: boolean;
    bulkActionBlocked?: {type: 'delete' | 'role', hasContentCount: number} | null;
}

export function RoleChangeModal({
    showRoleConfirm, 
    selectedUsers,
    onCancel,
    onBulkRoleChange,
    onSingleRoleChange,
    currentUserRole,
    bulkActionBlocked
}: RoleChangeModalProps) {
    const [isChanging, setIsChanging] = useState(false);
    
    return (
        <AlertDialog open={!!showRoleConfirm} onOpenChange={(open) => !open && onCancel()}>
            <AlertDialogContent>
                <AlertDialogHeader>
                    <AlertDialogTitle>Change User Role</AlertDialogTitle>
                    <AlertDialogDescription>
                        {showRoleConfirm === 'bulk' ? (
                            bulkActionBlocked ? (
                                <>
                                    Cannot change roles in bulk. {bulkActionBlocked.hasContentCount} selected team lead(s) have content that requires individual transfer.
                                </>
                            ) : (
                                `Change role of ${selectedUsers.size} selected users to:`
                            )
                        ) : typeof showRoleConfirm === 'object' && showRoleConfirm ? (
                            <>
                                Do you want to switch role of user &quot;{showRoleConfirm.username}&quot; to {
                                    currentUserRole === 'teamLead' ? 'Team Member' : 'Team Lead'
                                }?
                            </>
                        ) : null}
                    </AlertDialogDescription>
                </AlertDialogHeader>
                
                
                <AlertDialogFooter>
                    <AlertDialogCancel 
                    >
                        {bulkActionBlocked ? 'Close' : 'Cancel'}
                    </AlertDialogCancel>
                    
                    {/* Bulk Role Change Buttons */}
                    {showRoleConfirm === 'bulk' && !bulkActionBlocked && (
                        <>
                            <Button
                                onClick={() => onBulkRoleChange('teamMember')}
                            >
                                Team Member
                            </Button>
                            <Button
                                onClick={() => onBulkRoleChange('teamLead')}
                            >
                                Team Lead
                            </Button>
                        </>
                    )}
                    
                    {/* Single Role Change - Unified Simple Confirmation */}
                    {showRoleConfirm !== 'bulk' && showRoleConfirm && typeof showRoleConfirm === 'object' && (
                        <AlertDialogAction
                            onClick={async () => {
                                setIsChanging(true);
                                const newRole = currentUserRole === 'teamLead' ? 'teamMember' : 'teamLead';
                                await onSingleRoleChange(showRoleConfirm.userId, newRole);
                                setIsChanging(false);
                            }}
                            disabled={isChanging}
                        >
                            {isChanging ? 'Changing...' : 'Confirm'}
                        </AlertDialogAction>
                    )}
                </AlertDialogFooter>
            </AlertDialogContent>
        </AlertDialog>
    );
}
import { useState } from 'react';
import { Input } from '@/components/ui/input';
import { Checkbox } from '@/components/ui/checkbox';
import { 
    Select, 
    SelectContent, 
    SelectItem, 
    SelectTrigger, 
    SelectValue 
} from '@/components/ui/select';
import {
    AlertDialog,
    AlertDialogAction,
    AlertDialogCancel,
    AlertDialogContent,
    AlertDialogDescription,
    AlertDialogFooter,
    AlertDialogHeader,
    AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import { Label } from '@/components/ui/label';

interface Team {
    _id: string;
    name: string;
}

interface AddUserModalProps {
    showAddUser: boolean;
    teams: Team[];
    onCancel: () => void;
    onAddUser: (userData: {
        username: string;
        name: string;
        password: string;
        teamId?: string;
        role: 'teamLead' | 'teamMember';
    }) => Promise<void>;
}

export function AddUserModal({
    showAddUser,
    teams,
    onCancel,
    onAddUser
}: AddUserModalProps) {
    const [username, setUsername] = useState('');
    const [name, setName] = useState('');
    const [password, setPassword] = useState('');
    const [selectedTeamId, setSelectedTeamId] = useState<string | undefined>(undefined);
    const [isTeamLead, setIsTeamLead] = useState(false);
    const [isSubmitting, setIsSubmitting] = useState(false);

    const handleSubmit = async () => {
        if (!username.trim() || !name.trim() || !password.trim()) {
            return;
        }

        setIsSubmitting(true);
        try {
            await onAddUser({
                username: username.trim(),
                name: name.trim(),
                password: password.trim(),
                teamId: selectedTeamId === "no-team" ? undefined : selectedTeamId,
                role: isTeamLead ? 'teamLead' : 'teamMember'
            });

            // Reset form on success
            setUsername('');
            setName('');
            setPassword('');
            setSelectedTeamId(undefined);
            setIsTeamLead(false);
        } finally {
            setIsSubmitting(false);
        }
    };

    return (
        <AlertDialog open={showAddUser} onOpenChange={(open) => !open && onCancel()}>
            <AlertDialogContent>
                <AlertDialogHeader>
                    <AlertDialogTitle>Add New User</AlertDialogTitle>
                    <AlertDialogDescription>
                        Create a new user account and assign them to a team.
                    </AlertDialogDescription>
                </AlertDialogHeader>
                
                <div className="space-y-4 py-4">
                    <div>
                        <Label className="text-sm font-medium mb-1 block">Name</Label>
                        <Input
                            placeholder="Enter user's full name"
                            value={name}
                            onChange={(e) => setName(e.target.value)}
                        />
                    </div>
                    
                    <div>
                        <Label className="text-sm font-medium mb-1 block">Username</Label>
                        <Input
                            placeholder="Enter username"
                            value={username}
                            onChange={(e) => setUsername(e.target.value)}
                        />
                    </div>
                    
                    <div>
                        <Label className="text-sm font-medium mb-1 block">Password</Label>
                        <Input
                            type="password"
                            placeholder="Enter password"
                            value={password}
                            onChange={(e) => setPassword(e.target.value)}
                        />
                    </div>
                    
                    <div>
                        <Label className="text-sm font-medium mb-1 block">Team</Label>
                        <Select onValueChange={setSelectedTeamId}>
                            <SelectTrigger>
                                <SelectValue placeholder="Select team or leave unassigned" />
                            </SelectTrigger>
                            <SelectContent>
                                <SelectItem value="no-team">No Team</SelectItem>
                                {teams.map(team => (
                                    <SelectItem key={team._id} value={team._id}>
                                        {team.name}
                                    </SelectItem>
                                ))}
                            </SelectContent>
                        </Select>
                    </div>
                    
                    <div className="flex items-center space-x-2">
                        <Checkbox
                            id="teamLead"
                            checked={isTeamLead}
                            onCheckedChange={(checked) => setIsTeamLead(checked as boolean)}
                        />
                        <Label htmlFor="teamLead" className="text-sm">Team Lead</Label>
                    </div>
                </div>
                
                <AlertDialogFooter>
                    <AlertDialogCancel 
                    >
                        Cancel
                    </AlertDialogCancel>
                    <AlertDialogAction
                        onClick={handleSubmit}
                        disabled={isSubmitting || !username.trim() || !name.trim() || !password.trim()}
                    >
                        {isSubmitting ? 'Creating...' : 'Create User'}
                    </AlertDialogAction>
                </AlertDialogFooter>
            </AlertDialogContent>
        </AlertDialog>
    );
}
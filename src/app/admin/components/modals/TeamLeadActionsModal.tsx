import { useState } from 'react';
import { 
    Select, 
    SelectContent, 
    SelectItem, 
    SelectTrigger, 
    SelectValue 
} from '@/components/ui/select';
import {
    AlertDialog,
    AlertDialogAction,
    AlertDialogCancel,
    AlertDialogContent,
    AlertDialogDescription,
    AlertDialogFooter,
    AlertDialogHeader,
    AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import Image from 'next/image';

interface User {
    _id: string;
    name: string;
    username: string;
    role: 'admin' | 'teamLead' | 'teamMember';
    teamId?: string;
    teamName?: string;
}

interface TeamLeadActionsModalProps {
    showTeamLeadActions: { 
        userId: string; 
        username: string; 
        hasIdeas: boolean; 
        hasContent?: boolean; 
        teamId?: string;
        newTeamId?: string; 
        isTeamChange?: boolean;
        actionType?: 'delete' | 'roleChange' | 'teamChange'; 
    } | null;
    availableTeamLeads: User[];
    onCancel: () => void;
    onConfirmTransfer: (newTeamLeadId: string, newTeamId?: string) => Promise<void>;
    onConfirmDelete?: (userId: string) => Promise<void>;
    userHasVotes?: boolean;
}

export function TeamLeadActionsModal({
    showTeamLeadActions,
    availableTeamLeads,
    onCancel,
    onConfirmTransfer,
    onConfirmDelete,
    userHasVotes
}: TeamLeadActionsModalProps) {
    const [selectedTeamLead, setSelectedTeamLead] = useState<string | null>(null);
    const [isTransferring, setIsTransferring] = useState(false);

    if (!showTeamLeadActions) return null;

    const hasContent = showTeamLeadActions.hasContent ?? showTeamLeadActions.hasIdeas;
    const actionType = showTeamLeadActions.actionType ?? 'delete';
    
    // Dynamic text based on action type
    const actionLabels = {
        delete: { title: 'Delete Team Lead', action: 'deletion', verb: 'delete' },
        roleChange: { title: 'Change Role', action: 'role change', verb: 'change role' },
        teamChange: { title: 'Change Team', action: 'team change', verb: 'change team' }
    };
    const labels = actionLabels[actionType];

    return (
        <AlertDialog open={!!showTeamLeadActions} onOpenChange={(open) => !open && onCancel()}>
            <AlertDialogContent>
                <AlertDialogHeader>
                    <AlertDialogTitle>
                        {labels.title}
                    </AlertDialogTitle>
                    <AlertDialogDescription>
                        {hasContent ? (
                            <>
                                User &quot;{showTeamLeadActions.username}&quot; has content associated with them.
                                {availableTeamLeads.length === 0 
                                    ? ' Please assign at least one team lead to transfer the ideas and spark submissions to.'
                                    : ' Please select a team lead to transfer the ideas and spark submissions to:'}
                                <br/>
                                {actionType === 'delete' && userHasVotes && (
                                    <span className="text-destructive font-medium">
                                        Note: This will also permanently delete all votes cast by this user.
                                    </span>
                                )}
                            </>
                        ) : (
                            <>
                                Confirm {labels.action} of team lead &quot;{showTeamLeadActions.username}&quot;. 
                                This user has no content associated with them. {labels.title} can proceed safely.
                                {actionType === 'delete' && userHasVotes && (
                                    <>
                                        <br/>
                                        <span className="text-destructive font-medium">
                                            This will permanently delete all votes cast by this user.
                                        </span>
                                    </>
                                )}
                                {actionType === 'delete' && !userHasVotes && (
                                    <>
                                        <br/>
                                        <span className="text-muted-foreground">This user has no votes to delete.</span>
                                    </>
                                )}
                            </>
                        )}
                    </AlertDialogDescription>
                </AlertDialogHeader>
                
                {availableTeamLeads.length > 0 && hasContent && (
                    <div className="py-4">
                        <Select onValueChange={setSelectedTeamLead}>
                            <SelectTrigger>
                                <SelectValue placeholder="Select team lead..." />
                            </SelectTrigger>
                            <SelectContent>
                                {availableTeamLeads
                                    .filter(teamLead => teamLead._id !== showTeamLeadActions.userId)
                                    .map(teamLead => (
                                        <SelectItem key={teamLead._id} value={teamLead._id}>
                                            <div className="flex items-center gap-2">
                                                <Image
                                                    src="/svgs/Asset 3.svg"
                                                    alt="Team Lead"
                                                    width={20}
                                                    height={20}
                                                />
                                                <div className="font-medium">{teamLead.name}</div>
                                            </div>
                                        </SelectItem>
                                    ))}
                            </SelectContent>
                        </Select>
                    </div>
                )}
                
                <AlertDialogFooter>
                    <AlertDialogCancel 
                    >
                        Cancel
                    </AlertDialogCancel>
                    
                    {hasContent ? (
                        selectedTeamLead && availableTeamLeads.length > 0 && (
                            <AlertDialogAction
                                onClick={async () => {
                                    setIsTransferring(true);
                                    await onConfirmTransfer(selectedTeamLead, showTeamLeadActions.newTeamId);
                                    setIsTransferring(false);
                                }}
                                disabled={isTransferring}
                            >
                                {isTransferring ? 'Transferring...' : 'Confirm'}
                            </AlertDialogAction>
                        )
                    ) : (
                        onConfirmDelete && (
                            <AlertDialogAction
                                onClick={async () => {
                                    setIsTransferring(true);
                                    await onConfirmDelete(showTeamLeadActions.userId);
                                    setIsTransferring(false);
                                }}
                                disabled={isTransferring}
                            >
                                {isTransferring ? 'Processing...' : `Confirm ${labels.title}`}
                            </AlertDialogAction>
                        )
                    )}
                </AlertDialogFooter>
            </AlertDialogContent>
        </AlertDialog>
    );
}
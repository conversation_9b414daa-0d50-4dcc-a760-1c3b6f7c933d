import { useQuery } from "convex/react";
import { api } from "@/../convex/_generated/api";
import { Id } from "@/../convex/_generated/dataModel";
import {
    AlertDialog,
    AlertDialogCancel,
    AlertDialogContent,
    AlertDialogDescription,
    AlertDialogFooter,
    AlertDialogHeader,
    AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import Image from 'next/image';

interface IdeaStatsModalProps {
    showIdeaStats: string | null;
    onClose: () => void;
}

export function IdeaStatsModal({
    showIdeaStats,
    onClose
}: IdeaStatsModalProps) {
    // Use Convex query to get user content stats
    const userContentData = useQuery(
        api.users.getUserIdeasForDeletion, 
        showIdeaStats ? { userId: showIdeaStats as Id<"users"> } : "skip"
    );

    const loading = userContentData === undefined;

    return (
        <AlertDialog open={!!showIdeaStats} onOpenChange={(open) => !open && onClose()}>
            <AlertDialogContent>
                <AlertDialogHeader>
                    <AlertDialogTitle>User Content Statistics</AlertDialogTitle>
                    <AlertDialogDescription>
                        View detailed statistics for this user&apos;s content and activity.
                    </AlertDialogDescription>
                </AlertDialogHeader>
                
                <div className="py-4">
                    {loading && (
                        <div className="text-center py-4">
                            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-2"></div>
                            <p className="text-muted-foreground">Loading statistics...</p>
                        </div>
                    )}
                    
                    {userContentData && !loading && (
                        <div className="space-y-4">
                            {userContentData.counts.ideas > 0 && (
                                <div className="flex items-center justify-between p-3 bg-muted rounded-lg">
                                    <div className="flex items-center gap-2">
                                        <Image
                                            src="/svgs/idea-icon.svg"
                                            alt="Ideas"
                                            width={20}
                                            height={20}
                                        />
                                        <span>Ideas</span>
                                    </div>
                                    <span className="font-semibold">{userContentData.counts.ideas}</span>
                                </div>
                            )}
                            
                            {userContentData.counts.sparkSubmissions > 0 && (
                                <div className="flex items-center justify-between p-3 bg-muted rounded-lg">
                                    <div className="flex items-center gap-2">
                                        <Image
                                            src="/svgs/idea-icon.svg"
                                            alt="Spark Submissions"
                                            width={20}
                                            height={20}
                                        />
                                        <span>Spark Submissions</span>
                                    </div>
                                    <span className="font-semibold">{userContentData.counts.sparkSubmissions}</span>
                                </div>
                            )}
                            
                            {userContentData.counts.votes > 0 && (
                                <div className="flex items-center justify-between p-3 bg-muted rounded-lg">
                                    <div className="flex items-center gap-2">
                                        <Image
                                            src="/svgs/idea-icon.svg"
                                            alt="Votes"
                                            width={20}
                                            height={20}
                                        />
                                        <span>Votes</span>
                                    </div>
                                    <span className="font-semibold">{userContentData.counts.votes}</span>
                                </div>
                            )}
                            
                            {userContentData.counts.quickfireVotes > 0 && (
                                <div className="flex items-center justify-between p-3 bg-muted rounded-lg">
                                    <div className="flex items-center gap-2">
                                        <Image
                                            src="/svgs/idea-icon.svg"
                                            alt="Quickfire Votes"
                                            width={20}
                                            height={20}
                                        />
                                        <span>Quickfire Votes</span>
                                    </div>
                                    <span className="font-semibold">{userContentData.counts.quickfireVotes}</span>
                                </div>
                            )}
                            
                            {!userContentData.hasAnyData && (
                                <div className="text-center py-4 text-muted-foreground">
                                    This user has no associated data.
                                </div>
                            )}
                        </div>
                    )}
                </div>
                
                <AlertDialogFooter>
                    <AlertDialogCancel 
                    >
                        Close
                    </AlertDialogCancel>
                </AlertDialogFooter>
            </AlertDialogContent>
        </AlertDialog>
    );
}
import {
    AlertDialog,
    AlertDialogAction,
    AlertDialogCancel,
    AlertDialogContent,
    AlertDialogDescription,
    AlertDialogFooter,
    AlertDialogHeader,
    AlertDialogTitle,
} from '@/components/ui/alert-dialog';

interface DeleteConfirmModalProps {
    showDeleteConfirm: string | null;
    selectedUsers: Set<string>;
    onCancel: () => void;
    bulkActionBlocked?: {type: 'delete' | 'role', hasContentCount: number} | null;
    onConfirm: (userId: string, isBulk: boolean) => Promise<void>;
    userHasVotes?: boolean;
}

export function DeleteConfirmModal({
    showDeleteConfirm,
    selectedUsers,
    onCancel,
    bulkActionBlocked,
    onConfirm,
    userHasVotes
}: DeleteConfirmModalProps) {
    
    return (
        <AlertDialog open={!!showDeleteConfirm} onOpenChange={(open) => !open && onCancel()}>
            <AlertDialogContent>
                <AlertDialogHeader>
                    <AlertDialogTitle>Delete User{showDeleteConfirm === 'bulk' ? 's' : ''}</AlertDialogTitle>
                    <AlertDialogDescription>
                        {showDeleteConfirm === 'bulk' ? (
                            bulkActionBlocked ? (
                                <>
                                    Cannot delete users in bulk. {bulkActionBlocked.hasContentCount} selected team lead(s) have content that requires individual transfer.
                                </>
                            ) : (
                                <>
                                    Are you sure you want to delete {selectedUsers.size} selected users?<br/>
                                    <span className="text-destructive font-medium">This will permanently delete all votes and data associated with these users.</span>
                                </>
                            )
                        ) : (
                            <>
                                Are you sure you want to delete this user?<br/>
                                {userHasVotes && (
                                    <span className="text-destructive font-medium">
                                        This will permanently delete all votes and data associated with this user.
                                    </span>
                                )}
                                {!userHasVotes && (
                                    <span className="text-muted-foreground">This user has no votes or data to delete.</span>
                                )}
                            </>
                        )}
                    </AlertDialogDescription>
                </AlertDialogHeader>
                
                
                <AlertDialogFooter>
                    <AlertDialogCancel 
                    >
                        {bulkActionBlocked ? 'Close' : 'Cancel'}
                    </AlertDialogCancel>
                    
                    {showDeleteConfirm === 'bulk' && !bulkActionBlocked && (
                        <AlertDialogAction
                            onClick={() => onConfirm(showDeleteConfirm, true)}
                        >
                            Delete Users
                        </AlertDialogAction>
                    )}
                    
                    {showDeleteConfirm !== 'bulk' && showDeleteConfirm && (
                        <AlertDialogAction
                            onClick={() => onConfirm(showDeleteConfirm, false)}
                        >
                            Delete User
                        </AlertDialogAction>
                    )}
                </AlertDialogFooter>
            </AlertDialogContent>
        </AlertDialog>
    );
}
import { useState } from 'react';
import { 
    Select, 
    SelectContent, 
    SelectItem, 
    SelectTrigger, 
    SelectValue 
} from '@/components/ui/select';
import {
    AlertDialog,
    AlertDialogAction,
    AlertDialogCancel,
    AlertDialogContent,
    AlertDialogDescription,
    AlertDialogFooter,
    AlertDialogHeader,
    AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Label } from '@/components/ui/label';
import Image from 'next/image';

interface User {
    _id: string;
    name: string;
    username: string;
    role: 'admin' | 'teamLead' | 'teamMember';
}

interface TeamLeadWithdrawalModalProps {
    showWithdrawModal: { 
        teamId: string; 
        teamName: string; 
        sessionId: string;
        eventId: string;
    } | null;
    teamLeads: User[];
    isLoadingTeamLeads: boolean;
    onCancel: () => void;
    onConfirmWithdrawAll: () => Promise<void>;
    onConfirmWithdrawTeamLead: (teamLeadId: string) => Promise<void>;
    isWithdrawing: boolean;
}

export function TeamLeadWithdrawalModal({
    showWithdrawModal,
    teamLeads,
    isLoadingTeamLeads,
    onCancel,
    onConfirmWithdrawAll,
    onConfirmWithdrawTeamLead,
    isWithdrawing
}: TeamLeadWithdrawalModalProps) {
    const [withdrawalType, setWithdrawalType] = useState<'all' | 'specific'>('all');
    const [selectedTeamLeadId, setSelectedTeamLeadId] = useState<string | null>(null);

    if (!showWithdrawModal) return null;

    // If team has only one team lead, show simple confirmation modal
    const hasOnlyOneTeamLead = !isLoadingTeamLeads && teamLeads.length === 1;

    const handleConfirm = async () => {
        if (hasOnlyOneTeamLead || withdrawalType === 'all') {
            await onConfirmWithdrawAll();
        } else if (withdrawalType === 'specific' && selectedTeamLeadId) {
            await onConfirmWithdrawTeamLead(selectedTeamLeadId);
        }
    };

    const canConfirm = hasOnlyOneTeamLead || withdrawalType === 'all' || (withdrawalType === 'specific' && selectedTeamLeadId);

    return (
        <AlertDialog open={!!showWithdrawModal} onOpenChange={(open) => !open && onCancel()}>
            <AlertDialogContent>
                <AlertDialogHeader>
                    <AlertDialogTitle>
                        Withdraw Team Submission
                    </AlertDialogTitle>
                    <AlertDialogDescription>
                        {hasOnlyOneTeamLead 
                            ? `Are you sure you want to withdraw team "${showWithdrawModal?.teamName}" submission?`
                            : `Choose withdrawal option for team "${showWithdrawModal?.teamName}":`
                        }
                    </AlertDialogDescription>
                </AlertDialogHeader>
                
                {!hasOnlyOneTeamLead && (
                    <div className="py-4 space-y-4">
                        <RadioGroup 
                            value={withdrawalType} 
                            onValueChange={(value) => {
                                setWithdrawalType(value as 'all' | 'specific');
                                if (value === 'all') {
                                    setSelectedTeamLeadId(null);
                                }
                            }}
                            className="space-y-3"
                        >
                            <div className="flex items-center space-x-2">
                                <RadioGroupItem value="all" id="withdraw-all" />
                                <Label htmlFor="withdraw-all" className="font-medium">
                                    Withdraw all team&apos;s ideas
                                </Label>
                            </div>
                            
                            <div className="flex items-center space-x-2">
                                <RadioGroupItem value="specific" id="withdraw-specific" />
                                <Label htmlFor="withdraw-specific" className="font-medium">
                                    Withdraw specific team lead ideas
                                </Label>
                            </div>
                        </RadioGroup>

                        {withdrawalType === 'specific' && (
                            <div className="ml-6 space-y-2">
                                {isLoadingTeamLeads ? (
                                    <div className="text-sm text-muted-foreground">Loading team leads...</div>
                                ) : teamLeads.length === 0 ? (
                                    <div className="text-sm text-muted-foreground">No team leads found for this team.</div>
                                ) : (
                                    <Select onValueChange={setSelectedTeamLeadId} value={selectedTeamLeadId || ""}>
                                        <SelectTrigger>
                                            <SelectValue placeholder="Select team lead..." />
                                        </SelectTrigger>
                                        <SelectContent>
                                            {teamLeads.map(teamLead => (
                                                <SelectItem key={teamLead._id} value={teamLead._id}>
                                                    <div className="flex items-center gap-2">
                                                        <Image
                                                            src="/svgs/Asset 3.svg"
                                                            alt="Team Lead"
                                                            width={20}
                                                            height={20}
                                                        />
                                                        <div className="font-medium">{teamLead.name}</div>
                                                    </div>
                                                </SelectItem>
                                            ))}
                                        </SelectContent>
                                    </Select>
                                )}
                            </div>
                        )}
                    </div>
                )}
                
                <AlertDialogFooter>
                    <AlertDialogCancel disabled={isWithdrawing}>
                        Cancel
                    </AlertDialogCancel>
                    <AlertDialogAction
                        onClick={handleConfirm}
                        disabled={!canConfirm || isWithdrawing}
                    >
                        {isWithdrawing ? 'Withdrawing...' : 'Withdraw'}
                    </AlertDialogAction>
                </AlertDialogFooter>
            </AlertDialogContent>
        </AlertDialog>
    );
}
import { Button } from '@/components/ui/button';
import {
    AlertDialog,
    AlertDialogCancel,
    AlertDialogContent,
    AlertDialogDescription,
    AlertDialogFooter,
    AlertDialogHeader,
    AlertDialogTitle,
} from '@/components/ui/alert-dialog';

interface Team {
    _id: string;
    name: string;
}

interface User {
    _id: string;
    name: string;
    username: string;
    role: 'admin' | 'teamLead' | 'teamMember';
    teamId?: string;
    teamName?: string;
}

interface TeamChangeModalProps {
    showTeamChange: string | null;
    teams: Team[];
    selectedUsers: Set<string>;
    onCancel: () => void;
    onTeamChange: (userId: string, teamId?: string) => Promise<void>;
    onBulkTeamChange: (teamId: string | undefined, userIds: string[]) => Promise<void>;
    hasTeamLeadsInSelection?: boolean;
    availableTeamLeads?: User[];
    userRole?: 'teamLead' | 'teamMember';
    hasContent?: boolean;
    currentUserTeamId?: string;  // Add to track user's current team
}

export function TeamChangeModal({
    showTeamChange,
    teams,
    selectedUsers,
    onCancel,
    onTeamChange,
    onBulkTeamChange,
    hasTeamLeadsInSelection,
    availableTeamLeads = [],
    userRole,
    hasContent,
    currentUserTeamId
}: TeamChangeModalProps) {
    
    // Filter out current user's team from available teams
    const availableTeams = teams.filter(team => team._id !== currentUserTeamId);
    
    return (
        <AlertDialog open={!!showTeamChange} onOpenChange={(open) => !open && onCancel()}>
            <AlertDialogContent>
                <AlertDialogHeader>
                    <AlertDialogTitle>Change Team</AlertDialogTitle>
                    <AlertDialogDescription>
                        {showTeamChange === 'bulk' && hasTeamLeadsInSelection ? (
                            'Cannot change team in bulk. Bulk team change is not allowed for team leads.'
                        ) : showTeamChange !== 'bulk' && userRole === 'teamLead' && hasContent && availableTeamLeads.length === 0 ? (
                            'Cannot change team - no other team leads in current team to transfer content.'
                        ) : showTeamChange !== 'bulk' && userRole === 'teamLead' && hasContent ? (
                            'Select a team lead to transfer content and new team:'
                        ) : (
                            'Select a new team for the user(s).'
                        )}
                    </AlertDialogDescription>
                </AlertDialogHeader>
                
                
                {/* Regular Team Selection */}
                {!(showTeamChange === 'bulk' && hasTeamLeadsInSelection) && !(showTeamChange !== 'bulk' && userRole === 'teamLead' && hasContent) && (
                    <div className="py-4 space-y-2">
                        <Button
                            variant="outline"
                            onClick={async () => {
                                if (showTeamChange === 'bulk') {
                                    await onBulkTeamChange(undefined, Array.from(selectedUsers));
                                } else if (showTeamChange) {
                                    await onTeamChange(showTeamChange, undefined);
                                }
                                onCancel(); // Close modal after successful operation
                            }}
                            className="w-full justify-start"
                        >
                            No Team
                        </Button>
                        {availableTeams.map(team => (
                            <Button
                                key={team._id}
                                variant="outline"
                                onClick={async () => {
                                    if (showTeamChange === 'bulk') {
                                        await onBulkTeamChange(team._id, Array.from(selectedUsers));
                                    } else if (showTeamChange) {
                                        await onTeamChange(showTeamChange, team._id);
                                    }
                                    onCancel(); // Close modal after successful operation
                                }}
                                className="w-full justify-start"
                            >
                                {team.name}
                            </Button>
                        ))}
                    </div>
                )}
                
                <AlertDialogFooter>
                    <AlertDialogCancel 
                    >
                        {(showTeamChange === 'bulk' && hasTeamLeadsInSelection) || (showTeamChange !== 'bulk' && userRole === 'teamLead' && hasContent && availableTeamLeads.length === 0) ? 'Close' : 'Cancel'}
                    </AlertDialogCancel>
                    
                </AlertDialogFooter>
            </AlertDialogContent>
        </AlertDialog>
    );
}
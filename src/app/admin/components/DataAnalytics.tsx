"use client";

import { useState, useEffect } from 'react';
import { useQuery, useAction } from "convex/react";
import { useSession } from 'next-auth/react';
import { api } from "@/../convex/_generated/api";
import { Button } from '@/components/ui/button';
import { 
    Select, 
    SelectContent, 
    SelectItem, 
    SelectTrigger, 
    SelectValue 
} from '@/components/ui/select';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';
import { ChevronDown } from 'lucide-react';
import { StateCard } from '@/components/ui/state-card';
import NoActiveEventMessage from './ideas/components/NoActiveEventMessage';
import AdminErrorBoundary from './AdminErrorBoundary';

function DataAnalyticsCore() {
    const { data: session } = useSession();
    const [copyStates, setCopyStates] = useState<{[key: string]: string}>({});
    const [selectedTeam, setSelectedTeam] = useState<string>('');
    const [selectedSession, setSelectedSession] = useState<string>('');
    const [baseUrl, setBaseUrl] = useState('');
    
    useEffect(() => {
        setBaseUrl(window.location.origin);
    }, []);

    // Convex real-time queries
    const sessionsData = useQuery(api.analytics.getSessionsForLeaderboard);
    const teams = useQuery(api.analytics.getTeamsForDropdown);
    const allSessions = useQuery(api.sessions.getSessionsByActiveEventExcludingQuickfire);
    const dataSummary = useQuery(api.analytics.getDataSummaryForAnalytics);
    
    // Export actions
    const exportIdeas = useAction(api.analytics.exportIdeasData);
    const exportTeams = useAction(api.analytics.exportTeamsData);

    // Extract data from Convex queries
    const sessions = sessionsData?.sessions || [];
    const activeEvent = sessionsData?.event || null;
    
    // Filter sessions to only Ideas sessions for teams leaderboard using allSessions data
    const ideasSessions = allSessions?.filter(session => 
        session.type === "Ideas" || session.type === undefined // Include undefined as Ideas (legacy)
    ) || [];
    
    // Remove duplicates by _id and ensure unique sessions
    const uniqueIdeasSessions = ideasSessions.filter((session, index, self) => 
        index === self.findIndex(s => s._id === session._id)
    );
    const loading = sessionsData === undefined || dataSummary === undefined;
    const allSessionsLoading = allSessions === undefined;
    const error = !loading && !activeEvent ? 'No active event found. Please activate an event first.' : null;

    const handleItemClick = (url: string) => {
        window.open(url, '_blank');
    };

    const handleCopyLink = async (url: string, id: string, e: React.MouseEvent) => {
        e.stopPropagation();
        try {
            if (navigator.clipboard) {
                await navigator.clipboard.writeText(url);
                setCopyStates(prev => ({ ...prev, [id]: 'Copied!' }));
                setTimeout(() => {
                    setCopyStates(prev => ({ ...prev, [id]: '' }));
                }, 2000);
            } else {
                // Fallback for browsers without clipboard API
                const textArea = document.createElement('textarea');
                textArea.value = url;
                document.body.appendChild(textArea);
                textArea.select();
                document.execCommand('copy');
                document.body.removeChild(textArea);
                setCopyStates(prev => ({ ...prev, [id]: 'Copied!' }));
                setTimeout(() => {
                    setCopyStates(prev => ({ ...prev, [id]: '' }));
                }, 2000);
            }
        } catch (err) {
            console.error('Failed to copy:', err);
            setCopyStates(prev => ({ ...prev, [id]: 'Failed to copy' }));
            setTimeout(() => {
                setCopyStates(prev => ({ ...prev, [id]: '' }));
            }, 2000);
        }
    };

    const handleExportIdeas = async () => {
        try {
            if (!session?.user?.username) {
                alert('Unable to export: User session not found');
                return;
            }
            const result = await exportIdeas({ username: session.user.username });
            
            if (!result.success || !result.data) {
                throw new Error('Export failed');
            }
            
            // Create a blob from the CSV content
            const blob = new Blob([result.data.content], { type: 'text/csv' });
            
            // Create a download link and trigger it
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = result.data.filename;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            window.URL.revokeObjectURL(url);
        } catch (error) {
            console.error('Export error:', error);
            alert('Failed to export ideas data');
        }
    };

    const handleExportTeams = async () => {
        try {
            if (!session?.user?.username) {
                alert('Unable to export: User session not found');
                return;
            }
            const result = await exportTeams({ username: session.user.username });
            
            if (!result.success || !result.data) {
                throw new Error('Export failed');
            }
            
            // Create a blob from the CSV content
            const blob = new Blob([result.data.content], { type: 'text/csv' });
            
            // Create a download link and trigger it
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = result.data.filename;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            window.URL.revokeObjectURL(url);
        } catch (error) {
            console.error('Export error:', error);
            alert('Failed to export teams data');
        }
    };

    if (loading) {
        return (
            <div className="flex justify-center items-center min-h-[400px]">
                <StateCard state="loading" title="Loading data..." />
            </div>
        );
    }

    if (error) {
        return error === 'No active event found. Please activate an event first.' ? (
            <NoActiveEventMessage />
        ) : (
            <div className="space-y-4">
                <div className="error-display">
                    Error: {error}
                </div>
            </div>
        );
    }

    // Check if there's no data available
    if (dataSummary && !dataSummary.hasData) {
        return (
            <div className="flex justify-center items-center min-h-[400px]">
                <StateCard 
                    state="empty" 
                    title="No Data Available" 
                    message="No vote records yet!"
                />
            </div>
        );
    }

    const allLeaderboardUrl = `${baseUrl}/leaderboard/all`;
    const allTeamsLeaderboardUrl = `${baseUrl}/leaderboard/teams/all`;

    return (
        <div className="space-y-6">
            {activeEvent && (
                <div className="mb-6">
                    <h2 className="text-2xl font-bold text-primary">Active Event: {activeEvent.name}</h2>
                </div>
            )}

            <div className="space-y-6">
                {/* Leaderboard Section */}
                <div className="space-y-4">
                    <div className="mb-4">
                        <h3 className="text-xl font-semibold text-primary">Leaderboard Links</h3>
                    </div>
                    
                    {/* Sessions Leaderboards Collapsible */}
                    <Collapsible defaultOpen={false}>
                        <CollapsibleTrigger className="flex items-center justify-between w-full p-4 border border-secondary hover:border-primary transition-colors">
                            <span className="text-lg font-mono text-accent">Sessions Leaderboards</span>
                            <ChevronDown className="h-4 w-4 text-foreground" />
                        </CollapsibleTrigger>
                        <CollapsibleContent className="space-y-3 mt-3">
                            {/* All Sessions Link */}
                            <div 
                                className="flex items-center justify-between p-4 border border-popover hover:border-accent transition-colors cursor-pointer"
                                onClick={() => handleItemClick(allLeaderboardUrl)}
                            >
                                <div className="flex items-center justify-between w-full">
                                    <h4 className="text-lg font-medium text-foreground">All Sessions Ideas</h4>
                                    <div className="flex items-center gap-3">
                                        <div className="flex items-center gap-2">
                                            <Button
                                                variant="outline"
                                                size="sm"
                                                onClick={(e) => handleCopyLink(allLeaderboardUrl, 'all', e)}
                                            >
                                                {copyStates['all'] || 'Copy Link'}
                                            </Button>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            {/* Individual Session Links */}
                            {sessions.map((session) => {
                                const sessionUrl = `${baseUrl}/leaderboard/session/${session.id}`;
                                return (
                                    <div 
                                        key={session.id} 
                                        className="flex items-center justify-between p-4 border border-popover hover:border-accent transition-colors cursor-pointer"
                                        onClick={() => handleItemClick(sessionUrl)}
                                    >
                                        <div className="flex items-center justify-between w-full">
                                            <h4 className="text-lg font-medium text-foreground">{session.name} Ideas</h4>
                                            <div className="flex items-center gap-3">
                                                <div className="flex items-center gap-2">
                                                    <Button
                                                        variant="outline"
                                                        size="sm"
                                                        onClick={(e) => handleCopyLink(sessionUrl, session.id, e)}
                                                    >
                                                        {copyStates[session.id] || 'Copy Link'}
                                                    </Button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                );
                            })}
                        </CollapsibleContent>
                    </Collapsible>

                    {/* Teams Leaderboard Collapsible */}
                    <Collapsible defaultOpen={false}>
                        <CollapsibleTrigger 
                            disabled={allSessionsLoading}
                            className="flex items-center justify-between w-full p-4 border border-secondary hover:border-primary transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                        >
                            <span className="text-lg font-mono text-accent">Teams Leaderboard</span>
                            <ChevronDown className="h-4 w-4 text-foreground" />
                        </CollapsibleTrigger>
                        <CollapsibleContent className="space-y-3 mt-3">
                            {allSessionsLoading ? (
                                <div className="p-4 text-center text-muted-foreground">Loading Ideas sessions...</div>
                            ) : (
                                <>
                                    {/* All Sessions Teams Link */}
                            <div 
                                className="flex items-center justify-between p-4 border border-popover hover:border-accent  transition-colors cursor-pointer"
                                onClick={() => handleItemClick(allTeamsLeaderboardUrl)}
                            >
                                <div className="flex items-center justify-between w-full">
                                    <h4 className="text-lg font-medium text-foreground">All Sessions Teams</h4>
                                    <div className="flex items-center gap-3">
                                        <div className="flex items-center gap-2">
                                            <Button
                                                variant="outline"
                                                size="sm"
                                                onClick={(e) => handleCopyLink(allTeamsLeaderboardUrl, 'all-teams', e)}
                                            >
                                                {copyStates['all-teams'] || 'Copy Link'}
                                            </Button>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            {/* Individual Session Teams Links - Ideas sessions only */}
                            {uniqueIdeasSessions.map((session) => {
                                const sessionTeamsUrl = `${baseUrl}/leaderboard/teams/session/${session._id}`;
                                return (
                                    <div 
                                        key={`teams-${session._id}`} 
                                        className="flex items-center justify-between p-4 border border-popover hover:border-accent transition-colors cursor-pointer"
                                        onClick={() => handleItemClick(sessionTeamsUrl)}
                                    >
                                        <div className="flex items-center justify-between w-full">
                                            <h4 className="text-lg font-medium text-foreground">{session.name} Teams</h4>
                                            <div className="flex items-center gap-3">
                                                <div className="flex items-center gap-2">
                                                    <Button
                                                        variant="outline"
                                                        size="sm"
                                                        onClick={(e) => handleCopyLink(sessionTeamsUrl, `teams-${session._id}`, e)}
                                                    >
                                                        {copyStates[`teams-${session._id}`] || 'Copy Link'}
                                                    </Button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                );
                            })}
                                </>
                            )}
                        </CollapsibleContent>
                    </Collapsible>
                </div>

                {/* Data Export Section */}
                <div className="space-y-4">
                    <div className="mb-4">
                        <h3 className="text-xl font-semibold text-primary">Data Export</h3>
                    </div>
                    <div className="space-y-3">
                        <div className="flex flex-col sm:flex-row gap-3">
                            <Button
                                onClick={handleExportIdeas}
                                className="w-full sm:w-auto"
                            >
                                Export Ideas Data
                            </Button>
                            <Button
                                onClick={handleExportTeams}
                                className="w-full sm:w-auto"
                            >
                                Export Teams Data
                            </Button>
                        </div>
                    </div>
                </div>

                {/* Print Team Data Section */}
                <div className="space-y-4">
                    <div className="mb-4">
                        <h3 className="text-xl font-semibold text-primary">Print Team Data</h3>
                    </div>
                    <div className="space-y-3">
                        <div className="flex flex-col sm:flex-row sm:items-center gap-4 p-4 border border-border rounded-md">
                            <div className="flex-1">
                                <Select value={selectedTeam || ""} onValueChange={setSelectedTeam}>
                                    <SelectTrigger className="w-full shadow-none focus-visible:ring-0 focus-visible:ring-offset-0">
                                        <SelectValue placeholder="Select Team" />
                                    </SelectTrigger>
                                    <SelectContent>
                                        {teams?.map(team => (
                                            <SelectItem key={team.id} value={team.id}>
                                                {team.name}
                                            </SelectItem>
                                        )) || []}
                                    </SelectContent>
                                </Select>
                            </div>
                            <div className="flex items-center gap-3 flex-wrap sm:flex-nowrap">
                                <Button
                                    onClick={() => {
                                        if (selectedTeam && teams) {
                                            const team = teams.find(t => t.id === selectedTeam);
                                            if (team) {
                                                const teamNameSlug = team.name.toLowerCase().replace(/\s+/g, '-');
                                                window.open(`/print/${teamNameSlug}`, '_blank');
                                            }
                                        }
                                    }}
                                    disabled={!selectedTeam || !teams}
                                    className="w-full sm:w-auto"
                                >
                                    Print Team Data
                                </Button>
                            </div>
                        </div>
                    </div>
                </div>

                {/* Print by Session Section */}
                <div className="space-y-4">
                    <div className="mb-4">
                        <h3 className="text-xl font-semibold text-primary">Print by Session</h3>
                    </div>
                    <div className="space-y-3">
                        <div className="flex flex-col sm:flex-row sm:items-center gap-4 p-4 border border-border rounded-md">
                            <div className="flex-1">
                                <Select value={selectedSession || ""} onValueChange={setSelectedSession}>
                                    <SelectTrigger className="w-full shadow-none focus-visible:ring-0 focus-visible:ring-offset-0">
                                        <SelectValue placeholder="Select Session" />
                                    </SelectTrigger>
                                    <SelectContent>
                                        {allSessions?.filter(session => session.type === 'Ideas' || session.type === 'Sparks').map(session => (
                                            <SelectItem key={session._id} value={session._id}>
                                                {session.name}
                                            </SelectItem>
                                        )) || []}
                                    </SelectContent>
                                </Select>
                            </div>
                            <div className="flex items-center gap-3 flex-wrap sm:flex-nowrap">
                                <Button
                                    onClick={() => {
                                        if (selectedSession && allSessions) {
                                            const filteredSessions = allSessions.filter(session => session.type === 'Ideas' || session.type === 'Sparks');
                                            const session = filteredSessions.find(s => s._id === selectedSession);
                                            if (session) {
                                                const sessionNameSlug = session.name.toLowerCase().replace(/\s+/g, '-');
                                                window.open(`/print/session/${sessionNameSlug}`, '_blank');
                                            }
                                        }
                                    }}
                                    disabled={!selectedSession || !allSessions}
                                    className="w-full sm:w-auto"
                                >
                                    Print Session Data
                                </Button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
}

export default function DataAnalytics() {
    return (
        <AdminErrorBoundary 
            fallbackTitle="Data Analytics Error"
            fallbackMessage="There was an error loading the data analytics interface."
        >
            <DataAnalyticsCore />
        </AdminErrorBoundary>
    );
}
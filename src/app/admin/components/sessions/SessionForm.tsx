"use client";

import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Button } from '@/components/ui/button';
import { 
    Select, 
    SelectContent, 
    SelectItem, 
    SelectTrigger, 
    SelectValue 
} from '@/components/ui/select';
import {
    AlertDialog,
    AlertDialogAction,
    AlertDialogCancel,
    AlertDialogContent,
    AlertDialogFooter,
    AlertDialogHeader,
    AlertDialogTitle,
} from '@/components/ui/alert-dialog';

interface Spark {
    _id: string;
    name: string;
    description?: string;
}

interface SessionFormProps {
    isOpen: boolean;
    onClose: () => void;
    onSubmit: () => void;
    
    // Form data
    sessionName: string;
    setSessionName: (name: string) => void;
    sessionType: string;
    setSessionType: (type: string) => void;
    sessionDay: number;
    setSessionDay: (day: number) => void;
    selectedSparkId: string;
    setSelectedSparkId: (id: string) => void;
    
    // State
    isSubmitting: boolean;
    sparks?: Spark[];
    
    // Spark creation
    onCreateNewSpark: () => void;
}

export default function SessionForm({
    isOpen,
    onClose,
    onSubmit,
    sessionName,
    setSessionName,
    sessionType,
    setSessionType,
    sessionDay,
    setSessionDay,
    selectedSparkId,
    setSelectedSparkId,
    isSubmitting,
    sparks,
    onCreateNewSpark
}: SessionFormProps) {
    return (
        <AlertDialog open={isOpen} onOpenChange={(open) => !open && onClose()}>
            <AlertDialogContent>
                <AlertDialogHeader>
                    <AlertDialogTitle>Add New Session</AlertDialogTitle>
                </AlertDialogHeader>
                <div className="space-y-4 py-4">
                    <div className="space-y-2">
                        <Label>Session Name:</Label>
                        <Input
                            id="session-name"
                            type="text"
                            placeholder="Enter session name"
                            value={sessionName}
                            onChange={(e) => setSessionName(e.target.value)}
                            disabled={isSubmitting}
                        />
                    </div>
                    
                    <div className="space-y-2">
                        <Label>Session Type</Label>
                        <Select value={sessionType} onValueChange={setSessionType} disabled={isSubmitting}>
                            <SelectTrigger size="md">
                                <SelectValue />
                            </SelectTrigger>
                            <SelectContent>
                                <SelectItem value="Ideas">Ideas</SelectItem>
                                <SelectItem value="Quickfire">Quickfire</SelectItem>
                                <SelectItem value="Sparks">Sparks</SelectItem>
                            </SelectContent>
                        </Select>
                    </div>
                    
                    <div className="space-y-2">
                        <Label>Day:</Label>
                        <div className="flex items-center gap-3">
                            <Button
                                type="button"
                                variant="outline"
                                size="icon"
                                onClick={() => setSessionDay(Math.max(1, sessionDay - 1))}
                                disabled={isSubmitting || sessionDay <= 1}
                                className="h-10 w-10 shrink-0"
                            >
                                <svg className="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20 12H4" />
                                </svg>
                            </Button>
                            <div className="relative">
                                <Input
                                    id="session-day"
                                    type="number"
                                    min="1"
                                    value={sessionDay}
                                    onChange={(e) => setSessionDay(parseInt(e.target.value) || 1)}
                                    disabled={isSubmitting}
                                    className="w-20 text-center text-lg font-bold border-2"
                                />
                            </div>
                            <Button
                                type="button"
                                variant="outline"
                                size="icon"
                                onClick={() => setSessionDay(sessionDay + 1)}
                                disabled={isSubmitting}
                                className="h-10 w-10 shrink-0"
                            >
                                <svg className="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
                                </svg>
                            </Button>
                        </div>
                    </div>
                    
                    {/* Spark selection for Sparks-type sessions */}
                    {sessionType === 'Sparks' && (
                        <div className="space-y-2">
                            <Label>Spark Configuration:</Label>
                            {sparks === undefined ? (
                                <div className="text-muted-foreground text-sm">Loading sparks...</div>
                            ) : sparks.length === 0 ? (
                                <Select 
                                    value={selectedSparkId} 
                                    onValueChange={(value) => {
                                        if (value === '__create_new__') {
                                            onCreateNewSpark();
                                        } else {
                                            setSelectedSparkId(value);
                                        }
                                    }} 
                                    disabled={isSubmitting}
                                >
                                    <SelectTrigger size="md">
                                        <SelectValue placeholder="No sparks available - Create new spark..." />
                                    </SelectTrigger>
                                    <SelectContent>
                                        <SelectItem 
                                            value="__create_new__" 
                                            className="text-primary font-medium"
                                        >
                                            + Create New Spark
                                        </SelectItem>
                                    </SelectContent>
                                </Select>
                            ) : (
                                <Select 
                                    value={selectedSparkId} 
                                    onValueChange={(value) => {
                                        if (value === '__create_new__') {
                                            onCreateNewSpark();
                                        } else {
                                            setSelectedSparkId(value);
                                        }
                                    }} 
                                    disabled={isSubmitting}
                                >
                                    <SelectTrigger size="md">
                                        <SelectValue placeholder="Select a spark configuration..." />
                                    </SelectTrigger>
                                    <SelectContent>
                                        {sparks.map(spark => (
                                            <SelectItem key={spark._id} value={spark._id}>
                                                {spark.name}{spark.description ? ` - ${spark.description}` : ''}
                                            </SelectItem>
                                        ))}
                                        <SelectItem 
                                            value="__create_new__" 
                                            className="text-primary font-medium"
                                        >
                                            + Create New Spark
                                        </SelectItem>
                                    </SelectContent>
                                </Select>
                            )}
                        </div>
                    )}
                </div>
                <AlertDialogFooter>
                    <AlertDialogCancel 
                        disabled={isSubmitting}
                    >
                        Cancel
                    </AlertDialogCancel>
                    <AlertDialogAction
                        onClick={onSubmit}
                        disabled={isSubmitting}
                    >
                        {isSubmitting ? 'Adding...' : 'Add Session'}
                    </AlertDialogAction>
                </AlertDialogFooter>
            </AlertDialogContent>
        </AlertDialog>
    );
}
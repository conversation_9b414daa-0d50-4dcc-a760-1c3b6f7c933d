"use client";

import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Button } from '@/components/ui/button';
import { 
    Select, 
    SelectContent, 
    SelectItem, 
    SelectTrigger, 
    SelectValue 
} from '@/components/ui/select';
import {
    AlertDialog,
    AlertDialogAction,
    AlertDialogCancel,
    AlertDialogContent,
    AlertDialogDescription,
    AlertDialogFooter,
    AlertDialogHeader,
    AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import { Id } from "@/../convex/_generated/dataModel";
import SparkConfigEditor from '@/app/admin/components/SparkConfigEditor';

interface Session {
    _id: string;
    name: string;
    day?: number;
    eventId: string;
    active: boolean;
    type: string;
    sparkId?: string;
    createdAt: number;
}

interface Spark {
    _id: string;
    name: string;
    description?: string;
}

interface ModalState {
    showAddModal: boolean;
    showSwitchConfirm: Session | null;
    showDeleteConfirm: Session | null;
    showTypeChangeError: boolean;
    typeChangeErrorMessage: string;
    showDayEditModal: Session | null;
    showSparkSelectionModal: Session | null;
}

interface SessionConfirmationModalsProps {
    modalState: ModalState;
    sparks?: Spark[];
    activeEvent: { _id: string; name: string; active: boolean } | null;
    
    // Switch Confirmation
    switchingSession: boolean;
    handleConfirmSwitch: () => void;
    closeSwitchConfirm: () => void;
    
    // Delete Confirmation
    deletingSession: boolean;
    handleConfirmDelete: () => void;
    closeDeleteConfirm: () => void;
    
    // Type Change Error
    closeTypeChangeError: () => void;
    
    // Day Edit Modal
    editingDay: number;
    setEditingDay: (day: number) => void;
    updatingDay: boolean;
    handleUpdateDay: () => void;
    closeDayEditModal: () => void;
    
    // Spark Selection Modal
    selectedSparkId: string;
    setSelectedSparkId: (id: string) => void;
    sparkSelectionError: string | null;
    updatingSessionWithSpark: boolean;
    handleConfirmSparkSelection: () => void;
    handleCancelSparkSelection: () => void;
    
    // Spark Creation
    showSparkEditor: boolean;
    handleCreateNewSpark: () => void;
    handleSparkCreationSuccess: (sparkId: string) => void;
    handleSparkCreationError: (error: string) => void;
    handleSparkEditorClose: () => void;
}

export default function SessionConfirmationModals({
    modalState,
    sparks,
    activeEvent,
    switchingSession,
    handleConfirmSwitch,
    closeSwitchConfirm,
    deletingSession,
    handleConfirmDelete,
    closeDeleteConfirm,
    closeTypeChangeError,
    editingDay,
    setEditingDay,
    updatingDay,
    handleUpdateDay,
    closeDayEditModal,
    selectedSparkId,
    setSelectedSparkId,
    sparkSelectionError,
    updatingSessionWithSpark,
    handleConfirmSparkSelection,
    handleCancelSparkSelection,
    showSparkEditor,
    handleCreateNewSpark,
    handleSparkCreationSuccess,
    handleSparkCreationError,
    handleSparkEditorClose,
}: SessionConfirmationModalsProps) {
    return (
        <>
            {/* Switch Session Confirmation Dialog */}
            <AlertDialog open={!!modalState.showSwitchConfirm} onOpenChange={(open) => !open && closeSwitchConfirm()}>
                <AlertDialogContent>
                    <AlertDialogHeader>
                        <AlertDialogTitle>
                            {modalState.showSwitchConfirm?.active ? 'Deactivate Session' : 'Switch Session'}
                        </AlertDialogTitle>
                        <AlertDialogDescription>
                            This will stop the current voting process. Are you sure you want to continue?
                        </AlertDialogDescription>
                    </AlertDialogHeader>
                    <AlertDialogFooter>
                        <AlertDialogCancel disabled={switchingSession}>
                            Cancel
                        </AlertDialogCancel>
                        <AlertDialogAction
                            onClick={handleConfirmSwitch}
                            disabled={switchingSession}
                        >
                            {switchingSession ? 'Switching...' : 'Confirm'}
                        </AlertDialogAction>
                    </AlertDialogFooter>
                </AlertDialogContent>
            </AlertDialog>

            {/* Delete Session Confirmation Dialog */}
            <AlertDialog open={!!modalState.showDeleteConfirm} onOpenChange={(open) => !open && closeDeleteConfirm()}>
                <AlertDialogContent>
                    <AlertDialogHeader>
                        <AlertDialogTitle>Delete Session</AlertDialogTitle>
                        <AlertDialogDescription>
                            Are you sure you want to delete &quot;{modalState.showDeleteConfirm?.name}&quot;? This will permanently delete the session and all ideas inside it. This action cannot be undone.
                        </AlertDialogDescription>
                    </AlertDialogHeader>
                    <AlertDialogFooter>
                        <AlertDialogCancel disabled={deletingSession}>
                            Cancel
                        </AlertDialogCancel>
                        <AlertDialogAction
                            onClick={handleConfirmDelete}
                            disabled={deletingSession}
                        >
                            {deletingSession ? 'Deleting...' : 'Delete'}
                        </AlertDialogAction>
                    </AlertDialogFooter>
                </AlertDialogContent>
            </AlertDialog>

            {/* Type Change Error Dialog */}
            <AlertDialog open={modalState.showTypeChangeError} onOpenChange={(open) => !open && closeTypeChangeError()}>
                <AlertDialogContent>
                    <AlertDialogHeader>
                        <AlertDialogTitle>Cannot Change Session Type</AlertDialogTitle>
                        <AlertDialogDescription>
                            {modalState.typeChangeErrorMessage}
                        </AlertDialogDescription>
                    </AlertDialogHeader>
                    <AlertDialogFooter>
                        <AlertDialogAction onClick={closeTypeChangeError}>
                            OK
                        </AlertDialogAction>
                    </AlertDialogFooter>
                </AlertDialogContent>
            </AlertDialog>

            {/* Day Edit Dialog */}
            <AlertDialog open={!!modalState.showDayEditModal} onOpenChange={(open) => !open && closeDayEditModal()}>
                <AlertDialogContent>
                    <AlertDialogHeader>
                        <AlertDialogTitle>
                            Edit Day for &quot;{modalState.showDayEditModal?.name}&quot;
                        </AlertDialogTitle>
                        <AlertDialogDescription>
                            Change session day
                        </AlertDialogDescription>
                    </AlertDialogHeader>
                    <div className="py-4">
                        <Label htmlFor="day-input">Day Number:</Label>
                        <div className="flex items-center gap-3">
                            <Button
                                type="button"
                                variant="outline"
                                size="icon"
                                onClick={() => setEditingDay(Math.max(1, editingDay - 1))}
                                disabled={updatingDay || editingDay <= 1}
                                className="h-10 w-10 shrink-0"
                            >
                                <svg className="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20 12H4" />
                                </svg>
                            </Button>
                            <div className="relative">
                                <Input
                                    id="day-input"
                                    type="number"
                                    min="1"
                                    value={editingDay}
                                    onChange={(e) => setEditingDay(parseInt(e.target.value) || 1)}
                                    disabled={updatingDay}
                                    className="w-20 text-center text-lg font-bold border-2"
                                />
                            </div>
                            <Button
                                type="button"
                                variant="outline"
                                size="icon"
                                onClick={() => setEditingDay(editingDay + 1)}
                                disabled={updatingDay}
                                className="h-10 w-10 shrink-0"
                            >
                                <svg className="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
                                </svg>
                            </Button>
                        </div>
                    </div>
                    <AlertDialogFooter>
                        <AlertDialogCancel disabled={updatingDay}>
                            Cancel
                        </AlertDialogCancel>
                        <AlertDialogAction
                            onClick={handleUpdateDay}
                            disabled={updatingDay}
                        >
                            {updatingDay ? 'Updating...' : 'Update Day'}
                        </AlertDialogAction>
                    </AlertDialogFooter>
                </AlertDialogContent>
            </AlertDialog>

            {/* Spark Selection Modal */}
            <AlertDialog open={!!modalState.showSparkSelectionModal} onOpenChange={(open) => !open && handleCancelSparkSelection()}>
                <AlertDialogContent>
                    <AlertDialogHeader>
                        <AlertDialogTitle>Select Spark Configuration</AlertDialogTitle>
                        <AlertDialogDescription>
                            To change &quot;{modalState.showSparkSelectionModal?.name}&quot; to Sparks type, please select a spark configuration:
                        </AlertDialogDescription>
                    </AlertDialogHeader>
                    <div className="space-y-4 py-4">
                        <div className="space-y-2">
                            <Label>Spark Configuration</Label>
                            {sparks === undefined ? (
                                <div className="text-muted-foreground text-sm">Loading sparks...</div>
                            ) : sparks.length === 0 ? (
                                <Select 
                                    value={selectedSparkId} 
                                    onValueChange={(value) => {
                                        if (value === '__create_new__') {
                                            handleCreateNewSpark();
                                        } else {
                                            setSelectedSparkId(value);
                                        }
                                    }} 
                                    disabled={updatingSessionWithSpark}
                                >
                                    <SelectTrigger size="md">
                                        <SelectValue placeholder="No sparks available - Create new spark..." />
                                    </SelectTrigger>
                                    <SelectContent>
                                        <SelectItem 
                                            value="__create_new__" 
                                            className="text-primary font-medium"
                                        >
                                            + Create New Spark
                                        </SelectItem>
                                    </SelectContent>
                                </Select>
                            ) : (
                                <Select 
                                    value={selectedSparkId} 
                                    onValueChange={(value) => {
                                        if (value === '__create_new__') {
                                            handleCreateNewSpark();
                                        } else {
                                            setSelectedSparkId(value);
                                        }
                                    }} 
                                    disabled={updatingSessionWithSpark}
                                >
                                    <SelectTrigger size="md">
                                        <SelectValue placeholder="Select a spark configuration..." />
                                    </SelectTrigger>
                                    <SelectContent>
                                        {sparks.map(spark => (
                                            <SelectItem key={spark._id} value={spark._id}>
                                                {spark.name}{spark.description ? ` - ${spark.description}` : ''}
                                            </SelectItem>
                                        ))}
                                        <SelectItem 
                                            value="__create_new__" 
                                            className="text-primary font-medium"
                                        >
                                            + Create New Spark
                                        </SelectItem>
                                    </SelectContent>
                                </Select>
                            )}
                        </div>
                        
                        {sparkSelectionError && (
                            <div className="error-display">
                                {sparkSelectionError}
                            </div>
                        )}
                    </div>
                    <AlertDialogFooter>
                        <AlertDialogCancel disabled={updatingSessionWithSpark}>
                            Cancel
                        </AlertDialogCancel>
                        <AlertDialogAction
                            onClick={handleConfirmSparkSelection}
                            disabled={updatingSessionWithSpark || (!selectedSparkId && sparks?.length !== 0)}
                        >
                            {updatingSessionWithSpark ? 'Updating...' : 'Change to Sparks'}
                        </AlertDialogAction>
                    </AlertDialogFooter>
                </AlertDialogContent>
            </AlertDialog>

            {/* Spark Config Editor Modal */}
            {activeEvent && (
                <SparkConfigEditor
                    spark={null}
                    eventId={activeEvent._id as Id<"events">}
                    open={showSparkEditor}
                    onClose={handleSparkEditorClose}
                    onSuccess={handleSparkCreationSuccess}
                    onError={handleSparkCreationError}
                />
            )}
        </>
    );
}
"use client";

import React from 'react';
import { Button } from '@/components/ui/button';

interface SessionActionsProps {
    onCreateSession: () => void;
}

const SessionActions = React.memo(function SessionActions({
    onCreateSession
}: SessionActionsProps) {
    return (
        <div className="flex justify-start">
            <Button 
                onClick={onCreateSession}
                className="w-full"
            >
                ADD NEW SESSION
            </Button>
        </div>
    );
});

export default SessionActions;
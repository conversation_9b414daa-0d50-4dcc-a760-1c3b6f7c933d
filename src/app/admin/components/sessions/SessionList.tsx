"use client";

import React from 'react';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import { StateCard } from '@/components/ui/state-card';
import { 
    Select, 
    SelectContent, 
    SelectItem, 
    SelectTrigger, 
    SelectValue 
} from '@/components/ui/select';
import { Trash2, Calendar, Settings } from 'lucide-react';

interface Session {
    _id: string;
    name: string;
    day?: number;
    eventId: string;
    active: boolean;
    type: string;
    sparkId?: string;
    createdAt: number;
}

interface Spark {
    _id: string;
    name: string;
}

interface SessionItemProps {
    session: Session;
    sparks?: Spark[];
    isEditing: boolean;
    editingName: string;
    isUpdatingType: boolean;
    onToggleActive: (session: Session) => void;
    onStartEditing: (session: Session) => void;
    onEditSession: (session: Session, newName: string) => void;
    onEditNameChange: (name: string) => void;
    onKeyPress: (e: React.KeyboardEvent, session: Session) => void;
    onUpdateSessionType: (session: Session, newType: string) => void;
    onEditDay: (session: Session, currentDay: number) => void;
    onDeleteSession: (session: Session) => void;
    onConfigureSpark: (session: Session) => void;
}

// Memoized session item component with custom comparison
const SessionItem = React.memo(function SessionItem({
    session,
    sparks,
    isEditing,
    editingName,
    isUpdatingType,
    onToggleActive,
    onStartEditing,
    onEditSession,
    onEditNameChange,
    onKeyPress,
    onUpdateSessionType,
    onEditDay,
    onDeleteSession,
    onConfigureSpark
}: SessionItemProps) {
    // Memoize the checkbox handler with stable session ID
    const handleToggleActive = React.useCallback(() => {
        onToggleActive(session);
    }, [onToggleActive, session]);

    return (
        <div className="admin-card">
            {/* Top Row: Checkbox + Name */}
            <div className="flex items-center gap-4 flex-1 min-w-0">
                {/* Checkbox */}
                <Checkbox
                    checked={session.active}
                    onCheckedChange={handleToggleActive}
                    className="shrink-0"
                />

                {/* Session Name (Editable) */}
                <div className="flex-1 min-w-0">
                    {isEditing ? (
                        <Input
                            type="text"
                            value={editingName}
                            onChange={(e) => onEditNameChange(e.target.value)}
                            onKeyDown={(e) => onKeyPress(e, session)}
                            onBlur={() => onEditSession(session, editingName)}
                            autoFocus
                            className="input-standard"
                        />
                    ) : (
                        <div className="space-y-1">
                            <div
                                className="text-foreground hover:text-primary transition-colors cursor-text truncate"
                                onClick={() => onStartEditing(session)}
                            >
                                {session.name}
                            </div>
                            {/* Show Spark name for Sparks sessions */}
                            {session.type === 'Sparks' && session.sparkId && (
                                <div className="text-muted-foreground text-xs truncate">
                                    {sparks?.find(spark => spark._id === session.sparkId)?.name || 'Loading...'}
                                </div>
                            )}
                        </div>
                    )}
                </div>
            </div>

            {/* Action Buttons Row */}
            <div className="flex items-center gap-3 flex-wrap sm:flex-nowrap">
                {/* Sparks Configuration Button */}
                {session.type === 'Sparks' && (
                    <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => onConfigureSpark(session)}
                        className="order-3 sm:order-none"
                        title="Configure Sparks"
                    >
                        <Settings className="h-4 w-4" />
                    </Button>
                )}

                {/* Session Type */}
                <Select
                    value={session.type}
                    onValueChange={(newType) => onUpdateSessionType(session, newType)}
                    disabled={isUpdatingType}
                >
                    <SelectTrigger className="w-40" size="md">
                        <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                        <SelectItem value="Ideas">Ideas</SelectItem>
                        <SelectItem value="Sparks">Sparks</SelectItem>
                        <SelectItem value="Quickfire">Quickfire</SelectItem>
                    </SelectContent>
                </Select>

                {/* Day Number */}
                <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => onEditDay(session, session.day || 1)}
                    className="flex items-center gap-2"
                    title={`Day ${session.day || 1}`}
                >
                    <Calendar className="h-4 w-4" />
                </Button>

                {/* Delete Button */}
                <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => onDeleteSession(session)}
                    disabled={session.active}
                    className="hover:text-primary disabled:opacity-50 disabled:cursor-not-allowed"
                    title={session.active ? "Cannot delete active session" : "Delete session"}
                >
                    <Trash2 className="h-4 w-4" />
                </Button>
            </div>
        </div>
    );
});

interface SessionListProps {
    groupedSessions: Array<{
        day: number;
        sessions: Session[];
    }>;
    sparks?: Spark[];
    editingSession: Session | null;
    editingName: string;
    updatingType: string | null;
    onToggleActive: (session: Session) => void;
    onStartEditing: (session: Session) => void;
    onEditSession: (session: Session, newName: string) => void;
    onEditNameChange: (name: string) => void;
    onKeyPress: (e: React.KeyboardEvent, session: Session) => void;
    onUpdateSessionType: (session: Session, newType: string) => void;
    onEditDay: (session: Session, currentDay: number) => void;
    onDeleteSession: (session: Session) => void;
    onConfigureSpark: (session: Session) => void;
    error?: string | null;
}

const SessionList = React.memo(function SessionList({
    groupedSessions,
    sparks,
    editingSession,
    editingName,
    updatingType,
    onToggleActive,
    onStartEditing,
    onEditSession,
    onEditNameChange,
    onKeyPress,
    onUpdateSessionType,
    onEditDay,
    onDeleteSession,
    onConfigureSpark,
    error
}: SessionListProps) {
    // Show error state if present
    if (error) {
        return (
            <div className="flex justify-center items-center min-h-[200px]">
                <StateCard 
                    state="error"
                    title="Error"
                    message={error}
                />
            </div>
        );
    }

    // Show empty state if no sessions
    if (groupedSessions.length === 0) {
        return (
            <div className="flex justify-center items-center min-h-[200px]">
                <StateCard 
                    state="empty"
                    title="No Sessions Found"
                    message="Create your first session to get started."
                />
            </div>
        );
    }

    return (
        <div className="space-y-8">
            {groupedSessions.map(({ day, sessions }) => (
                <div key={`day-${day}`} className="space-y-4">
                    <h2 className="text-2xl font-bold text-primary">Day {day}</h2>
                    <div className="space-y-4">
                        {sessions.map((session) => (
                            <SessionItem
                                key={session._id}
                                session={session}
                                sparks={sparks}
                                isEditing={editingSession?._id === session._id}
                                editingName={editingName}
                                isUpdatingType={!!updatingType && updatingType === session._id}
                                onToggleActive={onToggleActive}
                                onStartEditing={onStartEditing}
                                onEditSession={onEditSession}
                                onEditNameChange={onEditNameChange}
                                onKeyPress={onKeyPress}
                                onUpdateSessionType={onUpdateSessionType}
                                onEditDay={onEditDay}
                                onDeleteSession={onDeleteSession}
                                onConfigureSpark={onConfigureSpark}
                            />
                        ))}
                    </div>
                </div>
            ))}
        </div>
    );
});

export default SessionList;
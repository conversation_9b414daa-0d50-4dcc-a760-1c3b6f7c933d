"use client";

import { useState, useCallback } from 'react';
import { useQuery, useMutation } from "convex/react";
import { api } from "@/../convex/_generated/api";
import { Id } from "@/../convex/_generated/dataModel";
import { useSession } from 'next-auth/react';
import { debugError } from '@/lib/utils';
import { toast } from 'sonner';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import { StateCard } from '@/components/ui/state-card';
import NoActiveEventMessage from './ideas/components/NoActiveEventMessage';
import AdminErrorBoundary from './AdminErrorBoundary';
import {
    AlertDialog,
    AlertDialogAction,
    AlertDialogCancel,
    AlertDialogContent,
    AlertDialogDescription,
    AlertDialogFooter,
    AlertDialogHeader,
    AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import { Trash2, Users } from 'lucide-react';
import TeamLeadIcon from './TeamLeadIcon';

interface User {
    _id: string;
    name: string;
    role: 'admin' | 'teamLead' | 'teamMember';
}


interface Team {
    _id: string;
    name: string;
    users: User[];
    eventId: string;
    active?: boolean; // Optional for existing teams
    createdAt: number;
}

function TeamsManagementCore() {
    const { data: session } = useSession();
    const teams = useQuery(api.teams.getTeamsByActiveEvent);
    const activeEvent = useQuery(api.events.getActiveEvent);
    
    const createTeam = useMutation(api.teams.createTeam);
    const updateTeam = useMutation(api.teams.updateTeam);
    const deleteTeam = useMutation(api.teams.deleteTeam);
    const toggleTeamActive = useMutation(api.teams.toggleTeamActive);
    
    const [teamName, setTeamName] = useState('');
    const [error, setError] = useState<string | null>(null);
    const [editingTeam, setEditingTeam] = useState<Team | null>(null);
    const [editingName, setEditingName] = useState('');
    const [showUsersTeam, setShowUsersTeam] = useState<Team | null>(null);
    const [showDeleteConfirm, setShowDeleteConfirm] = useState<Team | null>(null);


    const handleAddTeam = async () => {
        if (!teamName.trim()) {
            toast.error('Team name is required');
            return;
        }
        if (!activeEvent) {
            setError('No active event found. Please activate an event first.');
            return;
        }
        setError(null);

        try {
            await createTeam({
                username: session?.user?.username || '',
                name: teamName.trim(),
                eventId: activeEvent._id
            });
            setTeamName('');
            setError(null);
        } catch (error) {
            setError(error instanceof Error ? error.message : 'Failed to add team');
            if (error instanceof Error && error.message === 'Team name already exists in this event') {
                setTeamName('');
            }
            debugError('Error adding team:', error);
        }
    };

    const handleEditTeam = useCallback(async (team: Team, newName: string) => {
        if (!newName.trim() || newName === team.name) return setEditingTeam(null);

        try {
            await updateTeam({
                username: session?.user?.username || '',
                teamId: team._id as Id<"teams">,
                name: newName.trim()
            });
            setEditingTeam(null);
            setError(null);
        } catch (error) {
            setError(error instanceof Error ? error.message : 'Failed to update team');
            debugError('Error updating team:', error);
        }
    }, [updateTeam, session?.user?.username]);

    const handleDeleteTeam = async (team: Team) => {
        try {
            await deleteTeam({ 
                username: session?.user?.username || '',
                teamId: team._id as Id<"teams"> 
            });
            setError(null);
            setShowDeleteConfirm(null);
        } catch (error) {
            setError(error instanceof Error ? error.message : 'Failed to delete team');
            debugError('Error deleting team:', error);
        }
    };

    const handleToggleActive = async (team: Team) => {
        try {
            await toggleTeamActive({ 
                username: session?.user?.username || '',
                teamId: team._id as Id<"teams"> 
            });
            setError(null);
        } catch (error) {
            setError(error instanceof Error ? error.message : 'Failed to toggle team status');
            debugError('Error toggling team status:', error);
        }
    };

    const startEditing = useCallback((team: Team) => {
        setEditingTeam(team);
        setEditingName(team.name);
    }, []);

    const handleKeyPress = useCallback((e: React.KeyboardEvent, team: Team) => {
        if (e.key === 'Enter') {
            handleEditTeam(team, editingName);
        } else if (e.key === 'Escape') {
            setEditingTeam(null);
        }
    }, [editingName, handleEditTeam]);



    if (teams === undefined || activeEvent === undefined) {
        return (
            <div className="flex justify-center items-center min-h-[400px]">
                <StateCard state="loading" title="Loading..." />
            </div>
        );
    }

    if (!activeEvent) {
        return <NoActiveEventMessage />;
    }

    return (
        <div className="space-y-6">
            {/* Add Team Form */}
            <div className="space-y-4">
                <Input
                    type="text"
                    placeholder="Enter team name"
                    value={teamName}
                    onChange={(e) => setTeamName(e.target.value)}
                    onKeyDown={(e: React.KeyboardEvent<HTMLInputElement>) => {
                        if (e.key === 'Enter') {
                            handleAddTeam();
                        }
                    }}
                    className="input-standard"
                />
                <Button 
                    onClick={handleAddTeam}
                    className="w-full"
                >
                    ADD NEW TEAM
                </Button>
            </div>

            {/* Error Message */}
            {error && (
                <div className="error-display">
                    {error}
                </div>
            )}
            
            {/* Empty State */}
            {teams && teams.length === 0 && !error && (
                <div className="flex justify-center items-center min-h-[400px]">
                    <StateCard 
                        state="empty" 
                        title="No Teams Found" 
                        message="No teams found for this event. Create your first team to get started."
                    />
                </div>
            )}
            
            {/* Teams List */}
            {teams.length > 0 && (
                <div className="space-y-4">
                    {teams.map((team) => (
                        <div 
                            key={team._id}
                            className="admin-card"
                        >
                            {/* Top Row: Checkbox + Name */}
                            <div className="flex items-center gap-4 flex-1 min-w-0">
                                {/* Checkbox */}
                                <Checkbox
                                    checked={team.active ?? true}
                                    onCheckedChange={() => handleToggleActive(team)}
                                    className="shrink-0"
                                />

                                {/* Team Name (Editable) */}
                                <div className="flex-1 min-w-0">
                                    {editingTeam?._id === team._id ? (
                                        <Input
                                            type="text"
                                            value={editingName}
                                            onChange={(e) => setEditingName(e.target.value)}
                                            onKeyDown={(e) => handleKeyPress(e, team)}
                                            onBlur={() => handleEditTeam(team, editingName)}
                                            autoFocus
                                            className="input-standard"
                                        />
                                    ) : (
                                        <div
                                            className="text-foreground hover:text-foreground/80 transition-colors cursor-text truncate"
                                            onClick={() => startEditing(team)}
                                        >
                                            {team.name}
                                        </div>
                                    )}
                                </div>
                            </div>

                            {/* Action Buttons Row */}
                            <div className="flex items-center gap-3 flex-wrap sm:flex-nowrap">
                                {/* View Users Button */}
                                <Button
                                    variant="ghost"
                                    size="sm"
                                    onClick={() => setShowUsersTeam(team)}
                                    className="shrink-0 h-8 w-8 p-0 hover:!bg-transparent hover:text-primary"
                                    title="View team members"
                                >
                                    <Users className="h-4 w-4" />
                                </Button>

                                {/* Delete Button */}
                                <Button
                                    variant="ghost"
                                    size="sm"
                                    onClick={() => setShowDeleteConfirm(team)}
                                    className="shrink-0 h-8 w-8 p-0 hover:!bg-transparent hover:text-primary"
                                    title="Delete team"
                                >
                                    <Trash2 className="h-4 w-4" />
                                </Button>
                            </div>
                        </div>
                    ))}
                </div>
            )}

            {/* Team Members Dialog */}
            <AlertDialog open={!!showUsersTeam} onOpenChange={(open) => !open && setShowUsersTeam(null)}>
                <AlertDialogContent>
                    <AlertDialogHeader>
                        <AlertDialogTitle>Team Members - {showUsersTeam?.name}</AlertDialogTitle>
                    </AlertDialogHeader>
                    <div className="py-4">
                        {showUsersTeam?.users?.length ? (
                            <div className="space-y-3">
                                {showUsersTeam.users.map(user => (
                                    <div key={user._id} className="flex items-center gap-3 p-2 border border-border rounded-md">
                                        <div className="shrink-0">
                                            {user.role === 'teamLead' ? (
                                                <TeamLeadIcon className="w-6 h-6 text-primary" />
                                            ) : (
                                                <Users className="h-6 w-6 text-muted-foreground" />
                                            )}
                                        </div>
                                        <div className="flex-1">
                                            <div className="text-foreground font-medium">{user.name}</div>
                                            <div className="text-muted-foreground text-sm capitalize">{user.role.replace('teamLead', 'Team Lead').replace('teamMember', 'Team Member')}</div>
                                        </div>
                                    </div>
                                ))}
                            </div>
                        ) : (
                            <div className="text-muted-foreground text-center py-4">No users in this team</div>
                        )}
                    </div>
                    <AlertDialogFooter>
                        <AlertDialogAction
                            onClick={() => setShowUsersTeam(null)}
                        >
                            Close
                        </AlertDialogAction>
                    </AlertDialogFooter>
                </AlertDialogContent>
            </AlertDialog>

            {/* Delete Team Confirmation Dialog */}
            <AlertDialog open={!!showDeleteConfirm} onOpenChange={(open) => !open && setShowDeleteConfirm(null)}>
                <AlertDialogContent>
                    <AlertDialogHeader>
                        <AlertDialogTitle>Delete Team</AlertDialogTitle>
                        <AlertDialogDescription>
                            Are you sure you want to delete &quot;{showDeleteConfirm?.name}&quot;? This action cannot be undone.
                        </AlertDialogDescription>
                    </AlertDialogHeader>
                    <AlertDialogFooter>
                        <AlertDialogCancel 
                        >
                            Cancel
                        </AlertDialogCancel>
                        <AlertDialogAction
                            onClick={() => showDeleteConfirm && handleDeleteTeam(showDeleteConfirm)}
                            className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
                        >
                            Delete
                        </AlertDialogAction>
                    </AlertDialogFooter>
                </AlertDialogContent>
            </AlertDialog>
        </div>
    );
}

export default function TeamsManagement() {
    return (
        <AdminErrorBoundary 
            fallbackTitle="Teams Management Error"
            fallbackMessage="There was an error loading the teams management interface."
        >
            <TeamsManagementCore />
        </AdminErrorBoundary>
    );
}

import { RoleChangeModal } from '@/app/admin/components/modals/RoleChangeModal';
import { EditUserModal } from '@/app/admin/components/modals/EditUserModal';
import { TeamChangeModal } from '@/app/admin/components/modals/TeamChangeModal';
import { DeleteConfirmModal } from '@/app/admin/components/modals/DeleteConfirmModal';
import { TeamLeadActionsModal } from '@/app/admin/components/modals/TeamLeadActionsModal';
import { AddUserModal } from '@/app/admin/components/modals/AddUserModal';
import { IdeaStatsModal } from '@/app/admin/components/modals/IdeaStatsModal';

// Re-export all the individual components for backward compatibility
export { RoleChangeModal as RoleChangePopup };
export { EditUserModal as EditUserPopup };
export { TeamChangeModal as TeamChangePopup };
export { DeleteConfirmModal as DeleteConfirmPopup };
export { TeamLeadActionsModal as TeamLeadActionsPopup };
export { AddUserModal as AddUserPopup };
export { IdeaStatsModal as IdeaStatsPopup };

// Common interfaces that were shared between components
export interface Team {
    _id: string;
    name: string;
}

export interface User {
    _id: string;
    name: string;
    username: string;
    role: 'admin' | 'teamLead' | 'teamMember';
    teamId?: string;
    teamName?: string;
}

export type RoleConfirmState = null | string | {
    userId: string;
    username: string;
    hasIdeas?: boolean;
    teamId?: string;
};
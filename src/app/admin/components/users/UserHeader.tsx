import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Search, Plus, Users, Trash2 } from 'lucide-react';
import Image from 'next/image';

interface UserHeaderProps {
    totalUsers: number;
    searchTerm: string;
    selectedUsers: Set<string>;
    setSearchTerm: (term: string) => void;
    onTeamChange: () => void;
    onRoleChange: () => void;
    onDelete: () => void;
    onAddUser: () => void;
}

export default function UserHeader({
    totalUsers,
    searchTerm,
    selectedUsers,
    setSearchTerm,
    onTeamChange,
    onRoleChange,
    onDelete,
    onAddUser
}: UserHeaderProps) {
    const hasSelectedUsers = selectedUsers.size > 0;

    return (
        <div className="bg-card border rounded-lg p-6 space-y-4">
            {/* Header */}
            <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4">
                <div>
                    <h2 className="text-3xl font-bold text-primary">User Management</h2>
                    <p className="text-muted-foreground">
                        {totalUsers} total user{totalUsers !== 1 ? 's' : ''}
                        {hasSelectedUsers && (
                            <span className="ml-2 text-primary font-medium">
                                ({selectedUsers.size} selected)
                            </span>
                        )}
                    </p>
                </div>
                
                <Button
                    onClick={onAddUser}
                >
                    <Plus className="h-4 w-4 mr-2" />
                    Add User
                </Button>
            </div>

            {/* Search and Bulk Actions */}
            <div className="flex flex-col sm:flex-row gap-4">
                {/* Search */}
                <div className="relative flex-1">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                    <Input
                        placeholder="Search users by name or username..."
                        value={searchTerm}
                        onChange={(e) => setSearchTerm(e.target.value)}
                        className="pl-10"
                    />
                </div>

                {/* Bulk Actions */}
                {hasSelectedUsers && (
                    <div className="flex gap-2">
                        <Button
                            variant="outline"
                            size="sm"
                            onClick={onTeamChange}
                            title="Change Team"
                        >
                            <Users className="h-4 w-4" />
                        </Button>
                        
                        <Button
                            variant="outline"
                            size="sm"
                            onClick={onRoleChange}
                            title="Change Role"
                        >
                            <Image src="/svgs/Asset 2.svg" alt="Role" width={16} height={16} className="h-4 w-4" />
                        </Button>
                        
                        <Button
                            variant="outline"
                            size="sm"
                            onClick={onDelete}
                            className="hover:bg-destructive hover:text-destructive-foreground"
                            title="Delete"
                        >
                            <Trash2 className="h-4 w-4" />
                        </Button>
                    </div>
                )}
            </div>
        </div>
    );
}
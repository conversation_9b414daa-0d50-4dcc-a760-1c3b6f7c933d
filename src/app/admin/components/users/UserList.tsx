import { Button } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import { StateCard } from '@/components/ui/state-card';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';
import { Users, Edit, BarChart3, Trash2, Shield, ChevronDown } from 'lucide-react';
import UserRoleIcon from '../UserRoleIcon';

interface TeamGroup {
    _id: string;
    name: string;
    users: User[];
}

interface User {
    _id: string;
    name: string;
    username: string;
    role: 'admin' | 'teamLead' | 'teamMember';
    teamId?: string;
    teamName?: string;
}

interface GroupedUsers {
    teams: TeamGroup[];
    noTeam: User[];
}

interface UserListProps {
    groupedUsers: GroupedUsers;
    selectedUsers: Set<string>;
    setSelectedUsers: (users: Set<string>) => void;
    onTeamChange: (userId: string) => void;
    onPasswordChange: (userId: string) => void;
    onIdeaStatsClick: (userId: string) => void;
    onDeleteClick: (userId: string) => void;
    onRoleClick: (userId: string, username: string) => void;
    onMakePending: (userId: string) => void;
}

export default function UserList({
    groupedUsers,
    selectedUsers,
    setSelectedUsers,
    onTeamChange,
    onPasswordChange,
    onIdeaStatsClick,
    onDeleteClick,
    onRoleClick,
    onMakePending
}: UserListProps) {
    const toggleUserSelection = (userId: string) => {
        const newSelected = new Set(selectedUsers);
        if (selectedUsers.has(userId)) {
            newSelected.delete(userId);
        } else {
            newSelected.add(userId);
        }
        setSelectedUsers(newSelected);
    };


    const renderUserCard = (user: User, isNoTeam: boolean = false) => (
        <div
            key={user._id}
            className={`bg-card border p-4 transition-all duration-200 ${
                selectedUsers.has(user._id) 
                    ? 'bg-primary/5' 
                    : 'hover:shadow-md'
            }`}
        >
            {/* Responsive Layout */}
            <div className="flex flex-col sm:flex-row sm:items-center gap-3 sm:gap-4">
                {/* Top row: checkbox, role icon, user info */}
                <div className="flex items-center gap-3 flex-1 min-w-0">
                    <Checkbox
                        checked={selectedUsers.has(user._id)}
                        onCheckedChange={() => toggleUserSelection(user._id)}
                        className="shrink-0"
                    />

                    <UserRoleIcon 
                        role={user.role as 'teamLead' | 'teamMember'}
                        className="w-8 h-8 shrink-0"
                        onClick={() => onRoleClick(user._id, user.name)}
                    />

                    <div className="flex-1 min-w-0">
                        <div className="font-medium">{user.name}</div>
                        <div className="text-sm text-muted-foreground">@{user.username}</div>
                    </div>
                </div>

                {/* Action buttons - right aligned on desktop, full width on mobile */}
                <div className="flex items-center gap-1 shrink-0 justify-end">
                    {/* Shield button - only for NO TEAM users who are NOT team leads */}
                    {isNoTeam && user.role !== 'teamLead' && (
                        <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => onMakePending(user._id)}
                            className="h-8 w-8 p-0 hover:!bg-transparent hover:text-yellow-600"
                            title="Make pending"
                        >
                            <Shield className="h-4 w-4" />
                        </Button>
                    )}
                    
                    <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => onTeamChange(user._id)}
                        className="h-8 w-8 p-0 hover:!bg-transparent hover:text-primary"
                        title="Change team"
                    >
                        <Users className="h-4 w-4" />
                    </Button>
                    
                    <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => onPasswordChange(user._id)}
                        className="h-8 w-8 p-0 hover:!bg-transparent hover:text-primary"
                        title="Edit user"
                    >
                        <Edit className="h-4 w-4" />
                    </Button>
                    
                    <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => onIdeaStatsClick(user._id)}
                        className="h-8 w-8 p-0 hover:!bg-transparent hover:text-primary"
                        title="View idea stats"
                    >
                        <BarChart3 className="h-4 w-4" />
                    </Button>
                    
                    <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => onDeleteClick(user._id)}
                        className="h-8 w-8 p-0 hover:!bg-transparent hover:text-destructive"
                        title="Delete user"
                    >
                        <Trash2 className="h-4 w-4" />
                    </Button>
                </div>
            </div>
        </div>
    );

    const teamsWithUsers = groupedUsers.teams.filter(team => team.users.length > 0);
    
    if (!teamsWithUsers.length && !groupedUsers.noTeam.length) {
        return (
            <div className="flex justify-center items-center min-h-[400px]">
                <StateCard 
                    state="empty" 
                    title="No Users Found" 
                    message="No users found for this event."
                />
            </div>
        );
    }

    return (
        <div className="space-y-6">
            {/* Teams - Only show teams that have users */}
            {teamsWithUsers.map((team) => (
                <Collapsible key={team._id} defaultOpen={true} className="space-y-4">
                    {/* Team Header - Collapsible trigger */}
                    <div className="flex items-center justify-between">
                        <CollapsibleTrigger className="flex items-center gap-2 group flex-1 min-w-0">
                            <ChevronDown className="h-5 w-5 transition-transform duration-200 group-data-[state=open]:rotate-180 text-primary shrink-0" />
                            <h1 className="text-lg font-semibold text-primary">{team.name}</h1>
                        </CollapsibleTrigger>
                        
                        <span className="inline-flex items-center px-2.5 py-0.5 text-xs font-medium bg-primary text-primary-foreground">
                            {team.users.length} user{team.users.length !== 1 ? 's' : ''}
                        </span>
                    </div>

                    {/* Team Users - Collapsible content */}
                    <CollapsibleContent className="space-y-2">
                        {team.users.map(user => renderUserCard(user, false))}
                    </CollapsibleContent>
                </Collapsible>
            ))}

            {/* No Team Users */}
            {groupedUsers.noTeam.length > 0 && (
                <Collapsible defaultOpen={true} className="space-y-4">
                    {/* No Team Header - Collapsible trigger */}
                    <div className="flex items-center justify-between">
                        <CollapsibleTrigger className="flex items-center gap-2 group flex-1 min-w-0">
                            <ChevronDown className="h-5 w-5 transition-transform duration-200 group-data-[state=open]:rotate-180 text-primary shrink-0" />
                            <h1 className="text-lg font-semibold text-primary">No Team</h1>
                        </CollapsibleTrigger>
                        
                        <span className="inline-flex items-center px-2.5 py-0.5 text-xs font-medium bg-muted text-muted-foreground">
                            {groupedUsers.noTeam.length} user{groupedUsers.noTeam.length !== 1 ? 's' : ''}
                        </span>
                    </div>

                    {/* No Team Users - Collapsible content */}
                    <CollapsibleContent className="space-y-2">
                        {groupedUsers.noTeam.map(user => renderUserCard(user, true))}
                    </CollapsibleContent>
                </Collapsible>
            )}
        </div>
    );
}
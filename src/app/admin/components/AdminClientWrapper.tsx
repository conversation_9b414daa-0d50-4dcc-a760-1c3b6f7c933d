"use client";

import { useState, useEffect } from 'react';
import { SidebarProvider, SidebarInset } from "@/components/ui/sidebar";
import { Toaster } from 'sonner';
import AdminSidebar from './AdminSidebar';
import AdminHeader from './AdminHeader';

interface AdminClientWrapperProps {
  children: React.ReactNode;
}

export default function AdminClientWrapper({ children }: AdminClientWrapperProps) {
  const [activeSection, setActiveSection] = useState('events');

  useEffect(() => {
    // Get initial section from URL on mount
    const params = new URLSearchParams(window.location.search);
    const section = params.get('section');
    if (section) {
      setActiveSection(section);
    }
  }, []);

  return (
    <>
      <SidebarProvider>
        <div className="min-h-screen flex w-full bg-background text-foreground">
          <AdminSidebar 
            activeSection={activeSection} 
            onSectionChange={setActiveSection} 
          />
          
          <SidebarInset className="flex-1 bg-background text-foreground">
            <AdminHeader activeSection={activeSection} />
            
            {/* Main Content Area */}
            <main className="flex-1 p-6">
              {children}
            </main>
          </SidebarInset>
        </div>
      </SidebarProvider>
      <Toaster 
        position="top-right" 
        toastOptions={{
          unstyled: true,
          classNames: {
            toast: 'bg-background border-2 border-primary text-foreground font-medium p-4 shadow-none flex items-center gap-3',
            success: 'border-secondary',
            error: 'border-destructive',
            title: 'font-medium text-foreground',
            description: 'text-muted-foreground',
            actionButton: 'bg-primary text-primary-foreground border-0 px-3 py-1',
            cancelButton: 'bg-secondary text-secondary-foreground border-0 px-3 py-1',
            closeButton: 'bg-muted text-muted-foreground border-0 p-1',
            icon: 'flex-shrink-0'
          },
          style: {
            fontFamily: "'neue-machina-Medium', sans-serif"
          }
        }}
      />
    </>
  );
}
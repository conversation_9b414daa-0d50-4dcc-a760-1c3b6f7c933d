"use client";

import { useState } from 'react';
import { useMutation } from 'convex/react';
import { api } from '@/../convex/_generated/api';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import { toast } from 'sonner';
import { AlertTriangle } from 'lucide-react';
import { Label } from '@/components/ui/label';

export default function DangerZoneCard() {
  const [showModal, setShowModal] = useState(false);
  const [confirmText, setConfirmText] = useState('');
  const [isClearing, setIsClearing] = useState(false);
  
  const clearAllData = useMutation(api.backupData.clearAllDataMutation);

  const handleClearData = async () => {
    if (confirmText !== 'DELETE ALL DATA' || isClearing) return;
    
    setIsClearing(true);
    try {
      await clearAllData({});
      setShowModal(false);
      setConfirmText('');
      toast.success('All data has been cleared successfully');
    } catch (error) {
      console.error('Clear data error:', error);
      toast.error('Failed to clear data');
    } finally {
      setIsClearing(false);
    }
  };

  const isConfirmValid = confirmText === 'DELETE ALL DATA';

  return (
    <>
      <div className="p-6 border-2 border-destructive bg-card/90 backdrop-blur-sm">
        <div className="flex items-center gap-3 mb-6">
          <AlertTriangle className="h-6 w-6 text-destructive" />
          <h2 className="text-xl font-semibold text-destructive">Danger Zone</h2>
        </div>
        
        <div className="flex flex-col sm:flex-row sm:items-center gap-4 p-4 border border-destructive/20 bg-destructive/5 rounded-md">
          <div className="flex-1">
            <h3 className="text-lg font-medium text-foreground mb-2">Clear All Data</h3>
            <p className="text-sm text-muted-foreground">
              Permanently delete all data from the Convex database. Backup metadata will be preserved for recovery.
            </p>
          </div>
          <Button
            variant="default"
            onClick={() => setShowModal(true)}
            className="shrink-0"
          >
            Clear All Data
          </Button>
        </div>
      </div>

      {/* Clear Data Confirmation Modal */}
      <AlertDialog 
        open={showModal} 
        onOpenChange={(open) => {
          if (!open) {
            setShowModal(false);
            setConfirmText('');
          }
        }}
      >
        <AlertDialogContent className="max-w-2xl">
          <AlertDialogHeader>
            <AlertDialogTitle className="flex items-center gap-3 text-destructive">
              <AlertTriangle className="h-6 w-6" />
              Clear All Data
            </AlertDialogTitle>
            <AlertDialogDescription>
              This will permanently delete ALL data from your Convex database.
            </AlertDialogDescription>
          </AlertDialogHeader>
          
          <div className="py-4 space-y-4">
            <div className="p-4 bg-destructive/10 border border-destructive/20 rounded-md">
              <h4 className="font-medium text-destructive mb-3">What will be deleted:</h4>
              <ul className="text-sm text-muted-foreground space-y-1">
                <li>• All non-admin users, events, teams, and ideas will be deleted</li>
                <li>• All sessions, votes, and quickfire data will be deleted</li>
                <li>• All settings and admin configurations will be deleted</li>
              </ul>
            </div>

            <div className="p-4 bg-accent/10 border border-accent/20 rounded-md">
              <h4 className="font-medium text-accent mb-3">What will be preserved:</h4>
              <ul className="text-sm text-muted-foreground space-y-1">
                <li>• <strong>Admin users will be preserved</strong> - you will not lose access</li>
                <li>• <strong>Backup metadata and files will be preserved</strong> - you can still restore from existing backups</li>
                <li>• <strong>File storage will be preserved</strong> - all backup ZIP files remain intact</li>
              </ul>
            </div>

            <div className="p-4 bg-secondary/10 border border-secondary/20 rounded-md">
              <p className="text-sm text-secondary">
                <strong>Important:</strong> This action cannot be undone without a backup restore.
              </p>
            </div>

            <div className="space-y-2">
              <Label htmlFor="confirm-text">
                Type <strong>DELETE ALL DATA</strong> to confirm:
              </Label>
              <Input
                id="confirm-text"
                type="text"
                value={confirmText}
                onChange={(e) => setConfirmText(e.target.value)}
                placeholder="DELETE ALL DATA"
                disabled={isClearing}
                className="font-mono shadow-none focus-visible:ring-0 focus-visible:ring-offset-0"
              />
            </div>

            {isClearing && (
              <div className="text-center text-sm text-muted-foreground">
                Clearing data...
              </div>
            )}
          </div>

          <AlertDialogFooter>
            <AlertDialogCancel disabled={isClearing}>
              Cancel
            </AlertDialogCancel>
            <AlertDialogAction
              onClick={handleClearData}
              disabled={!isConfirmValid || isClearing}
              
            >
              {isClearing ? 'Clearing...' : 'Clear All Data'}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
}
"use client";

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { toast } from 'sonner';
import { Label } from '@/components/ui/label';

interface BackupCreationCardProps {
  onBackupCreated: () => void;
}

export default function BackupCreationCard({ onBackupCreated }: BackupCreationCardProps) {
  const [description, setDescription] = useState('');
  const [isCreating, setIsCreating] = useState(false);
  
  const handleCreateBackup = async () => {
    if (isCreating) return;
    
    setIsCreating(true);
    try {
      const response = await fetch('/api/backup/create', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          description: description.trim() || undefined,
          includeStorage: false, // Always false since no file storage used
          createdBy: 'admin',
        }),
      });
      
      const result = await response.json();
      
      if (result.success) {
        setDescription('');
        onBackupCreated();
        toast.success('Backup created successfully!');
      } else {
        toast.error(`Backup failed: ${result.message || 'Unknown error'}`);
      }
    } catch (error) {
      console.error('Backup creation error:', error);
      toast.error('Failed to create backup');
    } finally {
      setIsCreating(false);
    }
  };

  return (
    <div className="p-6 border-2 border-accent bg-card/90 backdrop-blur-sm">
      <h2 className="text-xl font-semibold text-primary mb-6">Create New Backup</h2>
      
      <div className="space-y-4">
        <div className="space-y-2">
          <Label htmlFor="backup-description">
            Description (optional)
          </Label>
          <Input
            id="backup-description"
            type="text"
            placeholder="Backup description (optional)"
            value={description}
            onChange={(e) => setDescription(e.target.value)}
            disabled={isCreating}
            className="w-full border-accent shadow-none focus-visible:ring-0 focus-visible:ring-offset-0"
          />
        </div>
        
        <Button
          onClick={handleCreateBackup}
          disabled={isCreating}
          className="w-full"
          variant="outline"
        >
          {isCreating ? 'Creating...' : 'Create Backup'}
        </Button>
      </div>
    </div>
  );
}
"use client";

import { useState } from 'react';
import { useAction } from 'convex/react';
import { api } from '@/../convex/_generated/api';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Progress } from '@/components/ui/progress';
import { toast } from 'sonner';
import { Upload } from 'lucide-react';
import { Label } from '@/components/ui/label';

interface BackupUploadCardProps {
  onBackupUploaded: () => void;
}

interface UploadProgress {
  isUploading: boolean;
  progress: number;
  message: string;
  fileName: string;
}

export default function BackupUploadCard({ onBackupUploaded }: BackupUploadCardProps) {
  const [uploadProgress, setUploadProgress] = useState<UploadProgress | null>(null);
  const [description, setDescription] = useState('');
  
  const uploadBackup = useAction(api.backupActions.uploadBackupAction);

  const handleFileSelect = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    // Start upload immediately after file selection
    setUploadProgress({
      isUploading: true,
      progress: 0,
      message: 'Starting upload...',
      fileName: file.name
    });

    try {
      // Simulate progress updates
      let progress = 0;
      const progressInterval = setInterval(() => {
        progress += Math.random() * 20;
        if (progress > 95) progress = 95;
        setUploadProgress(prev => prev ? {
          ...prev,
          progress,
          message: progress < 30 ? 'Reading file...' :
                  progress < 60 ? 'Uploading to server...' :
                  progress < 90 ? 'Processing upload...' : 'Almost done...'
        } : null);
      }, 300);

      const fileBuffer = await file.arrayBuffer();
      const result = await uploadBackup({
        fileName: file.name,
        fileContent: fileBuffer,
        description: description.trim() || undefined,
        createdBy: 'admin',
      });

      clearInterval(progressInterval);

      if (result.success) {
        setUploadProgress(prev => prev ? {
          ...prev,
          progress: 100,
          message: 'Upload completed!'
        } : null);
        
        setTimeout(() => {
          setUploadProgress(null);
          setDescription('');
          onBackupUploaded();
          toast.success('Backup uploaded successfully!');
        }, 1000);
      } else {
        setUploadProgress(null);
        toast.error(`Upload failed: ${result.message || 'Unknown error'}`);
      }
    } catch (error) {
      setUploadProgress(null);
      console.error('Upload error:', error);
      toast.error('Failed to upload backup');
    }
    
    // Reset file input
    event.target.value = '';
  };

  return (
    <div className="p-6 border-2 border-primary bg-card/90 backdrop-blur-sm">
      <h2 className="text-xl font-semibold text-primary mb-6">Upload Backup File</h2>
      
      <div className="space-y-4">
        <div className="space-y-2">
          <Label htmlFor="upload-description">
            Description (optional)
          </Label>
          <Input
            id="upload-description"
            type="text"
            placeholder="Upload description (optional)"
            value={description}
            onChange={(e) => setDescription(e.target.value)}
            disabled={uploadProgress?.isUploading}
            className="w-full border-primary shadow-none focus-visible:ring-0 focus-visible:ring-offset-0"
          />
        </div>

        <div className="space-y-3">
          <Input
            type="file"
            accept=".zip"
            onChange={handleFileSelect}
            disabled={uploadProgress?.isUploading}
            style={{ display: 'none' }}
            id="backup-file-input"
          />
          
          <Button
            asChild
            variant="outline"
            className="w-full"
            disabled={uploadProgress?.isUploading}
          >
            <Label htmlFor="backup-file-input">
              <Upload className="h-4 w-4" />
              {uploadProgress ? 'Uploading...' : 'Choose File to Upload'}
            </Label>
          </Button>

          {uploadProgress && (
            <div className="space-y-2">
              <div className="flex justify-between text-sm text-muted-foreground">
                <span>{uploadProgress.fileName}</span>
                <span>{Math.round(uploadProgress.progress)}%</span>
              </div>
              <Progress 
                value={uploadProgress.progress} 
                className="w-full h-3"
              />
              <div className="text-sm text-center text-muted-foreground">
                {uploadProgress.message}
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
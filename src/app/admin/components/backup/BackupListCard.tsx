"use client";

import { useState } from 'react';
import { useQuery, useAction } from 'convex/react';
import { api } from '@/../convex/_generated/api';
import { Id } from '@/../convex/_generated/dataModel';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { toast } from 'sonner';
import { Download, RotateCcw, Trash2, Info } from 'lucide-react';

interface Backup {
  _id: Id<"backups">;
  id: string;
  timestamp: number;
  description?: string;
  size: number;
  status: "completed" | "failed" | "creating";
  createdBy: string;
  fileName: string;
  fileId?: Id<"_storage">;
  includeStorage: boolean;
  tablesIncluded: string[];
  createdAt: number;
  updatedAt?: number;
}

interface RestoreProgress {
  fileName: string;
  progress: number;
  message: string;
}

const getStatusVariant = (status: string) => {
  if (status === 'completed') return 'outline';
  if (status === 'failed') return 'destructive';
  return 'secondary';
};

export default function BackupListCard() {
  const backups = useQuery(api.backupData.getBackupsList);
  const [confirmAction, setConfirmAction] = useState<{
    type: 'delete' | 'restore';
    backup: Backup;
  } | null>(null);
  const [restoringBackup, setRestoringBackup] = useState<RestoreProgress | null>(null);
  const [deletingBackup, setDeletingBackup] = useState<string | null>(null);
  
  const deleteBackup = useAction(api.backupActions.deleteBackupAction);
  const getDownloadUrl = useAction(api.backupActions.getBackupDownloadUrlAction);

  const handleDownload = async (backup: Backup) => {
    try {
      const result = await getDownloadUrl({ fileName: backup.fileName });
      if (result.success && result.downloadUrl) {
        // Fetch the file and force download
        const response = await fetch(result.downloadUrl);
        if (!response.ok) {
          throw new Error('Failed to fetch backup file');
        }
        
        const blob = await response.blob();
        const url = URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.download = backup.fileName;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        URL.revokeObjectURL(url);
        
        toast.success('Download completed successfully!');
      } else {
        toast.error(`Download failed: ${result.message || 'Unknown error'}`);
      }
    } catch (error) {
      console.error('Download error:', error);
      toast.error('Failed to download backup');
    }
  };

  const handleDelete = async (backup: Backup) => {
    try {
      setDeletingBackup(backup._id);
      setConfirmAction(null);
      
      const result = await deleteBackup({
        backupId: backup._id,
        fileName: backup.fileName,
      });
      
      if (result.success) {
        toast.success('Backup deleted successfully!');
      } else {
        toast.error(`Delete failed: ${result.message || 'Unknown error'}`);
      }
    } catch (error) {
      console.error('Delete error:', error);
      toast.error('Failed to delete backup');
    } finally {
      setDeletingBackup(null);
    }
  };

  const handleRestore = async (backup: Backup) => {
    setConfirmAction(null);
    setRestoringBackup({
      fileName: backup.fileName,
      progress: 0,
      message: 'Starting restore...'
    });

    try {
      // Simulate progress updates
      let progress = 0;
      const progressInterval = setInterval(() => {
        progress += Math.random() * 15;
        if (progress > 95) progress = 95;
        setRestoringBackup(prev => prev ? {
          ...prev,
          progress,
          message: progress < 30 ? 'Extracting backup...' :
                  progress < 60 ? 'Restoring tables...' :
                  progress < 90 ? 'Finishing restore...' : 'Almost done...'
        } : null);
      }, 500);

      const response = await fetch('/api/backup/restore', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          fileName: backup.fileName,
          replaceAll: true,
        }),
      });
      
      clearInterval(progressInterval);
      const result = await response.json();
      
      if (result.success) {
        setRestoringBackup(prev => prev ? {
          ...prev,
          progress: 100,
          message: 'Restore completed!'
        } : null);
        
        setTimeout(() => {
          setRestoringBackup(null);
          toast.success('Backup restored successfully!');
        }, 1000);
      } else {
        setRestoringBackup(null);
        toast.error(`Restore failed: ${result.message || 'Unknown error'}`);
      }
    } catch (error) {
      setRestoringBackup(null);
      console.error('Restore error:', error);
      toast.error('Failed to restore backup');
    }
  };

  const formatSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const formatDate = (timestamp: number) => {
    return new Date(timestamp).toLocaleString();
  };

  if (backups === undefined) {
    return (
      <div className="p-6 border-2 border-secondary bg-card/90 backdrop-blur-sm">
        <h2 className="text-xl font-semibold text-primary mb-6">Backup History</h2>
        <div className="text-muted-foreground">Loading backups...</div>
      </div>
    );
  }

  if (backups.length === 0) {
    return (
      <div className="p-6 border-2 border-secondary bg-card/90 backdrop-blur-sm">
        <h2 className="text-xl font-semibold text-primary mb-6">Backup History</h2>
        <div className="text-center text-muted-foreground py-8">
          No backups found. Create your first backup above.
        </div>
      </div>
    );
  }

  return (
    <>
      <div className="p-6 border-2 border-secondary bg-card/90 backdrop-blur-sm">
        <h2 className="text-xl font-semibold text-primary mb-6">Backup History</h2>
        
        <div className="space-y-4">
          {backups.map((backup: Backup) => {
            const isRestoring = restoringBackup?.fileName === backup.fileName;
            const isDeleting = deletingBackup === backup._id;
            
            return (
              <div 
                key={backup._id} 
                className="relative overflow-hidden p-4 border border-border hover:bg-muted/50 transition-colors"
              >
                {isRestoring && (
                  <div 
                    className="absolute inset-0 bg-accent/10 transition-all duration-500 ease-out"
                    style={{ width: `${restoringBackup.progress}%` }}
                  />
                )}
                
                {/* Mobile Layout */}
                <div className="lg:hidden relative z-10">
                  {/* Badge at Top */}
                  <div className="mb-3">
                    <Badge variant={getStatusVariant(backup.status)} className="shrink-0">
                      {backup.status === 'completed' ? 'Completed' :
                       backup.status === 'failed' ? 'Failed' :
                       'Creating'}
                    </Badge>
                  </div>
                  
                  {/* Backup Info - Clean Layout */}
                  <div className="space-y-1 mb-4">
                    <div className="text-foreground hover:text-primary transition-colors cursor-text truncate font-ultrabold text-lg">
                      {backup.description || 'Untitled Backup'}
                    </div>
                    <div className="text-sm text-accent">
                      {formatDate(backup.timestamp)}
                    </div>
                    {isRestoring && (
                      <div className="mt-3 space-y-2">
                        <Progress value={restoringBackup.progress} className="w-full h-2" />
                        <p className="text-sm font-medium text-foreground">
                          {restoringBackup.message} ({Math.round(restoringBackup.progress)}%)
                        </p>
                      </div>
                    )}
                  </div>
                  
                  {/* Buttons at Bottom */}
                  <div className="flex items-center gap-2 flex-wrap justify-end">
                    <Dialog>
                      <DialogTrigger asChild>
                        <Button
                          variant="ghost"
                          size="sm"
                          className="btn-icon"
                          title="View backup details"
                        >
                          <Info className="h-4 w-4" />
                        </Button>
                      </DialogTrigger>
                      <DialogContent className="max-w-md">
                        <DialogHeader>
                          <DialogTitle>Backup Details</DialogTitle>
                          <DialogDescription>
                            View detailed information about this backup
                          </DialogDescription>
                        </DialogHeader>
                        <div className="space-y-4">
                          <div className="grid grid-cols-1 gap-y-3 text-sm">
                            <div>
                              <span className="text-accent font-medium">Name:</span>
                              <div className="text-foreground mt-1">{backup.description || 'Untitled Backup'}</div>
                            </div>
                            <div>
                              <span className="text-accent font-medium">Created by:</span>
                              <div className="text-foreground mt-1">{backup.createdBy}</div>
                            </div>
                            <div>
                              <span className="text-accent font-medium">Date:</span>
                              <div className="text-foreground mt-1">{formatDate(backup.timestamp)}</div>
                            </div>
                            <div>
                              <span className="text-accent font-medium">File:</span>
                              <div className="text-foreground mt-1 break-all">{backup.fileName}</div>
                            </div>
                            <div>
                              <span className="text-accent font-medium">Size:</span>
                              <div className="text-foreground mt-1">{formatSize(backup.size)}</div>
                            </div>
                            <div>
                              <span className="text-accent font-medium">Storage:</span>
                              <div className="text-foreground mt-1">{backup.includeStorage ? 'Included' : 'Excluded'}</div>
                            </div>
                            <div>
                              <span className="text-accent font-medium">Status:</span>
                              <div className="mt-1">
                                <Badge variant={getStatusVariant(backup.status)}>
                                  {backup.status === 'completed' ? 'Completed' :
                                   backup.status === 'failed' ? 'Failed' :
                                   'Creating'}
                                </Badge>
                              </div>
                            </div>
                          </div>
                        </div>
                      </DialogContent>
                    </Dialog>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleDownload(backup)}
                      disabled={backup.status !== 'completed' || isRestoring}
                      className="btn-icon"
                      title="Download backup"
                    >
                      <Download className="h-4 w-4" />
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => setConfirmAction({ type: 'restore', backup })}
                      disabled={backup.status !== 'completed' || isRestoring}
                      className="btn-icon"
                      title={isRestoring ? 'Restoring...' : 'Restore backup'}
                    >
                      <RotateCcw className="h-4 w-4" />
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => setConfirmAction({ type: 'delete', backup })}
                      disabled={isRestoring || isDeleting}
                      className="btn-icon"
                      title={isDeleting ? 'Deleting...' : 'Delete backup'}
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                </div>

                {/* Desktop Layout - Follow admin-card pattern */}
                <div className="hidden lg:flex lg:flex-row lg:items-start lg:gap-4 relative z-10">
                  {/* Backup Info - Left Side */}
                  <div className="flex-1 min-w-0">
                    <div className="space-y-1">
                      <div className="text-foreground hover:text-primary transition-colors cursor-text truncate font-ultrabold text-lg">
                        {backup.description || 'Untitled Backup'}
                      </div>
                      <div className="text-sm text-accent">
                        {formatDate(backup.timestamp)}
                      </div>
                      {isRestoring && (
                        <div className="mt-3 space-y-2">
                          <Progress value={restoringBackup.progress} className="w-full h-2" />
                          <p className="text-sm font-medium text-foreground">
                            {restoringBackup.message} ({Math.round(restoringBackup.progress)}%)
                          </p>
                        </div>
                      )}
                    </div>
                  </div>

                  {/* Right Side - Badge and Buttons */}
                  <div className="flex flex-col items-end gap-3 lg:min-h-[80px] lg:justify-between min-w-0">
                    {/* Status Badge - Top Row */}
                    <Badge variant={getStatusVariant(backup.status)}>
                      {backup.status === 'completed' ? 'Completed' :
                       backup.status === 'failed' ? 'Failed' :
                       'Creating'}
                    </Badge>
                    
                    {/* Action Buttons - Bottom Row */}
                    <div className="flex items-center gap-2 flex-wrap justify-end">
                      <Dialog>
                        <DialogTrigger asChild>
                          <Button
                            variant="ghost"
                            size="sm"
                            className="btn-icon"
                            title="View backup details"
                          >
                            <Info className="h-4 w-4" />
                          </Button>
                        </DialogTrigger>
                        <DialogContent className="max-w-md">
                          <DialogHeader>
                            <DialogTitle>Backup Details</DialogTitle>
                            <DialogDescription>
                              View detailed information about this backup
                            </DialogDescription>
                          </DialogHeader>
                          <div className="space-y-4">
                            <div className="grid grid-cols-1 gap-y-3 text-sm">
                              <div>
                                <span className="text-accent font-medium">Name:</span>
                                <div className="text-foreground mt-1">{backup.description || 'Untitled Backup'}</div>
                              </div>
                              <div>
                                <span className="text-accent font-medium">Created by:</span>
                                <div className="text-foreground mt-1">{backup.createdBy}</div>
                              </div>
                              <div>
                                <span className="text-accent font-medium">Date:</span>
                                <div className="text-foreground mt-1">{formatDate(backup.timestamp)}</div>
                              </div>
                              <div>
                                <span className="text-accent font-medium">File:</span>
                                <div className="text-foreground mt-1 break-all">{backup.fileName}</div>
                              </div>
                              <div>
                                <span className="text-accent font-medium">Size:</span>
                                <div className="text-foreground mt-1">{formatSize(backup.size)}</div>
                              </div>
                              <div>
                                <span className="text-accent font-medium">Storage:</span>
                                <div className="text-foreground mt-1">{backup.includeStorage ? 'Included' : 'Excluded'}</div>
                              </div>
                              <div>
                                <span className="text-accent font-medium">Status:</span>
                                <div className="mt-1">
                                  <Badge variant={getStatusVariant(backup.status)}>
                                    {backup.status === 'completed' ? 'Completed' :
                                     backup.status === 'failed' ? 'Failed' :
                                     'Creating'}
                                  </Badge>
                                </div>
                              </div>
                            </div>
                          </div>
                        </DialogContent>
                      </Dialog>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleDownload(backup)}
                        disabled={backup.status !== 'completed' || isRestoring}
                        className="btn-icon"
                        title="Download backup"
                      >
                        <Download className="h-4 w-4" />
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => setConfirmAction({ type: 'restore', backup })}
                        disabled={backup.status !== 'completed' || isRestoring}
                        className="btn-icon"
                        title={isRestoring ? 'Restoring...' : 'Restore backup'}
                      >
                        <RotateCcw className="h-4 w-4" />
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => setConfirmAction({ type: 'delete', backup })}
                        disabled={isRestoring || isDeleting}
                        className="btn-icon"
                        title={isDeleting ? 'Deleting...' : 'Delete backup'}
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                </div>
              </div>
            );
          })}
        </div>
      </div>

      {/* Delete Confirmation Modal */}
      <AlertDialog 
        open={confirmAction?.type === 'delete'} 
        onOpenChange={(open) => !open && setConfirmAction(null)}
      >
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Delete Backup</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to delete this backup?
            </AlertDialogDescription>
          </AlertDialogHeader>
          <div className="py-4">
            <p className="font-medium text-foreground">
              {confirmAction?.backup?.description || 'Untitled Backup'}
            </p>
            <p className="text-sm text-destructive mt-2">
              This action cannot be undone.
            </p>
          </div>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={() => confirmAction && handleDelete(confirmAction.backup)}
            >
              Delete
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* Restore Confirmation Modal */}
      <AlertDialog 
        open={confirmAction?.type === 'restore'} 
        onOpenChange={(open) => !open && setConfirmAction(null)}
      >
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Restore Backup</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to restore this backup?
            </AlertDialogDescription>
          </AlertDialogHeader>
          <div className="py-4 space-y-3">
            <p className="font-medium text-foreground">
              {confirmAction?.backup?.description || 'Untitled Backup'}
            </p>
            <div className="p-3 bg-destructive/10 border border-destructive/20 rounded-md">
              <p className="text-sm font-medium text-destructive mb-2">
                Warning: This will replace ALL current data with the backup data.
              </p>
              <ul className="text-sm text-muted-foreground space-y-1">
                <li>• All current users, events, teams, and ideas will be replaced</li>
                <li>• Current sessions and voting data will be lost</li>
                <li>• This action cannot be undone</li>
              </ul>
            </div>
          </div>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={() => confirmAction && handleRestore(confirmAction.backup)}
            >
              Restore Backup
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
}
"use client";

import { useState, Suspense } from 'react';
import { ErrorBoundary } from 'react-error-boundary';
import { StateCard } from '@/components/ui/state-card';
import BackupErrorBoundary from './BackupErrorBoundary';
import BackupCreationCard from './backup/BackupCreationCard';
import BackupUploadCard from './backup/BackupUploadCard';
import BackupListCard from './backup/BackupListCard';
import DangerZoneCard from './backup/DangerZoneCard';

// Main error fallback for the entire backup management section
function MainBackupErrorFallback({ error, resetErrorBoundary }: { error: Error; resetErrorBoundary: () => void }) {
  return (
    <BackupErrorBoundary 
      error={error}
      resetErrorBoundary={resetErrorBoundary}
      title="Backup Management Error"
      message="There was an error loading the backup management interface."
    />
  );
}

// Section-specific error fallbacks
function BackupCreationErrorFallback({ error, resetErrorBoundary }: { error: Error; resetErrorBoundary: () => void }) {
  return (
    <BackupErrorBoundary 
      error={error}
      resetErrorBoundary={resetErrorBoundary}
      title="Backup Creation Error"
      message="Failed to load backup creation section."
    />
  );
}

function BackupUploadErrorFallback({ error, resetErrorBoundary }: { error: Error; resetErrorBoundary: () => void }) {
  return (
    <BackupErrorBoundary 
      error={error}
      resetErrorBoundary={resetErrorBoundary}
      title="Backup Upload Error"
      message="Failed to load backup upload section."
    />
  );
}

function BackupListErrorFallback({ error, resetErrorBoundary }: { error: Error; resetErrorBoundary: () => void }) {
  return (
    <BackupErrorBoundary 
      error={error}
      resetErrorBoundary={resetErrorBoundary}
      title="Backup History Error"
      message="Failed to load backup history section."
    />
  );
}

function DangerZoneErrorFallback({ error, resetErrorBoundary }: { error: Error; resetErrorBoundary: () => void }) {
  return (
    <BackupErrorBoundary 
      error={error}
      resetErrorBoundary={resetErrorBoundary}
      title="Danger Zone Error"
      message="Failed to load danger zone section."
    />
  );
}

// Loading component using StateCard for consistency
function BackupLoading() {
  return (
    <div className="flex justify-center items-center min-h-[400px]">
      <StateCard
        state="loading"
        title="Loading..."
      />
    </div>
  );
}

// Main backup content component
function BackupContent() {
  const [refreshKey, setRefreshKey] = useState(0);

  const handleBackupCreated = () => {
    setRefreshKey(prev => prev + 1);
  };

  return (
    <div className="space-y-6" key={refreshKey}>
      {/* Top Row - Create and Upload Side by Side */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <ErrorBoundary 
          FallbackComponent={BackupCreationErrorFallback}
          onReset={() => setRefreshKey(prev => prev + 1)}
        >
          <Suspense fallback={
            <div className="p-6 border-2 border-accent bg-card/90 backdrop-blur-sm">
              <StateCard state="loading" title="Loading..." />
            </div>
          }>
            <BackupCreationCard onBackupCreated={handleBackupCreated} />
          </Suspense>
        </ErrorBoundary>

        <ErrorBoundary 
          FallbackComponent={BackupUploadErrorFallback}
          onReset={() => setRefreshKey(prev => prev + 1)}
        >
          <Suspense fallback={
            <div className="p-6 border-2 border-primary bg-card/90 backdrop-blur-sm">
              <StateCard state="loading" title="Loading..." />
            </div>
          }>
            <BackupUploadCard onBackupUploaded={handleBackupCreated} />
          </Suspense>
        </ErrorBoundary>
      </div>

      {/* Backup History Section */}
      <ErrorBoundary 
        FallbackComponent={BackupListErrorFallback}
        onReset={() => setRefreshKey(prev => prev + 1)}
      >
        <Suspense fallback={
          <div className="p-6 border-2 border-secondary bg-card/90 backdrop-blur-sm">
            <StateCard state="loading" title="Loading backup history..." />
          </div>
        }>
          <BackupListCard />
        </Suspense>
      </ErrorBoundary>

      {/* Danger Zone Section */}
      <ErrorBoundary 
        FallbackComponent={DangerZoneErrorFallback}
        onReset={() => setRefreshKey(prev => prev + 1)}
      >
        <Suspense fallback={
          <div className="p-6 border-2 border-destructive bg-card/90 backdrop-blur-sm">
            <StateCard state="loading" title="Loading..." />
          </div>
        }>
          <DangerZoneCard />
        </Suspense>
      </ErrorBoundary>
    </div>
  );
}

export default function BackupsManagement() {
  return (
    <ErrorBoundary FallbackComponent={MainBackupErrorFallback}>
      <Suspense fallback={<BackupLoading />}>
        <BackupContent />
      </Suspense>
    </ErrorBoundary>
  );
}
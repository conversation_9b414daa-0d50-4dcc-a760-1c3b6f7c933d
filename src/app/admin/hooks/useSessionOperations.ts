import { useState, useCallback } from 'react';
import { useQuery, useMutation } from "convex/react";
import { useSession } from 'next-auth/react';
import { api } from "@/../convex/_generated/api";
import { Id } from "@/../convex/_generated/dataModel";
import { debugError } from '@/lib/utils';
import { validateSessionForm } from '@/app/admin/utils/sessionUtils';

interface Session {
    _id: string;
    name: string;
    day?: number;
    eventId: string;
    active: boolean;
    type: string;
    sparkId?: string;
    createdAt: number;
}

export const useSessionOperations = () => {
    const { data: sessionData } = useSession();
    const [error, setError] = useState<string | null>(null);

    // Queries
    const sessions = useQuery(api.sessions.getSessionsByActiveEvent);
    const activeEvent = useQuery(api.events.getActiveEvent);
    const votingStatus = useQuery(api.voting.getVotingStatus);
    const sparks = useQuery(api.sparks.getSparks, 
        activeEvent ? { eventId: activeEvent._id } : "skip"
    );

    // Mutations
    const createSessionMutation = useMutation(api.sessions.createSession);
    const updateSessionMutation = useMutation(api.sessions.updateSession);
    const activateSessionMutation = useMutation(api.sessions.activateSession);
    const deactivateSessionMutation = useMutation(api.sessions.deactivateSession);
    const deleteSessionMutation = useMutation(api.sessions.deleteSession);
    const toggleVotingMutation = useMutation(api.voting.toggleVoting);

    const createSession = useCallback(async (
        sessionName: string,
        sessionType: string,
        sessionDay: number,
        selectedSparkId: string
    ) => {
        const validation = validateSessionForm(sessionName, sessionType, sessionDay, selectedSparkId);
        if (!validation.isValid) {
            setError(validation.error!);
            return { success: false };
        }

        if (!activeEvent) {
            setError('No active event found. Please activate an event first.');
            return { success: false };
        }

        setError(null);

        try {
            const sessionCreateData: {
                name: string;
                eventId: Id<"events">;
                type: string;
                day: number;
                sparkId?: Id<"sparks">;
            } = {
                name: sessionName.trim(),
                eventId: activeEvent._id as Id<"events">,
                type: sessionType,
                day: sessionDay
            };

            // Add sparkId for Sparks-type sessions
            if (sessionType === 'Sparks' && selectedSparkId) {
                sessionCreateData.sparkId = selectedSparkId as Id<"sparks">;
            }

            await createSessionMutation({
                ...sessionCreateData,
                username: sessionData?.user?.username || ''
            });

            return { success: true };
        } catch (error) {
            const errorMessage = error instanceof Error ? error.message : 'Failed to create session';
            setError(errorMessage);
            debugError('Error creating session:', error);
            return { success: false };
        }
    }, [activeEvent, createSessionMutation, sessionData?.user?.username]);

    const updateSession = useCallback(async (
        sessionId: string,
        updates: {
            name?: string;
            type?: string;
            day?: number;
            sparkId?: Id<"sparks">;
            clearSparkId?: boolean;
        }
    ) => {
        setError(null);

        try {
            await updateSessionMutation({
                sessionId: sessionId as Id<"sessions">,
                username: sessionData?.user?.username || '',
                ...updates
            });
            return { success: true };
        } catch (error) {
            const errorMessage = error instanceof Error ? error.message : 'Failed to update session';
            setError(errorMessage);
            debugError('Error updating session:', error);
            return { success: false, error: errorMessage };
        }
    }, [updateSessionMutation, sessionData?.user?.username]);

    const deleteSession = useCallback(async (sessionId: string) => {
        setError(null);

        try {
            await deleteSessionMutation({
                sessionId: sessionId as Id<"sessions">,
                username: sessionData?.user?.username || ''
            });
            return { success: true };
        } catch (error) {
            const errorMessage = error instanceof Error ? error.message : 'Failed to delete session';
            setError(errorMessage);
            debugError('Error deleting session:', error);
            return { success: false };
        }
    }, [deleteSessionMutation, sessionData?.user?.username]);

    const toggleSessionActive = useCallback(async (session: Session) => {
        setError(null);

        try {
            if (!session.active) {
                await activateSessionMutation({ 
                    sessionId: session._id as Id<"sessions">,
                    username: sessionData?.user?.username || ''
                });
            } else {
                await deactivateSessionMutation({ 
                    sessionId: session._id as Id<"sessions">,
                    username: sessionData?.user?.username || ''
                });
            }
            return { success: true };
        } catch (error) {
            const errorMessage = error instanceof Error ? error.message : `Failed to ${session.active ? 'deactivate' : 'activate'} session`;
            setError(errorMessage);
            debugError('Error toggling session:', error);
            return { success: false };
        }
    }, [activateSessionMutation, deactivateSessionMutation, sessionData?.user?.username]);

    const stopVoting = useCallback(async () => {
        setError(null);

        try {
            await toggleVotingMutation({
                username: sessionData?.user?.username || ''
            });
            return { success: true };
        } catch (error) {
            const errorMessage = error instanceof Error ? error.message : 'Failed to stop voting';
            setError(errorMessage);
            debugError('Error stopping voting:', error);
            return { success: false };
        }
    }, [toggleVotingMutation, sessionData?.user?.username]);

    return {
        // Data
        sessions,
        activeEvent,
        votingStatus,
        sparks,
        
        // State
        error,
        
        // Actions
        createSession,
        updateSession,
        deleteSession,
        toggleSessionActive,
        stopVoting,
        
        // Utilities
        setError,
        clearError: () => setError(null),
    };
};
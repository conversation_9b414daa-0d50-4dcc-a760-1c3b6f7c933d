import { useState, useEffect, useCallback } from 'react';
import { useQuery } from "convex/react";
import { ConvexError } from 'convex/values';
import { api } from "@/../convex/_generated/api";
import { Id } from "@/../convex/_generated/dataModel";

interface User {
    _id: string;
    name: string;
    username: string;
    role: 'admin' | 'teamLead' | 'teamMember';
    teamId?: string;
    teamName?: string;
}

interface ContentTransferIntent {
    userId: string;
    username: string;
    teamId?: string;
    actionType: 'delete' | 'roleChange' | 'teamChange';
    onSuccessCallback: (userId: string) => void | Promise<void>;
}

interface BulkContentCheck {
    type: 'delete' | 'role';
    teamLeadIds: string[];
    checkedCount: number;
    hasContentCount: number;
}

interface BulkActionBlocked {
    type: 'delete' | 'role';
    hasContentCount: number;
}

interface UserContentData {
    hasContent: boolean;
    hasIdeas: boolean;
    hasSparks: boolean;
    counts: {
        votes: number;
        quickfireVotes: number;
    };
}

interface UserContentHookReturn {
    checkingContentUserId: string | null;
    contentTransferIntent: ContentTransferIntent | null;
    checkingBulkContent: BulkContentCheck | null;
    bulkActionBlocked: BulkActionBlocked | null;
    userContentData: UserContentData | undefined;
    setCheckingContentUserId: (userId: string | null) => void;
    setContentTransferIntent: (intent: ContentTransferIntent | null) => void;
    setCheckingBulkContent: (state: BulkContentCheck | null) => void;
    setBulkActionBlocked: (state: BulkActionBlocked | null) => void;
    handleContentTransferWithCallback: (
        userId: string,
        username: string,
        teamId: string | undefined,
        actionType: 'delete' | 'roleChange' | 'teamChange',
        onSuccessCallback: (userId: string) => void | Promise<void>
    ) => void;
    handlePopupTransferConfirm: (fromUserId: string, toUserId: string) => Promise<void>;
}

type RoleConfirmState = null | string | {
    userId: string;
    username: string;
    hasIdeas?: boolean;
    teamId?: string;
};

type TeamLeadActionsState = {
    userId: string;
    username: string;
    hasIdeas: boolean;
    hasContent: boolean;
    teamId?: string;
    newTeamId?: string;
    isTeamChange?: boolean;
    actionType?: 'delete' | 'roleChange' | 'teamChange';
} | null;

export const useUserContent = (
    allUsers: User[],
    setError: (error: string) => void,
    setShowRoleConfirm: (state: RoleConfirmState) => void,
    setShowTeamLeadActions: (state: TeamLeadActionsState) => void,
    setShowDeleteConfirm: (state: string | null) => void,
    handleTransferContent: (fromUserId: string, toUserId: string) => Promise<void>
): UserContentHookReturn => {
    // State for tracking which user we're checking content for
    const [checkingContentUserId, setCheckingContentUserId] = useState<string | null>(null);
    const [contentTransferIntent, setContentTransferIntent] = useState<ContentTransferIntent | null>(null);
    
    // State for bulk content checking
    const [checkingBulkContent, setCheckingBulkContent] = useState<BulkContentCheck | null>(null);
    const [bulkActionBlocked, setBulkActionBlocked] = useState<BulkActionBlocked | null>(null);

    // Query user content only when needed
    const userContentData = useQuery(
        api.users.getUserIdeasForDeletion, 
        checkingContentUserId ? { userId: checkingContentUserId as Id<"users"> } : "skip"
    );

    // UNIFIED CONTENT TRANSFER SYSTEM
    const handleContentTransferWithCallback = useCallback((
        userId: string, 
        username: string, 
        teamId: string | undefined,
        actionType: 'delete' | 'roleChange' | 'teamChange',
        onSuccessCallback: (userId: string) => void | Promise<void>
    ) => {
        // Always proceed with content checking - popup will handle blocking if needed
        setCheckingContentUserId(userId);
        setContentTransferIntent({ userId, username, teamId, actionType, onSuccessCallback });
    }, []);

    // Unified handler for popup transfer confirmation
    const handlePopupTransferConfirm = useCallback(async (fromUserId: string, toUserId: string) => {
        try {
            await handleTransferContent(fromUserId, toUserId);
            
            // Find the stored callback for this user and execute it
            if (contentTransferIntent && contentTransferIntent.userId === fromUserId) {
                await contentTransferIntent.onSuccessCallback(fromUserId);
            }
            
            setShowTeamLeadActions(null);
        } catch (error) {
            if (error instanceof ConvexError) {
                const errorData = error.data as { message: string };
                setError(errorData.message);
            } else {
                setError(error instanceof Error ? error.message : 'Failed to transfer content');
            }
        }
    }, [handleTransferContent, contentTransferIntent, setError, setShowTeamLeadActions]);

    // Handle content check completion - EXACT logic from lines 126-220
    useEffect(() => {
        const handleContentCheck = async () => {
        if (checkingContentUserId && userContentData) {
            const user = allUsers.find(u => u._id === checkingContentUserId);
            
            if (user) {
                // Check if this is bulk content checking
                if (checkingBulkContent && checkingBulkContent.teamLeadIds.includes(checkingContentUserId)) {
                    // This is bulk content checking
                    const newCheckedCount = checkingBulkContent.checkedCount + 1;
                    const newHasContentCount = checkingBulkContent.hasContentCount + (userContentData.hasContent ? 1 : 0);
                    
                    if (newCheckedCount === checkingBulkContent.teamLeadIds.length) {
                        // All teamLeads checked - show appropriate popup
                        if (newHasContentCount > 0) {
                            // Some teamLeads have content - show blocked popup
                            setBulkActionBlocked({
                                type: checkingBulkContent.type,
                                hasContentCount: newHasContentCount
                            });
                            if (checkingBulkContent.type === 'delete') {
                                setShowDeleteConfirm('bulk');
                            } else {
                                setShowRoleConfirm('bulk');
                            }
                        } else {
                            // No teamLeads have content - proceed with bulk action
                            setBulkActionBlocked(null);
                            if (checkingBulkContent.type === 'delete') {
                                setShowDeleteConfirm('bulk');
                            } else {
                                setShowRoleConfirm('bulk');
                            }
                        }
                        setCheckingBulkContent(null);
                    } else {
                        // Continue checking next teamLead
                        const nextTeamLeadId = checkingBulkContent.teamLeadIds[newCheckedCount];
                        setCheckingBulkContent({
                            ...checkingBulkContent,
                            checkedCount: newCheckedCount,
                            hasContentCount: newHasContentCount
                        });
                        setCheckingContentUserId(nextTeamLeadId);
                        return; // Don't clear checkingContentUserId yet
                    }
                } else if (contentTransferIntent && contentTransferIntent.userId === checkingContentUserId) {
                    // UNIFIED CONTENT TRANSFER SYSTEM - route based on content
                    if (userContentData.hasContent) {
                        // Has transferable content → Show transfer dialog (let popup handle team lead availability)
                        setShowTeamLeadActions({ 
                            userId: checkingContentUserId, 
                            username: contentTransferIntent.username, 
                            hasIdeas: userContentData.hasIdeas || userContentData.hasSparks,
                            hasContent: userContentData.hasContent,
                            teamId: contentTransferIntent.teamId,
                            isTeamChange: contentTransferIntent.actionType === 'teamChange',
                            actionType: contentTransferIntent.actionType
                        });
                        // DON'T clear contentTransferIntent yet - popup needs it for callback
                    } else {
                        // No transferable content → Execute success callback directly
                        try {
                            contentTransferIntent.onSuccessCallback(checkingContentUserId);
                        } catch (error) {
                            if (error instanceof ConvexError) {
                                const errorData = error.data as { message: string };
                                setError(errorData.message);
                            } else {
                                setError(error instanceof Error ? error.message : 'Failed to execute action');
                            }
                        }
                        // Clear contentTransferIntent since callback was executed
                        setContentTransferIntent(null);
                    }
                } else {
                    // This is for single role change
                    setShowRoleConfirm({ 
                        userId: checkingContentUserId, 
                        username: user.name, 
                        hasIdeas: userContentData.hasContent, 
                        teamId: 'teamId' in user ? (user as { teamId: string }).teamId : undefined 
                    });
                }
            }
            
            // Clear the checking state (unless we're continuing bulk checks)
            if (!checkingBulkContent || checkingBulkContent.checkedCount + 1 === checkingBulkContent.teamLeadIds.length) {
                setCheckingContentUserId(null);
            }
        }
        };
        
        handleContentCheck();
    }, [checkingContentUserId, userContentData, allUsers, contentTransferIntent, checkingBulkContent, setError, setShowRoleConfirm, setShowTeamLeadActions, setShowDeleteConfirm]);

    return {
        checkingContentUserId,
        contentTransferIntent,
        checkingBulkContent,
        bulkActionBlocked,
        userContentData,
        setCheckingContentUserId,
        setContentTransferIntent,
        setCheckingBulkContent,
        setBulkActionBlocked,
        handleContentTransferWithCallback,
        handlePopupTransferConfirm,
    };
};
import { useState, useCallback } from 'react';

interface Session {
    _id: string;
    name: string;
    day?: number;
    eventId: string;
    active: boolean;
    type: string;
    sparkId?: string;
    createdAt: number;
}

interface ModalState {
    showAddModal: boolean;
    showSwitchConfirm: Session | null;
    showDeleteConfirm: Session | null;
    showTypeChangeError: boolean;
    typeChangeErrorMessage: string;
    showDayEditModal: Session | null;
    showSparkSelectionModal: Session | null;
}

export const useSessionModals = () => {
    const [modalState, setModalState] = useState<ModalState>({
        showAddModal: false,
        showSwitchConfirm: null,
        showDeleteConfirm: null,
        showTypeChangeError: false,
        typeChangeErrorMessage: '',
        showDayEditModal: null,
        showSparkSelectionModal: null,
    });

    const openAddModal = useCallback(() => {
        setModalState(prev => ({ ...prev, showAddModal: true }));
    }, []);

    const closeAddModal = useCallback(() => {
        setModalState(prev => ({ ...prev, showAddModal: false }));
    }, []);

    const openSwitchConfirm = useCallback((session: Session) => {
        setModalState(prev => ({ ...prev, showSwitchConfirm: session }));
    }, []);

    const closeSwitchConfirm = useCallback(() => {
        setModalState(prev => ({ ...prev, showSwitchConfirm: null }));
    }, []);

    const openDeleteConfirm = useCallback((session: Session) => {
        setModalState(prev => ({ ...prev, showDeleteConfirm: session }));
    }, []);

    const closeDeleteConfirm = useCallback(() => {
        setModalState(prev => ({ ...prev, showDeleteConfirm: null }));
    }, []);

    const openTypeChangeError = useCallback((message: string) => {
        setModalState(prev => ({ 
            ...prev, 
            showTypeChangeError: true, 
            typeChangeErrorMessage: message 
        }));
    }, []);

    const closeTypeChangeError = useCallback(() => {
        setModalState(prev => ({ 
            ...prev, 
            showTypeChangeError: false, 
            typeChangeErrorMessage: '' 
        }));
    }, []);

    const openDayEditModal = useCallback((session: Session) => {
        setModalState(prev => ({ ...prev, showDayEditModal: session }));
    }, []);

    const closeDayEditModal = useCallback(() => {
        setModalState(prev => ({ ...prev, showDayEditModal: null }));
    }, []);

    const openSparkSelectionModal = useCallback((session: Session) => {
        setModalState(prev => ({ ...prev, showSparkSelectionModal: session }));
    }, []);

    const closeSparkSelectionModal = useCallback(() => {
        setModalState(prev => ({ ...prev, showSparkSelectionModal: null }));
    }, []);

    return {
        modalState,
        openAddModal,
        closeAddModal,
        openSwitchConfirm,
        closeSwitchConfirm,
        openDeleteConfirm,
        closeDeleteConfirm,
        openTypeChangeError,
        closeTypeChangeError,
        openDayEditModal,
        closeDayEditModal,
        openSparkSelectionModal,
        closeSparkSelectionModal,
    };
};
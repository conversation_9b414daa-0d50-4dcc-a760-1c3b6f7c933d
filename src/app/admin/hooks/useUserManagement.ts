import { useState, useCallback } from 'react';
import { useQuery, useMutation, useAction } from "convex/react";
import { ConvexError } from 'convex/values';
import { api } from "@/../convex/_generated/api";
import { Id } from "@/../convex/_generated/dataModel";

interface User {
    _id: string;
    name: string;
    username: string;
    role: 'admin' | 'teamLead' | 'teamMember';
    teamId?: string;
    teamName?: string;
}

interface TeamGroup {
    _id: Id<"teams">;
    name: string;
    users: User[];
}

interface GroupedUsers {
    teams: TeamGroup[];
    noTeam: User[];
}

export function useUserManagement() {
    const [error, setError] = useState<string | null>(null);
    
    // Real-time data from Convex
    const activeEvent = useQuery(api.events.getActiveEvent);
    const groupedUsers: GroupedUsers | undefined = useQuery(api.users.getUsersGroupedByTeam);
    
    // Convex mutations
    const updateUserName = useMutation(api.users.updateUserName);
    const updateUserRole = useMutation(api.users.updateUserRole);
    const updateUserPassword = useAction(api.userActions.updateUserPasswordAction);
    const updateUserTeam = useMutation(api.users.updateUserTeam);
    const bulkUpdateUserTeam = useMutation(api.users.bulkUpdateUserTeam);
    const deleteUser = useMutation(api.users.deleteUser);
    const createUserWithTeam = useAction(api.userActions.createUserWithTeamAction);
    const transferUserContent = useMutation(api.users.transferUserContent);
    const updateUserStatus = useMutation(api.users.updateUserStatus);

    // Edit user handler
    const handleEditUser = useCallback(async (userId: string, data: { name: string; username: string; password?: string }) => {
        try {
            // Update name if changed
            await updateUserName({ userId: userId as Id<"users">, name: data.name });
            
            // Update password if provided
            if (data.password) {
                const passwordResult = await updateUserPassword({ userId: userId as Id<"users">, password: data.password });
                if (!passwordResult.success) {
                    setError(passwordResult.message || 'Failed to update password');
                    throw new Error(passwordResult.message || 'Failed to update password');
                }
            }
            
            setError(null);
        } catch (error) {
            if (error instanceof ConvexError) {
                const errorData = error.data as { message: string };
                setError(errorData.message);
                throw error;
            } else {
                const errorMsg = error instanceof Error ? error.message : 'Failed to update user';
                setError(errorMsg);
                throw new Error(errorMsg);
            }
        }
    }, [updateUserName, updateUserPassword]);

    // Team change handler
    const handleTeamChange = useCallback(async (userId: string, teamId?: string) => {
        try {
            await updateUserTeam({ 
                userId: userId as Id<"users">, 
                teamId: teamId ? teamId as Id<"teams"> : undefined 
            });
            setError(null);
        } catch (error) {
            if (error instanceof ConvexError) {
                const errorData = error.data as { message: string };
                setError(errorData.message);
            } else {
                setError(error instanceof Error ? error.message : 'Failed to update team');
            }
        }
    }, [updateUserTeam]);

    // Bulk team change handler
    const handleBulkTeamChange = useCallback(async (teamId: string | undefined, userIds: string[]) => {
        try {
            const validUserIds = userIds.filter(id => id && typeof id === 'string');
            
            if (validUserIds.length === 0) {
                setError('No valid users selected');
                return;
            }
            
            await bulkUpdateUserTeam({ 
                userIds: validUserIds as Id<"users">[], 
                teamId: teamId ? teamId as Id<"teams"> : undefined 
            });
            setError(null);
        } catch (error) {
            if (error instanceof ConvexError) {
                const errorData = error.data as { message: string };
                setError(errorData.message);
            } else {
                setError(error instanceof Error ? error.message : 'Failed to update teams');
            }
        }
    }, [bulkUpdateUserTeam]);

    // Role change handler
    const handleRoleChange = useCallback(async (userId: string, newRole: 'teamLead' | 'teamMember', transferToUserId?: string) => {
        try {
            // If transferring content, do that first
            if (transferToUserId) {
                await transferUserContent({ 
                    fromUserId: userId as Id<"users">, 
                    toUserId: transferToUserId as Id<"users"> 
                });
            }

            await updateUserRole({ userId: userId as Id<"users">, role: newRole });
            setError(null);
        } catch (error) {
            if (error instanceof ConvexError) {
                const errorData = error.data as { message: string };
                setError(errorData.message);
            } else {
                setError(error instanceof Error ? error.message : 'Failed to update role');
            }
        }
    }, [updateUserRole, transferUserContent]);

    // Delete user handler
    const handleDeleteUser = useCallback(async (userId: string) => {
        try {
            await deleteUser({ 
                userId: userId as Id<"users">, 
                cascadeDelete: true 
            });
            setError(null);
        } catch (error) {
            if (error instanceof ConvexError) {
                const errorData = error.data as { message: string };
                setError(errorData.message);
            } else {
                setError(error instanceof Error ? error.message : 'Failed to delete user');
            }
        }
    }, [deleteUser]);

    // Add user handler
    const handleAddUser = useCallback(async (userData: {
        username: string;
        name: string;
        password: string;
        teamId?: string;
        role: 'teamLead' | 'teamMember';
    }) => {
        try {
            const result = await createUserWithTeam({
                username: userData.username,
                name: userData.name,
                password: userData.password,
                role: userData.role,
                teamId: userData.teamId ? userData.teamId as Id<"teams"> : undefined
            });
            
            if (!result.success) {
                setError(result.message || 'Failed to create user');
                return;
            }
            
            setError(null);
        } catch (error) {
            if (error instanceof ConvexError) {
                const errorData = error.data as { message: string };
                setError(errorData.message);
            } else {
                setError(error instanceof Error ? error.message : 'Failed to create user');
            }
        }
    }, [createUserWithTeam]);

    // Make pending handler
    const handleMakePending = useCallback(async (userId: string) => {
        try {
            await updateUserStatus({ 
                userId: userId as Id<"users">, 
                status: 'pending' 
            });
            setError(null);
        } catch (error) {
            if (error instanceof ConvexError) {
                const errorData = error.data as { message: string };
                setError(errorData.message);
            } else {
                setError(error instanceof Error ? error.message : 'Failed to make user pending');
            }
        }
    }, [updateUserStatus]);

    // Transfer content handler
    const handleTransferContent = useCallback(async (fromUserId: string, toUserId: string) => {
        try {
            await transferUserContent({ 
                fromUserId: fromUserId as Id<"users">, 
                toUserId: toUserId as Id<"users"> 
            });
            setError(null);
        } catch (error) {
            if (error instanceof ConvexError) {
                const errorData = error.data as { message: string };
                setError(errorData.message);
            } else {
                setError(error instanceof Error ? error.message : 'Failed to transfer content');
            }
        }
    }, [transferUserContent]);

    return {
        // Data
        activeEvent,
        groupedUsers,
        error,
        
        // Handlers
        handleEditUser,
        handleTeamChange,
        handleBulkTeamChange,
        handleRoleChange,
        handleDeleteUser,
        handleAddUser,
        handleMakePending,
        handleTransferContent,
        
        // Utils
        setError,
        clearError: () => setError(null)
    };
}
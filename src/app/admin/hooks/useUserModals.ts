import { useState } from 'react';

type RoleConfirmState = null | string | {
    userId: string;
    username: string;
    hasIdeas?: boolean;
    teamId?: string;
};

interface UserModalStates {
    showRoleConfirm: RoleConfirmState;
    showEditUser: string | null;
    showTeamChange: string | null;
    showDeleteConfirm: string | null;
    showTeamLeadActions: { 
        userId: string; 
        username: string; 
        hasIdeas: boolean; 
        hasContent?: boolean; 
        teamId?: string;
        newTeamId?: string; 
        isTeamChange?: boolean;
        actionType?: 'delete' | 'roleChange' | 'teamChange'; 
    } | null;
    showIdeaStats: string | null;
    showAddUser: boolean;
}

export function useUserModals() {
    const [modals, setModals] = useState<UserModalStates>({
        showRoleConfirm: null,
        showEditUser: null,
        showTeamChange: null,
        showDeleteConfirm: null,
        showTeamLeadActions: null,
        showIdeaStats: null,
        showAddUser: false
    });

    const openModal = <K extends keyof UserModalStates>(
        modalType: K,
        value: UserModalStates[K]
    ) => {
        setModals(prev => ({ ...prev, [modalType]: value }));
    };

    const closeModal = (modalType: keyof UserModalStates) => {
        setModals(prev => ({ ...prev, [modalType]: modalType === 'showAddUser' ? false : null }));
    };

    const closeAllModals = () => {
        setModals({
            showRoleConfirm: null,
            showEditUser: null,
            showTeamChange: null,
            showDeleteConfirm: null,
            showTeamLeadActions: null,
            showIdeaStats: null,
            showAddUser: false
        });
    };

    return {
        modals,
        openModal,
        closeModal,
        closeAllModals
    };
}
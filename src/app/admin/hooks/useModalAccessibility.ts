import { useEffect, useRef, useCallback } from 'react';

interface UseModalAccessibilityProps {
    isOpen: boolean;
    onClose: () => void;
    trapFocus?: boolean;
}

export const useModalAccessibility = ({ 
    isOpen, 
    onClose, 
    trapFocus = true 
}: UseModalAccessibilityProps) => {
    const modalRef = useRef<HTMLDivElement>(null);
    const previouslyFocusedElement = useRef<HTMLElement | null>(null);

    // Handle ESC key press
    const handleKeyDown = useCallback((event: KeyboardEvent) => {
        if (event.key === 'Escape') {
            onClose();
        }

        // Handle focus trap
        if (trapFocus && event.key === 'Tab' && modalRef.current) {
            const focusableElements = modalRef.current.querySelectorAll(
                'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
            );
            const firstFocusable = focusableElements[0] as HTMLElement;
            const lastFocusable = focusableElements[focusableElements.length - 1] as HTMLElement;

            if (event.shiftKey) {
                // Shift + Tab
                if (document.activeElement === firstFocusable) {
                    event.preventDefault();
                    lastFocusable?.focus();
                }
            } else {
                // Tab
                if (document.activeElement === lastFocusable) {
                    event.preventDefault();
                    firstFocusable?.focus();
                }
            }
        }
    }, [onClose, trapFocus]);

    // Handle click outside
    const handleClickOutside = useCallback((event: MouseEvent) => {
        if (modalRef.current && !modalRef.current.contains(event.target as Node)) {
            // Check if clicked on overlay (not modal content)
            const target = event.target as HTMLElement;
            if (target.classList.contains('modalOverlay')) {
                onClose();
            }
        }
    }, [onClose]);

    useEffect(() => {
        if (isOpen) {
            // Store previously focused element
            previouslyFocusedElement.current = document.activeElement as HTMLElement;

            // Add event listeners
            document.addEventListener('keydown', handleKeyDown);
            document.addEventListener('mousedown', handleClickOutside);

            // Focus first focusable element in modal
            setTimeout(() => {
                if (modalRef.current) {
                    const firstFocusable = modalRef.current.querySelector(
                        'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
                    ) as HTMLElement;
                    firstFocusable?.focus();
                }
            }, 100);

            // Prevent body scroll
            document.body.style.overflow = 'hidden';
        } else {
            // Restore focus to previously focused element
            if (previouslyFocusedElement.current) {
                previouslyFocusedElement.current.focus();
            }

            // Restore body scroll
            document.body.style.overflow = 'unset';
        }

        return () => {
            document.removeEventListener('keydown', handleKeyDown);
            document.removeEventListener('mousedown', handleClickOutside);
            document.body.style.overflow = 'unset';
        };
    }, [isOpen, handleKeyDown, handleClickOutside]);

    return { modalRef };
};
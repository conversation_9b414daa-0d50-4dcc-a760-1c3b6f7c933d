import { useState, useMemo } from 'react';
import { Id } from "@/../convex/_generated/dataModel";

interface User {
    _id: string;
    name: string;
    username: string;
    role: 'admin' | 'teamLead' | 'teamMember';
    teamId?: string;
    teamName?: string;
}

interface TeamGroup {
    _id: Id<"teams">;
    name: string;
    users: User[];
}

interface GroupedUsers {
    teams: TeamGroup[];
    noTeam: User[];
}

export function useUserFilters(groupedUsers: GroupedUsers | undefined) {
    const [searchTerm, setSearchTerm] = useState('');

    // Filtered grouped users based on search term
    const filteredGroupedUsers = useMemo(() => {
        if (!groupedUsers || !searchTerm) return groupedUsers;
        
        // Type guard to ensure groupedUsers has the expected structure
        if (!('teams' in groupedUsers) || !('noTeam' in groupedUsers)) {
            return groupedUsers;
        }
        
        const term = searchTerm.toLowerCase();
        const filterUsers = (users: User[]) => 
            users.filter(user =>
                user.name.toLowerCase().includes(term) ||
                user.username.toLowerCase().includes(term)
            );

        return {
            teams: groupedUsers.teams.map((team: TeamGroup) => ({
                ...team,
                users: filterUsers(team.users)
            })).filter((team: TeamGroup) => team.users.length > 0),
            noTeam: filterUsers(groupedUsers.noTeam)
        };
    }, [groupedUsers, searchTerm]);

    // Flattened users array for easier processing
    const allUsers = useMemo(() => {
        if (!groupedUsers) return [];
        return [
            ...groupedUsers.teams.flatMap((t: TeamGroup) => 
                t.users.map((u: User) => ({ ...u, teamId: t._id, teamName: t.name }))
            ),
            ...groupedUsers.noTeam.map((u: User) => ({ ...u, teamId: undefined, teamName: 'No Team' }))
        ];
    }, [groupedUsers]);

    const clearSearch = () => setSearchTerm('');

    return {
        searchTerm,
        setSearchTerm,
        clearSearch,
        filteredGroupedUsers,
        allUsers
    };
}
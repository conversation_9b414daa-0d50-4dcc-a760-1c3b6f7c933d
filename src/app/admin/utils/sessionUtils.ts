interface Session {
    _id: string;
    name: string;
    day?: number;
    eventId: string;
    active: boolean;
    type: string;
    sparkId?: string;
    createdAt: number;
}

/**
 * Sort sessions by creation date (ascending)
 */
export const sortSessionsByDate = (sessions: Session[]): Session[] => {
    return [...sessions].sort((a, b) => a.createdAt - b.createdAt);
};

/**
 * Group sessions by day and sort them
 */
export const groupSessionsByDay = (sessions: Session[]): Array<{
    day: number;
    sessions: Session[];
}> => {
    const grouped: Record<number, Session[]> = {};
    
    sessions.forEach(session => {
        const day = session.day || 1;
        if (!grouped[day]) {
            grouped[day] = [];
        }
        grouped[day].push(session);
    });
    
    // Sort days in ascending order
    return Object.entries(grouped)
        .sort(([dayA], [dayB]) => parseInt(dayA) - parseInt(dayB))
        .map(([day, sessions]) => ({
            day: parseInt(day),
            sessions: sortSessionsByDate(sessions)
        }));
};

/**
 * Validate session form data
 */
export const validateSessionForm = (
    sessionName: string,
    sessionType: string,
    sessionDay: number,
    selectedSparkId: string
): { isValid: boolean; error?: string } => {
    if (!sessionName.trim()) {
        return { isValid: false, error: 'Session name is required' };
    }
    
    if (!sessionType) {
        return { isValid: false, error: 'Session type is required' };
    }
    
    if (!sessionDay || sessionDay < 1) {
        return { isValid: false, error: 'Day must be a positive number' };
    }
    
    if (sessionType === 'Sparks' && !selectedSparkId) {
        return { isValid: false, error: 'Spark configuration is required for Sparks-type sessions' };
    }
    
    return { isValid: true };
};

/**
 * Get type-specific configuration for session updates
 */
export const getTypeSpecificConfig = (type: string): Record<string, unknown> => {
    switch (type) {
        case 'Ideas':
            return {};
        case 'Quickfire':
            return {};
        case 'Sparks':
            return {}; // Spark configuration should be handled separately
        default:
            return {};
    }
};

/**
 * Check if a session type change is valid
 */
export const isValidTypeChange = (
    fromType: string,
    toType: string,
    hasIdeas: boolean = false
): { isValid: boolean; error?: string } => {
    // Can't change type if there are ideas associated
    if (hasIdeas && fromType !== toType) {
        return {
            isValid: false,
            error: "You can't change session type when there's ideas associated with this session"
        };
    }
    
    return { isValid: true };
};
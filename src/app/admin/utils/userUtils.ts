interface User {
    _id: string;
    name: string;
    username: string;
    role: 'admin' | 'teamLead' | 'teamMember';
    teamId?: string;
    teamName?: string;
}

// Helper function to get current user role
export const getCurrentUserRole = (allUsers: User[], userId: string): 'teamLead' | 'teamMember' | undefined => {
    const user = allUsers.find(u => u._id === userId);
    return user?.role === 'admin' ? undefined : user?.role;
};

// Helper function to get available team leads for a team
export const getAvailableTeamLeads = (allUsers: User[], teamId?: string, excludeUserId?: string, excludeModalUserId?: string) => {
    if (!teamId) return [];
    
    const teamLeads = allUsers.filter(
        u => u.teamId === teamId && 
             u.role === 'teamLead' && 
             u._id !== excludeUserId &&
             u._id !== excludeModalUserId
    );

    return teamLeads;
};

// Helper function to start bulk content check
export const startBulkContentCheck = (
    allUsers: User[], 
    selectedUsers: Set<string>,
    type: 'delete' | 'role',
    setCallback: (data: {type: 'delete' | 'role', teamLeadIds: string[], checkedCount: number, hasContentCount: number}) => void,
    setUserCallback: (userId: string) => void,
    showDirectCallback: () => void
) => {
    if (!allUsers.length) return;
    
    const selectedTeamLeads = Array.from(selectedUsers).filter(userId => {
        const user = allUsers.find(u => u._id === userId);
        return user?.role === 'teamLead';
    });

    if (selectedTeamLeads.length === 0) {
        // No teamLeads selected - proceed directly
        showDirectCallback();
        return;
    }

    // Start checking content for all selected teamLeads
    setCallback({
        type,
        teamLeadIds: selectedTeamLeads,
        checkedCount: 0,
        hasContentCount: 0
    });
    setUserCallback(selectedTeamLeads[0]);
};

// Helper function to check if user has team leads in selection
export const hasTeamLeadsInSelection = (allUsers: User[], selectedUsers: Set<string>) => {
    return Array.from(selectedUsers).some(userId => {
        const user = allUsers.find(u => u._id === userId);
        return user?.role === 'teamLead';
    });
};

// Helper function to get total user count
export const getTotalUserCount = (allUsers: User[]) => allUsers.length;
import { Suspense } from 'react';
import { StateCard } from '@/components/ui/state-card';
import '@/app/globals.css';
import AdminClientWrapper from './components/AdminClientWrapper';

interface AdminLayoutProps {
  children: React.ReactNode;
}

export default function AdminLayout({ children }: AdminLayoutProps) {

  return (
    <AdminClientWrapper>
      <Suspense fallback={
        <div className="flex justify-center items-center min-h-[400px]">
          <StateCard state="loading" title="Loading..." />
        </div>
      }>
        {children}
      </Suspense>
    </AdminClientWrapper>
  );
}
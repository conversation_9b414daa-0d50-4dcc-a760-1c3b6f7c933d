'use client'

export default function GlobalError({
  error,
  reset,
}: {
  error: Error & { digest?: string }
  reset: () => void
}) {
  return (
    <html>
      <body className="bg-background text-foreground font-mono">
        <div className="flex justify-center items-center min-h-screen p-6">
          <div className="space-y-4 w-full max-w-md text-center">
            <div className="space-y-2">
              <h1 className="text-2xl font-bold text-destructive">
                Critical Error
              </h1>
              <p className="text-muted-foreground">
                A critical system error occurred. Please refresh the page or contact support.
              </p>
            </div>
            <button
              onClick={reset}
              className="px-4 py-2 bg-primary text-primary-foreground hover:bg-primary/90 transition-colors rounded-md"
            >
              Try Again
            </button>
            {process.env.NODE_ENV === 'development' && (
              <div className="mt-4 p-4 bg-destructive/10 border border-destructive/20 text-left">
                <div className="text-sm font-mono text-destructive">
                  {error.message}
                </div>
                {error.digest && (
                  <div className="text-xs font-mono text-destructive mt-2 opacity-70">
                    Error ID: {error.digest}
                  </div>
                )}
              </div>
            )}
          </div>
        </div>
      </body>
    </html>
  )
}
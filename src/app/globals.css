@import "tailwindcss";
@import "tw-animate-css";

@theme {
  /* Font Size Control - Change --font-base to adjust all sizes */
  --font-base: 19px;
  --text-xs: calc(var(--font-base) - 4px);    /* Base - 4px = 14px */
  --text-sm: calc(var(--font-base) - 2px);    /* Base - 2px = 16px */
  --text-base: calc(var(--font-base) + 0px);  /* Base = 18px */
  --text-lg: calc(var(--font-base) + 2px);    /* Base + 2px = 20px */
  --text-xl: calc(var(--font-base) + 4px);    /* Base + 4px = 22px */
  --text-2xl: calc(var(--font-base) + 8px);   /* Base + 8px = 26px */
  --text-3xl: calc(var(--font-base) + 14px);  /* Base + 14px = 32px */
  --text-4xl: calc(var(--font-base) + 20px);  /* Base + 20px = 38px */
  --text-5xl: calc(var(--font-base) + 32px);  /* Base + 32px = 50px */
  --text-6xl: calc(var(--font-base) + 44px);  /* Base + 44px = 62px */
}

@custom-variant dark (&:where(.dark, .dark *));

/* Simple theme background switching */
.theme-background {
  background-image: url('/images/background-light2.jpg');
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
}

.dark .theme-background {
  background-image: url('/images/background-01.jpg');
}


/* Font Imports */
@font-face {
    font-family: 'Gotham-Medium';
    src: url('/fonts/Gotham-Medium.woff2') format('woff2');
    font-weight: 500;
    font-style: normal;
    font-display: swap;
}
@font-face {
    font-family: 'Gotham-Bold';
    src: url('/fonts/Gotham-Bold.woff2') format('woff2');
    font-weight: 900;
    font-style: bold;
    font-display: swap;
}
@font-face {
    font-family: 'Gotham-Book';
    src: url('/fonts/Gotham-Book.woff2') format('woff2');
    font-weight: 400;
    font-style: normal;
    font-display: swap;
}

@font-face {
    font-family: 'neue-machina-Medium';
    src: url('/fonts/neue-machina-Medium.otf') format('opentype');
    font-weight: 500;
    font-style: normal;
    font-display: swap;
}

@font-face {
    font-family: 'NeueMachina-Regular';
    src: url('/fonts/NeueMachina-Regular.woff2') format('woff2');
    font-weight: 400;
    font-style: normal;
    font-display: swap;
}
@font-face {
    font-family: 'NeueMachina-Ultrabold';
    src: url('/fonts/NeueMachina-Ultrabold.woff2') format('woff2');
    font-weight: 900;
    font-style: bold;
    font-display: swap;
}

:root {
  
  --background: oklch(1 0 0);                      /* White */
  --foreground: oklch(0.1450 0 0);                 /* Almost Black */
  --card: oklch(1 0 0);                            /* White */
  --card-foreground: oklch(0.1450 0 0);            /* Almost Black */
  --popover: oklch(.5 0 0);                         /* White */
  --popover-foreground: oklch(0.1450 0 0);         /* Almost Black */
  --primary: oklch(0.6340 0.2533 346.3113);       /* Pink/Magenta */
  --primary-foreground: oklch(0.9850 0 0);         /* White */
  --secondary: oklch(0.6833 0.2113 305.3605);     /* Purple */
  --secondary-foreground: oklch(0.2050 0 0);       /* Dark Gray */
  --muted: oklch(0.95 0 0);                      /* Light Gray */
  --muted-foreground: oklch(0.5560 0 0);           /* Medium Gray */
  --accent: oklch(0.6833 0.2113 305.3605);        /* Cyan */
  --accent-foreground: oklch(0.2050 0 0);          /* Dark Gray */
  --destructive: oklch(0.6340 0.2533 346.3113);     /* Red/Orange */
  --destructive-foreground: oklch(1 0 0);          /* White */
  --border: oklch(0.8 0 0);                     /* Light Gray */
  --input: oklch(0.8 0 0);                      /* Light Gray */
  --ring: oklch(0.7080 0 0);                       /* Medium Gray */
  --chart-1: oklch(0.8100 0.1000 252);
  --chart-2: oklch(0.6200 0.1900 260);
  --chart-3: oklch(0.5500 0.2200 263);
  --chart-4: oklch(0.4900 0.2200 264);
  --chart-5: oklch(0.4200 0.1800 266);
  --sidebar: oklch(1 0 0);
  --sidebar-foreground: oklch(0.1450 0 0);
  --sidebar-primary: oklch(0.2050 0 0);
  --sidebar-primary-foreground: oklch(0.9850 0 0);
  --sidebar-accent: oklch(0.9700 0 0);
  --sidebar-accent-foreground: oklch(0.2050 0 0);
  --sidebar-border: oklch(0.9220 0 0);
  --sidebar-ring: oklch(0.7080 0 0);
  --font-sans: 'Gotham-Medium', sans-serif;
  --font-bold: 'Gotham-Bold', sans-serif;
  --font-serif: ui-serif, Georgia, Cambria, "Times New Roman", Times, serif;
  --font-mono: 'neue-machina-Medium', monospace;
  --font-ultrabold: 'NeueMachina-Ultrabold', sans-serif;
  --profile-box-background-color: oklch(0.6833 0.2113 305.3605);
  --skeleton: oklch(0.9500 0 0);                      /* Very light gray for skeleton */
  
  --radius: 0rem;
  --button-icon-size: 1.3rem;
  --select-height-sm: 2rem;      /* 32px */
  --select-height-md: 2.5rem;      /* 48px */
  --select-height-default: 3rem; /* 56px */
  --shadow-2xs: 0 1px 0px -50px hsl(0 0% 0% / 0.00);
  --shadow-xs: 0 1px 0px -50px hsl(0 0% 0% / 0.00);
  --shadow-sm: 0 1px 0px -50px hsl(0 0% 0% / 0.00), 0 1px 2px -51px hsl(0 0% 0% / 0.00);
  --shadow: 0 1px 0px -50px hsl(0 0% 0% / 0.00), 0 1px 2px -51px hsl(0 0% 0% / 0.00);
  --shadow-md: 0 1px 0px -50px hsl(0 0% 0% / 0.00), 0 2px 4px -51px hsl(0 0% 0% / 0.00);
  --shadow-lg: 0 1px 0px -50px hsl(0 0% 0% / 0.00), 0 4px 6px -51px hsl(0 0% 0% / 0.00);
  --shadow-xl: 0 1px 0px -50px hsl(0 0% 0% / 0.00), 0 8px 10px -51px hsl(0 0% 0% / 0.00);
  --shadow-2xl: 0 1px 0px -50px hsl(0 0% 0% / 0.00);
}

.dark {
  --background: oklch(0 0 0);                      /* Black */
  --foreground: oklch(0.9850 0 0);                 /* White */
  --card: oklch(0.1500 0 0);                       /* Dark Gray */
  --card-foreground: oklch(0.9850 0 0);            /* White */
  --popover: oklch(0.4 0 0);                    /* Dark Gray */
  --popover-foreground: oklch(0.9850 0 0);         /* White */
  --primary: oklch(0.6340 0.2533 346.3113);       /* Pink/Magenta */
  --primary-foreground: oklch(0.2050 0 0);         /* Dark Gray */
  --secondary: oklch(0.6833 0.2113 305.3605);     /* Purple */
  --secondary-foreground: oklch(0 0 0);            /* Black */
  --muted: oklch(0.1600 0 0);                      /* Dark Gray */
  --muted-foreground: oklch(0.7080 0 0);           /* Light Gray */
  --accent: oklch(0.8188 0.1401 190.1725);        /* Cyan */
  --accent-foreground: oklch(0 0 0);               /* Black */
  --destructive: oklch(0.6340 0.2533 346.3113);   /* Pink/Magenta */
  --destructive-foreground: oklch(0.9850 0 0);     /* White */
  --border: oklch(0.8188 0.1401 190.1725);        /* Cyan */
  --input: oklch(0.3250 0 0);                      /* Dark Gray */
  --ring: oklch(0.8188 0.1401 190.1725);          /* Cyan */
  --chart-1: oklch(0.8100 0.1000 252);
  --chart-2: oklch(0.6200 0.1900 260);
  --chart-3: oklch(0.5500 0.2200 263);
  --chart-4: oklch(0.4900 0.2200 264);
  --chart-5: oklch(0.4200 0.1800 266);
  --sidebar: oklch(0.1500 0 0);
  --sidebar-foreground: oklch(0.9850 0 0);
  --sidebar-primary: oklch(0.6833 0.2113 305.3605);
  --sidebar-primary-foreground: oklch(0.9850 0 0);
  --sidebar-accent: oklch(0.2690 0 0);
  --sidebar-accent-foreground: oklch(0.9850 0 0);
  --sidebar-border: oklch(0.2750 0 0);
  --sidebar-ring: oklch(0.4390 0 0);
  --font-sans: 'Gotham-Medium', sans-serif;
  --font-serif: ui-serif, Georgia, Cambria, "Times New Roman", Times, serif;
  --font-mono: 'neue-machina-Medium', monospace;
  --font-ultrabold: 'NeueMachina-Ultrabold', sans-serif;
  --radius: 0rem;
  --button-icon-size: 1.3rem;
  --shadow-2xs: 0 1px 0px -50px hsl(0 0% 0% / 0.00);
  --shadow-xs: 0 1px 0px -50px hsl(0 0% 0% / 0.00);
  --shadow-sm: 0 1px 0px -50px hsl(0 0% 0% / 0.00), 0 1px 2px -51px hsl(0 0% 0% / 0.00);
  --shadow: 0 1px 0px -50px hsl(0 0% 0% / 0.00), 0 1px 2px -51px hsl(0 0% 0% / 0.00);
  --shadow-md: 0 1px 0px -50px hsl(0 0% 0% / 0.00), 0 2px 4px -51px hsl(0 0% 0% / 0.00);
  --shadow-lg: 0 1px 0px -50px hsl(0 0% 0% / 0.00), 0 4px 6px -51px hsl(0 0% 0% / 0.00);
  --shadow-xl: 0 1px 0px -50px hsl(0 0% 0% / 0.00), 0 8px 10px -51px hsl(0 0% 0% / 0.00);
  --shadow-2xl: 0 1px 0px -50px hsl(0 0% 0% / 0.00);
  --profile-box-background-color: oklch(0.6833 0.2113 305.3605);
  --skeleton: oklch(0.2500 0 0);                      /* Dark gray for skeleton */
  
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-destructive-foreground: var(--destructive-foreground);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);
  --color-sidebar: var(--sidebar);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);
  --color-skeleton: var(--skeleton);

  --font-sans: var(--font-sans);
  --font-mono: var(--font-mono);
  --font-serif: var(--font-serif);
  --font-ultrabold: var(--font-ultrabold);

  

  --radius-sm: calc(var(--radius) - 0px);
  --radius-md: calc(var(--radius) - 0px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 0px);

  --shadow-2xs: var(--shadow-2xs);
  --shadow-xs: var(--shadow-xs);
  --shadow-sm: var(--shadow-sm);
  --shadow: var(--shadow);
  --shadow-md: var(--shadow-md);
  --shadow-lg: var(--shadow-lg);
  --shadow-xl: var(--shadow-xl);
  --shadow-2xl: var(--shadow-2xl);


}

@layer base {
  * {
    @apply border-border outline-ring/50;
    }
  body {
    @apply bg-background text-foreground;
    font-family: 'Gotham-Medium', sans-serif;
    }
  
  /* Typography - Font Families */
  h1 {
    font-family: 'NeueMachina-Ultrabold', sans-serif;
  }
  
  h2, h3, h4, h5, h6 {
    font-family: 'neue-machina-Medium', sans-serif;
  }
  
  p, span, div, input, textarea {
    font-family: 'Gotham-Medium', sans-serif;
  }

  button, label {
    font-family: 'neue-machina-Medium', sans-serif;
  }

  /* Leaderboard specific font classes */
  .font-ultrabold {
    font-family: var(--font-ultrabold);
  }
  
  /* Global cursor pointer for all clickable elements */
  button,
  [role="button"],
  input[type="submit"],
  input[type="button"],
  input[type="reset"],
  input[type="file"],
  label[for],
  select,
  [tabindex]:not([tabindex="-1"]),
  a,
  area,
  summary,
  [data-clickable],
  .cursor-pointer {
    cursor: pointer;
  }
  
  /* Override cursor for TiptapEditor content */
  .tiptap-content p,
  .tiptap-content div,
  .tiptap-content span {
    cursor: text !important;
  }
  
  /* Fix placeholder to only show when editor is completely empty */
  .ProseMirror.ProseMirror-focused p.is-editor-empty::before {
    display: none !important;
  }
  
  .ProseMirror p.is-empty:first-child:only-child::before {
    content: attr(data-placeholder);
    color: #666666;
    pointer-events: none;
    position: absolute;
    left: 0;
    top: 0;
    height:100%;
  }
  
  .ProseMirror p.is-empty:first-child:only-child {
    position: relative;
  }
}

/* Admin Utility Classes */
@import "../styles/_admin-utilities.css";
@import "../styles/_user-utilities.css";
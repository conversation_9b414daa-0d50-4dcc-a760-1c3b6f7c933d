import './globals.css';
import { getServerSession, Session } from 'next-auth';
import SessionProvider from '@/components/providers/SessionProvider';
import { ConvexProvider } from '@/components/providers/ConvexProvider';
import { ThemeProvider } from '@/components/providers/ThemeProvider';
import { authOptions } from '@/lib/auth';
import MaintenanceMode from '@/components/MaintenanceMode';
import type { Metadata, Viewport } from 'next'

export const metadata: Metadata = {
  title: 'LionX',
  description: 'LionX Voting Platform',
  manifest: '/manifest.json',
  appleWebApp: {
    capable: true,
    statusBarStyle: 'black-translucent',
    title: 'LionX',
  },
  formatDetection: {
    telephone: false,
  }
};

export const viewport: Viewport = {
  themeColor: '#000000',
};

// Client component wrapper for providers with session prop
function ClientProviders({ children, session }: { children: React.ReactNode, session: Session | null }) {
  'use client';
  
  return (
    <ThemeProvider
      attribute="class"
      defaultTheme="system"
      enableSystem={true}
      disableTransitionOnChange={false}
      storageKey="theme"
    >
      <ConvexProvider>
        <SessionProvider session={session}>
          {children}
          <MaintenanceMode />
        </SessionProvider>
      </ConvexProvider>
    </ThemeProvider>
  );
}

export default async function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const session = await getServerSession(authOptions);
  
  return (
    <html lang="en" suppressHydrationWarning>
      <head>
        {/* Apple touch icons */}
        <link rel="apple-touch-icon" href="/favicons/apple-touch-icon.png" />
        <link rel="apple-touch-icon" sizes="180x180" href="/favicons/apple-touch-icon.png" />
        
        {/* Favicons */}
        <link rel="icon" type="image/png" sizes="96x96" href="/favicons/favicon-96x96.png" />
        <link rel="icon" type="image/svg+xml" href="/favicons/favicon.svg" />
        <link rel="icon" type="image/x-icon" href="/favicons/favicon.ico" />
        
        {/* PWA meta tags */}
        <meta name="mobile-web-app-capable" content="yes" />
      </head>
      <body>
        <ClientProviders session={session}>
          {children}
        </ClientProviders>
      </body>
    </html>
  );
}

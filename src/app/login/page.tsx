"use client";

import Link from 'next/link';
import { useEffect, Suspense, useActionState } from 'react';
import { ConvexError } from 'convex/values';
import { useRouter } from 'next/navigation';
import { signIn, useSession } from 'next-auth/react';
import { useQuery } from "convex/react";
import { ConvexHttpClient } from "convex/browser";
import { api } from "@/../convex/_generated/api";
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { PasswordInput } from '@/components/ui/password-input';
import { Card, CardContent, CardHeader } from '@/components/ui/card';
import LoginLogo from '@/components/icons/LoginLogo';
import { useFormStatus } from 'react-dom';

// Initialize Convex client for imperative queries
const convex = new ConvexHttpClient(process.env.NEXT_PUBLIC_CONVEX_URL!);

function LoginSubmitButton() {
    const { pending } = useFormStatus();
    
    return (
        <Button type="submit" disabled={pending} className="flex-1">
            {pending ? "Logging in..." : "LOGIN"}
        </Button>
    );
}

export default function LoginPage() {
    const router = useRouter();
    const { data: session, status } = useSession();
    const activeEvent = useQuery(api.events.getActiveEvent);
    
    // React 19 Actions for login form
    const [error, loginAction, isPending] = useActionState(
        async (_previousState: string | null, formData: FormData) => {
            try {
                const username = formData.get("username") as string;
                const password = formData.get("password") as string;

                // Validate required fields
                if (!username || !password) {
                    return 'Please fill in all fields';
                }

                // First check if the user is an admin
                const isAdminData = await convex.query(api.users.checkUserAdmin, { username });
                const isAdmin = isAdminData.isAdmin;

                // If not admin, check for active events
                if (!isAdmin && !activeEvent) {
                    return 'No active events for now! Please try again later.';
                }

                const result = await signIn('credentials', {
                    username,
                    password,
                    redirect: false
                });

                if (result?.error) {
                    // Handle specific error messages
                    if (result.error === 'Your account is pending approval') {
                        return 'Your account is pending administrator approval';
                    } else if (result.error === 'Your registration was not approved') {
                        return 'Your registration was not approved. Please contact an administrator.';
                    } else if (result.error === 'Your account is not registered for the current event') {
                        return `Your account is not registered for the current event: ${activeEvent?.name}`;
                    } else if (result.error === 'No active events for now!') {
                        return 'No active events for now! Please try again later.';
                    } else {
                        return result.error;
                    }
                }

                return null; // Success - no error
            } catch (error) {
                if (error instanceof ConvexError) {
                    const errorData = error.data as { message: string };
                    return errorData.message;
                } else {
                    return error instanceof Error ? error.message : 'Login failed';
                }
            }
        },
        null
    );

    // Handle session-based redirects
    useEffect(() => {
        if (status === 'authenticated' && session?.user) {
            if (session.user.role === 'admin') {
                router.push('/admin');
            } else if (session.user.status === 'approved') {
                router.push('/user');
            }
            // Note: Actions will handle the "pending approval" error case
        }
    }, [status, session, router]);

    // Don't render the form if we're already authenticated
    if (status === 'authenticated' && !error) {
        return null;
    }

    return (
        <div className="relative w-screen min-h-screen flex justify-center items-center bg-background p-8 md:p-4 theme-background">
            

            {/* Main Content */}
            <div className="relative z-10 w-full max-w-md">
                <Suspense fallback={
                    <Card className="bg-card/90 backdrop-blur-sm border-accent">
                        <CardContent className="flex items-center justify-center py-12">
                            <div className="text-muted-foreground">Loading...</div>
                        </CardContent>
                    </Card>
                }>
                    <Card className="bg-card/90 backdrop-blur-sm border-accent">
                        <CardHeader className="flex flex-col items-center space-y-4 pb-0">
                            {/* Logo Container - Keep same logic */}
                            <LoginLogo className="text-secondary" size={100} />
                            <div className="text-card-foreground text-center">
                                <div className="text-sm font-mono">
                                    Welcome to
                                </div>
                                <h1 className="text-2xl md:text-3xl font-ultrabold">
                                    {activeEvent ? activeEvent.name : 'Lion X'}
                                </h1>
                            </div>
                        </CardHeader>
                        
                        <CardContent className="space-y-6">
                            <form action={loginAction} className="space-y-5">
                                <div className="space-y-4">
                                    <Input
                                        type="text"
                                        name="username"
                                        placeholder="Username"
                                        disabled={isPending}
                                        className="input-standard"
                                        onInput={(e) => {
                                            const target = e.target as HTMLInputElement;
                                            target.value = target.value.toLowerCase().replace(/\s+/g, '');
                                        }}
                                    />
                                    <PasswordInput
                                        name="password"
                                        placeholder="••••••••"
                                        disabled={isPending}
                                        className="input-standard"
                                    />
                                </div>
                                
                                {error && (
                                    <div className="text-sm bg-primary/10 border border-primary/20 text-primary p-3">
                                        {error}
                                    </div>
                                )}
                                
                                <div className={`flex flex-col sm:flex-row gap-4 ${activeEvent ? 'sm:justify-between' : 'justify-center'}`}>
                                    <LoginSubmitButton />
                                    {activeEvent && (
                                        <Button asChild variant="outline" className="flex-1">
                                            <Link href="/register">
                                                SIGN UP
                                            </Link>
                                        </Button>
                                    )}
                                </div>
                            </form>
                        </CardContent>
                    </Card>
                </Suspense>
            </div>
        </div>
    );
}

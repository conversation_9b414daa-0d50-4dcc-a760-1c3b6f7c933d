import { Toaster } from 'sonner';
import UserAuthWrapper from './components/UserAuthWrapper';
import UserDataProvider from './components/UserDataProvider';
import UserHeader from './components/UserHeader';
import UserMainContent from './components/UserMainContent';


export default function UserLayout({
    children,
}: {
    children: React.ReactNode;
}) {

    return (
        <UserAuthWrapper>
            <UserDataProvider>
                <div className="min-h-screen text-foreground font-mono flex flex-col relative theme-background">
                    <UserHeader />
                    <UserMainContent>
                        {children}
                    </UserMainContent>
                    <Toaster 
                        position="top-right" 
                        offset="80px"
                        toastOptions={{
                            unstyled: true,
                            classNames: {
                                toast: 'bg-background border-2 border-primary text-foreground font-medium p-4 shadow-none flex items-center gap-3',
                                success: 'border-[#15B14B] text-[#15B14B]',
                                error: 'border-destructive text-destructive',
                                warning: 'border-accent text-accent',
                                info: 'border-secondary text-secondary',
                            }
                        }}
                    />
                </div>
            </UserDataProvider>
        </UserAuthWrapper>
    );
}
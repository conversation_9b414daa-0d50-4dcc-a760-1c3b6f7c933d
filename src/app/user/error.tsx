'use client'

import { useEffect } from 'react'
import { StateCard } from '@/components/ui/state-card'
import { Button } from '@/components/ui/button'

export default function UserError({
  error,
  reset,
}: {
  error: Error & { digest?: string }
  reset: () => void
}) {
  useEffect(() => {
    console.error('User route error boundary caught an error:', error)
  }, [error])

  return (
    <div className="flex justify-center items-center min-h-[400px] p-6">
      <div className="space-y-4 w-full max-w-md">
        <StateCard
          state="error"
          title="Oops! Something went wrong"
          message="We encountered an issue while loading this section. Please try again in a moment."
        />
        <div className="flex justify-center gap-3">
          <Button onClick={reset}>
            Try Again
          </Button>
          <Button 
            variant="outline" 
            onClick={() => window.location.href = '/user'}
          >
            Back to User Page
          </Button>
        </div>
        {process.env.NODE_ENV === 'development' && (
          <div className="mt-4 p-4 bg-destructive/10 border border-destructive/20 text-left">
            <div className="text-sm font-mono text-destructive">
              {error.message}
            </div>
            {error.digest && (
              <div className="text-xs font-mono text-destructive mt-2 opacity-70">
                Error ID: {error.digest}
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  )
}
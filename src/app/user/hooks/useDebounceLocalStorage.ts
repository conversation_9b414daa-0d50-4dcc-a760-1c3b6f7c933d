import { useEffect } from 'react';
import { useDebounce } from './useDebounce';

// Custom hook for debounced localStorage operations
export const useDebounceLocalStorage = (key: string, value: unknown, delay = 1000, enabled = true) => {
  const debouncedValue = useDebounce(value, delay);
  
  useEffect(() => {
    if (enabled && debouncedValue !== undefined && debouncedValue !== null && key) {
      try {
        localStorage.setItem(key, JSON.stringify(debouncedValue));
      } catch (error) {
        console.error('Failed to save to localStorage:', error);
      }
    }
  }, [key, debouncedValue, enabled]);
};
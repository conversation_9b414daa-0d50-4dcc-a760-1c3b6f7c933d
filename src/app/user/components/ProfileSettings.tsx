"use client";

import { useState } from 'react';
import { signOut, useSession } from 'next-auth/react';
import { useAction } from 'convex/react';
import { api } from '@/../convex/_generated/api';
import { Id } from '@/../convex/_generated/dataModel';
import {
    AlertDialog,
    AlertDialogAction,
    AlertDialogCancel,
    AlertDialogContent,
    AlertDialogDescription,
    AlertDialogFooter,
    AlertDialogHeader,
    AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { toast } from 'sonner';
import { AppUser } from '@/types/user';

interface ProfileSettingsProps {
    user: AppUser | null;
    open: boolean;
    onClose: () => void;
    onUpdate: (userData: AppUser) => void;
}

export default function ProfileSettings({ user, open, onClose, onUpdate }: ProfileSettingsProps) {
    const { data: session, status } = useSession();
    const [formData, setFormData] = useState({ name: '', username: '', password: '', confirmPassword: '' });
    const [updateError, setUpdateError] = useState('');
    const [isUpdating, setIsUpdating] = useState(false);
    
    const updateUserProfile = useAction(api.users.updateUserProfile);

    const validateUsername = (username: string) => {
        if (username.includes(' ')) {
            return 'Username cannot contain spaces';
        }
        if (username !== username.toLowerCase()) {
            return 'Username must be lowercase';
        }
        return null;
    };

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();
        setUpdateError('');

        // Check session - type assertion for NextAuth session typing issue
        if (status !== 'authenticated' || !((session?.user as { username?: string })?.username) || !user?.id) {
            setUpdateError('Not authenticated');
            return;
        }

        if (formData.username) {
            const usernameError = validateUsername(formData.username);
            if (usernameError) {
                setUpdateError(usernameError);
                return;
            }
        }

        if (formData.password !== formData.confirmPassword) {
            setUpdateError('Passwords do not match');
            return;
        }
        
        try {
            setIsUpdating(true);
            
            // Prepare update data
            const updateData: { name?: string; username?: string; password?: string } = {};
            
            if (formData.name) {
                updateData.name = formData.name;
            }
            if (formData.username) {
                updateData.username = formData.username;
            }
            if (formData.password) {
                // Send raw password - will be hashed server-side in Convex
                updateData.password = formData.password;
            }

            // Only proceed if there's something to update
            if (Object.keys(updateData).length === 0) {
                setUpdateError('No fields to update');
                return;
            }

            // Call Convex action
            const result = await updateUserProfile({
                userId: user.id as Id<"users">,
                ...updateData
            });
            
            if (!result.success) {
                setUpdateError(result.message || 'Failed to update profile');
                return;
            }

            // Create updated user data for onUpdate callback
            const updatedUser = {
                ...user,
                ...(formData.name && { name: formData.name }),
                ...(formData.username && { username: formData.username })
            };
            
            onUpdate(updatedUser);
            
            // Show success toast
            toast.success('Profile updated successfully!');
            
            // Close modal
            onClose();
            
            // Sign out after profile update (especially if username/password changed)
            await signOut({ redirect: true, callbackUrl: '/login' });
        } catch (error) {
            const errorMessage = error instanceof Error ? error.message : 'Failed to update profile';
            setUpdateError(errorMessage);
            toast.error(errorMessage);
        } finally {
            setIsUpdating(false);
        }
    };

    return (
        <AlertDialog open={open} onOpenChange={(open) => !open && onClose()}>
            <AlertDialogContent>
                <AlertDialogHeader>
                    <AlertDialogTitle>Profile Settings</AlertDialogTitle>
                    <AlertDialogDescription>
                        Update your profile information. You will be logged out after making changes.
                    </AlertDialogDescription>
                </AlertDialogHeader>
                
                <div className="space-y-4 py-4">
                    <div className="space-y-2">
                        <Label htmlFor="name">Name</Label>
                        <Input
                            id="name"
                            type="text"
                            value={formData.name}
                            onChange={e => setFormData(prev => ({ ...prev, name: e.target.value }))}
                            placeholder={user?.name || ''}
                        />
                    </div>
                    <div className="space-y-2">
                        <Label htmlFor="username">Username</Label>
                        <Input
                            id="username"
                            type="text"
                            value={formData.username}
                            onChange={e => setFormData(prev => ({ ...prev, username: e.target.value }))}
                            placeholder={user?.username || ''}
                        />
                    </div>
                    <div className="space-y-2">
                        <Label htmlFor="password">New Password</Label>
                        <Input
                            id="password"
                            type="password"
                            value={formData.password}
                            onChange={e => setFormData(prev => ({ ...prev, password: e.target.value }))}
                            placeholder="Leave blank to keep current password"
                        />
                    </div>
                    <div className="space-y-2">
                        <Label htmlFor="confirmPassword">Confirm Password</Label>
                        <Input
                            id="confirmPassword"
                            type="password"
                            value={formData.confirmPassword}
                            onChange={e => setFormData(prev => ({ ...prev, confirmPassword: e.target.value }))}
                            placeholder="Confirm new password"
                        />
                    </div>
                    {updateError && (
                        <div className="text-destructive text-sm bg-destructive/10 border border-destructive/20 p-3 rounded">
                            {updateError}
                        </div>
                    )}
                </div>

                <AlertDialogFooter>
                    <AlertDialogCancel disabled={isUpdating}>
                        Cancel
                    </AlertDialogCancel>
                    <AlertDialogAction
                        onClick={(e) => {
                            e.preventDefault();
                            handleSubmit(e as React.FormEvent<HTMLButtonElement>);
                        }}
                        disabled={isUpdating}
                    >
                        {isUpdating ? 'Updating...' : 'Update Profile'}
                    </AlertDialogAction>
                </AlertDialogFooter>
            </AlertDialogContent>
        </AlertDialog>
    );
}

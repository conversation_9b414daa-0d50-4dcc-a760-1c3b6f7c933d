import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Label } from '@/components/ui/label';

interface SparkRadioGroupProps {
  label: string;
  options: string[];
  required?: boolean;
  value: string;
  onChange: (value: string) => void;
  disabled?: boolean;
  fieldId: string; // Unique identifier for radio group
  name?: string;
}

export default function SparkRadioGroup({
  label,
  options,
  required = false,
  value,
  onChange,
  disabled = false,
  fieldId,
  name,
}: SparkRadioGroupProps) {
  return (
    <div className="flex flex-col gap-2">
      <Label className="text-sm font-medium text-secondary flex gap-1">
        {label}
        {required && <span className="text-accent">*</span>}
      </Label>
      
      {/* Hidden input for form submission */}
      <input type="hidden" name={name} value={value} />
      
      <RadioGroup value={value} onValueChange={onChange} disabled={disabled}>
        {options.map((option, index) => (
          <div key={index} className="flex items-center gap-3">
            <RadioGroupItem 
              value={option} 
              id={`${fieldId}-${index}`}
              className="border-2 border-secondary text-secondary"
            />
            <Label 
              htmlFor={`${fieldId}-${index}`}
              className="text-foreground font-mono cursor-pointer"
            >
              {option}
            </Label>
          </div>
        ))}
      </RadioGroup>
    </div>
  );
}
"use client";

import { useState, useEffect, useCallback, useActionState } from 'react';
import { useQuery, useMutation } from "convex/react";
import { api } from "@/../convex/_generated/api";
import { useSession } from 'next-auth/react';
import { debugError } from '@/lib/utils';
import SparkFieldRenderer from './SparkFieldRenderer';
import { Button } from '@/components/ui/button';
import { toast } from 'sonner';
import { Id } from "@/../convex/_generated/dataModel";
import { useFormStatus } from 'react-dom';

function SubmitButton({ editingSubmission }: { editingSubmission: Id<"sparkSubmissions"> | null }) {
    const { pending } = useFormStatus();
    
    return (
        <Button type="submit" disabled={pending}>
            {pending 
                ? (editingSubmission ? 'Updating...' : 'Submitting...') 
                : (editingSubmission ? 'Update Submission' : 'Submit')
            }
        </Button>
    );
}

interface SparkField {
  type: 'text' | 'richtext' | 'dropdown' | 'radio' | 'checkbox';
  label: string;
  placeholder?: string;
  options?: string[];
  required?: boolean;
}

interface SparkConfig {
  _id: string;
  name: string;
  description?: string;
  fields: SparkField[];
}

interface SparkFormProps {
  sparkConfig: SparkConfig;
  editingSubmission: Id<"sparkSubmissions"> | null;
  setEditingSubmission: (id: Id<"sparkSubmissions"> | null) => void;
  onSubmissionComplete: () => void;
}

export default function SparkForm({ 
  sparkConfig, 
  editingSubmission, 
  setEditingSubmission, 
  onSubmissionComplete 
}: SparkFormProps) {
  const { data: sessionData } = useSession();
  const [formData, setFormData] = useState<Record<string, string | boolean>>({});
  const [isStorageSaved, setIsStorageSaved] = useState(false);
  
  // Real-time Convex queries
  const activeEvent = useQuery(api.events.getActiveEvent);
  const activeSession = useQuery(api.sessions.getActiveSession);
  const currentUser = useQuery(
    api.users.getCurrentUser, 
    sessionData?.user?.username ? {
      username: sessionData.user.username,
      activeEventId: activeEvent?._id
    } : "skip"
  );

  const existingSubmissions = useQuery(
    api.sparkSubmissions.getSparkSubmissionsByUser,
    currentUser ? { 
      userId: currentUser.id as Id<"users">
    } : "skip"
  );

  // Real-time mutations
  const submitSparkDataMutation = useMutation(api.sparkSubmissions.submitSparkData);
  const updateSparkSubmissionMutation = useMutation(api.sparkSubmissions.updateSparkSubmission);
  
  // React 19 Actions for form submission
  const [error, submitAction, isPending] = useActionState(
    async (_previousState: string | null, formData: FormData) => {
      try {
        if (!currentUser || !activeSession || !sparkConfig) {
          throw new Error('Missing required data');
        }

        // Convert FormData to form data object for validation
        const formDataObj: Record<string, string | boolean> = {};
        
        // Process each field from the form
        for (let i = 0; i < sparkConfig.fields.length; i++) {
          const field = sparkConfig.fields[i];
          const fieldId = `field_${i}`;
          const value = formData.get(fieldId);
          
          if (field.type === 'checkbox') {
            formDataObj[fieldId] = value === 'on';
          } else {
            formDataObj[fieldId] = (value as string) || '';
          }
        }

        // Validate form
        for (let i = 0; i < sparkConfig.fields.length; i++) {
          const field = sparkConfig.fields[i];
          const fieldId = `field_${i}`;
          const value = formDataObj[fieldId];

          if (field.required) {
            if (!value || (typeof value === 'string' && value.trim() === '')) {
              return `${field.label} is required`;
            }
          }
        }

        const result = editingSubmission
          ? await updateSparkSubmissionMutation({
              submissionId: editingSubmission,
              userId: currentUser.id as Id<"users">,
              data: formDataObj,
            })
          : await submitSparkDataMutation({
              sessionId: activeSession._id,
              sparkId: sparkConfig._id as Id<"sparks">,
              userId: currentUser.id as Id<"users">,
              data: formDataObj,
            });

        if (result.success) {
          clearFormAndLocalStorage();
          toast.success(editingSubmission ? 'Spark data updated successfully!' : 'Spark data submitted successfully!');
          onSubmissionComplete();
          return null; // Success - no error
        } else {
          return result.error || 'Failed to submit spark data';
        }
      } catch (error) {
        debugError('Failed to submit spark data:', error);
        const errorMessage = error instanceof Error ? error.message : 'Failed to submit spark data';
        return errorMessage;
      }
    },
    null
  );

  // Check if autosave is enabled for current user
  const autosaveEnabled = currentUser?.autosave !== false; // undefined or true means enabled

  // Save form data to localStorage with improved validation
  useEffect(() => {
    // Add delay to prevent race conditions during session switches
    const saveTimeout = setTimeout(() => {
      if (autosaveEnabled && currentUser?.id && activeSession?._id && sparkConfig?._id && sparkConfig.fields) {
        // Check if localStorage is available
        if (typeof Storage === "undefined") {
          console.warn("localStorage is not available");
          return;
        }
        
        const localStorageKey = `sparksFormData_${currentUser.id}_${activeSession._id}_${sparkConfig._id}`;
        
        // Only save if there's actual data
        const hasData = Object.keys(formData).some(key => {
          const value = formData[key];
          return value !== undefined && value !== null && 
                 (typeof value === 'string' ? value.trim() !== '' : true);
        });
        
        if (editingSubmission || hasData) {
          try {
            const dataToSave = {
              formData,
              editingSubmission,
              sparkConfigId: sparkConfig._id,
              sessionId: activeSession._id,
              fieldCount: sparkConfig.fields.length,
              fieldTypes: sparkConfig.fields.map(f => f.type), // Store field types for validation
              timestamp: Date.now()
            };
            
            localStorage.setItem(localStorageKey, JSON.stringify(dataToSave));
            
            // Trigger save animation only if autosave is enabled
            if (autosaveEnabled) {
              setIsStorageSaved(true);
              setTimeout(() => setIsStorageSaved(false), 1000);
            }
          } catch (error) {
            if (error instanceof DOMException && error.name === 'QuotaExceededError') {
              console.error("localStorage quota exceeded");
              toast.error("Unable to save form data: Storage quota exceeded");
            } else {
              console.error("Failed to save Sparks form data:", error);
            }
          }
        }
      }
    }, 100); // Small delay to prevent race conditions

    return () => {
      clearTimeout(saveTimeout);
    };
  }, [formData, editingSubmission, currentUser?.id, activeSession?._id, sparkConfig?._id, sparkConfig?.fields, autosaveEnabled]);

  // Helper function to validate field types match
  const validateFieldTypesMatch = useCallback((savedFieldTypes: string[], currentFieldTypes: string[]) => {
    return savedFieldTypes.length === currentFieldTypes.length &&
           savedFieldTypes.every((type: string, index: number) => type === currentFieldTypes[index]);
  }, []);

  // Helper function to validate individual field values
  const validateFieldValue = useCallback((_fieldKey: string, savedValue: string | boolean, currentField: { type: string; options?: string[] }, savedFieldType?: string) => {
    if (!currentField || savedValue === undefined) return null;
    
    // Strict type validation - only allow values if the field type matches exactly
    if (currentField.type === 'checkbox' && typeof savedValue === 'boolean') {
      // Only allow checkbox values if the saved field was also a checkbox
      return savedFieldType === 'checkbox' ? savedValue : null;
    } else if (currentField.type !== 'checkbox' && typeof savedValue === 'string') {
      // For dropdown fields, validate the value exists in current options
      if (currentField.type === 'dropdown') {
        const validOptions = currentField.options || [];
        // Only allow if saved field was also dropdown and value is valid
        if (savedFieldType === 'dropdown' && (validOptions.includes(savedValue) || savedValue === '')) {
          return savedValue;
        }
      } 
      // For radio fields, validate the value exists in current options  
      else if (currentField.type === 'radio') {
        const validOptions = currentField.options || [];
        // Only allow if saved field was also radio and value is valid
        if (savedFieldType === 'radio' && (validOptions.includes(savedValue) || savedValue === '')) {
          return savedValue;
        }
      }
      // For rich text fields, only accept if saved field was also richtext
      else if (currentField.type === 'richtext') {
        return savedFieldType === 'richtext' ? savedValue : null;
      } 
      // For text fields, only accept if saved field was also text
      else if (currentField.type === 'text') {
        return savedFieldType === 'text' ? savedValue : null;
      }
    }
    return null;
  }, []);

  // Helper function to validate all form data
  const validateFormData = useCallback((parsedData: { formData?: Record<string, string | boolean>; fieldTypes?: string[] }, sparkConfig: { fields: { type: string; options?: string[] }[] }) => {
    const validatedFormData: Record<string, string | boolean> = {};
    const savedFieldTypes = parsedData.fieldTypes || [];
    
    Object.keys(parsedData.formData || {}).forEach(fieldKey => {
      const fieldIndex = parseInt(fieldKey.replace('field_', ''));
      const currentField = sparkConfig.fields[fieldIndex];
      const savedValue = parsedData.formData![fieldKey];
      const savedFieldType = savedFieldTypes[fieldIndex];
      
      if (fieldIndex < sparkConfig.fields.length) {
        const validValue = validateFieldValue(fieldKey, savedValue, currentField, savedFieldType);
        if (validValue !== null) {
          validatedFormData[fieldKey] = validValue;
        }
      }
    });
    
    return validatedFormData;
  }, [validateFieldValue]);

  // Load saved form data when user and session are identified (only if autosave is enabled)
  useEffect(() => {
    if (autosaveEnabled && currentUser?.id && activeSession?._id && sparkConfig?._id && sparkConfig.fields) {
      const localStorageKey = `sparksFormData_${currentUser.id}_${activeSession._id}_${sparkConfig._id}`;
      
      try {
        const savedData = localStorage.getItem(localStorageKey);
        if (savedData) {
          const parsedData = JSON.parse(savedData);
          
          // Strict validation: all IDs must match exactly
          if (parsedData.sparkConfigId === sparkConfig._id && 
              parsedData.sessionId === activeSession._id &&
              parsedData.fieldCount === sparkConfig.fields.length) {
            
            // Validate field types haven't changed
            const savedFieldTypes = parsedData.fieldTypes || [];
            const currentFieldTypes = sparkConfig.fields.map(f => f.type);
            const fieldTypesMatch = validateFieldTypesMatch(savedFieldTypes, currentFieldTypes);
            
            if (fieldTypesMatch) {
              // Validate each saved field against current spark config
              const validatedFormData = validateFormData(parsedData, sparkConfig);
              
              // Update form data and editing state atomically
              setFormData(validatedFormData);
              setEditingSubmission(parsedData.editingSubmission || null);
            } else {
              // Field configuration changed, clear saved data
              localStorage.removeItem(localStorageKey);
            }
          } else {
            // Configuration mismatch, clear saved data
            localStorage.removeItem(localStorageKey);
          }
        }
      } catch (error) {
        console.error("Failed to load Sparks form data:", error);
        // Clear corrupted data
        try {
          localStorage.removeItem(localStorageKey);
        } catch (cleanupError) {
          console.error("Failed to cleanup corrupted localStorage:", cleanupError);
        }
      }
    }
  }, [currentUser?.id, activeSession?._id, sparkConfig, validateFieldTypesMatch, validateFormData, setEditingSubmission, autosaveEnabled]);

  // Handle editing submission - populate form with existing data
  useEffect(() => {
    if (editingSubmission && existingSubmissions) {
      const submission = existingSubmissions.find(s => s._id === editingSubmission);
      if (submission && submission.data) {
        const submissionData = submission.data as Record<string, string | boolean>;
        setFormData(submissionData);
      }
    }
  }, [editingSubmission, existingSubmissions]);

  // Clear localStorage when form is empty and not editing (only if autosave was enabled)
  useEffect(() => {
    // Add delay to prevent clearing during session switches
    const clearStorageTimeout = setTimeout(() => {
      if (autosaveEnabled && currentUser?.id && activeSession?._id && sparkConfig?._id) {
        const localStorageKey = `sparksFormData_${currentUser.id}_${activeSession._id}_${sparkConfig._id}`;
        
        const hasData = Object.keys(formData).some(key => {
          const value = formData[key];
          return value !== undefined && value !== null && 
                 (typeof value === 'string' ? value.trim() !== '' : true);
        });
        
        if (!editingSubmission && !hasData) {
          try {
            localStorage.removeItem(localStorageKey);
          } catch (error) {
            console.error("Failed to clear Sparks localStorage:", error);
          }
        }
      }
    }, 200); // Delay to prevent premature clearing during session switches

    return () => {
      clearTimeout(clearStorageTimeout);
    };
  }, [formData, editingSubmission, currentUser?.id, activeSession?._id, sparkConfig?._id, autosaveEnabled]);

  const handleFieldChange = (fieldId: string, value: string | boolean) => {
    setFormData(prev => ({
      ...prev,
      [fieldId]: value
    }));
  };


  const clearFormAndLocalStorage = () => {
    setFormData({});
    setEditingSubmission(null);
    
    // Clear localStorage
    if (currentUser?.id && activeSession?._id && sparkConfig?._id) {
      const localStorageKey = `sparksFormData_${currentUser.id}_${activeSession._id}_${sparkConfig._id}`;
      try {
        localStorage.removeItem(localStorageKey);
      } catch (error) {
        console.error("Failed to clear Sparks localStorage:", error);
      }
    }
  };


  const handleCancelEdit = () => {
    setEditingSubmission(null);
    setFormData({});
  };

  return (
    <div className="bg-background border-2 border-secondary responsive-user-card w-full relative">
      {/* Storage save indicator */}
      <div className={`absolute top-15 right-10 px-2.5 py-1.5 bg-secondary text-background font-mono text-xs font-bold uppercase z-10 transition-opacity duration-300 ${isStorageSaved ? 'opacity-100 animate-[flash-save_2.5s_ease-in-out]' : 'opacity-0'}`}>SAVED</div>
      <h2 className="font-ultrabold text-2xl text-primary mb-2 text-center">{sparkConfig.name}</h2>
      {sparkConfig.description && (
        <p className="text-accent text-center mb-6 font-mono text-lg">{sparkConfig.description}</p>
      )}
      
      
      <form action={submitAction} className="flex flex-col gap-6">
        {sparkConfig?.fields?.map((field, index) => {
          const fieldId = `field_${index}`;
          const fieldValue = formData[fieldId] || (field.type === 'checkbox' ? false : '');
          
          return (
            <SparkFieldRenderer
              key={fieldId}
              field={field}
              fieldId={fieldId}
              value={fieldValue}
              onChange={(value) => handleFieldChange(fieldId, value)}
              disabled={isPending}
              onSubmit={() => {
                const form = document.querySelector('form');
                if (form) {
                  const submitEvent = new Event('submit', { bubbles: true, cancelable: true });
                  form.dispatchEvent(submitEvent);
                }
              }}
            />
          );
        })}
        
        {/* Error display */}
        {error && (
          <div className="bg-destructive/10 border border-destructive text-destructive px-4 py-2 font-mono text-sm">
            {error}
          </div>
        )}
        
        {/* Separator before buttons */}
        <div className="border-t border-secondary/20 pt-6">
          <div className="flex justify-between items-center flex-wrap gap-4">
            <div className="flex gap-4 items-center flex-wrap">
              <SubmitButton editingSubmission={editingSubmission} />
              <Button
                type="button"
                variant="outline"
                onClick={clearFormAndLocalStorage}
                disabled={isPending}
              >
                Clear
              </Button>
              {editingSubmission && (
                <Button
                  type="button"
                  variant="outline"
                  onClick={handleCancelEdit}
                  disabled={isPending}
                >
                  Cancel Edit
                </Button>
              )}
            </div>
          </div>
        </div>
      </form>
    </div>
  );
}
import { Checkbox } from '@/components/ui/checkbox';
import { Label } from '@/components/ui/label';

interface SparkCheckboxProps {
  label: string;
  required?: boolean;
  value: boolean;
  onChange: (value: boolean) => void;
  disabled?: boolean;
  name?: string;
}

export default function SparkCheckbox({
  label,
  required = false,
  value,
  onChange,
  disabled = false,
  name,
}: SparkCheckboxProps) {
  return (
    <div className="flex items-center gap-3">
      {/* Hidden input for form submission */}
      <input type="hidden" name={name} value={value ? 'on' : ''} />
      
      <Checkbox
        checked={value}
        onCheckedChange={onChange}
        disabled={disabled}
        className="shrink-0"
      />
      <Label className="text-foreground font-mono cursor-pointer flex gap-1">
        {label}
        {required && <span className="text-accent">*</span>}
      </Label>
    </div>
  );
}
import { memo } from 'react';
import SparkTextField from './SparkTextField';
import SparkRichTextField from './SparkRichTextField';
import SparkDropdown from './SparkDropdown';
import SparkRadioGroup from './SparkRadioGroup';
import SparkCheckbox from './SparkCheckbox';

interface SparkField {
  type: 'text' | 'richtext' | 'dropdown' | 'radio' | 'checkbox';
  label: string;
  placeholder?: string;
  options?: string[];
  required?: boolean;
}

interface SparkFieldRendererProps {
  field: SparkField;
  fieldId: string;
  value: string | boolean;
  onChange: (value: string | boolean) => void;
  disabled?: boolean;
  onSubmit?: () => void;
}

const SparkFieldRenderer = memo(function SparkFieldRenderer({
  field,
  fieldId,
  value,
  onChange,
  disabled = false,
  onSubmit,
}: SparkFieldRendererProps) {
  switch (field.type) {
    case 'text':
      return (
        <SparkTextField
          label={field.label}
          placeholder={field.placeholder}
          required={field.required}
          value={typeof value === 'string' ? value : ''}
          onChange={onChange}
          disabled={disabled}
          name={fieldId}
        />
      );

    case 'richtext':
      return (
        <SparkRichTextField
          label={field.label}
          placeholder={field.placeholder}
          required={field.required}
          value={typeof value === 'string' ? value : ''}
          onChange={onChange}
          disabled={disabled}
          onSubmit={onSubmit}
          name={fieldId}
        />
      );

    case 'dropdown':
      return (
        <SparkDropdown
          label={field.label}
          options={field.options || []}
          required={field.required}
          value={typeof value === 'string' ? value : ''}
          onChange={onChange}
          disabled={disabled}
          name={fieldId}
        />
      );

    case 'radio':
      return (
        <SparkRadioGroup
          label={field.label}
          options={field.options || []}
          required={field.required}
          value={typeof value === 'string' ? value : ''}
          onChange={onChange}
          disabled={disabled}
          fieldId={fieldId}
          name={fieldId}
        />
      );

    case 'checkbox':
      return (
        <SparkCheckbox
          label={field.label}
          required={field.required}
          value={typeof value === 'boolean' ? value : false}
          onChange={onChange}
          disabled={disabled}
          name={fieldId}
        />
      );

    default:
      return (
        <div className="flex flex-col gap-2">
          <div className="text-destructive text-sm bg-destructive/10 border border-destructive/20 p-3">
            Unsupported field type: {field.type}
          </div>
        </div>
      );
  }
});

export default SparkFieldRenderer;
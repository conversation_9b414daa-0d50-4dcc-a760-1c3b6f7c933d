import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from '@/components/ui/select';
import { Label } from '@/components/ui/label';

interface SparkDropdownProps {
  label: string;
  options: string[];
  required?: boolean;
  value: string;
  onChange: (value: string) => void;
  disabled?: boolean;
  name?: string;
}

export default function SparkDropdown({
  label,
  options,
  required = false,
  value,
  onChange,
  disabled = false,
  name,
}: SparkDropdownProps) {
  const controlledValue = value || '';
  
  return (
    <div className="flex flex-col gap-2">
      <Label className="text-sm font-medium text-secondary flex gap-1">
        {label}
        {required && <span className="text-accent">*</span>}
      </Label>
      
      {/* Hidden input for form submission */}
      <input type="hidden" name={name} value={controlledValue} />
      
      <Select 
        key={controlledValue} // Force re-render when value changes
        value={controlledValue}
        onValueChange={onChange} 
        disabled={disabled}
      >
        <SelectTrigger className="w-full shadow-none focus-visible:ring-0 focus-visible:ring-offset-0">
          <SelectValue placeholder="Select an option..." />
        </SelectTrigger>
        <SelectContent>
          {options.map((option, index) => (
            <SelectItem key={index} value={option}>
              {option}
            </SelectItem>
          ))}
        </SelectContent>
      </Select>
    </div>
  );
}
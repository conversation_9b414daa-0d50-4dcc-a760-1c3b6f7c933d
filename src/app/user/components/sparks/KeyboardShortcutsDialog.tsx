import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
} from '@/components/ui/dialog';
import { Badge } from '@/components/ui/badge';

interface KeyboardShortcutsDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export default function KeyboardShortcutsDialog({ open, onOpenChange }: KeyboardShortcutsDialogProps) {
  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-md">
        <DialogHeader>
          <DialogTitle className="text-primary font-ultrabold">
            Shortcuts
          </DialogTitle>
          <DialogDescription className="text-foreground">
            Editor keyboard shortcuts
          </DialogDescription>
        </DialogHeader>
        
        <div className="space-y-0 mt-4">
          <div className="flex items-center justify-between py-2 border-b border-muted-foreground/20">
            <span className="text-accent">Maximize</span>
            <div className="flex items-center gap-1">
              <Badge variant="secondary">Ctrl</Badge>
              <Badge variant="secondary">Shift</Badge>
              <Badge variant="secondary">F</Badge>
            </div>
          </div>
          
          <div className="flex items-center justify-between py-2 border-b border-muted-foreground/20">
            <span className="text-accent">Submit</span>
            <div className="flex items-center gap-1">
              <Badge variant="secondary">Ctrl</Badge>
              <Badge variant="secondary">Enter</Badge>
            </div>
          </div>
          
          <div className="flex items-center justify-between py-2 border-b border-muted-foreground/20">
            <span className="text-accent">Help</span>
            <div className="flex items-center gap-1">
              <Badge variant="secondary">Ctrl</Badge>
              <Badge variant="secondary">Shift</Badge>
              <Badge variant="secondary">?</Badge>
            </div>
          </div>
          
          <div className="flex items-center justify-between py-2">
            <span className="text-accent">Clear</span>
            <div className="flex items-center gap-1">
              <Badge variant="secondary">Ctrl</Badge>
              <Badge variant="secondary">;</Badge>
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
import { useRef, useState, useEffect, useCallback } from 'react';
import TiptapEditor, { TiptapEditorHandle } from '@/components/editors/TiptapEditor';
import { Label } from '@/components/ui/label';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
} from '@/components/ui/dialog';
import { Maximize2, HelpCircle } from 'lucide-react';
import KeyboardShortcutsDialog from './KeyboardShortcutsDialog';

interface SparkRichTextFieldProps {
  label: string;
  placeholder?: string;
  required?: boolean;
  value: string;
  onChange: (value: string) => void;
  disabled?: boolean;
  onSubmit?: () => void;
  onClear?: () => void;
  name?: string;
}

export default function SparkRichTextField({
  label,
  placeholder,
  required = false,
  value,
  onChange,
  disabled = false,
  onSubmit,
  onClear,
  name,
}: SparkRichTextFieldProps) {
  const editorRef = useRef<TiptapEditorHandle>(null);
  const fullscreenEditorRef = useRef<TiptapEditorHandle>(null);
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [showKeyboardShortcuts, setShowKeyboardShortcuts] = useState(false);

  const handleFullscreenToggle = () => {
    setIsFullscreen(!isFullscreen);
  };

  const handleKeyboardShortcuts = () => {
    setShowKeyboardShortcuts(true);
  };

  const handleClear = useCallback(() => {
    if (onClear) {
      onClear();
    } else {
      onChange('');
    }
  }, [onClear, onChange]);

  // Keyboard shortcuts handler
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (disabled) return;
      
      // CTRL + SHIFT + F >> Maximize editor
      if (e.ctrlKey && e.shiftKey && e.key === 'F') {
        e.preventDefault();
        setIsFullscreen(true);
      }
      
      // CTRL + Return >> Submit
      if (e.ctrlKey && e.key === 'Enter') {
        e.preventDefault();
        if (onSubmit) {
          onSubmit();
        }
      }
      
      // CTRL + SHIFT + / >> Show keyboard shortcuts
      if (e.ctrlKey && e.shiftKey && e.key === '?') {
        e.preventDefault();
        setShowKeyboardShortcuts(true);
      }
      
      // CTRL + ; >> Clear
      if (e.ctrlKey && e.key === ';') {
        e.preventDefault();
        handleClear();
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [disabled, onSubmit, handleClear]);

  return (
    <div className="flex flex-col gap-2">
      <div className="flex items-center justify-between">
        <Label className="text-sm font-medium text-secondary flex gap-1">
          {label}
          {required && <span className="text-accent">*</span>}
        </Label>
        <div className="flex gap-2">
          <Button
            type="button"
            variant="ghost"
            size="sm"
            onClick={handleFullscreenToggle}
            disabled={disabled}
            className="shrink-0 p-2 hover:!bg-transparent hover:text-primary"
            title="Expand to fullscreen (Ctrl+Shift+F)"
          >
            <Maximize2 className="h-4 w-4" />
          </Button>
          <Button
            type="button"
            variant="ghost"
            size="sm"
            onClick={handleKeyboardShortcuts}
            disabled={disabled}
            className="shrink-0 p-2 hover:!bg-transparent hover:text-primary"
            title="Show keyboard shortcuts (Ctrl+Shift+?)"
          >
            <HelpCircle className="h-4 w-4" />
          </Button>
        </div>
      </div>
      
      {/* Hidden input for form submission */}
      <input type="hidden" name={name} value={value} />
      
      <div className="relative">
        <TiptapEditor
          ref={editorRef}
          content={value}
          onChange={onChange}
          editable={!disabled}
          className="w-full"
          placeholder={placeholder}
        />
      </div>

      {/* Fullscreen Dialog */}
      <Dialog open={isFullscreen} onOpenChange={setIsFullscreen}>
        <DialogContent className="max-w-[99vw] sm:max-w-[99vw] w-[99vw] h-[95vh] flex flex-col p-0">
          <DialogHeader className="p-4 pb-4 border-b border-border shrink-0">
            <DialogTitle className="flex items-center gap-0 text-primary text-left text-sm sm:text-base md:text-lg">
              {label}
              {required && <span className="text-accent">*</span>}
            </DialogTitle>
            <DialogDescription className="text-left text-xs sm:text-sm">
              Changes are automatically saved.
            </DialogDescription>
          </DialogHeader>
          
          <div className="flex-1 p-2 sm:p-4 md:p-6 lg:p-6 pt-4 min-h-0">
            <TiptapEditor
              ref={fullscreenEditorRef}
              content={value}
              onChange={onChange} // Sync content automatically
              editable={!disabled}
              className="w-full"
              placeholder={placeholder}
              fullscreen={true}
            />
          </div>
        </DialogContent>
      </Dialog>

      <KeyboardShortcutsDialog 
        open={showKeyboardShortcuts} 
        onOpenChange={setShowKeyboardShortcuts} 
      />
    </div>
  );
}
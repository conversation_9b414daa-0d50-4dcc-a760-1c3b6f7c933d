import { memo } from 'react';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';

interface SparkTextFieldProps {
  label: string;
  placeholder?: string;
  required?: boolean;
  value: string;
  onChange: (value: string) => void;
  disabled?: boolean;
  name?: string;
}

const SparkTextField = memo(function SparkTextField({
  label,
  placeholder,
  required = false,
  value,
  onChange,
  disabled = false,
  name,
}: SparkTextFieldProps) {
  return (
    <div className="flex flex-col gap-2">
      <Label className="text-sm font-medium text-secondary flex gap-1">
        {label}
        {required && <span className="text-accent">*</span>}
      </Label>
      <Input
        type="text"
        name={name}
        value={value}
        onChange={(e) => onChange(e.target.value)}
        placeholder={placeholder}
        required={required}
        disabled={disabled}
        className="w-full shadow-none focus-visible:ring-0 focus-visible:ring-offset-0"
      />
    </div>
  );
});

export default SparkTextField;
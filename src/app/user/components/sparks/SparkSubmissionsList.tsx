"use client";

import { useState } from 'react';
import { useQuery, useMutation } from "convex/react";
import { api } from "@/../convex/_generated/api";
import { useSession } from 'next-auth/react';
import { debugError } from '@/lib/utils';
import TiptapContentDisplay from '@/components/editors/TiptapContentDisplay';
import { Button } from '@/components/ui/button';
import {
    AlertDialog,
    AlertDialogAction,
    AlertDialogCancel,
    AlertDialogContent,
    AlertDialogDescription,
    AlertDialogFooter,
    AlertDialogHeader,
    AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import { toast } from 'sonner';
import { Id } from "@/../convex/_generated/dataModel";
import { Edit, Trash2 } from 'lucide-react';

interface SparkField {
  type: 'text' | 'richtext' | 'dropdown' | 'radio' | 'checkbox';
  label: string;
  placeholder?: string;
  options?: string[];
  required?: boolean;
}

interface SparkConfig {
  _id: string;
  name: string;
  description?: string;
  fields: SparkField[];
}

interface SparkSubmissionsListProps {
  sparkConfig: SparkConfig;
  onEditSubmission: (submission: Record<string, unknown>) => void;
}

export default function SparkSubmissionsList({ sparkConfig, onEditSubmission }: SparkSubmissionsListProps) {
  const { data: sessionData } = useSession();
  const [showDeleteConfirm, setShowDeleteConfirm] = useState<string | null>(null);
  
  // Real-time Convex queries
  const activeEvent = useQuery(api.events.getActiveEvent);
  const activeSession = useQuery(api.sessions.getActiveSession);
  const currentUser = useQuery(
    api.users.getCurrentUser, 
    sessionData?.user?.username ? {
      username: sessionData.user.username,
      activeEventId: activeEvent?._id
    } : "skip"
  );

  const existingSubmissions = useQuery(
    api.sparkSubmissions.getSparkSubmissionsByUser,
    currentUser ? { 
      userId: currentUser.id as Id<"users">
    } : "skip"
  );

  // Real-time mutations
  const deleteSparkSubmissionMutation = useMutation(api.sparkSubmissions.deleteSparkSubmission);

  const handleEdit = (submission: Record<string, unknown>) => {
    onEditSubmission(submission);
    
    // Scroll to the top of the page smoothly
    window.scrollTo({
      top: 0,
      behavior: 'smooth'
    });
  };

  const handleDelete = async (submissionId: string) => {
    if (!currentUser) return;

    try {
      const result = await deleteSparkSubmissionMutation({
        submissionId: submissionId as Id<"sparkSubmissions">,
        userId: currentUser.id as Id<"users">,
      });
      
      if (result.success) {
        toast.success('Spark data deleted successfully!');
      } else {
        throw new Error(result.error || 'Failed to delete spark data');
      }
    } catch (error) {
      debugError('Failed to delete spark data:', error);
      toast.error('Failed to delete spark data');
    } finally {
      setShowDeleteConfirm(null);
    }
  };

  // Filter submissions for current session and spark config
  const filteredSubmissions = existingSubmissions?.filter(s => 
    s.sessionId === activeSession?._id && s.sparkId === sparkConfig._id
  ) || [];

  // Don't render if no submissions
  if (filteredSubmissions.length === 0) {
    return null;
  }

  return (
    <div className="bg-background border-2 border-primary responsive-user-card w-full mt-4">
      <h2 className="font-ultrabold text-2xl text-primary mb-6 text-center">Submissions</h2>
      <div className="flex flex-col gap-6 mb-8">
        {filteredSubmissions.slice().reverse().map((submission) => (
          <div key={submission._id} className="bg-background border-2 border-secondary responsive-user-inner-card hover:bg-muted/50 transition-colors">
            
            {/* Render submission data dynamically */}
            {sparkConfig && sparkConfig.fields.map((field, index) => {
              const fieldId = `field_${index}`;
              const value = submission.data?.[fieldId];
              
              const isEmpty =
                value === undefined || value === null ||
                (typeof value === 'string' && value.trim() === '');
              if (isEmpty && field.type !== 'checkbox') return null;
              
              return (
                <div key={fieldId} className="mb-4">
                  <span className="font-mono text-sm text-secondary font-bold">{field.label}: </span>
                  {field.type === 'richtext' ? (
                    <div className="mt-2">
                      <TiptapContentDisplay content={value as string} />
                    </div>
                  ) : field.type === 'checkbox' ? (
                    <span className="font-mono text-sm text-foreground">
                      {value ? 'Yes' : 'No'}
                    </span>
                  ) : (
                    <span className="font-mono text-sm text-foreground">{value as string}</span>
                  )}
                </div>
              );
            })}
            
            <div className="flex justify-between items-start mt-4 pt-4 border-t border-secondary/20">
              <div className="block font-mono text-sm text-primary">
                Submitted at: {new Date(submission.submittedAt).toLocaleString()}
                {submission.updatedAt && (
                  <div className="text-muted-foreground">
                    Updated: {new Date(submission.updatedAt).toLocaleString()}
                  </div>
                )}
              </div>
              <div className="flex gap-2 ml-4">
                <Button 
                  variant="ghost"
                  size="sm"
                  onClick={() => handleEdit(submission)}
                  className="shrink-0 p-0 hover:!bg-transparent hover:text-primary"
                  title="Edit submission"
                >
                  <Edit className="h-4 w-4" />
                </Button>
                <Button 
                  variant="ghost"
                  size="sm"
                  onClick={() => setShowDeleteConfirm(submission._id)}
                  className="shrink-0 p-0 hover:!bg-transparent hover:text-primary"
                  title="Delete submission"
                >
                  <Trash2 className="h-4 w-4" />
                </Button>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Delete Confirmation Modal */}
      <AlertDialog open={!!showDeleteConfirm} onOpenChange={(open) => !open && setShowDeleteConfirm(null)}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Delete Submission</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to delete this submission? This action cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>
              Cancel
            </AlertDialogCancel>
            <AlertDialogAction
              onClick={() => showDeleteConfirm && handleDelete(showDeleteConfirm)}
            >
              Delete
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
}
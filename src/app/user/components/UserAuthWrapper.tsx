"use client";

import { useEffect, useCallback } from 'react';
import { useRouter } from 'next/navigation';
import { signOut, useSession } from 'next-auth/react';
import { useQuery } from "convex/react";
import { api } from "@/../convex/_generated/api";

interface UserAuthWrapperProps {
  children: React.ReactNode;
}

export default function UserAuthWrapper({ children }: UserAuthWrapperProps) {
  const router = useRouter();
  const { data: sessionData, status } = useSession();

  // Auto-logout handler following app patterns (same as UserHeader and AdminSidebar)
  const handleAutoLogout = useCallback(async (reason: string) => {
    try {
      console.log(`Auto-logout triggered: ${reason}`);
      await signOut({ redirect: false });
      router.push('/login');
    } catch (error) {
      console.error('Auto-logout error:', error);
    }
  }, [router]);
  
  // Real-time Convex queries for authentication checks
  const activeEvent = useQuery(api.events.getActiveEvent);
  const currentUser = useQuery(
    api.users.getCurrentUser, 
    sessionData?.user?.username ? {
      username: sessionData.user.username,
      activeEventId: activeEvent?._id
    } : "skip"
  );
  
  // Get full user data including events array for explicit registration validation
  const userData = useQuery(
    api.users.getUserByUsername,
    sessionData?.user?.username ? {
      username: sessionData.user.username
    } : "skip"
  );
  
  // Check user approval status
  const userApprovalStatus = useQuery(
    api.users.getUserApprovalStatus,
    sessionData?.user?.username ? {
      username: sessionData.user.username
    } : "skip"
  );

  // Handle authentication and role-based access
  useEffect(() => {
    if (status === 'unauthenticated') {
      router.push('/login');
      return;
    }

    if (status === 'authenticated' && sessionData?.user?.role === 'admin') {
      router.push('/admin');
      return;
    }
  }, [status, sessionData, router]);

  // Update local user state when Convex data changes
  useEffect(() => {
    if (currentUser === null && sessionData?.user?.username) {
      // User was deleted from database but session still exists
      handleAutoLogout('User deleted from database');
      return;
    }
  }, [currentUser, sessionData?.user?.username, handleAutoLogout]);

  // Check approval status and logout if not approved
  useEffect(() => {
    if (userApprovalStatus && userApprovalStatus.status !== 'approved') {
      handleAutoLogout(`User approval status changed to: ${userApprovalStatus.status}`);
    }
  }, [userApprovalStatus, handleAutoLogout]);

  // Check event status and user registration, logout if appropriate
  useEffect(() => {
    // Skip checks for admin users or if data is still loading
    if (sessionData?.user?.role === 'admin' || 
        activeEvent === undefined || 
        userData === undefined) {
      return;
    }

    // Check if user should be logged out
    let shouldLogout = false;
    let logoutReason = '';

    // Case 1: No active event exists
    if (activeEvent === null) {
      shouldLogout = true;
      logoutReason = 'No active event';
    }
    // Case 2: Active event exists but user is not registered for it
    else if (activeEvent && userData) {
      const userRegistration = userData.events?.find(
        (event: { eventId: string; teamId?: string }) => event.eventId === activeEvent._id
      );
      
      if (!userRegistration) {
        shouldLogout = true;
        logoutReason = 'User not registered for active event';
      }
    }

    if (shouldLogout) {
      console.log(`Auto-logout details:`, {
        reason: logoutReason,
        activeEvent: activeEvent?.name || 'none',
        userEvents: userData?.events?.map((e: { eventId: string; teamId?: string }) => e.eventId) || []
      });
      handleAutoLogout(logoutReason);
    }
  }, [activeEvent, userData, sessionData, handleAutoLogout]);

  // Don't render anything while checking authentication
  if (status === 'loading' || !sessionData) {
    return null;
  }

  // Show loading if authentication queries are still loading
  const isLoadingAuth = (
    activeEvent === undefined ||
    currentUser === undefined ||
    userData === undefined ||
    userApprovalStatus === undefined
  );

  if (isLoadingAuth) {
    return (
      <div className="min-h-screen text-white font-mono flex flex-col relative theme-background">
      </div>
    );
  }

  return <>{children}</>;
}
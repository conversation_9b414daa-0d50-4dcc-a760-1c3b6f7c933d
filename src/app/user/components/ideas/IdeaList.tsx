"use client";

import { useState } from 'react';
import { useQuery, useMutation } from "convex/react";
import { api } from "@/../convex/_generated/api";
import { useSession } from 'next-auth/react';
import { debugError } from '@/lib/utils';
import TiptapContentDisplay from '@/components/editors/TiptapContentDisplay';
import { Button } from '@/components/ui/button';
import {
    AlertDialog,
    AlertDialogAction,
    AlertDialogCancel,
    AlertDialogContent,
    AlertDialogDescription,
    AlertDialogFooter,
    AlertDialogHeader,
    AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import { toast } from 'sonner';
import { Id } from "@/../convex/_generated/dataModel";
import { Edit, Trash2 } from 'lucide-react';

interface IdeaListProps {
    onEditIdea: (ideaId: string | null) => void;
}

export default function IdeaList({ onEditIdea }: IdeaListProps) {
    const { data: sessionData } = useSession();
    const [submitting, setSubmitting] = useState(false);
    const [showSubmitConfirm, setShowSubmitConfirm] = useState(false);
    const [showDeleteConfirm, setShowDeleteConfirm] = useState<string | null>(null);
    
    // Real-time Convex queries
    const activeEvent = useQuery(api.events.getActiveEvent);
    const activeSession = useQuery(api.sessions.getActiveSession);
    const currentUser = useQuery(
        api.users.getCurrentUser, 
        sessionData?.user?.username ? {
            username: sessionData.user.username,
            activeEventId: activeEvent?._id
        } : "skip"
    );
    const ideas = useQuery(
        api.ideas.getIdeasByCurrentUser,
        currentUser ? { userId: currentUser.id as Id<"users"> } : "skip"
    );
    
    // Real-time mutations
    const deleteIdeaMutation = useMutation(api.ideas.deleteIdea);
    const submitTeamIdeasMutation = useMutation(api.ideas.submitTeamIdeas);

    const handleEdit = async (ideaId: string) => {
        onEditIdea(ideaId);
        
        // Scroll to the top of the page smoothly
        window.scrollTo({
            top: 0,
            behavior: 'smooth'
        });
    };

    const handleDelete = async (ideaId: string) => {
        try {
            const result = await deleteIdeaMutation({
                ideaId: ideaId as Id<"ideas">,
            });
            
            if (result.success) {
                toast.success('Idea deleted successfully!');
            } else {
                throw new Error(result.error || 'Failed to delete idea');
            }
        } catch (error) {
            debugError('Failed to delete idea:', error);
            toast.error('Failed to delete idea');
        } finally {
            setShowDeleteConfirm(null);
        }
    };

    const handleSubmitAll = async () => {
        setSubmitting(true);

        try {
            if (!currentUser || !activeSession) {
                throw new Error('User or session not found');
            }

            const result = await submitTeamIdeasMutation({
                userId: currentUser.id as Id<"users">,
                sessionId: activeSession._id,
            });

            if (result.success) {
                toast.success('Thank you for submitting!');
            } else {
                throw new Error(result.error || 'Failed to submit ideas');
            }
            
            // Clear saved form data from localStorage
            if (currentUser?.id) {
                const localStorageKey = `ideasFormData_${currentUser.id}`;
                try {
                    localStorage.removeItem(localStorageKey);
                } catch (error) {
                    console.error("Failed to remove form data from localStorage:", error);
                }
            }
        } catch (error) {
            debugError('Failed to submit ideas:', error);
            toast.error('Failed to submit ideas');
        } finally {
            setSubmitting(false);
            setShowSubmitConfirm(false);
        }
    };

    // Don't render if no ideas
    if (!ideas || ideas.length === 0) {
        return null;
    }

    return (
        <div className="bg-background border-2 border-primary responsive-user-card w-full mt-4">
            <h2 className="font-ultrabold text-2xl text-primary mb-6 text-center">Ideas Board</h2>
            <div className="flex flex-col gap-6 mb-8">
                {ideas?.map((idea) => (
                    <div key={idea._id} className="bg-background border-2 border-secondary responsive-user-inner-card hover:bg-muted/50 transition-colors">
                        <div className="flex justify-between items-start mb-4">
                            <h4 className="font-ultrabold text-xl text-secondary m-0 flex-1">{idea.name}</h4>
                            <div className="flex gap-2 ml-4">
                                <Button 
                                    variant="ghost"
                                    size="sm"
                                    onClick={() => handleEdit(idea._id)}
                                    className="shrink-0 p-0 hover:!bg-transparent hover:text-primary"
                                    title="Edit idea"
                                >
                                    <Edit className="h-4 w-4" />
                                </Button>
                                <Button 
                                    variant="ghost"
                                    size="sm"
                                    onClick={() => setShowDeleteConfirm(idea._id)}
                                    className="shrink-0 p-0 hover:!bg-transparent hover:text-primary"
                                    title="Delete idea"
                                >
                                    <Trash2 className="h-4 w-4" />
                                </Button>
                            </div>
                        </div>
                        {idea.description && (
                            <div className="mb-4">
                                <TiptapContentDisplay content={idea.description} />
                            </div>
                        )}
                        <div className="block font-mono text-sm text-primary mb-4">
                            {idea.presenters && idea.presenters.length > 0 && (
                                <div className="mb-2 flex gap-2 items-baseline">
                                    <span className="font-mono text-sm text-secondary font-bold">Presenters:</span>
                                    <span className="font-mono text-sm text-foreground">
                                        {idea.presenters.map((p: { name: string }) => p.name).join(', ')}
                                    </span>
                                </div>
                            )}
                            Submitted at: {new Date(idea.createdAt).toLocaleString()}
                        </div>
                    </div>
                ))}
            </div>

            {/* Submit All Ideas Button */}
            <div className="flex justify-center mt-8 pt-8 border-t-2 border-primary/20">
                <Button 
                    onClick={() => setShowSubmitConfirm(true)}
                    className="text-xl px-12 py-6"
                    disabled={submitting || !ideas || ideas.length === 0}
                >
                    Submit Ideas
                </Button>
            </div>

            {/* Submit Confirmation Modal */}
            <AlertDialog open={showSubmitConfirm} onOpenChange={setShowSubmitConfirm}>
                <AlertDialogContent>
                    <AlertDialogHeader>
                        <AlertDialogTitle>Submit All Ideas</AlertDialogTitle>
                        <AlertDialogDescription>
                            Are you sure you want to submit all ideas? After submission, you won&apos;t be able to add, edit, or delete ideas.
                        </AlertDialogDescription>
                    </AlertDialogHeader>
                    <AlertDialogFooter>
                        <AlertDialogCancel disabled={submitting}>
                            Cancel
                        </AlertDialogCancel>
                        <AlertDialogAction
                            onClick={handleSubmitAll}
                            disabled={submitting}
                        >
                            {submitting ? 'Submitting...' : 'Submit'}
                        </AlertDialogAction>
                    </AlertDialogFooter>
                </AlertDialogContent>
            </AlertDialog>

            {/* Delete Confirmation Modal */}
            <AlertDialog open={!!showDeleteConfirm} onOpenChange={(open) => !open && setShowDeleteConfirm(null)}>
                <AlertDialogContent>
                    <AlertDialogHeader>
                        <AlertDialogTitle>Delete Idea</AlertDialogTitle>
                        <AlertDialogDescription>
                            Are you sure you want to delete this idea? This action cannot be undone.
                        </AlertDialogDescription>
                    </AlertDialogHeader>
                    <AlertDialogFooter>
                        <AlertDialogCancel>
                            Cancel
                        </AlertDialogCancel>
                        <AlertDialogAction
                            onClick={() => showDeleteConfirm && handleDelete(showDeleteConfirm)}
                        >
                            Delete
                        </AlertDialogAction>
                    </AlertDialogFooter>
                </AlertDialogContent>
            </AlertDialog>
        </div>
    );
}
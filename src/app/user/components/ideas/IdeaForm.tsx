"use client";

import React, { useState, useEffect, useRef, useMemo, useCallback, useActionState } from 'react';
import { useQuery, useMutation } from "convex/react";
import { api } from "@/../convex/_generated/api";
import { useSession } from 'next-auth/react';
import { debugError } from '@/lib/utils';
import SparkRichTextField from '../sparks/SparkRichTextField';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import {
    AlertDialog,
    AlertDialogAction,
    AlertDialogCancel,
    AlertDialogContent,
    AlertDialogDescription,
    AlertDialogFooter,
    AlertDialogHeader,
    AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import { toast } from 'sonner';
import { Id } from "@/../convex/_generated/dataModel";
import { Plus } from 'lucide-react';
import { useDebounceLocalStorage } from '@/app/user/hooks/useDebounceLocalStorage';
import { useFormStatus } from 'react-dom';

function SubmitButton({ editingIdea }: { editingIdea: string | null }) {
    const { pending } = useFormStatus();
    
    return (
        <Button type="submit" disabled={pending}>
            {pending 
                ? (editingIdea ? 'Updating...' : 'Adding...') 
                : (editingIdea ? 'Update Idea' : 'Add Idea')
            }
        </Button>
    );
}

interface IdeaFormData {
    name: string;
    description?: string | undefined;
    presenters: string[];
}

interface IdeaFormProps {
    editingIdea: string | null;
    setEditingIdea: (id: string | null) => void;
    onIdeaSubmitted: () => void;
}

const IdeaFormComponent = React.memo(({ editingIdea, setEditingIdea, onIdeaSubmitted }: IdeaFormProps) => {
    // Add a ref to track initial render to prevent clearing localStorage on mount
    const initialRenderRef = useRef(true);
    const { data: sessionData } = useSession();
    
    const [formData, setFormData] = useState<IdeaFormData>({
        name: '',
        description: undefined,
        presenters: []
    });
    const [showPresenterModal, setShowPresenterModal] = useState(false);
    const [selectedPresenters, setSelectedPresenters] = useState<string[]>([]);
    const [isStorageSaved, setIsStorageSaved] = useState(false);
    
    // Phase 2 Optimization: Single combined query for all form data
    const ideaFormData = useQuery(
        api.ideas.getIdeaFormData,
        sessionData?.user?.username ? { username: sessionData.user.username } : "skip"
    );

    // Destructure all needed data from single query
    const { activeEvent, activeSession, currentUser, ideas, teamMembers } = ideaFormData || {};
    
    // Real-time mutations
    const createIdeaMutation = useMutation(api.ideas.createIdea);
    const updateIdeaMutation = useMutation(api.ideas.updateIdea);
    
    // React 19 Actions for form submission
    const [error, submitAction, isPending] = useActionState(
        async (_previousState: string | null, formData: FormData) => {
            try {
                if (!currentUser || !activeSession) {
                    throw new Error('User or session not found');
                }

                const name = formData.get("name") as string;
                const description = formData.get("description") as string;

                // Validate required fields
                if (!name || name.trim() === '') {
                    return 'Idea name is required';
                }

                // Validate presenter IDs before submission
                const validPresenters = selectedPresenters.filter(id => 
                    teamMembers?.some(member => member.id === id)
                );

                const result = editingIdea 
                    ? await updateIdeaMutation({
                        ideaId: editingIdea as Id<"ideas">,
                        name: name,
                        description: description || undefined,
                        presenters: validPresenters,
                    })
                    : await createIdeaMutation({
                        name: name,
                        description: description || undefined,
                        presenters: validPresenters as Id<"users">[],
                        userId: currentUser._id,
                        sessionId: activeSession._id,
                        teamId: currentUser.events?.find((e: { eventId: string }) => e.eventId === activeEvent?._id)?.teamId as Id<"teams">,
                    });

                if (result.success) {
                    clearFormAndLocalStorage();
                    toast.success(editingIdea ? 'Idea updated successfully!' : 'Idea added successfully!');
                    if (editingIdea) {
                        setEditingIdea(null);
                    }
                    onIdeaSubmitted();
                    return null; // Success - no error
                } else {
                    return result.error || 'Failed to submit idea';
                }
            } catch (error) {
                debugError('Failed to submit idea:', error);
                const errorMessage = error instanceof Error ? error.message : 'Failed to submit idea';
                return errorMessage;
            }
        },
        null
    );

    // Phase 2 Optimization: Memoized localStorage key
    const storageKey = useMemo(() => 
        currentUser?._id ? `ideasFormData_${currentUser._id}_${activeEvent?._id || 'no-event'}_${activeSession?._id || 'no-session'}` : null,
        [currentUser?._id, activeEvent?._id, activeSession?._id]
    );

    // Phase 2 Optimization: Debounced localStorage - only save if there's meaningful data
    const dataToSave = useMemo(() => {
        if (!storageKey || initialRenderRef.current) return null;
        
        const descriptionIsNotEmpty = formData.description && formData.description.trim() !== '' && 
                                      formData.description !== '{}' && formData.description !== '{"type":"doc","content":[]}';
        
        if (editingIdea || formData.name || descriptionIsNotEmpty || formData.presenters.length > 0) {
            return { formData, editingIdea };
        }
        return null;
    }, [formData, editingIdea, storageKey]);

    // Use debounced localStorage hook - only if autosave is enabled
    const autosaveEnabled = currentUser?.autosave !== false; // undefined or true means enabled
    useDebounceLocalStorage(storageKey || '', dataToSave, 1000, autosaveEnabled);

    // Trigger save animation when data is saved and autosave is enabled
    useEffect(() => {
        if (dataToSave && !initialRenderRef.current && autosaveEnabled) {
            setIsStorageSaved(true);
            const timer = setTimeout(() => setIsStorageSaved(false), 2500);
            return () => clearTimeout(timer);
        }
    }, [dataToSave, autosaveEnabled]);

    // REMOVE OLD LOCALSTORAGE LOGIC - Skip the first render to prevent clearing localStorage on mount
    useEffect(() => {
        if (initialRenderRef.current) {
            initialRenderRef.current = false;
            return;
        }
    }, []);

    // Phase 2 Optimization: Simplified localStorage loading

    useEffect(() => {
        if (storageKey && !initialRenderRef.current && autosaveEnabled) {
            try {
                const savedData = localStorage.getItem(storageKey);
                if (savedData) {
                    const parsedData = JSON.parse(savedData);
                    if (parsedData?.formData) {
                        setFormData(parsedData.formData);
                        setSelectedPresenters(parsedData.formData.presenters || []);
                        if (parsedData.editingIdea) setEditingIdea(parsedData.editingIdea);
                    }
                }
            } catch (error) {
                console.error("Failed to load form data from localStorage:", error);
                if (storageKey) {
                    try { localStorage.removeItem(storageKey); } catch (e) { console.error('Failed to remove corrupted data:', e); }
                }
            }
        }
    }, [storageKey, setEditingIdea, autosaveEnabled]);

    // Phase 2 Optimization: Clear localStorage when form is empty (only if autosave was enabled)
    useEffect(() => {
        if (!dataToSave && storageKey && !initialRenderRef.current && autosaveEnabled) {
            try { localStorage.removeItem(storageKey); } catch (error) { console.error('Failed to clear localStorage:', error); }
        }
    }, [dataToSave, storageKey, autosaveEnabled]);

    // Handle editing state changes
    useEffect(() => {
        if (editingIdea && ideas) {
            const idea = ideas.find(i => i._id === editingIdea);
            if (idea) {
                setFormData({
                    name: idea.name,
                    description: idea.description || undefined,
                    presenters: idea.presenters?.map((p: { id: string; name: string }) => p.id) || []
                });
                setSelectedPresenters(idea.presenters?.map((p: { id: string; name: string }) => p.id) || []);
            }
        }
    }, [editingIdea, ideas]);

    const handlePresenterSelect = useCallback((userId: string) => {
        const updatedPresenters = selectedPresenters.includes(userId) 
            ? selectedPresenters.filter(id => id !== userId) 
            : [...selectedPresenters, userId];
        
        setSelectedPresenters(updatedPresenters);
        // Immediately sync with formData to prevent state inconsistency
        setFormData(prev => ({ ...prev, presenters: updatedPresenters }));
    }, [selectedPresenters]);

    const clearFormAndLocalStorage = useCallback(() => {
        setFormData({
            name: '',
            description: undefined,
            presenters: []
        });
        setSelectedPresenters([]);
        
        // Clear localStorage
        if (storageKey) {
            try {
                localStorage.removeItem(storageKey);
            } catch (error) {
                console.error("Failed to remove form data from localStorage:", error);
            }
        }
    }, [storageKey]);


    const handleClearForm = () => {
        clearFormAndLocalStorage();
        setEditingIdea(null);
    };

    const handleCancelEdit = () => {
        setEditingIdea(null);
        clearFormAndLocalStorage();
    };

    return (
        <div className="bg-background border-2 border-secondary responsive-user-card w-full relative">
            {/* Storage save indicator */}
            <div className={`absolute top-15 right-10 px-2.5 py-1.5 bg-secondary text-background font-mono text-xs font-bold uppercase z-10 transition-opacity duration-300 ${isStorageSaved ? 'opacity-100 animate-[flash-save_2.5s_ease-in-out]' : 'opacity-0'}`}>SAVED</div>
            <h2 className="font-ultrabold text-2xl text-primary mb-6 text-center">Submit Your Ideas</h2>
            
            <form action={submitAction} className="flex flex-col gap-6">
                <div className="flex flex-col gap-2">
                    <Label htmlFor="ideaName" className="text-sm font-medium text-secondary flex gap-1">
                        Idea Name
                        <span className="text-accent">*</span>
                    </Label>
                    <Input
                        type="text"
                        id="ideaName"
                        name="name"
                        value={formData.name}
                        onChange={(e) => {
                            setFormData(prev => ({ ...prev, name: e.target.value }));
                        }}
                        className="w-full shadow-none focus-visible:ring-0 focus-visible:ring-offset-0"
                        disabled={isPending}
                        placeholder="Enter idea name"
                    />
                </div>
                
                {/* Hidden input for description to work with form action */}
                <input 
                    type="hidden" 
                    name="description" 
                    value={formData.description || ''} 
                />
                
                <SparkRichTextField
                    label="Description (Optional)"
                    placeholder="Enter description..."
                    value={formData.description || ''}
                    onChange={(jsonString: string) => setFormData(prev => ({ ...prev, description: jsonString }))}
                    disabled={isPending}
                    onSubmit={() => {
                        const form = document.querySelector('form');
                        if (form) {
                            const submitEvent = new Event('submit', { bubbles: true, cancelable: true });
                            form.dispatchEvent(submitEvent);
                        }
                    }}
                />
                <div className="flex flex-col gap-4">
                    {/* Error display */}
                    {error && (
                        <div className="bg-destructive/10 border border-destructive text-destructive px-4 py-2 font-mono text-sm">
                            {error}
                        </div>
                    )}
                    
                    {/* All buttons in one row */}
                    <div className="flex justify-between items-center flex-wrap gap-4">
                        <div className="flex gap-4 items-center flex-wrap">
                            <SubmitButton editingIdea={editingIdea} />
                            <Button
                                type="button"
                                variant="outline"
                                onClick={handleClearForm}
                                disabled={isPending}
                            >
                                Clear
                            </Button>
                            {editingIdea && (
                                <Button
                                    type="button"
                                    variant="outline"
                                    onClick={handleCancelEdit}
                                    disabled={isPending}
                                >
                                    Cancel Edit
                                </Button>
                            )}
                        </div>
                        <Button
                            type="button"
                            variant="outline"
                            className="flex items-center gap-2"
                            onClick={() => setShowPresenterModal(true)}
                            disabled={isPending}
                        >
                            <Plus className="h-4 w-4" /> Add Presenters
                        </Button>
                    </div>
                    
                    {/* Selected presenters display */}
                    {selectedPresenters.length > 0 && (
                        <div className="flex flex-wrap gap-2">
                            {teamMembers
                                ?.filter(member => selectedPresenters.includes(member.id))
                                .map(member => (
                                    <div key={member.id} className="bg-secondary/10 border border-secondary px-3 py-1 flex items-center gap-2 font-mono text-sm text-secondary">
                                        {member.name}
                                        <span className="cursor-pointer text-destructive text-xl leading-none hover:text-destructive/80" onClick={() => handlePresenterSelect(member.id)}>×</span>
                                    </div>
                                ))}
                        </div>
                    )}
                </div>
            </form>

            {/* Presenter Selection Modal */}
            <AlertDialog open={showPresenterModal} onOpenChange={setShowPresenterModal}>
                <AlertDialogContent className="max-h-[80vh] flex flex-col">
                    <AlertDialogHeader>
                        <AlertDialogTitle>Select Presenters</AlertDialogTitle>
                        <AlertDialogDescription>
                            Choose team members who will present this idea.
                        </AlertDialogDescription>
                    </AlertDialogHeader>
                    <div className="flex-1 overflow-y-auto my-4 flex flex-col gap-2">
                        {teamMembers?.map((member) => (
                            <div
                                key={member.id}
                                className={`p-4 border-2 border-secondary text-foreground cursor-pointer flex justify-between items-center transition-colors hover:bg-secondary/10 ${
                                    selectedPresenters.includes(member.id) ? 'bg-secondary/30' : 'bg-secondary/10'
                                }`}
                                onClick={() => handlePresenterSelect(member.id)}
                            >
                                <span className="font-mono">{member.name}</span>
                                {selectedPresenters.includes(member.id) && (
                                    <span className="text-secondary font-bold">✓</span>
                                )}
                            </div>
                        ))}
                    </div>
                    <AlertDialogFooter>
                        <AlertDialogCancel>
                            Cancel
                        </AlertDialogCancel>
                        <AlertDialogAction
                            onClick={() => {
                                setShowPresenterModal(false);
                                toast.success('Presenters updated successfully!');
                            }}
                        >
                            Confirm
                        </AlertDialogAction>
                    </AlertDialogFooter>
                </AlertDialogContent>
            </AlertDialog>
        </div>
    );
});

IdeaFormComponent.displayName = 'IdeaFormComponent';

export default IdeaFormComponent;
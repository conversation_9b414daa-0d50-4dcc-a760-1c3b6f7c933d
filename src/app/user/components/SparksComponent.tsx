"use client";

import { useState } from 'react';
import { useQuery } from "convex/react";
import { api } from "@/../convex/_generated/api";
import { useSession } from 'next-auth/react';
import { StateCard } from '@/components/ui/state-card';
import SparkForm from './sparks/SparkForm';
import SparkSubmissionsList from './sparks/SparkSubmissionsList';
import { Id } from "@/../convex/_generated/dataModel";

interface SparkField {
  type: 'text' | 'richtext' | 'dropdown' | 'radio' | 'checkbox';
  label: string;
  placeholder?: string;
  options?: string[];
  required?: boolean;
}

interface SparkConfig {
  _id: string;
  name: string;
  description?: string;
  fields: SparkField[];
}

export default function SparksComponent() {
  const { data: sessionData } = useSession();
  const [editingSubmission, setEditingSubmission] = useState<Id<"sparkSubmissions"> | null>(null);
  const [refreshKey, setRefreshKey] = useState(0);

  // Real-time Convex queries
  const activeEvent = useQuery(api.events.getActiveEvent);
  const activeSession = useQuery(api.sessions.getActiveSession);
  const currentUser = useQuery(
    api.users.getCurrentUser, 
    sessionData?.user?.username ? {
      username: sessionData.user.username,
      activeEventId: activeEvent?._id
    } : "skip"
  );

  const sparkConfig = useQuery(
    api.sparks.getSparkById,
    activeSession?.sparkId ? { sparkId: activeSession.sparkId } : "skip"
  ) as SparkConfig | undefined;

  const hasSubmitted = activeSession?.finishedTeams?.includes(currentUser?.teamId?.toString() || '') || false;
  const loading = activeEvent === undefined || activeSession === undefined || currentUser === undefined;

  const handleEditSubmission = (submission: Record<string, unknown>) => {
    setEditingSubmission(submission._id as Id<"sparkSubmissions">);
    
    // Scroll to the top of the page smoothly
    window.scrollTo({
      top: 0,
      behavior: 'smooth'
    });
  };

  const handleSubmissionComplete = () => {
    setRefreshKey(prev => prev + 1);
  };


  if (loading) {
    return (
      <div className="w-full max-w-[1200px] mx-auto grid place-items-center animate-in fade-in duration-300">
        <StateCard state="loading" title="Loading..." />
      </div>
    );
  }

  // Show message when no active session or not a Sparks session
  if (!activeSession || activeSession.type !== 'Sparks') {
    return (
      <div className="w-full max-w-[1200px] mx-auto grid place-items-center animate-in fade-in duration-300">
        <StateCard
          state="info"
          title="No Active Sparks Session"
          message="Waiting for a Sparks session to begin..."
        />
      </div>
    );
  }

  // Show submitted message if team has already submitted
  if (hasSubmitted) {
    return (
      <div className="w-full max-w-[1200px] mx-auto grid place-items-center animate-in fade-in duration-300">
        <StateCard 
          state="completed" 
          title="Your spark data has been submitted successfully!" 
          message="Thank you for your participation." 
        />
      </div>
    );
  }

  // Show error if spark configuration not found
  if (!sparkConfig) {
    return (
      <div className="w-full max-w-[1200px] mx-auto grid place-items-center animate-in fade-in duration-300">
        <StateCard
          state="error"
          title="Spark Configuration Missing"
          message="The spark configuration for this session could not be found. Please contact an administrator."
        />
      </div>
    );
  }

  return (
    <div key={refreshKey} className="w-full max-w-[1200px] mx-auto grid place-items-center animate-in fade-in duration-300">
      <SparkForm 
        sparkConfig={sparkConfig}
        editingSubmission={editingSubmission}
        setEditingSubmission={setEditingSubmission}
        onSubmissionComplete={handleSubmissionComplete}
      />
      <SparkSubmissionsList 
        sparkConfig={sparkConfig}
        onEditSubmission={handleEditSubmission}
      />
    </div>
  );
}
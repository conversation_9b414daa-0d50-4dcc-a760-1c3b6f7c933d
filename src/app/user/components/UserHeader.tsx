"use client";

import Image from 'next/image';
import { useRouter } from 'next/navigation';
import { useState } from 'react';
import { signOut } from 'next-auth/react';
import { useMutation } from "convex/react";
import { api } from "@/../convex/_generated/api";
import { Id } from "@/../convex/_generated/dataModel";
import { Button } from '@/components/ui/button';
import ProfileSettings from './ProfileSettings';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuLabel, DropdownMenuSeparator, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { Checkbox } from '@/components/ui/checkbox';
import { Badge } from '@/components/ui/badge';
import { Settings, LogOut, Save } from 'lucide-react';
import { ThemeSwitcher } from '@/components/ui/theme-switcher';
import { debugError } from '@/lib/utils';
import { useUserData } from './UserDataProvider';
import { AppUser } from '@/types/user';

export default function UserHeader() {
  const router = useRouter();
  const { user, setUser, activeEvent, activeSession } = useUserData();
  const [showProfileSettings, setShowProfileSettings] = useState(false);

  // Real-time mutations
  const updateAutosaveMutation = useMutation(api.userProfile.updateUserAutosaveSetting);

  const handleUserUpdate = (updatedUser: AppUser) => {
    setUser(updatedUser);
  };

  const handleLogout = async () => {
    try {
      await signOut({ redirect: false });
      router.push('/login');
    } catch (error) {
      debugError('Logout error:', error);
    }
  };

  const handleAutosaveToggle = async () => {
    if (!user?.id) return;
    
    try {
      const newAutosaveValue = user.autosave !== false ? false : true;
      await updateAutosaveMutation({
        userId: user.id as Id<"users">,
        autosave: newAutosaveValue
      });
      
      // Update local user state
      setUser((prev: AppUser | null) => prev ? { ...prev, autosave: newAutosaveValue } : null);
    } catch (error) {
      debugError('Error updating autosave setting:', error);
    }
  };

  return (
    <>
      <header className="relative z-10 h-18 bg-background/80 backdrop-blur-sm border-b border-secondary">
        <div className="flex justify-between items-center w-full px-4 h-full">
          <div className="flex items-center gap-3">
            <div className="w-12 h-12 flex items-center">
              <Image src="/svgs/logo-small.svg" alt="Logo" width={48} height={48} priority className="w-full h-full object-contain" />
            </div>
            <div className="flex flex-col justify-center">
              <div className="text-sm font-ultrabold uppercase leading-tight sm:text-lg">
                {activeEvent?.name || 'Loading...'}
              </div>
              {activeSession && (
                <div className="text-xs text-secondary leading-tight sm:text-base font-mono">
                  {activeSession?.name}
                </div>
              )}
            </div>
          </div>
          
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" className="h-10 w-10 p-1 focus-visible:ring-0 focus-visible:border-transparent border-1" style={{borderColor: 'var(--profile-box-background-color)', backgroundColor: 'transparent'}}>
                <div className="h-8 w-8 text-sm font-medium flex items-center justify-center text-accent">
                  {user?.name?.[0]?.toUpperCase() || 'U'}
                </div>
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuLabel>
                <div className="flex items-center gap-2">
                  <span className="font-ultabold">{user?.name}</span>
                  <Badge variant="secondary" className="text-xs font-bold px-2 py-0.5 text-[10px]">
                    {user?.role === 'teamLead' ? 'Lead' : 'Member'}
                  </Badge>
                </div>
                <div className="text-sm text-muted-foreground">{user?.teamName}</div>
              </DropdownMenuLabel>
              <DropdownMenuSeparator />
              <DropdownMenuItem onClick={() => setShowProfileSettings(true)}>
                <Settings className="mr-2 h-4 w-4" />
                Profile Settings
              </DropdownMenuItem>
              {user?.role === 'teamLead' && (
                <>
                  <DropdownMenuSeparator />
                  <div className="flex items-center gap-2 rounded-none px-2 py-1.5 text-sm text-foreground cursor-default select-none">
                    <Save className="size-4" />
                    <span className="flex-1">Autosave</span>
                    <Checkbox
                      checked={user.autosave !== false}
                      onCheckedChange={handleAutosaveToggle}
                      className="size-6"
                    />
                  </div>
                </>
              )}
              <DropdownMenuSeparator />
              <ThemeSwitcher />
              <DropdownMenuSeparator />
              <DropdownMenuItem onClick={handleLogout} className="text-destructive">
                <LogOut className="mr-2 h-4 w-4" />
                Logout
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </header>

      <ProfileSettings
        user={user}
        open={showProfileSettings}
        onClose={() => setShowProfileSettings(false)}
        onUpdate={handleUserUpdate}
      />
    </>
  );
}
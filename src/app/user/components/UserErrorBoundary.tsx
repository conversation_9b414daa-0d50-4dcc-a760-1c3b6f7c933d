"use client";

import React from 'react';
import { StateCard } from '@/components/ui/state-card';
import { signOut } from 'next-auth/react';

interface Props {
  children: React.ReactNode;
  fallback?: React.ReactNode;
}

interface State {
  hasError: boolean;
  error?: Error;
}

export class UserErrorBoundary extends React.Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error): State {
    // Update state so the next render will show the fallback UI
    return { hasError: true, error };
  }

  componentDidUpdate(_prevProps: Props, prevState: State) {
    // Auto-redirect when auth error state is first set
    if (!prevState.hasError && this.state.hasError && this.state.error) {
      if (this.isAuthError(this.state.error)) {
        // Immediate redirect for auth errors
        this.handleSignOut();
      }
    }
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    // Log error for debugging
    console.error('UserErrorBoundary caught an error:', error, errorInfo);
    
    // Auto-redirect on authentication errors
    if (this.isAuthError(error)) {
      this.handleSignOut();
    }
  }

  private isAuthError(error: Error): boolean {
    // Check if it's a ConvexError and extract the correct error message
    let errorMessage = error.message;
    
    // For ConvexError, check the data property
    if (error.name === 'ConvexError' && (error as { data?: unknown }).data) {
      const errorData = (error as unknown as { data: { message?: string; code?: string } }).data;
      errorMessage = errorData.message || errorMessage;
      
      // Also check the code property
      if (errorData.code) {
        return errorData.code === 'USER_NOT_FOUND' ||
               errorData.code === 'UNAUTHORIZED' ||
               errorData.code === 'ACCESS_DENIED' ||
               errorData.code === 'AUTHENTICATION_REQUIRED';
      }
    }
    
    return errorMessage.includes('User not found') || 
           errorMessage.includes('USER_NOT_FOUND') ||
           errorMessage.includes('UNAUTHORIZED') ||
           errorMessage.includes('ACCESS_DENIED') ||
           errorMessage.includes('AUTHENTICATION_REQUIRED') ||
           errorMessage.includes('Please check your login credentials');
  }

  private handleSignOut = async () => {
    try {
      await signOut({ callbackUrl: '/login' });
    } catch (error) {
      console.error('Error during sign out:', error);
      // Fallback: redirect directly to login
      window.location.href = '/login';
    }
  };

  private handleRetry = () => {
    // Reset error state to retry
    this.setState({ hasError: false, error: undefined });
  };

  render() {
    if (this.state.hasError) {
      const isAuthError = this.state.error && this.isAuthError(this.state.error);
      
      // Don't render anything for auth errors - just redirect
      if (isAuthError) {
        return null;
      }
      
      // Custom fallback UI
      if (this.props.fallback) {
        return this.props.fallback;
      }

      // Default error UI with retry functionality
      return (
        <div className="flex flex-col items-center justify-center min-h-[400px] p-8">
          <StateCard 
            state="error" 
            title="Something went wrong" 
            message="An unexpected error occurred while loading this component."
          />
          <button
            onClick={this.handleRetry}
            className="mt-4 px-4 py-2 bg-primary text-primary-foreground hover:bg-primary/90 transition-colors"
          >
            Try Again
          </button>
        </div>
      );
    }

    return this.props.children;
  }
}
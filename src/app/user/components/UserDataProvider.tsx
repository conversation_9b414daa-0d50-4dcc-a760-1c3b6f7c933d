"use client";

import { useState, useEffect, createContext, useContext } from 'react';
import { useSession } from 'next-auth/react';
import { useQuery } from "convex/react";
import { api } from "@/../convex/_generated/api";
import { AppUser } from '@/types/user';

// Let TypeScript infer the Convex query return types
type ActiveEventType = ReturnType<typeof useQuery<typeof api.events.getActiveEvent>>;
type ActiveSessionType = ReturnType<typeof useQuery<typeof api.sessions.getActiveSession>>;

interface UserDataContextType {
  user: AppUser | null;
  setUser: React.Dispatch<React.SetStateAction<AppUser | null>>;
  activeEvent: ActiveEventType;
  activeSession: ActiveSessionType;
  teamSelectionEnabled: boolean | undefined;
  isLoading: boolean;
}

const UserDataContext = createContext<UserDataContextType | null>(null);

export const useUserData = () => {
  const context = useContext(UserDataContext);
  if (!context) {
    throw new Error('useUserData must be used within UserDataProvider');
  }
  return context;
};

interface UserDataProviderProps {
  children: React.ReactNode;
}

export default function UserDataProvider({ children }: UserDataProviderProps) {
  const { data: sessionData } = useSession();
  const [user, setUser] = useState<AppUser | null>(null);

  // Real-time Convex queries
  const teamSelectionEnabled = useQuery(api.settings.getTeamSelectionSetting);
  const activeEvent = useQuery(api.events.getActiveEvent);
  const activeSession = useQuery(api.sessions.getActiveSession);
  const currentUser = useQuery(
    api.users.getCurrentUser, 
    sessionData?.user?.username ? {
      username: sessionData.user.username,
      activeEventId: activeEvent?._id
    } : "skip"
  );

  // Update local user state when Convex data changes
  useEffect(() => {
    if (currentUser) {
      setUser(currentUser);
    }
  }, [currentUser]);

  // Check if still loading Convex data
  const isLoading = (
    teamSelectionEnabled === undefined ||
    activeEvent === undefined ||
    activeSession === undefined ||
    currentUser === undefined
  );

  const contextValue: UserDataContextType = {
    user,
    setUser,
    activeEvent,
    activeSession,
    teamSelectionEnabled,
    isLoading
  };

  return (
    <UserDataContext.Provider value={contextValue}>
      {children}
    </UserDataContext.Provider>
  );
}
"use client";

import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from "@/components/ui/select";

interface TeamsNavProps {
    activeTeam: string | null;
    onTeamChange: (team: string | null) => void;
    teams: { id: string; name: string }[];
}

export default function TeamsNav({ activeTeam, onTeamChange, teams }: TeamsNavProps) {
    const allTeams = [
        { id: null, name: 'All' },
        ...teams
    ];

    const handleValueChange = (value: string) => {
        onTeamChange(value === 'null' ? null : value);
    };

    return (
        <div className="flex justify-center w-full mb-8">
            <Select 
                value={activeTeam === null ? 'null' : activeTeam} 
                onValueChange={handleValueChange}
            >
                <SelectTrigger className="w-full rounded-none">
                    <SelectValue placeholder="Select a team" />
                </SelectTrigger>
                <SelectContent className="rounded-none">
                    {allTeams.map((team) => (
                        <SelectItem 
                            key={team.id ?? 'all'} 
                            value={team.id === null ? 'null' : team.id}
                        >
                            {team.name}
                        </SelectItem>
                    ))}
                </SelectContent>
            </Select>
        </div>
    );
}

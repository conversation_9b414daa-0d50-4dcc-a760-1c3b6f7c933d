"use client";

import { useState } from 'react';
import { useQuery } from "convex/react";
import { api } from "@/../convex/_generated/api";
import { useSession } from 'next-auth/react';
import { StateCard } from '@/components/ui/state-card';
import IdeaForm from './ideas/IdeaForm';
import IdeaList from './ideas/IdeaList';

export default function Ideas() {
    const { data: sessionData } = useSession();
    const [editingIdea, setEditingIdea] = useState<string | null>(null);
    
    // Phase 2 Optimization: Combined query for better performance
    const ideasPageData = useQuery(
        api.ideas.getIdeasPageData,
        sessionData?.user?.username ? { username: sessionData.user.username } : "skip"
    );

    // Destructure all needed data from single query
    const { activeEvent, activeSession, currentUser } = ideasPageData || {};

    // Check if team has already submitted - get teamId from user events
    const userTeamId = currentUser?.events?.find((e: { eventId: string }) => e.eventId === activeEvent?._id)?.teamId;
    const hasSubmitted = activeSession?.finishedTeams?.includes(userTeamId?.toString() || '') || false;

    // Loading state from combined query
    const loading = ideasPageData === undefined;

    const handleIdeaSubmitted = () => {
        // The IdeaList will automatically update through Convex reactive queries
    };

    if (loading) {
        return (
            <div className="w-full max-w-[1200px] mx-auto grid place-items-center animate-in fade-in duration-300">
                <StateCard state="loading" title="Loading..." />
            </div>
        );
    }

    // Show message when no active session or not an Ideas session
    if (!activeSession || activeSession.type !== 'Ideas') {
        return (
            <div className="w-full max-w-[1200px] mx-auto grid place-items-center animate-in fade-in duration-300">
                <StateCard
                    state="info"
                    title="No Active Ideas Session"
                    message="Waiting for an Ideas session to begin..."
                />
            </div>
        );
    }

    // Show submitted message if team has already submitted
    if (hasSubmitted) {
        return (
            <div className="w-full max-w-[1200px] mx-auto grid place-items-center animate-in fade-in duration-300">
                <StateCard 
                    state="completed" 
                    title="Your ideas have been submitted successfully!" 
                    message="Thank you for your participation." 
                />
            </div>
        );
    }

    return (
        <div className="w-full max-w-[1200px] mx-auto grid place-items-center animate-in fade-in duration-300">
            <IdeaForm 
                editingIdea={editingIdea}
                setEditingIdea={setEditingIdea}
                onIdeaSubmitted={handleIdeaSubmitted}
            />
            <IdeaList 
                onEditIdea={setEditingIdea}
            />
        </div>
    );
}

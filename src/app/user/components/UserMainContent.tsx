"use client";

import { useMutation } from "convex/react";
import { api } from "@/../convex/_generated/api";
import { Id } from "@/../convex/_generated/dataModel";
import TeamSelector from '@/app/user/components/teams/TeamSelector';
import { UserErrorBoundary } from '@/app/user/components/UserErrorBoundary';
import { StateCard } from '@/components/ui/state-card';
import { debugError, debug } from '@/lib/utils';
import { useUserData } from './UserDataProvider';

interface UserMainContentProps {
  children: React.ReactNode;
}

export default function UserMainContent({ children }: UserMainContentProps) {
  const { user, activeEvent, teamSelectionEnabled } = useUserData();
  
  // Real-time mutations
  const joinTeamMutation = useMutation(api.users.joinTeam);

  const handleTeamSelected = async (teamId: string) => {
    try {
      if (!user || !activeEvent?._id) {
        throw new Error('User or active event not found');
      }
      
      await joinTeamMutation({
        userId: user.id as Id<"users">,
        teamId: teamId as Id<"teams">,
        eventId: activeEvent._id as Id<"events">
      });
      
      debug('Successfully joined team:', teamId);
    } catch (error) {
      debugError('Error joining team:', error);
    }
  };

  // If user has no team, show team selector or message based on setting
  const shouldShowTeamSelector = user && (!user.teamId || user.teamId === null);

  return (
    <main className="relative z-10 flex-1 w-full">
      <UserErrorBoundary>
        <div className="animate-in fade-in duration-300">
          {shouldShowTeamSelector ? (
            teamSelectionEnabled ? (
              <TeamSelector onTeamSelected={handleTeamSelected} />
            ) : (
              <div className="w-full max-w-[1200px] mx-auto grid place-items-center pt-16">
                <StateCard 
                  state="info" 
                  title="No Team assigned for you yet!" 
                  message="Please check back later"
                />
              </div>
            )
          ) : (
            children
          )}
        </div>
      </UserErrorBoundary>
    </main>
  );
}
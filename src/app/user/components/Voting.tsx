"use client";

import React, { useState, useMemo, useEffect, useCallback, useRef } from 'react';
import { useQuery, useMutation } from "convex/react";
import { api } from "@/../convex/_generated/api";
import { Slider } from '@/components/ui/slider';
import { Textarea } from '@/components/ui/textarea';
import { StateCard } from '@/components/ui/state-card';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';
import TiptapContentDisplay from '@/components/editors/TiptapContentDisplay';
import { ChevronDown } from 'lucide-react';
import TeamsNav from './TeamsNav';
import { debug, debugError } from '@/lib/utils';
import { useSession } from 'next-auth/react';
import { Id } from "@/../convex/_generated/dataModel";
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { useDebounce } from '@/app/user/hooks/useDebounce';

interface Presenter {
    id: string;
    name: string;
}

interface Idea {
    _id: Id<"ideas">;
    title: string;
    description: string;
    teamId: Id<"teams">;
    teamName?: string;
    presenters?: Presenter[];
    eventId: Id<"events">;
    sessionId: Id<"sessions">;
    userId: Id<"users">;
    submitted: boolean;
    createdAt: number;
}

interface Vote {
    _id: Id<"votes">;
    ideaId: Id<"ideas">;
    score: number;
    comment?: string;
    userId: Id<"users">;
    eventId: Id<"events">;
    createdAt: number;
    updatedAt?: number;
}

interface LocalVote {
    score: number;
    comment: string;
}

// Interface removed - using direct Convex response types now

const VotingComponent = React.memo(() => {
    const { data: session, status } = useSession();
    const [activeTeam, setActiveTeam] = useState<string | null>(null);
    const [localVotes, setLocalVotes] = useState<{ [key: string]: Partial<LocalVote> }>({});
    const [submittedBadges, setSubmittedBadges] = useState<{ [key: string]: boolean }>({});
    const badgeTimers = useRef<{ [key: string]: NodeJS.Timeout }>({});

    const isSessionReady = status === 'authenticated' && session?.user?.username;

    // Phase 2 Optimization: Single combined query for all voting data
    const votingData = useQuery(
        api.votes.getVotingPageData,
        isSessionReady ? { username: session.user.username } : "skip"
    );

    // Memoized mutation
    const submitVote = useMutation(api.votes.submitVote);

    // Destructure data from combined query - all pre-computed on server with memoization
    const ideas = useMemo(() => votingData?.votingIdeas?.ideas || [], [votingData?.votingIdeas?.ideas]);
    const votingEnabled = votingData?.votingStatus?.votingStarted || false;
    const noTeamSelected = votingData?.votingIdeas?.noTeamSelected || false;
    const votingMode = votingData?.votingTeamStatus?.mode || "none";
    const userVotes = useMemo(() => votingData?.userVotes || [], [votingData?.userVotes]);
    const loading = status === 'loading' || !isSessionReady || votingData === undefined;

    // Optimized vote mapping with memoization
    const votes = useMemo(() => {
        if (!userVotes.length) return {};
        return userVotes.reduce((acc: { [key: string]: Vote }, vote) => {
            acc[vote.ideaId] = vote;
            return acc;
        }, {});
    }, [userVotes]);

    // Initialize local state from server votes, but only for ideas that aren't already being edited locally.
    useEffect(() => {
        if (userVotes.length > 0) {
            const initialLocalVotes: { [key: string]: Partial<LocalVote> } = {};
            userVotes.forEach(vote => {
                // Only initialize if there's no local data for this idea yet.
                if (localVotes[vote.ideaId] === undefined) {
                    initialLocalVotes[vote.ideaId] = {
                        score: vote.score,
                        comment: vote.comment, // Keep undefined as undefined
                    };
                }
            });
            if (Object.keys(initialLocalVotes).length > 0) {
                setLocalVotes(prev => ({ ...prev, ...initialLocalVotes }));
            }
        }
    }, [userVotes, localVotes]);

    const debouncedVotes = useDebounce(localVotes, 500);

    // Optimized team grouping with improved memoization
    const teamGroups = useMemo(() => {
        if (!ideas.length) return {};
        return ideas.reduce((groups: { [teamId: string]: Idea[] }, idea) => {
            const teamId = idea.teamId.toString();
            if (!groups[teamId]) groups[teamId] = [];
            groups[teamId].push(idea);
            return groups;
        }, {});
    }, [ideas]);

    const teams = useMemo(() => {
        return Object.entries(teamGroups).map(([teamId, teamIdeas]) => ({
            id: teamId,
            name: teamIdeas[0].teamName || teamId
        }));
    }, [teamGroups]);

    const handleVoteChange = useCallback((ideaId: string, value: Partial<LocalVote>) => {
        setLocalVotes(prev => ({
            ...prev,
            [ideaId]: { ...(prev[ideaId] || {}), ...value }
        }));
    }, []);

    const triggerSubmitBadge = useCallback((ideaId: string) => {
        if (badgeTimers.current[ideaId]) {
            clearTimeout(badgeTimers.current[ideaId]);
        }
        setSubmittedBadges(prev => ({ ...prev, [ideaId]: true }));
        badgeTimers.current[ideaId] = setTimeout(() => {
            setSubmittedBadges(prev => {
                const newState = { ...prev };
                delete newState[ideaId];
                return newState;
            });
            delete badgeTimers.current[ideaId];
        }, 1000);
    }, []);

    // Removed unused handleVoteSubmit - voting is handled through debounced effects

    useEffect(() => {
        // Early return if basic conditions not met
        if (!session?.user?.username || !votingEnabled) return;
        
        // Early return if no votes to process
        if (Object.keys(debouncedVotes).length === 0) return;

        const submitChangedVotes = async () => {
            const submissions = Object.entries(debouncedVotes)
                .filter(([ideaId, localVote]) => {
                    const serverVote = votes[ideaId];

                    // Normalize local and server values for comparison
                    const roundedScore = localVote.score !== undefined ? Math.round(localVote.score) : undefined;
                    const serverScore = serverVote?.score;

                    const localComment = localVote.comment || '';
                    const serverComment = serverVote?.comment || '';

                    // Check if score has been interacted with and has changed
                    const scoreChanged = roundedScore !== undefined && roundedScore !== serverScore;
                    // Check if comment has been interacted with and has changed
                    const commentChanged = localVote.comment !== undefined && localComment !== serverComment;

                    return scoreChanged || commentChanged;
                })
                .map(async ([ideaId, localVote]) => {
                    const serverVote = votes[ideaId];
                    // Use local score if it exists, otherwise fall back to server score
                    const roundedScore = localVote.score !== undefined ? Math.round(localVote.score) : serverVote?.score ?? 0;

                    try {
                        const result = await submitVote({
                            username: session.user.username,
                            ideaId: ideaId as Id<"ideas">,
                            score: roundedScore,
                            // Only send the comment if it has been defined in the local state
                            comment: localVote.comment,
                        });
                        
                        if (result.success) {
                            debug(`Vote updated for idea ${ideaId}:`, { score: roundedScore, comment: localVote.comment });
                            triggerSubmitBadge(ideaId);
                        } else {
                            throw new Error(result.error || 'Failed to submit vote');
                        }
                    } catch (error) {
                        // Silently handle connection interruption errors during page unload
                        if (error instanceof Error && error.message.includes('interrupted')) {
                            debug('Vote submission interrupted during page transition');
                            return;
                        }
                        debugError('Error submitting vote:', error);
                    }
                });

            if (submissions.length > 0) {
                try {
                    await Promise.all(submissions);
                } catch (error) {
                    // Handle Promise.all rejection gracefully
                    if (error instanceof Error && !error.message.includes('interrupted')) {
                        debugError('Error in batch vote submission:', error);
                    }
                }
            }
        };

        submitChangedVotes();
    }, [debouncedVotes, session?.user?.username, submitVote, votes, triggerSubmitBadge, votingEnabled]);

    // Cleanup effect for timers and pending operations
    useEffect(() => {
        const timers = badgeTimers.current;
        return () => {
            Object.values(timers).forEach(clearTimeout);
        };
    }, []);

    // Cleanup effect to handle component unmounting during vote submission
    useEffect(() => {
        return () => {
            // Clear any pending debounced votes on unmount
            setLocalVotes({});
        };
    }, []);

    if (loading) {
        return (
            <div className="w-full max-w-[1200px] mx-auto grid place-items-center animate-in fade-in duration-300">
                <StateCard state="loading" title="Loading..." />
            </div>
        );
    }

    if (!votingEnabled || noTeamSelected) {
        return (
            <div className="w-full max-w-[1200px] mx-auto grid place-items-center animate-in fade-in duration-300">
                {!votingEnabled ? (
                    <StateCard
                        state="info"
                        title="Ideas Flowing!"
                        message="Voting has not started yet. Keep checking back!"
                    />
                ) : (
                    <StateCard
                        state="info"
                        title="Get Ready!"
                        message="No teams have finished presenting yet. Keep checking back!"
                    />
                )}
            </div>
        );
    }

    return (
        <div className="w-full max-w-[1200px] mx-auto grid place-items-center animate-in fade-in duration-300">
            {ideas.length > 0 ? (
                <>
                    {votingMode === 'all' && teams.length > 1 && (
                        <TeamsNav
                            activeTeam={activeTeam}
                            onTeamChange={setActiveTeam}
                            teams={teams}
                        />
                    )}
                    <div className="w-full flex flex-col">
                        {Object.entries(teamGroups)
                            .filter(([teamId]) => activeTeam === null || teamId === activeTeam)
                            .map(([teamId, teamIdeas], index) => (
                                <div
                                    key={teamId}
                                    className="mb-4 w-full last:mb-0"
                                    style={{ animationDelay: `${index * 100}ms` }}
                                >
                                    <h1 className="font-ultrabold text-3xl text-primary mb-2">{teamIdeas[0].teamName}&apos;s Ideas</h1>
                                    {teamIdeas.map((idea, ideaIndex) => {
                                        const localVote = localVotes[idea._id];
                                        const serverVote = votes[idea._id];
                                        const displayScore = localVote?.score ?? serverVote?.score ?? 0;
                                        const displayComment = localVote?.comment ?? serverVote?.comment ?? '';

                                        return (
                                            <div
                                                key={idea._id}
                                                className="bg-background border-2 border-accent p-0 hover:bg-muted/30 backdrop-blur-sm transition-colors mb-8"
                                                style={{ animationDelay: `${(index * 100) + (ideaIndex * 50)}ms` }}
                                            >
                                                <div className="responsive-user-voting-card relative">
                                                    <Badge
                                                        variant="outline"
                                                        className={`absolute top-4 right-4 transition-opacity duration-400 ${submittedBadges[idea._id] ? 'opacity-100 animate-[flash-save_1000ms_ease-in-out]' : 'opacity-0'}`}
                                                    >
                                                        submitted
                                                    </Badge>
                                                    <h2 className="font-ultrabold text-xl text-accent">{idea.title}</h2>

                                                    <Collapsible defaultOpen={false} className="mt-2">
                                                        <CollapsibleTrigger className="flex items-center gap-2 text-base text-secondary hover:text-accent transition-colors group" style={{ fontFamily: 'neue-machina-Medium, sans-serif' }}>
                                                            <ChevronDown className="h-4 w-4 transition-transform duration-200 group-data-[state=open]:rotate-180" />
                                                            <span className="group-data-[state=open]:hidden" style={{ fontFamily: 'inherit' }}>See more</span>
                                                            <span className="hidden group-data-[state=open]:block" style={{ fontFamily: 'inherit' }}>See less</span>
                                                        </CollapsibleTrigger>
                                                        <CollapsibleContent className="mt-3">
                                                            <TiptapContentDisplay
                                                                content={idea.description}
                                                                className="text-sm"
                                                            />
                                                        </CollapsibleContent>
                                                    </Collapsible>

                                                    {idea.presenters && idea.presenters.length > 0 && (
                                                        <div className="flex flex-wrap gap-2 items-center mt-4">
                                                            <span className="font-mono text-secondary">Presenters:</span>
                                                            <span className="text-accent">
                                                                {idea.presenters.map(p => p.name).join(', ')}
                                                            </span>
                                                        </div>
                                                    )}
                                                </div>

                                                <div className="flex flex-col gap-6 border-t-2 border-secondary responsive-user-voting-card">
                                                    <div className="flex items-center gap-6">
                                                        <span className="font-mono text-accent min-w-[60px]">Score:</span>
                                                        <span className="font-ultrabold text-2xl text-primary">{Math.round(displayScore)}</span>
                                                    </div>
                                                    <div className="w-full">
                                                        <Slider
                                                            min={0}
                                                            max={10}
                                                            step={1}
                                                            value={[displayScore]}
                                                            onValueChange={(value) => handleVoteChange(idea._id, { score: value[0] })}
                                                            className="w-full mb-4"
                                                        />
                                                    </div>

                                                    <div className="w-full space-y-2">
                                                        <Label>
                                                            Comment (optional)
                                                        </Label>
                                                        <Textarea
                                                            value={displayComment}
                                                            onChange={(e) => handleVoteChange(idea._id, { comment: e.target.value })}
                                                            className="rounded-none border-2 text-foreground bg-background"
                                                            placeholder="Add your feedback..."
                                                            rows={3}
                                                        />
                                                    </div>
                                                </div>
                                            </div>
                                        );
                                    })}
                                </div>
                            ))}
                    </div>
                </>
            ) : (
                <StateCard
                    state="info"
                    title="Ideas Flowing!"
                    message="Keep checking back"
                />
            )}
        </div>
    );
});

VotingComponent.displayName = 'VotingComponent';

export default VotingComponent;
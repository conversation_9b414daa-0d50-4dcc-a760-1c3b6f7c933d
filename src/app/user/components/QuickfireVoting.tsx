"use client";

import React, { useState, useEffect, useCallback, useRef, useMemo } from 'react';
import { useSession } from 'next-auth/react';
import { useQuery, useMutation } from "convex/react";
import { api } from "@/../convex/_generated/api";
import { Id } from "@/../convex/_generated/dataModel";
import { Slider } from '@/components/ui/slider';
import { StateCard } from '@/components/ui/state-card';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';
import { ChevronDown } from 'lucide-react';
import { useDebounce } from '@/app/user/hooks/useDebounce';
import { Badge } from '@/components/ui/badge';
import { debug, debugError } from '@/lib/utils';

interface QuickfireItem {
    _id: Id<"quickfires">;
    idea: string;
    comments?: string;
    question?: string;
    sessionId: Id<"sessions">;
    votingActive: boolean;
    createdAt: number;
    updatedAt?: number;
}

const QuickfireVotingComponent = React.memo(() => {
    const { data: session, status } = useSession();
    const [localScores, setLocalScores] = useState<{ [key: string]: number }>({});
    const [submittedBadges, setSubmittedBadges] = useState<{ [key: string]: boolean }>({});
    const badgeTimers = useRef<{ [key: string]: NodeJS.Timeout }>({});

    const isSessionReady = status === 'authenticated' && session?.user?.username;

    // Phase 2 Optimization: Single combined query for quickfire data
    const quickfireData = useQuery(
        api.quickfire.getQuickfirePageData,
        isSessionReady ? { username: session.user.username } : "skip"
    );

    // Memoized mutation
    const submitVote = useMutation(api.quickfire.submitQuickfireVote);

    // Destructure data from combined query with memoization
    const quickfireItems = useMemo(() => quickfireData?.quickfireItems || [], [quickfireData?.quickfireItems]);
    const votes = useMemo(() => quickfireData?.userVotes || {}, [quickfireData?.userVotes]);
    
    // Check if any quickfire items are currently active for voting
    const hasActiveVoting = useMemo(() => 
        quickfireItems.some(item => item.votingActive), 
        [quickfireItems]
    );

    useEffect(() => {
        if (Object.keys(votes).length > 0) {
            const initialScores: { [key: string]: number } = {};
            Object.entries(votes).forEach(([quickfireId, voteData]) => {
                if (localScores[quickfireId] === undefined) {
                    initialScores[quickfireId] = voteData.score;
                }
            });
            if (Object.keys(initialScores).length > 0) {
                setLocalScores(prev => ({ ...prev, ...initialScores }));
            }
        }
    }, [votes, localScores]);

    const debouncedScores = useDebounce(localScores, 500);

    const handleScoreChange = useCallback((quickfireId: string, score: number) => {
        setLocalScores(prev => ({ ...prev, [quickfireId]: score }));
    }, []);

    const triggerSubmitBadge = useCallback((quickfireId: string) => {
        if (badgeTimers.current[quickfireId]) {
            clearTimeout(badgeTimers.current[quickfireId]);
        }
        setSubmittedBadges(prev => ({ ...prev, [quickfireId]: true }));
        badgeTimers.current[quickfireId] = setTimeout(() => {
            setSubmittedBadges(prev => {
                const newState = { ...prev };
                delete newState[quickfireId];
                return newState;
            });
            delete badgeTimers.current[quickfireId];
        }, 1000);
    }, []);

    // Removed unused handleVoteSubmit - voting is handled through debounced effects

    useEffect(() => {
        // Early return if basic conditions not met
        if (!isSessionReady || !hasActiveVoting) return;
        
        // Early return if no scores to process
        if (Object.keys(debouncedScores).length === 0) return;

        const submitChangedScores = async () => {
            const submissions = Object.entries(debouncedScores)
                .filter(([quickfireId, localScore]) => {
                    const serverScore = votes[quickfireId]?.score;
                    const roundedScore = Math.round(localScore);
                    return roundedScore !== serverScore;
                })
                .map(async ([quickfireId, localScore]) => {
                    const roundedScore = Math.round(localScore);
                    try {
                        const result = await submitVote({
                            username: session.user.username,
                            quickfireId: quickfireId as Id<"quickfires">,
                            score: roundedScore,
                        });
                        
                        if (result.success) {
                            debug(`Vote updated for quickfire item ${quickfireId}:`, { score: roundedScore });
                            triggerSubmitBadge(quickfireId);
                        } else {
                            throw new Error(result.error || 'Failed to submit quickfire vote');
                        }
                    } catch (error) {
                        // Silently handle connection interruption errors during page unload
                        if (error instanceof Error && error.message.includes('interrupted')) {
                            debug('Quickfire vote submission interrupted during page transition');
                            return;
                        }
                        debugError('Error submitting quickfire vote:', error);
                    }
                });

            if (submissions.length > 0) {
                try {
                    await Promise.all(submissions);
                } catch (error) {
                    // Handle Promise.all rejection gracefully
                    if (error instanceof Error && !error.message.includes('interrupted')) {
                        debugError('Error in batch quickfire vote submission:', error);
                    }
                }
            }
        };

        submitChangedScores();
    }, [debouncedScores, isSessionReady, session?.user?.username, submitVote, votes, triggerSubmitBadge, hasActiveVoting]);

    // Cleanup effect for timers and pending operations
    useEffect(() => {
        const timers = badgeTimers.current;
        return () => {
            Object.values(timers).forEach(clearTimeout);
        };
    }, []);

    // Cleanup effect to handle component unmounting during vote submission
    useEffect(() => {
        return () => {
            // Clear any pending debounced scores on unmount
            setLocalScores({});
        };
    }, []);

    if (status === 'loading' || quickfireData === undefined) {
        return (
            <div className="w-full max-w-[1200px] mx-auto grid place-items-center animate-in fade-in duration-300">
                <StateCard state="loading" title="Loading..." />
            </div>
        );
    }

    if (status !== 'authenticated' || !session?.user?.username) {
        return (
            <div className="w-full max-w-[1200px] mx-auto grid place-items-center animate-in fade-in duration-300">
                <StateCard
                    state="warning"
                    title="Authentication Required"
                    message="Please log in to view quickfire voting."
                />
            </div>
        );
    }

    return (
        <div className="w-full max-w-[1200px] mx-auto grid place-items-center animate-in fade-in duration-300">
            {quickfireItems.length > 0 ? (
                <div className="w-full flex flex-col gap-4">
                    <h1 className="font-ultrabold text-3xl text-primary">Quickfire Idea</h1>
                    {quickfireItems.map((item: QuickfireItem, index: number) => {
                        const displayScore = localScores[item._id] ?? votes[item._id]?.score ?? 0;

                        return (
                            <div 
                                key={item._id}
                                className="bg-background border-2 border-accent p-0 hover:bg-muted/30 backdrop-blur-sm transition-colors relative"
                                style={{ animationDelay: `${index * 50}ms` }}
                            >
                                <Badge 
                                    variant="outline" 
                                    className={`absolute top-4 right-4 transition-opacity duration-400 ${submittedBadges[item._id] ? 'opacity-100 animate-[flash-save_1000ms_ease-in-out]' : 'opacity-0'}`}
                                >
                                    submitted
                                </Badge>
                                <div className="responsive-user-voting-card">
                                    <h2 className="font-ultrabold text-xl text-accent">{item.idea}</h2>
                                    
                                    {(item.question || item.comments) && (
                                        <Collapsible defaultOpen={false} className="mt-2">
                                            <CollapsibleTrigger className="flex items-center gap-2 text-base text-secondary hover:text-accent transition-colors group" style={{ fontFamily: 'neue-machina-Medium, sans-serif' }}>
                                                <ChevronDown className="h-4 w-4 transition-transform duration-200 group-data-[state=open]:rotate-180" />
                                                <span className="group-data-[state=open]:hidden" style={{ fontFamily: 'inherit' }}>See more</span>
                                                <span className="hidden group-data-[state=open]:block" style={{ fontFamily: 'inherit' }}>See less</span>
                                            </CollapsibleTrigger>
                                            <CollapsibleContent className="mt-3">
                                                {item.question && (
                                                    <div className="mb-4">
                                                        <div className="font-ultrabold text-accent mb-2">Question:</div>
                                                        <div className="text-sm text-foreground whitespace-pre-wrap">{item.question}</div>
                                                    </div>
                                                )}
                                                {item.comments && (
                                                    <div>
                                                        <div className="font-ultrabold text-accent mb-2">Comments:</div>
                                                        <div className="text-sm text-foreground whitespace-pre-wrap">{item.comments}</div>
                                                    </div>
                                                )}
                                            </CollapsibleContent>
                                        </Collapsible>
                                    )}
                                </div>
                                
                                <div className="flex flex-col gap-6 border-t-2 border-secondary responsive-user-voting-card">
                                    <div className="flex items-center gap-6">
                                        <span className="font-ultrabold text-accent min-w-[60px]">Score:</span>
                                        <span className="font-ultrabold text-2xl text-primary">{Math.round(displayScore)}</span>
                                    </div>
                                    
                                    <div className="w-full mb-8">
                                        <Slider
                                            min={0}
                                            max={10}
                                            step={1}
                                            value={[displayScore]}
                                            onValueChange={(value) => handleScoreChange(item._id, value[0])}
                                            className="w-full"
                                        />
                                    </div>
                                </div>
                            </div>
                        );
                    })}
                </div>
            ) : (
                <StateCard 
                    state="info" 
                    title="Get Ready!" 
                    message="Quickfire voting will begin shortly. Keep checking back!" 
                />
            )}
        </div>
    );
});

QuickfireVotingComponent.displayName = 'QuickfireVotingComponent';

export default QuickfireVotingComponent;

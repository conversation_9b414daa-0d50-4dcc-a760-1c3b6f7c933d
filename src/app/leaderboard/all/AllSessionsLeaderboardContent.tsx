"use client";

import { useQuery } from "convex/react";
import { api } from "@/../convex/_generated/api";
import LeaderboardShell from '@/components/leaderboard/LeaderboardShell';
import LeaderboardTable from '@/components/leaderboard/LeaderboardTable';
import { StateCard } from '@/components/ui/state-card';

export default function AllSessionsLeaderboardContent() {
    // Get all sessions leaderboard data using Convex (no sessionId = all sessions)
    const leaderboard = useQuery(api.analytics.getLeaderboard, {});
    
    const loading = leaderboard === undefined;

    // Filter for ideas only (exclude quickfire)
    const ideasData = leaderboard ? leaderboard.filter((item) => item.type === 'idea') : [];
    
    // Get the first event name for the header
    const firstEventName = ideasData.length > 0 ? ideasData[0].eventName : 'Event Name';

    if (loading) {
        return (
            <LeaderboardShell eventName="Loading..." sessionName="Ideas Leaderboard">
                <div className="flex justify-center">
                    <StateCard state="loading" title="Loading leaderboard data..." />
                </div>
            </LeaderboardShell>
        );
    }

    return (
        <LeaderboardShell eventName={firstEventName} sessionName="Ideas Leaderboard">
            <LeaderboardTable data={ideasData} />
        </LeaderboardShell>
    );
}
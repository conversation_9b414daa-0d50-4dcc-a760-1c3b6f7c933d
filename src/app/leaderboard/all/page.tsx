"use client";

import { Suspense, useState, useEffect } from 'react';
import { useTheme } from 'next-themes';
import Image from 'next/image';
import AllSessionsLeaderboardContent from './AllSessionsLeaderboardContent';

export default function AllSessionsLeaderboard() {
  return (
    <Suspense fallback={<AllSessionsLeaderboardLoading />}>
      <AllSessionsLeaderboardContent />
    </Suspense>
  );
}

function AllSessionsLeaderboardLoading() {
  const { resolvedTheme } = useTheme();
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  return (
    <div className="relative w-full min-h-screen bg-background">
      {/* Background Image */}
      <Image 
        src={mounted && resolvedTheme === 'light' ? "/images/background-light2.jpg" : "/images/background-01.jpg"}
        alt="Background"
        fill
        className="absolute inset-0 object-cover z-0"
        priority
      />
      
      <div className="relative z-[1] flex flex-col items-center w-full min-h-screen pt-20 pb-8 px-4">
        <div className="w-full text-center mb-8">
          <h1 className="text-4xl lg:text-5xl font-bold text-foreground mb-2">
            Loading...
          </h1>
          <h2 className="text-2xl lg:text-3xl font-medium text-secondary">
            Ideas Leaderboard
          </h2>
        </div>
        <div className="w-full max-w-7xl">
          <div className="bg-card/90 border-2 border-accent p-6 text-center">
            <div className="text-foreground text-xl">Loading leaderboard data...</div>
          </div>
        </div>
      </div>
    </div>
  );
}

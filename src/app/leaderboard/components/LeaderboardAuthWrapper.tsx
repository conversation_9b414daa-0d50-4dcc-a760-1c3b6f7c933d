"use client";

import { useSession } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import { useEffect } from 'react';
import { ErrorBoundary } from 'react-error-boundary';

function ErrorFallback({ error }: { error: Error }) {
  return (
    <div className="min-h-screen bg-background flex items-center justify-center">
      <div className="bg-background/90 border-2 border-destructive p-8 text-center max-w-md">
        <h2 className="text-destructive text-xl font-bold mb-4">Something went wrong</h2>
        <p className="text-muted-foreground mb-4">{error.message}</p>
        <button 
          onClick={() => window.location.reload()}
          className="px-4 py-2 bg-primary text-primary-foreground hover:bg-primary/90 transition-colors font-medium"
        >
          Reload Page
        </button>
      </div>
    </div>
  );
}

function AuthWrapper({ children }: { children: React.ReactNode }) {
  const { data: session, status } = useSession();
  const router = useRouter();

  useEffect(() => {
    if (status === 'loading') return; // Still loading
    
    if (!session?.user) {
      router.push('/login');
      return;
    }
  }, [session, status, router]);

  if (status === 'loading') {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <div className="text-foreground text-xl">Loading...</div>
      </div>
    );
  }

  if (!session?.user) {
    return null;
  }

  return <>{children}</>;
}

interface LeaderboardAuthWrapperProps {
  children: React.ReactNode;
}

export default function LeaderboardAuthWrapper({ children }: LeaderboardAuthWrapperProps) {
  return (
    <ErrorBoundary FallbackComponent={ErrorFallback}>
      <AuthWrapper>
        {children}
      </AuthWrapper>
    </ErrorBoundary>
  );
}
"use client";

import { useQuery } from "convex/react";
import { api } from "@/../convex/_generated/api";
import LeaderboardShell from '@/components/leaderboard/LeaderboardShell';
import TeamsLeaderboardTable from '@/components/leaderboard/TeamsLeaderboardTable';
import { StateCard } from '@/components/ui/state-card';

export default function TeamsLeaderboardContent() {
    // Get all sessions teams leaderboard data using Convex (no sessionId = all sessions)
    const teamsLeaderboard = useQuery(api.analytics.getTeamsLeaderboard, {});
    
    const loading = teamsLeaderboard === undefined;

    // Get the first event name for the header
    const firstEventName = teamsLeaderboard && teamsLeaderboard.length > 0 ? teamsLeaderboard[0].eventName : 'Event Name';

    if (loading) {
        return (
            <LeaderboardShell eventName="Loading..." sessionName="Teams Leaderboard">
                <div className="flex justify-center">
                    <StateCard state="loading" title="Loading teams leaderboard data..." />
                </div>
            </LeaderboardShell>
        );
    }

    return (
        <LeaderboardShell eventName={firstEventName} sessionName="Teams Leaderboard">
            <TeamsLeaderboardTable data={teamsLeaderboard || []} />
        </LeaderboardShell>
    );
}
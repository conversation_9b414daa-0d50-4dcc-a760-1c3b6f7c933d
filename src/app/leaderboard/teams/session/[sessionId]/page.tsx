import { Metadata } from 'next';
import TeamsLeaderboardContent from './TeamsLeaderboardContent';

interface Props {
  params: Promise<{ sessionId: string }>;
}

export async function generateMetadata(): Promise<Metadata> {
  return {
    title: 'Teams Leaderboard',
    description: 'Teams leaderboard for specific session showing aggregated team scores',
  };
}

export default async function TeamsLeaderboardSessionPage({ params }: Props) {
  const { sessionId } = await params;
  return <TeamsLeaderboardContent sessionId={sessionId} />;
}
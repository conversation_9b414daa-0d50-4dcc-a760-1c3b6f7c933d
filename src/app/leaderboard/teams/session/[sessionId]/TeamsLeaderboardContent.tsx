"use client";

import { useQuery } from "convex/react";
import { api } from "@/../convex/_generated/api";
import { Id } from "@/../convex/_generated/dataModel";
import LeaderboardShell from '@/components/leaderboard/LeaderboardShell';
import TeamsLeaderboardTable from '@/components/leaderboard/TeamsLeaderboardTable';
import { StateCard } from '@/components/ui/state-card';

interface Props {
  sessionId: string;
}

export default function TeamsLeaderboardContent({ sessionId }: Props) {
  // Get teams leaderboard data for specific session using Convex
  const teamsLeaderboard = useQuery(
    api.analytics.getTeamsLeaderboard, 
    { sessionId: sessionId as Id<"sessions"> }
  );
  
  // Get session details from sessions data
  const sessionsData = useQuery(api.analytics.getSessionsForLeaderboard);
  const session = sessionsData?.sessions.find(s => s.id === sessionId);
  
  const loading = teamsLeaderboard === undefined || sessionsData === undefined;

  if (loading) {
    return (
      <LeaderboardShell eventName="Loading..." sessionName="Teams Leaderboard">
        <div className="flex justify-center">
          <StateCard state="loading" title="Loading teams leaderboard data..." />
        </div>
      </LeaderboardShell>
    );
  }

  const eventName = sessionsData?.event?.name || 'Event';
  const sessionName = session?.name || 'Session';

  return (
    <LeaderboardShell eventName={eventName} sessionName={`${sessionName} - Teams`}>
      <TeamsLeaderboardTable data={teamsLeaderboard || []} />
    </LeaderboardShell>
  );
}
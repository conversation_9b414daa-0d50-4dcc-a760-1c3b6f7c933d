"use client";

import { useQuery } from "convex/react";
import { api } from "@/../convex/_generated/api";
import { Id } from "@/../convex/_generated/dataModel";
import LeaderboardShell from '@/components/leaderboard/LeaderboardShell';
import LeaderboardTable from '@/components/leaderboard/LeaderboardTable';
import { StateCard } from '@/components/ui/state-card';

interface Props {
  sessionId: string;
}

export default function SessionLeaderboardContent({ sessionId }: Props) {
  // Get leaderboard data for specific session using Convex
  const leaderboard = useQuery(
    api.analytics.getLeaderboard, 
    { sessionId: sessionId as Id<"sessions"> }
  );
  
  // Get session details from sessions data
  const sessionsData = useQuery(api.analytics.getSessionsForLeaderboard);
  const session = sessionsData?.sessions.find(s => s.id === sessionId);
  
  const loading = leaderboard === undefined || sessionsData === undefined;

  if (loading) {
    return (
      <LeaderboardShell eventName="Loading..." sessionName="Session Leaderboard">
        <div className="flex justify-center">
          <StateCard state="loading" title="Loading leaderboard data..." />
        </div>
      </LeaderboardShell>
    );
  }

  const eventName = sessionsData?.event?.name || 'Event';
  const sessionName = session?.name || 'Session';

  return (
    <LeaderboardShell eventName={eventName} sessionName={sessionName}>
      <LeaderboardTable data={leaderboard || []} />
    </LeaderboardShell>
  );
}
'use client'

import { StateCard } from '@/components/ui/state-card'
import { Button } from '@/components/ui/button'
import Link from 'next/link'

export default function NotFound() {
  return (
    <div className="flex justify-center items-center min-h-screen p-6 bg-background">
      <div className="space-y-4 w-full max-w-md">
        <StateCard
          state="error"
          title="Page Not Found"
          message="The page you're looking for doesn't exist or may have been moved."
        />
        <div className="flex justify-center">
          <Link href="/">
            <Button>
              Go Home
            </Button>
          </Link>
        </div>
      </div>
    </div>
  )
}
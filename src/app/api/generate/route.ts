import { openai } from "@ai-sdk/openai";
import { streamText, CoreMessage } from "ai";
import { match } from "ts-pattern";
import { getToken } from "next-auth/jwt";
import { NextRequest } from "next/server";

// IMPORTANT! Set the runtime to edge: https://vercel.com/docs/functions/edge-functions/edge-runtime
export const runtime = "edge";

// Comprehensive TypeScript interfaces
interface GenerateRequestBody {
  prompt: string;
  option: GenerateOption;
  command?: string;
}

type GenerateOption = 'continue' | 'improve' | 'shorter' | 'longer' | 'fix' | 'zap';

// Remove unused interfaces - keeping only what's needed

export async function POST(req: NextRequest): Promise<Response> {
  // Authentication check
  try {
    const token = await getToken({ req, secret: process.env.NEXTAUTH_SECRET });
    if (!token || !token.id) {
      return new Response(JSON.stringify({ error: "Authentication required" }), {
        status: 401,
        headers: { "Content-Type": "application/json" },
      });
    }

    // Check if user is approved
    if (token.status !== 'approved') {
      return new Response(JSON.stringify({ error: "Account not approved" }), {
        status: 403,
        headers: { "Content-Type": "application/json" },
      });
    }
  } catch {
    return new Response(JSON.stringify({ error: "Authentication failed" }), {
      status: 401,
      headers: { "Content-Type": "application/json" },
    });
  }

  // Check if the OPENAI_API_KEY is set
  if (!process.env.OPENAI_API_KEY || process.env.OPENAI_API_KEY === "") {
    return new Response(JSON.stringify({ error: "Service temporarily unavailable" }), {
      status: 503,
      headers: { "Content-Type": "application/json" },
    });
  }

  // Parse and validate request body
  let requestBody: GenerateRequestBody;
  try {
    const rawBody = await req.text();
    requestBody = JSON.parse(rawBody);
  } catch {
    return new Response(JSON.stringify({ error: "Invalid request format" }), {
      status: 400,
      headers: { "Content-Type": "application/json" },
    });
  }

  const { prompt, option, command } = requestBody;

  // Comprehensive input validation
  if (!prompt || typeof prompt !== 'string') {
    return new Response(JSON.stringify({ error: "Invalid prompt" }), {
      status: 400,
      headers: { "Content-Type": "application/json" },
    });
  }

  if (!option || !['continue', 'improve', 'shorter', 'longer', 'fix', 'zap'].includes(option)) {
    return new Response(JSON.stringify({ error: "Invalid option" }), {
      status: 400,
      headers: { "Content-Type": "application/json" },
    });
  }

  if (option === 'zap' && (!command || typeof command !== 'string')) {
    return new Response(JSON.stringify({ error: "Command required for zap option" }), {
      status: 400,
      headers: { "Content-Type": "application/json" },
    });
  }

  // Sanitize inputs - limit length and remove potentially harmful content
  const sanitizedPrompt = prompt.trim().substring(0, 4000);
  const sanitizedCommand = command?.trim().substring(0, 500);

  if (sanitizedPrompt.length === 0) {
    return new Response(JSON.stringify({ error: "Empty prompt" }), {
      status: 400,
      headers: { "Content-Type": "application/json" },
    });
  }


  // Define messages based on the selected option
  const messages: CoreMessage[] = match(option)
    .with("continue", () => [
      {
        role: "system" as const,
        content:
          "You are an AI writing assistant. The user will provide text. Your task is to seamlessly continue that text. " +
          "First, if possible include the original text in the beginning of your response. " +
          "Then, immediately following the original text, add your continuation. " +
          "OR you can take the original text as a title and continue on it as a topic. " +
          "Focus on the last part of the original text for context when generating the continuation. " +
          "Do not add any introductory phrases like 'Here is the continuation:'. " +
          "Limit the *continuation part* to ~300 characters, ensuring complete sentences. Use Markdown formatting only if appropriate for the content itself.",
      },
      {
        role: "user" as const,
        content: sanitizedPrompt,
      },
    ])
    .with("improve", () => [
      {
        role: "system" as const,
        content:
          "You are an AI writing assistant. Improve the following text for clarity, grammar, and flow. " +
          "Output *only* the improved text, without any explanations or phrases like 'Here is the improved version:'. " +
          "Output Must be **the original text** with the improved grammar. It CANNOT be much shorter or longer " +
          "Limit your response to ~200 characters, ensuring complete sentences. Use Markdown formatting only if appropriate for the content itself.",
      },
      {
        role: "user" as const,
        content: `The existing text is: ${sanitizedPrompt}`,
      },
    ])
    .with("shorter", () => [
      {
        role: "system" as const,
        content:
          "You are an AI writing assistant. Shorten the following text while preserving the core meaning. " +
          "Output *only* the shortened text, without any explanations or phrases like 'Here is the shorter version:'. " +
          "Use Markdown formatting only if appropriate for the content itself.",
      },
      {
        role: "user" as const,
        content: `The existing text is: ${sanitizedPrompt}`,
      },
    ])
    .with("longer", () => [
      {
        role: "system" as const,
        content:
          "You are an AI writing assistant. Expand on the following text, adding relevant details or explanation. " +
          "Output *only* the lengthened text, without any explanations or phrases like 'Here is the longer version:'. " +
          "Use Markdown formatting only if appropriate for the content itself.",
      },
      {
        role: "user" as const,
        content: `The existing text is: ${sanitizedPrompt}`,
      },
    ])
    .with("fix", () => [
      {
        role: "system" as const,
        content:
          "You are an AI writing assistant. Correct grammar and spelling errors in the following text. " +
          "Output *only* the corrected text, without any explanations or phrases like 'Here is the corrected version:'. " +
          "Limit your response to ~200 characters, ensuring complete sentences. Use Markdown formatting only if appropriate for the content itself.",
      },
      {
        role: "user" as const,
        content: `The existing text is: ${sanitizedPrompt}`,
      },
    ])
    .with("zap", () => [
      {
        role: "system" as const,
        content:
          "You are an AI writing assistant. Follow the user's command to manipulate the provided text. " +
          "Output *only* the resulting text based on the command, without any explanations or conversational filler. " +
          "Output in Markdown format.",
      },
      {
        role: "user" as const,
        content: `For this text: ${sanitizedPrompt}. You have to respect the command: ${sanitizedCommand}`,
      },
    ])
    .exhaustive();

  // Add timeout handling
  const controller = new AbortController();
  const timeoutId = setTimeout(() => controller.abort(), 30000); // 30 seconds

  try {
    const result = await streamText({
      messages: messages,
      model: openai("gpt-4.1-mini"),
      maxTokens: 512,
      temperature: 0.7,
      topP: 1,
      frequencyPenalty: 0,
      presencePenalty: 0,
      abortSignal: controller.signal,
    });

    clearTimeout(timeoutId);
    return result.toDataStreamResponse();

  } catch (error) {
    clearTimeout(timeoutId);
    
    // Handle specific error types
    if (error instanceof Error && error.name === 'AbortError') {
      return new Response(JSON.stringify({ error: "Request timeout" }), {
        status: 408,
        headers: { "Content-Type": "application/json" },
      });
    }

    // Generic error response for security
    return new Response(JSON.stringify({ error: "Processing failed" }), {
      status: 500,
      headers: { "Content-Type": "application/json" },
    });
  }
}
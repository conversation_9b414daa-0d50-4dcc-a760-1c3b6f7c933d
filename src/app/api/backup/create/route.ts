import { NextRequest, NextResponse } from 'next/server';
import { ConvexHttpClient } from "convex/browser";
import { api } from "@/../convex/_generated/api";
import { spawn } from 'child_process';
import fs from 'fs';

if (!process.env.CONVEX_SELF_HOSTED_URL) {
    throw new Error('CONVEX_SELF_HOSTED_URL is not defined');
}

const convex = new ConvexHttpClient(process.env.CONVEX_SELF_HOSTED_URL);

export async function POST(request: NextRequest) {
  try {
    const { description, includeStorage, createdBy } = await request.json();
    
    const timestamp = Date.now();
    const backupId = `backup_${timestamp}`;
    
    // Create backup metadata first
    const metadataId = await convex.mutation(api.backupData.createBackupMetadata, {
      id: backupId,
      description,
      fileName: "pending",
      createdBy,
      includeStorage,
      tablesIncluded: ["all"],
    });
    
    // Create backup in working directory (Docker-friendly)

    // Ensure backups directory exists
    if (!fs.existsSync('./backups')) {
      fs.mkdirSync('./backups', { recursive: true });
    }

    const tempFileName = `./backups/backup_${timestamp}.zip`;
    
    // Execute Convex export to temporary file
    const exportArgs = ['convex', 'export', '--path', tempFileName];
    if (includeStorage) {
      exportArgs.push('--include-file-storage');
    }
    
    // console.log('Executing Convex export to temp file:', exportArgs);
    // console.log('Environment variables:', {
    //   CONVEX_URL: process.env.CONVEX_URL,
    //   CONVEX_SELF_HOSTED_ADMIN_KEY: process.env.CONVEX_SELF_HOSTED_ADMIN_KEY ? 'SET' : 'NOT SET',
    //   cwd: process.cwd()
    // });

    const exportProcess = spawn('bunx', exportArgs, {
      cwd: process.cwd(),
      stdio: ['pipe', 'pipe', 'pipe'],
    });
    
    let stderr = '';
    
    exportProcess.stderr.on('data', (data) => {
      stderr += data.toString();
      console.error('Export stderr:', data.toString());
    });
    
    exportProcess.stdout.on('data', (data) => {
      console.log('Export stdout:', data.toString());
    });
    
    // Wait for export to complete
    await new Promise<void>((resolve, reject) => {
      exportProcess.on('close', (code) => {
        console.log('Export finished with code:', code);
        if (code === 0) {
          resolve();
        } else {
          reject(new Error(`Export failed with code ${code}. stderr: ${stderr}`));
        }
      });
      
      exportProcess.on('error', (error) => {
        console.error('Export process error:', error);
        reject(error);
      });
    });
    
    // Get file size without loading into memory
    const stats = fs.statSync(tempFileName);
    const fileSize = stats.size;

    // Generate upload URL from Convex
    const uploadUrl = await convex.mutation(api.backupData.generateUploadUrl);

    // Upload file directly to Convex storage
    const fileData = fs.readFileSync(tempFileName);
    const uploadResponse = await fetch(uploadUrl, {
      method: 'POST',
      headers: { 'Content-Type': 'application/zip' },
      body: fileData,
    });

    if (!uploadResponse.ok) {
      throw new Error(`Upload failed: ${uploadResponse.statusText}`);
    }

    const { storageId } = await uploadResponse.json();

    // Clean up temp file
    fs.unlinkSync(tempFileName);

    const fileName = `snapshot_convex-self-hosted_${timestamp}.zip`;

    // Update metadata with success
    await convex.mutation(api.backupData.updateBackupMetadata, {
      backupId: metadataId,
      fileName,
      size: fileSize,
      status: "completed",
      fileId: storageId,
    });
    
    console.log(`Backup created successfully: ${fileName} (${fileSize} bytes)`);
    
    return NextResponse.json({
      success: true,
      backupId,
      fileName,
      size: fileSize,
      storageId,
      message: "Backup created successfully",
    });
    
  } catch (error) {
    console.error('Backup creation failed:', error);
    
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      message: "Backup creation failed",
    }, { status: 500 });
  }
}
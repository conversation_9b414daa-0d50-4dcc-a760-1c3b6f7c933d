import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { ConvexHttpClient } from "convex/browser";
import { api } from "@/../convex/_generated/api";
import { spawn } from 'child_process';
import fs from 'fs';
import path from 'path';

if (!process.env.CONVEX_SELF_HOSTED_URL) {
    throw new Error('CONVEX_SELF_HOSTED_URL is not defined');
}

const convex = new ConvexHttpClient(process.env.CONVEX_SELF_HOSTED_URL);

export async function POST(request: NextRequest) {
  let tempDir: string | null = null;
  let username = '';

  try {
    // Authentication check
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json(
        { 
          success: false,
          error: 'Unauthorized',
          message: 'Authentication required'
        },
        { status: 401 }
      );
    }

    // Authorization check - only admins can restore backups
    if (session.user.role !== 'admin') {
      return NextResponse.json(
        { 
          success: false,
          error: 'Forbidden',
          message: 'Admin access required'
        },
        { status: 403 }
      );
    }

    // Extract username for mutations
    username = session.user.username || '';
    const body = await request.json();
    const { fileName, replaceAll } = body;

    // Validate fileName
    if (typeof fileName !== 'string') {
      return NextResponse.json({
        success: false,
        error: "Invalid fileName: must be a string",
        message: "Backup restoration failed",
      }, { status: 400 });
    }

    // Validate fileName format (only allow alphanumeric, dashes, dots, underscores, and must end with .zip)
    const fileNamePattern = /^[\w\-\.]+\.zip$/;
    if (!fileNamePattern.test(fileName)) {
      return NextResponse.json({
        success: false,
        error: "Invalid fileName: must be a valid zip filename (alphanumeric, dashes, dots, underscores only, ending with .zip)",
        message: "Backup restoration failed",
      }, { status: 400 });
    }

    // Validate replaceAll
    if (typeof replaceAll !== 'boolean') {
      return NextResponse.json({
        success: false,
        error: "Invalid replaceAll: must be a boolean",
        message: "Backup restoration failed",
      }, { status: 400 });
    }

    // Find backup by filename
    const backups = await convex.query(api.backupData.getBackupsList);
    const backup = backups.find((b: { fileName: string }) => b.fileName === fileName);

    if (!backup || !backup.fileId) {
      return NextResponse.json({
        success: false,
        error: "Backup file not found",
        message: "Backup restoration failed",
      }, { status: 404 });
    }

    // Get backup file URL from Convex storage
    const downloadUrl = await convex.action(api.backupActions.getBackupDownloadUrlAction, {
      fileName,
    });

    if (!downloadUrl.success || !downloadUrl.downloadUrl) {
      return NextResponse.json({
        success: false,
        error: "Could not get backup file",
        message: "Backup restoration failed",
      }, { status: 500 });
    }

    // Create temp directory for download and extraction

    tempDir = `./backups/restore_${Date.now()}`;
    fs.mkdirSync(tempDir, { recursive: true });

    // Download backup file directly to disk using original filename
    const tempZipPath = path.join(tempDir, fileName);
    const response = await fetch(downloadUrl.downloadUrl);

    if (!response.ok) {
      throw new Error(`Failed to download backup file: ${response.status} ${response.statusText}`);
    }

    console.log(`Downloading backup file to: ${tempZipPath}`);
    console.log(`Content type: ${response.headers.get('content-type')}`);
    console.log(`Content length: ${response.headers.get('content-length')}`);

    // Get the response as an ArrayBuffer to ensure binary data integrity
    const arrayBuffer = await response.arrayBuffer();
    const buffer = Buffer.from(arrayBuffer);
    
    // Write buffer directly to file to avoid stream encoding issues
    fs.writeFileSync(tempZipPath, buffer);

    // Verify downloaded file
    const stats = fs.statSync(tempZipPath);
    console.log(`Downloaded file size: ${stats.size} bytes`);

    // Check first few bytes to verify it's a ZIP file
    const fullBuffer = fs.readFileSync(tempZipPath);
    const testBuffer = fullBuffer.subarray(0, 50);
    console.log(`First 50 bytes of downloaded file: ${testBuffer.toString('hex')}`);

    // Verify ZIP file integrity before extraction
    const zipSignature = fullBuffer.subarray(0, 4);
    const expectedSignature = Buffer.from([0x50, 0x4B, 0x03, 0x04]); // ZIP file signature
    
    if (!zipSignature.equals(expectedSignature)) {
      console.error(`Invalid ZIP signature. Expected: ${expectedSignature.toString('hex')}, Got: ${zipSignature.toString('hex')}`);
      throw new Error('Downloaded file is not a valid ZIP file');
    }

    // Extract backup zip to temp directory using native unzip command
    try {
      console.log('Starting ZIP extraction...');
      
      // Extract using spawn already imported
      
      await new Promise<void>((resolve, reject) => {
        const unzipProcess = spawn('unzip', ['-o', tempZipPath, '-d', tempDir!], {
          stdio: ['pipe', 'pipe', 'pipe'],
          env: { ...process.env, UNZIP_DISABLE_ZIPBOMB_DETECTION: 'TRUE' }
        });
        
        let stderr = '';
        
        unzipProcess.stdout.on('data', (data: Buffer) => {
          console.log('Unzip stdout:', data.toString());
        });
        
        unzipProcess.stderr.on('data', (data: Buffer) => {
          stderr += data.toString();
          console.log('Unzip stderr:', data.toString());
        });
        
        unzipProcess.on('close', (code: number | null) => {
          console.log('Unzip process finished with code:', code);
          if (code === 0) {
            console.log(`Successfully extracted backup to: ${tempDir}`);
            resolve();
          } else {
            reject(new Error(`Unzip failed with code ${code}. stderr: ${stderr}`));
          }
        });
        
        unzipProcess.on('error', (error: Error) => {
          console.error('Unzip process error:', error);
          reject(error);
        });
      });
      
    } catch (zipError) {
      console.error('ZIP extraction error:', zipError);
      console.error('ZIP file stats:', stats);
      console.error('ZIP file first 100 bytes:', fullBuffer.subarray(0, 100).toString('hex'));
      throw new Error(`Failed to extract backup: ${zipError instanceof Error ? zipError.message : 'Unknown ZIP error'}`);
    }

    console.log(`Backup extracted to: ${tempDir}`);

    // Set restore status to true
    await convex.mutation(api.settings.setSetting, {
      username,
      key: "restore_in_progress",
      value: true
    });

    // Get list of table directories, excluding only system tables
    const entries = fs.readdirSync(tempDir!);
    const tablesToRestore = entries.filter((entry: string) => {
      const fullPath = path.join(tempDir!, entry);
      const isDirectory = fs.statSync(fullPath).isDirectory();

      // Only exclude actual system/metadata folders, not user tables like 'ideas'
      // Also exclude settings table to avoid wiping out restore_in_progress flag
      const excludedTables = ['backups', '_tables', 'settings'];
      const isExcluded = excludedTables.includes(entry) ||
                        entry.startsWith('.') ||
                        entry.endsWith('.zip') ||
                        entry === 'README.md';

      return isDirectory && !isExcluded;
    });

    console.log(`Tables to restore: ${tablesToRestore.join(', ')}`);

    const results: Array<{table: string, success: boolean, error?: string}> = [];

    // Restore each table individually
    for (const tableName of tablesToRestore) {
      // Validate tableName to prevent command injection
      const tableNameRegex = /^[a-zA-Z0-9_]+$/;
      if (!tableNameRegex.test(tableName)) {
        console.error(`Invalid table name detected: ${tableName}. Skipping for security.`);
        results.push({
          table: tableName,
          success: false,
          error: 'Invalid table name - contains unsafe characters'
        });
        continue;
      }

      const tablePath = path.join(tempDir!, tableName, 'documents.jsonl');

      // Check if documents.jsonl exists
      if (!fs.existsSync(tablePath)) {
        console.warn(`No documents.jsonl found for table: ${tableName}`);
        results.push({
          table: tableName,
          success: false,
          error: 'No documents.jsonl file found'
        });
        continue;
      }

      try {
        console.log(`Restoring table: ${tableName}`);

        await new Promise<void>((resolve, reject) => {
          const importArgs = ['convex', 'import', '--table', tableName, tablePath];
          if (replaceAll) {
            importArgs.push('--replace');
          }
          importArgs.push('-y'); // Auto-confirm

          const importProcess = spawn('bunx', importArgs, {
            cwd: process.cwd(),
            stdio: ['pipe', 'pipe', 'pipe'],
          });

          let stderr = '';

          importProcess.stdout.on('data', (data: Buffer) => {
            console.log(`[${tableName}] stdout:`, data.toString());
          });

          importProcess.stderr.on('data', (data: Buffer) => {
            stderr += data.toString();
            console.log(`[${tableName}] stderr:`, data.toString());
          });

          importProcess.on('close', (code) => {
            console.log(`[${tableName}] Import finished with code: ${code}`);

            if (code === 0) {
              resolve();
            } else {
              reject(new Error(`Import failed with code ${code}. stderr: ${stderr}`));
            }
          });

          importProcess.on('error', (error) => {
            console.error(`[${tableName}] Import process error:`, error);
            reject(error);
          });
        });

        results.push({
          table: tableName,
          success: true
        });

        console.log(`✅ Successfully restored table: ${tableName}`);

      } catch (error) {
        console.error(`❌ Failed to restore table ${tableName}:`, error);
        results.push({
          table: tableName,
          success: false,
          error: error instanceof Error ? error.message : 'Unknown error'
        });
      }
    }

    const successCount = results.filter(r => r.success).length;
    const totalCount = results.length;

    console.log(`Restore completed: ${successCount}/${totalCount} tables restored successfully`);

    // Clear restore status after successful completion
    await convex.mutation(api.settings.setSetting, {
      username,
      key: "restore_in_progress",
      value: false
    });

    return NextResponse.json({
      success: successCount > 0,
      message: `Restored ${successCount}/${totalCount} tables successfully`,
      fileName: backup.fileName,
      results,
      summary: {
        total: totalCount,
        successful: successCount,
        failed: totalCount - successCount
      }
    });

  } catch (error) {
    console.error('Backup restoration failed:', error);

    // Clear restore status on error
    try {
      await convex.mutation(api.settings.setSetting, {
        username,
        key: "restore_in_progress",
        value: false
      });
    } catch (settingError) {
      console.error('Failed to clear restore status:', settingError);
    }

    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      message: "Backup restoration failed",
    }, { status: 500 });
  } finally {
    // Clean up temp directory
    if (tempDir) {
      try {
        fs.rmSync(tempDir, { recursive: true, force: true });
        console.log(`Cleaned up temp directory: ${tempDir}`);
      } catch (error) {
        console.warn('Failed to clean up temp directory:', error);
      }
    }
  }
}
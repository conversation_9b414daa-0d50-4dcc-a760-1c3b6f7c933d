"use client";

import { useEffect } from 'react';

export default function PrintThemeForcer() {
  useEffect(() => {
    // Force light mode for print pages
    document.documentElement.classList.remove('dark');
    document.documentElement.classList.add('light');
    document.documentElement.setAttribute('data-theme', 'light');
    document.body.classList.remove('dark');
    document.body.classList.add('light');
  }, []);

  return null; // This component only manipulates the DOM
}
"use client";

import { useMemo } from 'react';
import { use<PERSON><PERSON><PERSON>, useRouter } from 'next/navigation';
import { useSession } from 'next-auth/react';
import { useQuery } from "convex/react";
import { api } from "@/../convex/_generated/api";
import { Id } from "@/../convex/_generated/dataModel";
import TiptapContentDisplay from '@/components/editors/TiptapContentDisplay';
import { Printer } from 'lucide-react';

interface PrintIdea {
  _id: string;
  name: string;
  description?: string;
  userName: string;
  sessionName: string;
  submittedAt?: number;
}

interface PrintSparkSubmission {
  _id: string;
  data: Record<string, string | boolean>;
  userName: string;
  sessionName: string;
  sparkName: string;
  sparkColor: string;
  sparkFields: Array<{
    type: 'text' | 'richtext' | 'dropdown' | 'radio' | 'checkbox';
    label: string;
    placeholder?: string;
    options?: string[];
    required?: boolean;
  }>;
  submittedAt: number;
}




// Helper function to render spark field values based on field type
const renderSparkFieldValue = (field: PrintSparkSubmission['sparkFields'][0], value: string | boolean | null | undefined): React.ReactNode => {
  const isEmpty = value === undefined || value === null || (typeof value === 'string' && value.trim() === '');
  if (isEmpty && field.type !== 'checkbox') return null;

  switch (field.type) {
    case 'richtext':
      return <TiptapContentDisplay content={value as string} className="text-gray-800" />;
    case 'checkbox':
      return value ? 'Yes' : 'No';
    default:
      return value as string;
  }
};

export default function PrintPage() {
  const params = useParams();
  const router = useRouter();
  const { data: session, status } = useSession();
  const teamNameSlug = params.team as string;

  // Convex real-time queries
  const teams = useQuery(api.analytics.getTeamsForDropdown);
  
  // Find the team by slug
  const selectedTeam = useMemo(() => {
    if (!teams) return null;
    return teams.find(team => 
      team.name.toLowerCase().replace(/\s+/g, '-') === teamNameSlug
    );
  }, [teams, teamNameSlug]);

  // Get print data for the selected team (only if team found)
  const ideas = useQuery(
    api.analytics.getPrintIdeasByTeam,
    selectedTeam ? { teamId: selectedTeam.id as Id<"teams"> } : "skip"
  );

  const sparkSubmissions = useQuery(
    api.analytics.getPrintSparkSubmissionsByTeam,
    selectedTeam ? { teamId: selectedTeam.id as Id<"teams"> } : "skip"
  );

  // Loading and authentication logic
  const loading = teams === undefined || (selectedTeam && (ideas === undefined || sparkSubmissions === undefined));

  // Check authentication
  if (status === 'loading') {
    return <div className="flex items-center justify-center min-h-screen bg-white text-gray-800">Loading...</div>;
  }

  if (status === 'unauthenticated') {
    router.push('/login');
    return <div className="flex items-center justify-center min-h-screen bg-white text-gray-800">Redirecting...</div>;
  }

  // Check admin role
  if (session?.user?.role !== 'admin') {
    return <div className="flex items-center justify-center min-h-screen bg-white text-red-600">Admin access required</div>;
  }

  // Check for team not found
  if (teams && !selectedTeam) {
    return <div className="flex items-center justify-center min-h-screen bg-white text-red-600">Team not found</div>;
  }

  // Build team data object
  const teamData = selectedTeam && ideas && sparkSubmissions ? {
    name: selectedTeam.name,
    ideas: ideas || [],
    sparkSubmissions: sparkSubmissions || []
  } : null;

  // Show loading state
  if (loading) {
    return <div className="flex items-center justify-center min-h-screen bg-white text-gray-800">Loading...</div>;
  }

  // Show no data state
  if (!teamData) {
    return <div className="flex items-center justify-center min-h-screen bg-white text-primary">No data available</div>;
  }

  return (
    <div className="max-w-4xl mx-auto p-8 bg-white text-gray-800 print:p-4 print:max-w-none">
      <div className="flex items-center justify-between mb-8 print:mb-4">
        <h1 className="text-4xl font-bold text-gray-900 print:text-3xl">{teamData.name}</h1>
        <button 
          className="flex items-center gap-2 px-4 py-2 bg-gray-100 hover:bg-gray-200 text-gray-700 border border-popover transition-colors print:hidden"
          onClick={() => window.print()}
          title="Print"
        >
          <Printer className="h-5 w-5" />
          Print
        </button>
      </div>

      {teamData.ideas?.length > 0 && (
        <div className="mb-12 print:mb-8">
          <h2 className="text-2xl font-bold text-gray-900 mb-6 print:text-xl print:mb-4 border-b-2 border-muted pb-2">Ideas</h2>
          <div className="space-y-8 print:space-y-6">
            {teamData.ideas.map((idea: PrintIdea) => (
              <div key={idea._id} className="bg-gray-50 p-6 border border-popover print:bg-white print:border-popover">
                <h3 className="text-xl font-semibold text-primary mb-2 print:text-lg">{idea.name}</h3>
                <div className="text-sm text-gray-600 mb-4 font-medium">{idea.sessionName || 'Unknown Session'}</div>
                <div className="mb-4">
                  <TiptapContentDisplay content={idea.description || ''} className="text-gray-800" />
                </div>
                <div className="pt-4 border-t border-popover space-y-2">
                  <div className="flex items-center text-xs text-gray-600">
                    <span className="font-medium text-accent">Submitted by:</span>
                    <span className="ml-2 text-popover">{idea.userName || 'Unknown User'}</span>
                  </div>
                  {idea.submittedAt && (
                    <div className="flex items-center text-xs text-gray-600">
                      <span className="font-medium text-accent">Submitted At:</span>
                      <span className="ml-2 text-popover">{new Date(idea.submittedAt).toLocaleString()}</span>
                    </div>
                  )}
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {teamData.sparkSubmissions?.length > 0 && (
        <div className="mb-12 print:mb-8">
          <h2 className="text-2xl font-bold text-gray-900 mb-6 print:text-xl print:mb-4 border-b-2 border-gray-200 pb-2">Submissions</h2>
          <div className="space-y-8 print:space-y-6">
            {teamData.sparkSubmissions.map((submission: PrintSparkSubmission) => (
              <div 
                key={submission._id} 
                className="bg-gray-50 p-6 border-2 print:bg-white"
                style={{ borderColor: submission.sparkColor }}
              >
                <h3 
                  className="text-xl font-semibold mb-2 print:text-lg"
                  style={{ color: submission.sparkColor }}
                >
                  {submission.sparkName}
                </h3>
                <div className="text-sm text-gray-600 mb-4 font-medium">{submission.sessionName || 'Unknown Session'}</div>
                
                {/* Render dynamic spark fields */}
                <div className="space-y-4 mb-4">
                  {submission.sparkFields.map((field, fieldIndex) => {
                    const fieldId = `field_${fieldIndex}`;
                    const fieldValue = submission.data[fieldId];
                    const renderedValue = renderSparkFieldValue(field, fieldValue);
                    
                    if (!renderedValue && renderedValue !== 'No') return null;
                    
                    return (
                      <div key={fieldIndex} className="space-y-2">
                        <div 
                          className="font-semibold text-sm"
                          style={{ color: submission.sparkColor }}
                        >
                          {field.label}:
                        </div>
                        <div className="text-gray-800">
                          {field.type === 'richtext' ? (
                            renderedValue
                          ) : (
                            <div>{renderedValue}</div>
                          )}
                        </div>
                      </div>
                    );
                  })}
                </div>
                
                <div className="pt-4 border-t border-gray-200 space-y-2">
                  <div className="flex items-center text-xs text-gray-600">
                    <span className="font-medium">Submitted by:</span>
                    <span className="ml-2">{submission.userName || 'Unknown User'}</span>
                  </div>
                  <div className="flex items-center text-xs text-gray-600">
                    <span className="font-medium">Submitted At:</span>
                    <span className="ml-2">{new Date(submission.submittedAt).toLocaleString()}</span>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}


    </div>
  );
}

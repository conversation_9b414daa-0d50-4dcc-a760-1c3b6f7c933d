"use client";

import { useMemo } from 'react';
import { use<PERSON><PERSON><PERSON>, useRouter } from 'next/navigation';
import { useSession } from 'next-auth/react';
import { useQuery } from "convex/react";
import { api } from "@/../convex/_generated/api";
import { Id } from "@/../convex/_generated/dataModel";
import TiptapContentDisplay from '@/components/editors/TiptapContentDisplay';
import { Printer } from 'lucide-react';

interface PrintIdea {
  _id: string;
  name: string;
  description?: string;
  userName: string;
  teamName: string;
  submittedAt?: number;
}

interface PrintSparkSubmission {
  _id: string;
  data: Record<string, string | boolean>;
  userName: string;
  teamName: string;
  sparkName: string;
  sparkColor: string;
  sparkFields: Array<{
    type: 'text' | 'richtext' | 'dropdown' | 'radio' | 'checkbox';
    label: string;
    placeholder?: string;
    options?: string[];
    required?: boolean;
  }>;
  submittedAt: number;
}

// Helper function to render spark field values based on field type
const renderSparkFieldValue = (field: PrintSparkSubmission['sparkFields'][0], value: string | boolean | null | undefined): React.ReactNode => {
  const isEmpty = value === undefined || value === null || (typeof value === 'string' && value.trim() === '');
  if (isEmpty && field.type !== 'checkbox') return null;

  switch (field.type) {
    case 'richtext':
      return <TiptapContentDisplay content={value as string} forceTheme="light" />;
    case 'checkbox':
      return value ? 'Yes' : 'No';
    default:
      return value as string;
  }
};

export default function PrintSessionPage() {
  const params = useParams();
  const router = useRouter();
  const { data: session, status } = useSession();
  const sessionNameSlug = params.session as string;

  // Convex real-time queries
  const allSessions = useQuery(api.sessions.getSessionsByActiveEventExcludingQuickfire);
  
  // Find the session by slug (only Ideas and Sparks sessions)
  const selectedSession = useMemo(() => {
    if (!allSessions) return null;
    const filteredSessions = allSessions.filter(session => session.type === 'Ideas' || session.type === 'Sparks');
    return filteredSessions.find(session => 
      session.name.toLowerCase().replace(/\s+/g, '-') === sessionNameSlug
    );
  }, [allSessions, sessionNameSlug]);

  // Get print data for the selected session (only if session found)
  const sessionData = useQuery(
    api.ideas.getAllDataGroupedByTeam,
    selectedSession ? { sessionId: selectedSession._id as Id<"sessions"> } : "skip"
  );

  // Process session data - group by team (must be at top level for hooks rule)
  const teamGroups = useMemo(() => {
    if (!sessionData) return [];
    
    return Object.entries(sessionData as Record<string, { teamName: string; ideas?: PrintIdea[]; sparkSubmissions?: PrintSparkSubmission[] }>).map(([teamId, teamGroup]) => ({
      teamId,
      teamName: teamGroup.teamName,
      ideas: teamGroup.ideas || [],
      sparkSubmissions: teamGroup.sparkSubmissions || []
    })).filter(team => team.ideas.length > 0 || team.sparkSubmissions.length > 0);
  }, [sessionData]);

  // Loading and authentication logic
  const loading = allSessions === undefined || (selectedSession && sessionData === undefined);

  // Check authentication
  if (status === 'loading') {
    return <div className="flex items-center justify-center min-h-screen bg-white text-gray-800">Loading...</div>;
  }

  if (status === 'unauthenticated') {
    router.push('/login');
    return <div className="flex items-center justify-center min-h-screen bg-white text-gray-800">Redirecting...</div>;
  }

  // Check admin role
  if (session?.user?.role !== 'admin') {
    return <div className="flex items-center justify-center min-h-screen bg-white text-primary">Admin access required</div>;
  }

  // Check for session not found
  if (allSessions && !selectedSession) {
    return <div className="flex items-center justify-center min-h-screen bg-white text-primary">Session not found</div>;
  }

  // Show loading state
  if (loading) {
    return <div className="flex items-center justify-center min-h-screen bg-white text-gray-800">Loading...</div>;
  }

  // Show no data state
  if (!sessionData) {
    return <div className="flex items-center justify-center min-h-screen bg-white text-primary">No data available</div>;
  }

  return (
    <div className="max-w-4xl mx-auto p-8 bg-white text-gray-800 print:p-4 print:max-w-none">
      <div className="flex items-center justify-between mb-8 print:mb-4">
        <h1 className="text-4xl font-bold text-gray-900 print:text-3xl">{selectedSession?.name} - All Teams</h1>
        <button 
          className="flex items-center gap-2 px-4 py-2 bg-gray-100 hover:bg-gray-200 text-gray-700 border border-gray-300 transition-colors print:hidden"
          onClick={() => window.print()}
          title="Print"
        >
          <Printer className="h-5 w-5" />
          Print
        </button>
      </div>

      {teamGroups.length > 0 ? (
        <div className="space-y-12 print:space-y-8">
          {teamGroups.map((team) => (
            <div key={team.teamId} className="border-b-2 border-muted pb-8 print:pb-6 last:border-b-0">
              <h2 className="text-3xl font-bold text-gray-900 mb-6 print:text-2xl print:mb-4">{team.teamName}</h2>
            
              {/* Team's Ideas */}
              {team.ideas.length > 0 && (
                <div className="mb-8">
                  <h3 className="text-xl font-bold text-gray-900 mb-4 print:text-lg print:mb-3">Ideas</h3>
                  <div className="space-y-6 print:space-y-4">
                    {team.ideas.map((idea: PrintIdea) => (
                      <div key={idea._id} className="p-4 border border-popover print:bg-white">
                        <h4 className="text-lg font-semibold text-primary mb-3 print:text-base">{idea.name}</h4>
                        <div className="mb-3">
                          <TiptapContentDisplay content={idea.description || ''} className="text-gray-800" forceTheme="light" />
                        </div>
                        <div className="pt-3 border-t border-popover space-y-2">
                          <div className="text-xs text-gray-600">
                            <span className="font-medium text-accent">Submitted by:</span> {idea.userName || 'Unknown User'}
                          </div>
                          {idea.submittedAt && (
                            <div className="text-xs text-gray-600">
                              <span className="font-medium text-accent">Submitted At:</span> {new Date(idea.submittedAt).toLocaleString()}
                            </div>
                          )}
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}

            {/* Team's Spark Submissions */}
            {team.sparkSubmissions.length > 0 && (
              <div className="mb-8">
                <h3 className="text-xl font-bold text-gray-900 mb-4 print:text-lg print:mb-3">Submissions</h3>
                <div className="space-y-6 print:space-y-4">
                  {team.sparkSubmissions.map((submission: PrintSparkSubmission) => (
                    <div 
                      key={submission._id} 
                      className="bg-gray-50 p-6 border-2 print:bg-white"
                      style={{ borderColor: submission.sparkColor }}
                    >
                      <h4 
                        className="text-xl font-semibold mb-2 print:text-lg"
                        style={{ color: submission.sparkColor }}
                      >
                        {submission.sparkName}
                      </h4>
                      
                      {/* Render dynamic spark fields */}
                      <div className="space-y-4 mb-4">
                        {submission.sparkFields.map((field, fieldIndex) => {
                          const fieldId = `field_${fieldIndex}`;
                          const fieldValue = submission.data[fieldId];
                          const renderedValue = renderSparkFieldValue(field, fieldValue);
                          
                          if (!renderedValue && renderedValue !== 'No') return null;
                          
                          return (
                            <div key={fieldIndex} className="space-y-2">
                              <div 
                                className="font-semibold text-sm"
                                style={{ color: submission.sparkColor }}
                              >
                                {field.label}:
                              </div>
                              <div className="text-gray-800">
                                {field.type === 'richtext' ? (
                                  renderedValue
                                ) : (
                                  <div>{renderedValue}</div>
                                )}
                              </div>
                            </div>
                          );
                        })}
                      </div>
                      
                      <div className="pt-4 border-t border-popover space-y-2">
                        <div className="flex items-center text-xs text-gray-600">
                          <span className="font-medium text-accent">Submitted by:</span>
                          <span className="ml-2">{submission.userName || 'Unknown User'}</span>
                        </div>
                        <div className="flex items-center text-xs text-gray-600">
                          <span className="font-medium text-accent">Submitted At:</span>
                          <span className="ml-2">{new Date(submission.submittedAt).toLocaleString()}</span>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
        ))}
        </div>
      ) : (
        <div className="text-center py-12 bg-gray-50 border border-gray-200">
          <h2 className="text-xl font-semibold text-gray-900 mb-2">No data found for this session</h2>
          <p className="text-gray-600">No ideas or spark submissions have been created for this session yet.</p>
        </div>
      )}
    </div>
  );
}
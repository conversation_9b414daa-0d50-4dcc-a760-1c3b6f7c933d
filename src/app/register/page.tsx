"use client";

import Link from 'next/link';
import { Suspense } from 'react';
import { useQuery } from "convex/react";
import { api } from "@/../convex/_generated/api";
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader } from '@/components/ui/card';
import RegisterForm from '@/components/auth/RegisterForm';
import RegisterLogo from '@/components/icons/RegisterLogo';


export default function RegisterPage() {
    const activeEvent = useQuery(api.events.getActiveEvent);

    if (activeEvent === undefined) {
        return (
            <div className="relative w-screen min-h-screen flex justify-center items-center bg-background p-8 md:p-4 theme-background">
                

                <div className="relative z-10 w-full max-w-md">
                    <Card className="bg-card/90 backdrop-blur-sm border-accent">
                        <CardContent className="flex items-center justify-center py-12">
                            <div className="text-muted-foreground">Loading...</div>
                        </CardContent>
                    </Card>
                </div>
            </div>
        );
    }

    if (!activeEvent) {
        return (
            <div className="relative w-screen min-h-screen flex justify-center items-center bg-background p-8 md:p-4 theme-background">
                

                <div className="relative z-10 w-full max-w-md">
                    <Card className="bg-card/90 backdrop-blur-sm border-accent">
                        <CardHeader className="flex flex-col items-center space-y-4 pb-6">
                            <RegisterLogo className="text-secondary" size={100} />
                            <h1 className="text-card-foreground text-2xl md:text-3xl text-center font-ultrabold">
                                Join Lion X
                            </h1>
                        </CardHeader>
                        
                        <CardContent className="space-y-6 text-center">
                            <div className="space-y-4">
                                <p className="text-card-foreground">Registration is currently closed as there is no active event.</p>
                                <p className="text-muted-foreground">Please check back later when a new event starts!</p>
                            </div>
                            <Button asChild variant="outline" className="w-full">
                                <Link href="/login">
                                    Back to Login
                                </Link>
                            </Button>
                        </CardContent>
                    </Card>
                </div>
            </div>
        );
    }

    return (
        <div className="relative w-screen min-h-screen flex justify-center items-center bg-background px-4 py-8 md:p-4 theme-background">
            

            <div className="relative z-10 w-full max-w-md">
                <Suspense fallback={
                    <Card className="bg-card/90 backdrop-blur-sm border-accent">
                        <CardContent className="flex items-center justify-center py-12">
                            <div className="text-muted-foreground">Loading...</div>
                        </CardContent>
                    </Card>
                }>
                    <Card className="bg-card/90 backdrop-blur-sm border-accent">
                        <CardHeader className="flex flex-col items-center space-y-4 pb-0">
                            {/* Logo Container - Keep same logic */}
                            <RegisterLogo className="text-secondary" size={100} />
                            <div className="text-card-foreground text-center">
                                <div className="text-sm font-mono">
                                    Join
                                </div>
                                <h1 className="text-2xl md:text-3xl font-ultrabold">
                                    {activeEvent.name}
                                </h1>
                            </div>
                        </CardHeader>
                        
                        <CardContent className="space-y-6">
                            <RegisterForm eventName={activeEvent.name} />
                            <div className="flex justify-center">
                                <Button asChild variant="outline">
                                    <Link href="/login">
                                        Back to Login
                                    </Link>
                                </Button>
                            </div>
                        </CardContent>
                    </Card>
                </Suspense>
            </div>
        </div>
    );
}
import { DefaultSession, DefaultUser } from "next-auth"
import { DefaultJWT } from "next-auth/jwt"

declare module "next-auth" {
    /**
     * Returned by `useSession`, `getSession` and received as a prop on the `SessionProvider` React Context
     */
    interface Session {
        user: {
            /** Convex _id of the user */
            id: string
            /** Unique username */
            username: string
            /** User role: 'admin' | 'teamLead' | 'teamMember' */
            role: string
            /** User status: 'pending' | 'approved' | 'rejected' */
            status: string
            /** Convex _id of the team the user belongs to */
            teamId: string | null
            /** Convex _id of the event the user is participating in */
            eventId: string | null
        } & DefaultSession["user"]
    }

    /**
     * The shape of the user object returned in the OAuth providers' `profile` callback,
     * or the second parameter of the `session` callback, when using a database.
     */
    interface User extends DefaultUser {
        /** Unique username */
        username: string
        /** User role: 'admin' | 'teamLead' | 'teamMember' */
        role: string
        /** User status: 'pending' | 'approved' | 'rejected' */
        status: string
        /** Convex _id of the team the user belongs to */
        teamId: string | null
        /** Convex _id of the event the user is participating in */
        eventId: string | null
    }
}

declare module "next-auth/jwt" {
    /**
     * Returned by the `jwt` callback and `getToken`, when using JWT sessions
     */
    interface JWT extends DefaultJWT {
        /** Convex _id of the user */
        id: string
        /** Unique username */
        username: string
        /** User role: 'admin' | 'teamLead' | 'teamMember' */
        role: string
        /** User status: 'pending' | 'approved' | 'rejected' */
        status: string
        /** Convex _id of the team the user belongs to */
        teamId: string | null
        /** Convex _id of the event the user is participating in */
        eventId: string | null
    }
}

// Add type literals for role and status
export type UserRole = 'admin' | 'teamLead' | 'teamMember'
export type UserStatus = 'pending' | 'approved' | 'rejected'
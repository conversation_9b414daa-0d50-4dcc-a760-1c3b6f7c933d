export interface BaseLeaderboardItem {
    eventName: string;
    sessionName: string;
    sessionId: string;
    totalScore: number;
    voteCount: number;
}

export interface IdeaLeaderboardItem extends BaseLeaderboardItem {
    ideaId: string;
    ideaName: string;
    ideaDescription: string;
    teamName: string;
}

export interface QuickfireLeaderboardItem extends BaseLeaderboardItem {
    quickfireId: string;
    idea: string;
    comments: string;
    question?: string;
}

export type LeaderboardItem = IdeaLeaderboardItem | QuickfireLeaderboardItem;

export interface SessionDetails {
    name: string;
    eventName: string;
    type: 'Ideas' | 'Quickfire';
}

export interface LeaderboardResponse {
    leaderboard: LeaderboardItem[];
    session?: SessionDetails;
    message?: string;
}

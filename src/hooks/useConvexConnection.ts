import { useEffect, useState } from 'react';
import { useConvex } from 'convex/react';

export function useConvexConnection() {
  const convex = useConvex();
  const [connectionState, setConnectionState] = useState<'connected' | 'disconnected' | 'reconnecting'>('connected');

  useEffect(() => {
    // Monitor connection state changes
    // For now, assume connected state - future enhancement point
    setConnectionState('connected');

    // Add connection monitoring logic here when Convex client exposes connection events
    return () => {
      // Cleanup
    };
  }, [convex]);

  return connectionState;
}
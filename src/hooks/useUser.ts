import { useSession } from 'next-auth/react';

export interface User {
  _id: string;
  id: string;
  username: string;
  name: string;
  role: 'admin' | 'teamLead' | 'teamMember';
  status: 'pending' | 'approved' | 'rejected';
  teamId: string | null;
  eventId: string | null;
}

export function useUser() {
  const { data: session, status } = useSession();
  
  const user: User | null = session?.user ? {
    _id: session.user.id,
    id: session.user.id,
    username: session.user.username || '',
    name: session.user.name || '',
    role: session.user.role as 'admin' | 'teamLead' | 'teamMember',
    status: session.user.status as 'pending' | 'approved' | 'rejected',
    teamId: session.user.teamId,
    eventId: session.user.eventId,
  } : null;

  return {
    user,
    isLoading: status === 'loading',
    isAuthenticated: status === 'authenticated',
  };
}
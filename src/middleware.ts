import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';
import { getToken } from 'next-auth/jwt';
import { debugError } from '@/lib/utils';

// Configure the middleware to run on specific paths
export const config = {
    matcher: [
        //'/((?!manifest\\.json|manifest\\.webmanifest|sw\\.js|_next/static|_next/image|favicon\\.ico|icon-.*\\.png|apple-touch-icon.*\\.png|offline).*)',
        // Admin pages
        '/admin/:path*',
        
        // User pages
        '/user/:path*',

        // Protected API routes (excluding socket and events/active check)
        '/api/settings/:path*',     // All settings routes
        '/api/admin/:path*',        // Admin-specific endpoints
        '/api/users/:path*',        // All user routes
        '/api/teams/:path*',        // All team routes
        '/api/sessions/:path*',     // All session routes
        '/api/ideas/:path*',        // All ideas routes
        '/api/votes/:path*',        // All voting routes
    ]
};

export async function middleware(request: NextRequest) {
    try {
        // Skip middleware for socket connections
        if (request.headers.get('upgrade') === 'websocket') {
            return NextResponse.next();
        }

        const token = await getToken({ 
            req: request,
            secret: process.env.NEXTAUTH_SECRET,
            secureCookie: process.env.NODE_ENV === 'production',
            cookieName: 'next-auth.session-token'
        });
        
        //debugError('Token in middleware:', token); // Add debug log
        //debugError('Cookie header:', request.headers.get('cookie')); // Add cookie debug
        
        const pathname = request.nextUrl.pathname;
        // Skip auth check for public endpoints
        if (pathname === '/api/events/active' || pathname === '/api/users/check-admin') {
            return NextResponse.next();
        }

        
        // Determine route type
        const isAdminPage = pathname.startsWith('/admin');
        const isUserPage = pathname.startsWith('/user');
        const isProtectedApiRoute = pathname.startsWith('/api/');
        
        // If no token exists
        if (!token) {
            return handleUnauthorized(isAdminPage || isUserPage, request.url);
        }

        // For admin pages and protected admin API routes, verify admin role
        if (isAdminPage || (isProtectedApiRoute && pathname.includes('/api/admin'))) {
            if (token.role !== 'admin') {
                return handleUnauthorized(isAdminPage, request.url, 'Admin access required');
            }
        }

        // For user pages, verify user is not an admin
        if (isUserPage && token.role === 'admin') {
            return NextResponse.redirect(new URL('/admin', request.url));
        }

        // For user pages and protected user API routes, verify user is approved
        if ((isUserPage || isProtectedApiRoute) && token.role !== 'admin') {
            if (token.status !== 'approved') {
                return handleUnauthorized(isUserPage, request.url, 'Account not approved');
            }
        }

        // Session is valid, proceed
        return NextResponse.next();
    } catch (error) {
        debugError('Middleware error:', error);
        return handleUnauthorized(false, request.url, 'Internal server error');
    }
}

// Helper functions for consistent response handling
function handleUnauthorized(isPageRoute: boolean, requestUrl: string, message: string = 'Unauthorized') {
    if (isPageRoute) {
        const url = new URL('/login', requestUrl);
        url.searchParams.set('error', message);
        return NextResponse.redirect(url);
    }
    return NextResponse.json(
        { error: message },
        { 
            status: 401,
            headers: {
                'Content-Type': 'application/json',
                'Cache-Control': 'no-store'
            }
        }
    );
}
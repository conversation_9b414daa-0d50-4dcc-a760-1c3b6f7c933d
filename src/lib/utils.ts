import { clsx, type ClassValue } from "clsx"
import { twMerge } from "tailwind-merge"

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

export function getBaseUrl() {
  return process.env.NEXTAUTH_URL || 'http://localhost:3000';
}

export const debug = (...args: unknown[]) => {
   if (process.env.NODE_ENV === 'development') {
    console.log(...args);
}
}

export const debugError = (...args: unknown[]) => {
  if (process.env.NODE_ENV === 'development') {
  console.error(...args);
  }
}

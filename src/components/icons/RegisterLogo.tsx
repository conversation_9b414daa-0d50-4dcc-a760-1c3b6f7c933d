interface RegisterLogoProps {
  className?: string;
  size?: number;
}

export default function RegisterLogo({ className, size = 150 }: RegisterLogoProps) {
  return (
    <svg 
      className={className}
      width={size} 
      height={size} 
      viewBox="0 0 682.87 637.84" 
      xmlns="http://www.w3.org/2000/svg"
      fill="currentColor"
    >
      <g>
        <path d="M341.48,363.45c-39.99,0-72.55,32.56-72.55,72.55v30.77c0,11.76,2.87,22.87,7.86,32.7,2.49-1.98,5.08-3.81,7.76-5.55-3.95-8.23-6.26-17.46-6.26-27.15v-30.77c0-34.82,28.32-63.14,63.14-63.14s63.14,28.32,63.14,63.14v30.77c0,9.6-2.21,18.68-6.07,26.82,2.68,1.74,5.27,3.53,7.81,5.46,4.89-9.74,7.67-20.7,7.67-32.32v-30.77c0-39.99-32.56-72.55-72.55-72.55l.05.05Z" fill="none"/>
        <path d="M341.57,538.19c-20.32.09-38.77-8-52.27-21.17,13.22,13.69,31.71,22.25,52.18,22.25,25.97,0,48.7-13.74,61.54-34.3-13.27,19.85-35.8,33.08-61.45,33.22h0Z" fill="none"/>
        <path d="M393.85,488.6c3.53-7.48,5.55-15.81,5.55-24.61v-28.23c0-31.95-25.97-57.92-57.92-57.92s-57.92,25.97-57.92,57.92v28.23c0,8.94,2.07,17.36,5.74,24.94,14.96-9.74,32.79-15.53,51.99-15.62,19.38-.09,37.4,5.55,52.6,15.29h-.05Z"/>
        <path d="M682.87,268.32l-82.9-70.24-44.98,14.07-4.23,49.17-84.31,39.33v64.65h-46.34l11.24-139.08,160.15-70.24,2.82-81.49L479.1,0l-137.66,40.32L203.77,0l-115.22,74.48,2.82,81.49,160.15,70.24,11.24,139.08h-46.34v-64.65l-84.31-39.33-4.23-49.17-44.98-14.07L0,268.32l122.23,103.98-26.68,193.89,102.57,71.66,105.39-61.82h75.84l105.39,61.82,102.57-71.66-26.68-193.89,122.23-103.98h0ZM414.03,466.77c0,11.62-2.82,22.58-7.67,32.32.05.05.14.09.19.14-1.08,1.98-2.26,3.91-3.53,5.79-12.8,20.56-35.57,34.3-61.54,34.3-20.47,0-38.96-8.56-52.18-22.25-5.18-5.03-9.6-10.77-13.13-17.08.19-.19.42-.33.66-.47-4.99-9.83-7.86-20.94-7.86-32.7v-30.77c0-39.99,32.56-72.55,72.55-72.55s72.55,32.56,72.55,72.55v30.77l-.05-.05Z"/>
        <path d="M406.55,499.19s-.14-.09-.19-.14c-1.03,2.02-2.12,4-3.34,5.93,1.27-1.88,2.45-3.76,3.53-5.79Z"/>
        <path d="M276.18,499.94c3.53,6.35,8,12.09,13.13,17.08-4.99-5.18-9.22-11.1-12.52-17.6-.19.19-.42.33-.66.47l.05.05Z"/>
      </g>
    </svg>
  );
}
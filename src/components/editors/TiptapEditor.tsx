import React, { forwardRef, useImperativeHandle, useEffect, useState, useCallback, useRef } from 'react';
import { useEdit<PERSON>, EditorContent, Editor, JSONContent } from '@tiptap/react';
import StarterKit from '@tiptap/starter-kit';
import TextAlign from '@tiptap/extension-text-align';
import Highlight from '@tiptap/extension-highlight';
import Placeholder from '@tiptap/extension-placeholder';
import { generateHTML } from '@tiptap/html';
import { AISelector } from './ai/AISelector';
import SnippetsMenu from './SnippetsMenu';
import { Button } from '@/components/ui/button';
import {
    Dialog,
    DialogContent,
    DialogDescription,
    DialogHeader,
    DialogTitle,
} from '@/components/ui/dialog';
import {
    Bold, Italic, Strikethrough, Code, List, ListOrdered,
    Quote, AlignLeft, AlignCenter, AlignRight, AlignJustify,
    Undo, Redo, Minus, FileCode, FileText, Sparkles
} from 'lucide-react';

// Define base extensions used by the editor (needed for generateHTML)
const baseEditorExtensions = [
    StarterKit.configure({
        bulletList: { keepMarks: true, keepAttributes: false },
        orderedList: { keepMarks: true, keepAttributes: false },
    }),
    TextAlign.configure({ types: ['heading', 'paragraph'] }),
    Highlight.configure({ multicolor: true }), // Add Highlight extension
    // BubbleMenu is used via the AIBubbleMenu component, no need to configure here directly unless for other menus
];

// Function to create extensions with placeholder support
const createEditorExtensions = (placeholder?: string) => [
    ...baseEditorExtensions,
    ...(placeholder ? [Placeholder.configure({ placeholder })] : [])
];

interface TiptapEditorProps {
    content: string | undefined; // Input: stringified JSON or HTML
    onChange: (jsonString: string) => void; // Output: stringified JSON
    editable?: boolean;
    className?: string;
    placeholder?: string;
    fullscreen?: boolean; // Add fullscreen prop to override height constraints
}

// Exposed methods via ref handle
export interface TiptapEditorHandle {
  clear: () => void;
  setContent: (content: string | JSONContent, emitUpdate?: boolean) => void; // Add setContent
  getContent: () => string; // Add getContent to get JSON string
}

// Renders stringified Tiptap JSON to HTML, falling back to original string if invalid
export const renderTiptapJsonStringToHtml = (jsonString: string | undefined | null): string => {
    if (!jsonString) {
        return '';
    }
    try {
        const jsonContent: JSONContent = JSON.parse(jsonString);
        // Check if parsed content looks like valid Tiptap JSON
        if (jsonContent && typeof jsonContent === 'object' && jsonContent.type) {
            return generateHTML(jsonContent, baseEditorExtensions);
        }
        // Fallback: If not valid JSON, return original string (might be HTML/text)
        // Warning: Ensure sanitization if rendering this fallback directly elsewhere
        return jsonString;
    } catch {
        // Fallback: If JSON parsing fails, return original string (might be HTML/text)
        // Warning: Ensure sanitization if rendering this fallback directly elsewhere
        return jsonString;
    }
};


// Main Editor Component using forwardRef for the handle
const TiptapEditor = forwardRef<TiptapEditorHandle, TiptapEditorProps>(({
    content,
    onChange,
    editable = true,
    className = '',
    placeholder,
    fullscreen = false,
}, ref) => {

    const [isSnippetsMenuOpen, setIsSnippetsMenuOpen] = useState(false);
   const snippetsButtonRef = useRef<HTMLButtonElement>(null); // Ref for positioning
    const [isAISelectorOpen, setIsAISelectorOpen] = useState(false);
    const aiButtonRef = useRef<HTMLButtonElement>(null); // Ref for AI button positioning

   const editor = useEditor({
       extensions: createEditorExtensions(placeholder),
       // Initialize editor: try parsing content as JSON, fallback to string (HTML/text)
        content: (() => {
            if (!content) return '';
            try {
                const parsedJson = JSON.parse(content);
                if (parsedJson && typeof parsedJson === 'object' && parsedJson.type) {
                    return parsedJson; // Use parsed JSON
                }
                // Parsed but not valid Tiptap JSON, treat as original string
            } catch {
                // JSON parsing failed, treat as original string
            }
            const initialContent = content; 
            return initialContent; // Return original string (HTML/text)
        })(),
        editable: editable,
        immediatelyRender: false, // Avoid SSR hydration issues
        onUpdate: ({ editor }) => {
            // Emit stringified JSON on change
            onChange(JSON.stringify(editor.getJSON()));
        },
        editorProps: {
            attributes: {
                class: `flex-1 min-h-[300px] text-foreground font-sans text-base leading-relaxed break-words outline-none prose prose-invert max-w-none ${className}`,
            },
        },
    });

    // Expose methods via ref
    useImperativeHandle(ref, () => ({
        clear: () => {
            editor?.commands.clearContent(true);
        },
        // Expose setContent command
        setContent: (content: string | JSONContent, emitUpdate: boolean = false) => {
             if (!editor || editor.isDestroyed) return;
             // Attempt to parse if string looks like JSON, otherwise treat as string (HTML/text)
             let finalContent: JSONContent | string = content;
             if (typeof content === 'string') {
                 try {
                     const parsedJson = JSON.parse(content);
                     // Basic check if it looks like Tiptap JSON
                     if (parsedJson && typeof parsedJson === 'object' && parsedJson.type) {
                         finalContent = parsedJson;
                     }
                 } catch {
                     // Parsing error, treat as string (HTML/text)
                 }
             }
             editor.commands.setContent(finalContent, emitUpdate);
        },
        // Expose getContent method to get JSON string
        getContent: () => {
            if (!editor || editor.isDestroyed) return '';
            return JSON.stringify(editor.getJSON());
        }
    }), [editor]);

    // Update editor content if 'content' prop changes externally (e.g., on edit)
    useEffect(() => {
        if (!editor || editor.isDestroyed) return;

        // Determine the format of the incoming prop (JSON object or string)
        let newContent: JSONContent | string = '';
        let isIncomingJson = false;
        let incomingJsonString = ''; // String representation of incoming content

        if (content) {
            try {
                const parsedJson = JSON.parse(content);
                if (parsedJson && typeof parsedJson === 'object' && parsedJson.type) {
                    newContent = parsedJson;
                    isIncomingJson = true;
                    incomingJsonString = content; // Already stringified JSON
                } else {
                    newContent = content; // Treat as HTML/text string
                    incomingJsonString = content;
                }
            } catch {
                newContent = content; // Treat as HTML/text string if parse fails
                incomingJsonString = content;
            }
        } else {
             incomingJsonString = ''; // Handle empty/null content prop
        }


        // Get current editor content for comparison
        const currentHTML = editor.getHTML();
        const currentJSONString = JSON.stringify(editor.getJSON());

        // Avoid update if content hasn't changed significantly
        // Compare JSON string if incoming is JSON, otherwise compare HTML string
        if ((isIncomingJson && incomingJsonString === currentJSONString) ||
            (!isIncomingJson && incomingJsonString === currentHTML)) {
            return; // Content is the same, no need to update
        }

        // Set editor content if changed, avoiding triggering 'onUpdate'
        editor.commands.setContent(newContent, false);

    }, [content, editor]); // Dependencies: run if content prop or editor instance changes

    // --- Snippet Menu Logic --- (Hooks & Callbacks defined BEFORE conditional return)
    const toggleSnippetsMenu = useCallback(() => {
        setIsSnippetsMenuOpen(prev => !prev);
    }, []);

    const closeSnippetsMenu = useCallback(() => {
        setIsSnippetsMenuOpen(false);
        // Blur the editor to prevent auto-focus when modal closes
        if (editor && document.activeElement) {
            (document.activeElement as HTMLElement).blur();
        }
    }, [editor]);


    // --- AI Selector Logic ---
    const toggleAISelector = useCallback(() => {
        // Close snippets menu if open
        if (isSnippetsMenuOpen) setIsSnippetsMenuOpen(false);
        setIsAISelectorOpen(prev => !prev);
    }, [isSnippetsMenuOpen]);

    const closeAISelector = useCallback(() => {
        setIsAISelectorOpen(false);
    }, []);


    // Conditional return MUST come AFTER all hook calls
    if (!editor) {
        return null;
    }

    // --- Component Return ---
    return (
        <div className={`bg-background border-2 border-primary text-foreground transition-colors duration-200 p-0 flex flex-col focus-within:border-secondary min-w-0 ${
            fullscreen 
                ? 'h-full max-h-none' 
                : 'h-[50vh] max-h-[50vh] sm:h-[500px] sm:max-h-[500px]'
        }`}>
            {/* Render Toolbar */}
            {editable && editor && <EditorToolbar editor={editor} toggleSnippetsMenu={toggleSnippetsMenu} snippetsButtonRef={snippetsButtonRef} toggleAISelector={toggleAISelector} aiButtonRef={aiButtonRef} />}


           {/* Render Snippets Menu using Dialog */}
           <Dialog open={editable && isSnippetsMenuOpen} onOpenChange={(open: boolean) => {
               if (!open) closeSnippetsMenu();
           }}>
               <DialogContent className="max-w-2xl max-h-[80vh] overflow-hidden flex flex-col">
                   <DialogHeader>
                       <DialogTitle className="flex items-center gap-2">
                           <FileText className="h-4 w-4 text-secondary" />
                           Templates
                       </DialogTitle>
                       <DialogDescription>
                           Select a template to insert into your content.
                       </DialogDescription>
                   </DialogHeader>
                   <div className="flex-1 overflow-y-auto">
                       <SnippetsMenu
                           editor={editor}
                           onClose={() => {
                               closeSnippetsMenu();
                               onChange(JSON.stringify(editor.getJSON()));
                           }}
                       />
                   </div>
               </DialogContent>
           </Dialog>

           {/* Render AI Selector using Dialog */}
           <Dialog open={editable && isAISelectorOpen} onOpenChange={(open: boolean) => {
               if (!open) closeAISelector();
           }}>
               <DialogContent className="max-w-md max-h-[80vh] overflow-hidden flex flex-col p-0 focus:outline-none">
                   <DialogHeader className="p-6 pb-0">
                       <DialogTitle className="flex items-center gap-2">
                           <Sparkles className="h-4 w-4 text-secondary" />
                           Ask AI
                       </DialogTitle>
                       <DialogDescription>
                           Get AI assistance with your content writing and editing.
                       </DialogDescription>
                   </DialogHeader>
                   <div className="flex-1 overflow-hidden p-0">
                       <AISelector
                           editor={editor}
                           open={isAISelectorOpen}
                           onOpenChange={setIsAISelectorOpen}
                       />
                   </div>
               </DialogContent>
           </Dialog>

            {/* Render Editor Content */}
            <div className="flex-1 overflow-hidden">
                <EditorContent 
                    editor={editor} 
                    className="tiptap-content prose prose-invert max-w-none p-2 h-full overflow-y-auto text-foreground leading-relaxed [&>.ProseMirror]:outline-none [&>.ProseMirror]:border-none [&>.ProseMirror]:bg-transparent [&>.ProseMirror]:p-2 [&>.ProseMirror]:m-0 [&>.ProseMirror]:h-full [&>.ProseMirror]:w-full [&>.ProseMirror]:text-foreground [&>.ProseMirror]:cursor-text [&_.ProseMirror>:first-child]:mt-0 [&_.ProseMirror>:last-child]:mb-0 [&_p]:mb-2 [&_p]:min-h-[1.5em] [&_p]:text-foreground [&_p]:font-sans [&_strong]:font-bold [&_strong]:text-foreground [&_em]:italic [&_em]:text-foreground [&_h1]:font-black [&_h1]:text-foreground [&_h1]:text-3xl [&_h1]:mt-2 [&_h1]:mb-2 [&_h1]:leading-tight [&_h2]:[font-family:var(--font-ultrabold)] [&_h2]:text-foreground [&_h2]:text-2xl [&_h2]:mt-2 [&_h2]:mb-2 [&_h2]:leading-tight [&_h3]:[font-family:var(--font-ultrabold)] [&_h3]:text-foreground [&_h3]:text-lg [&_h3]:mt-2 [&_h3]:mb-2 [&_h3]:leading-tight [&_ul]:pl-8 [&_ul]:mb-4 [&_ul]:list-disc [&_ul]:font-ultrabold [&_ol]:pl-8 [&_ol]:mb-4 [&_ol]:list-decimal [&_ol]:font-ultrabold [&_li]:mb-1 [&_li]:font-Ultrabold [&_li>p]:mb-1 [&_li>p]:font-sans [&_blockquote]:border-l-4 [&_blockquote]:border-primary [&_blockquote]:ml-0 [&_blockquote]:pl-4 [&_blockquote]:text-muted-foreground [&_blockquote]:italic [&_blockquote]:my-4 [&_blockquote]:font-mono [&_pre]:bg-muted [&_pre]:border [&_pre]:border-border [&_pre]:text-foreground [&_pre]:p-3 [&_pre]:rounded-lg [&_pre]:my-4 [&_pre]:whitespace-pre-wrap [&_pre]:break-all [&_pre]:[font-family:'neue-machina-Medium',monospace] [&_code]:bg-primary/20 [&_code]:rounded [&_code]:px-1 [&_code]:py-0.5 [&_code]:text-sm [&_code]:text-primary [&_code]:[font-family:'neue-machina-Medium',monospace] [&_pre_code]:bg-transparent [&_pre_code]:p-0 [&_pre_code]:rounded-none [&_pre_code]:text-foreground [&_hr]:block [&_hr]:border-none [&_hr]:border-t-2 [&_hr]:border-border [&_hr]:my-6 [&_hr]:w-full [&_hr]:h-0" 
                />
            </div>
        </div>
    );
});

// Set display name for DevTools
TiptapEditor.displayName = 'TiptapEditor';

// Editor Toolbar Component Props
interface EditorToolbarProps {
    editor: Editor;
    toggleSnippetsMenu: () => void;
    snippetsButtonRef: React.RefObject<HTMLButtonElement | null>; // Allow null for initial ref value
    // Add props for AI Selector
    toggleAISelector: () => void;
    aiButtonRef: React.RefObject<HTMLButtonElement | null>;
}

// Editor Toolbar Component
const EditorToolbar: React.FC<EditorToolbarProps> = ({ editor, toggleSnippetsMenu, snippetsButtonRef, toggleAISelector, aiButtonRef }) => {
    return (
        <div className="w-0 min-w-full p-2 border-b border-border bg-background">
            <div className="flex items-center gap-2 overflow-x-auto scrollbar-none [-ms-overflow-style:none] [scrollbar-width:none] [&::-webkit-scrollbar]:hidden">
            {/* Heading Buttons */}
            <Button
                type="button"
                variant="ghost"
                size="sm"
                onClick={() => editor.chain().focus().toggleHeading({ level: 1 }).run()}
                className={`w-7 p-0 text-sm font-bold text-muted-foreground   ${
                    editor.isActive('heading', { level: 1 }) ? 'text-secondary bg-secondary/20' : ''
                }`}
                title="Heading 1"
            >
                H1
            </Button>
            <Button
                type="button"
                variant="ghost"
                size="sm"
                onClick={() => editor.chain().focus().toggleHeading({ level: 2 }).run()}
                className={`w-7 p-0 text-sm font-bold text-muted-foreground   ${
                    editor.isActive('heading', { level: 2 }) ? 'text-secondary bg-secondary/20' : ''
                }`}
                title="Heading 2"
            >
                H2
            </Button>
            <Button
                type="button"
                variant="ghost"
                size="sm"
                onClick={() => editor.chain().focus().toggleHeading({ level: 3 }).run()}
                className={`w-7 p-0 text-sm font-bold text-muted-foreground   ${
                    editor.isActive('heading', { level: 3 }) ? 'text-secondary bg-secondary/20' : ''
                }`}
                title="Heading 3"
            >
                H3
            </Button>
            
            {/* Separator */}
            <div className="w-px h-5 bg-border mx-1"></div>
            
            {/* Text Formatting */}
            <Button
                type="button"
                variant="ghost"
                size="sm"
                onClick={() => editor.chain().focus().toggleBold().run()}
                disabled={!editor.can().chain().focus().toggleBold().run()}
                className={`h-7 w-7 p-0 text-muted-foreground   ${
                    editor.isActive('bold') ? 'text-secondary bg-secondary/20' : ''
                }`}
                title="Bold"
            >
                <Bold className="h-4 w-4" />
            </Button>
            <Button
                type="button"
                variant="ghost"
                size="sm"
                onClick={() => editor.chain().focus().toggleItalic().run()}
                disabled={!editor.can().chain().focus().toggleItalic().run()}
                className={`h-7 w-7 p-0 text-muted-foreground   ${
                    editor.isActive('italic') ? 'text-secondary bg-secondary/20' : ''
                }`}
                title="Italic"
            >
                <Italic className="h-4 w-4" />
            </Button>
            <Button
                type="button"
                variant="ghost"
                size="sm"
                onClick={() => editor.chain().focus().toggleStrike().run()}
                disabled={!editor.can().chain().focus().toggleStrike().run()}
                className={`h-7 w-7 p-0 text-muted-foreground   ${
                    editor.isActive('strike') ? 'text-secondary bg-secondary/20' : ''
                }`}
                title="Strikethrough"
            >
                <Strikethrough className="h-4 w-4" />
            </Button>
            <Button
                type="button"
                variant="ghost"
                size="sm"
                onClick={() => editor.chain().focus().toggleCode().run()}
                disabled={!editor.can().chain().focus().toggleCode().run()}
                className={`h-7 w-7 p-0 text-muted-foreground   ${
                    editor.isActive('code') ? 'text-secondary bg-secondary/20' : ''
                }`}
                title="Code"
            >
                <Code className="h-4 w-4" />
            </Button>
            
            {/* Separator */}
            <div className="w-px h-5 bg-border mx-1"></div>
            
            {/* Lists */}
            <Button
                type="button"
                variant="ghost"
                size="sm"
                onClick={() => editor.chain().focus().toggleBulletList().run()}
                className={`h-7 w-7 p-0 text-muted-foreground   ${
                    editor.isActive('bulletList') ? 'text-secondary bg-secondary/20' : ''
                }`}
                title="Bullet List"
            >
                <List className="h-4 w-4" />
            </Button>
            <Button
                type="button"
                variant="ghost"
                size="sm"
                onClick={() => editor.chain().focus().toggleOrderedList().run()}
                className={`h-7 w-7 p-0 text-muted-foreground   ${
                    editor.isActive('orderedList') ? 'text-secondary bg-secondary/20' : ''
                }`}
                title="Ordered List"
            >
                <ListOrdered className="h-4 w-4" />
            </Button>
            
            {/* Separator */}
            <div className="w-px h-5 bg-border mx-1"></div>
            
            {/* Block Elements */}
            <Button
                type="button"
                variant="ghost"
                size="sm"
                onClick={() => editor.chain().focus().toggleCodeBlock().run()}
                className={`h-7 w-7 p-0 text-muted-foreground   ${
                    editor.isActive('codeBlock') ? 'text-secondary bg-secondary/20' : ''
                }`}
                title="Code Block"
            >
                <FileCode className="h-4 w-4" />
            </Button>
            <Button
                type="button"
                variant="ghost"
                size="sm"
                onClick={() => editor.chain().focus().toggleBlockquote().run()}
                className={`h-7 w-7 p-0 text-muted-foreground   ${
                    editor.isActive('blockquote') ? 'text-secondary bg-secondary/20' : ''
                }`}
                title="Blockquote"
            >
                <Quote className="h-4 w-4" />
            </Button>
            <Button
                type="button"
                variant="ghost"
                size="sm"
                onClick={() => editor.chain().focus().setHorizontalRule().run()}
                className="h-7 w-7 p-0 text-muted-foreground  "
                title="Horizontal Rule"
            >
                <Minus className="h-4 w-4" />
            </Button>
            
            {/* Separator */}
            <div className="w-px h-5 bg-border mx-1"></div>
            
            {/* Text Alignment */}
            <Button
                type="button"
                variant="ghost"
                size="sm"
                onClick={() => editor.chain().focus().setTextAlign('left').run()}
                className={`h-7 w-7 p-0 text-muted-foreground   ${
                    editor.isActive({ textAlign: 'left' }) ? 'text-secondary bg-secondary/20' : ''
                }`}
                title="Align Left"
            >
                <AlignLeft className="h-4 w-4" />
            </Button>
            <Button
                type="button"
                variant="ghost"
                size="sm"
                onClick={() => editor.chain().focus().setTextAlign('center').run()}
                className={`h-7 w-7 p-0 text-muted-foreground   ${
                    editor.isActive({ textAlign: 'center' }) ? 'text-secondary bg-secondary/20' : ''
                }`}
                title="Align Center"
            >
                <AlignCenter className="h-4 w-4" />
            </Button>
            <Button
                type="button"
                variant="ghost"
                size="sm"
                onClick={() => editor.chain().focus().setTextAlign('right').run()}
                className={`h-7 w-7 p-0 text-muted-foreground   ${
                    editor.isActive({ textAlign: 'right' }) ? 'text-secondary bg-secondary/20' : ''
                }`}
                title="Align Right"
            >
                <AlignRight className="h-4 w-4" />
            </Button>
            <Button
                type="button"
                variant="ghost"
                size="sm"
                onClick={() => editor.chain().focus().setTextAlign('justify').run()}
                className={`h-7 w-7 p-0 text-muted-foreground   ${
                    editor.isActive({ textAlign: 'justify' }) ? 'text-secondary bg-secondary/20' : ''
                }`}
                title="Align Justify"
            >
                <AlignJustify className="h-4 w-4" />
            </Button>
            
            {/* Separator */}
            <div className="w-px h-5 bg-border mx-1"></div>
            
            {/* Snippets Button */}
            <Button
                ref={snippetsButtonRef}
                type="button"
                variant="ghost"
                size="sm"
                onClick={toggleSnippetsMenu}
                className="h-7 w-7 p-0 text-muted-foreground  "
                title="Snippets"
            >
                <FileText className="h-4 w-4" />
            </Button>
            
            {/* AI Selector Button */}
            <Button
                ref={aiButtonRef}
                type="button"
                variant="ghost"
                size="sm"
                onClick={toggleAISelector}
                className="h-7 w-7 p-0 text-muted-foreground  "
                title="Ask AI"
            >
                <Sparkles className="h-4 w-4" />
            </Button>

            {/* History Group - Right Aligned */}
            <div className="ml-auto flex items-center gap-2">
                <div className="w-px h-5 bg-border mx-1"></div>
                <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    onClick={() => editor.chain().focus().undo().run()}
                    disabled={!editor.can().chain().focus().undo().run()}
                    className="h-7 w-7 p-0 text-muted-foreground   disabled:opacity-40 disabled:cursor-not-allowed"
                    title="Undo"
                >
                    <Undo className="h-4 w-4" />
                </Button>
                <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    onClick={() => editor.chain().focus().redo().run()}
                    disabled={!editor.can().chain().focus().redo().run()}
                    className="h-7 w-7 p-0 text-muted-foreground   disabled:opacity-40 disabled:cursor-not-allowed"
                    title="Redo"
                >
                    <Redo className="h-4 w-4" />
                </Button>
            </div>
        </div>
        </div>
    );
};

export default TiptapEditor;
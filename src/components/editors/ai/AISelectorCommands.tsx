import React from 'react';
import { Editor } from '@tiptap/react';
import {
    ArrowDownWideNarrow, CheckCheck, RefreshCcwDot, StepForward, WrapText
} from 'lucide-react';
import { toast } from 'sonner'; // Import toast

// Define the structure for AI command options
// Export the interface so it can be imported elsewhere
export interface CommandOption {
    value: 'improve' | 'fix' | 'shorter' | 'longer' | 'continue';
    label: string;
    icon: React.ElementType; // Lucide icons are components
}

// Define the command options array
const options: CommandOption[] = [
    { value: "improve", label: "Improve writing", icon: RefreshCcwDot },
    { value: "fix", label: "Fix grammar", icon: CheckCheck },
    { value: "shorter", label: "Make shorter", icon: ArrowDownWideNarrow },
    { value: "longer", label: "Make longer", icon: WrapText },
];

const continueOption: CommandOption = {
    value: "continue", label: "Continue writing", icon: StepForward
};

// Helper function to get text before the current selection/cursor
// Tiptap doesn't have a direct equivalent to novel's getPrevText,
// so we get text between start and selection 'from'. Adjust length as needed.
const getPreviousText = (editor: Editor, characters: number = 5000): string => {
    const { from } = editor.state.selection;
    const start = Math.max(0, from - characters); // Ensure start is not negative
    return editor.state.doc.textBetween(start, from, "\n\n"); // Use newline separator
};

// Helper function to get the currently selected text
const getSelectedText = (editor: Editor): string => {
    const { from, to, empty } = editor.state.selection;
    if (empty) return ""; // No text selected
    return editor.state.doc.textBetween(from, to, " "); // Use space separator
};


interface AISelectorCommandsProps {
    editor: Editor | null; // Editor can be null initially
    onSelect: (prompt: string, option: CommandOption['value']) => void;
    // Add basic styling via className props if needed
    buttonClassName?: string;
    iconClassName?: string;
    groupHeadingClassName?: string;
    separatorClassName?: string;
}

const AISelectorCommands: React.FC<AISelectorCommandsProps> = ({
    editor,
    onSelect,
    buttonClassName = "flex items-center gap-2 w-full p-2 bg-transparent border-none text-foreground cursor-pointer text-sm font-mono text-left transition-colors hover:bg-muted/20",
    iconClassName = "w-4 h-4 text-secondary flex-shrink-0",
    groupHeadingClassName = "text-xs text-muted-foreground uppercase px-2.5 pt-2.5 pb-1 font-bold",
    separatorClassName = "border-none border-t border-border my-1.5"
}) => {
    if (!editor) return null; // Don't render if editor is not available

    const handleSelect = (optionValue: CommandOption['value']) => {
        let text = "";
        let requiredTextType = ""; // To customize the error message

        // Check if there's selected text
        const selectedText = getSelectedText(editor);
        
        if (selectedText && selectedText.trim() !== "") {
            // If there's selected text, use it for all options
            text = selectedText;
            requiredTextType = "selected text";
        } else if (optionValue === 'continue') {
            // For "continue" with no selection, use preceding text
            text = getPreviousText(editor);
            requiredTextType = "preceding text";
        } else {
            // For other options with no selection, use all text in the editor
            text = editor.getText();
            requiredTextType = "text in editor";
        }

        // Check if the necessary text is empty
        if (!text || text.trim() === "") {
            const actionLabel = options.find(o => o.value === optionValue)?.label || continueOption.label;
            // Adjust message slightly for clarity
            toast.warning(`Cannot "${actionLabel}". No ${requiredTextType} available.`);
            console.warn(`AI command '${optionValue}' aborted: No ${requiredTextType} available.`);
            return; // Stop execution if text is empty
        }

        // Proceed with the selection if text is valid
        onSelect(text, optionValue);
    };

    return (
        <div className="space-y-1">
            <div className={groupHeadingClassName}>Edit or review selection</div>
            {options.map((option) => (
                <button
                    type="button"
                    key={option.value}
                    onClick={() => handleSelect(option.value)}
                    className={buttonClassName}
                    title={option.label}
                >
                    <option.icon className={iconClassName} aria-hidden="true" />
                    <span>{option.label}</span>
                </button>
            ))}

            <hr className={separatorClassName} />

            <div className={groupHeadingClassName}>Use AI to do more</div>
            <button
                type="button"
                key={continueOption.value}
                onClick={() => handleSelect(continueOption.value)}
                className={buttonClassName}
                title={continueOption.label}
            >
                <continueOption.icon className={iconClassName} aria-hidden="true" />
                <span>{continueOption.label}</span>
            </button>
        </div>
    );
};

export default AISelectorCommands;
// @refresh reset
"use client"; // Required for hooks like useState, useCompletion

import React, { useState, useCallback } from 'react';
import { Editor } from '@tiptap/react';
import { useCompletion } from 'ai/react';
import { <PERSON>U<PERSON>, <PERSON><PERSON>2, <PERSON>lace, Trash2, <PERSON><PERSON><PERSON> } from 'lucide-react'; // Using Loader2 for spinner
import Markdown from 'markdown-to-jsx';
import { toast } from 'sonner'; // Assuming sonner is set up globally
import { marked } from 'marked';

import AISelectorCommands from './AISelectorCommands'; // Import the commands component
import type { CommandOption } from './AISelectorCommands'; // Import type if needed elsewhere

// Helper to get selected text (can be moved to utils if shared)
const getSelectedText = (editor: Editor): string => {
    const { from, to, empty } = editor.state.selection;
    if (empty) return "";
    return editor.state.doc.textBetween(from, to, " ");
};

interface AISelectorProps {
    editor: Editor | null;
    open: boolean; // Keep track if the selector is open
    onOpenChange: (open: boolean) => void; // Function to close the selector
}

export function AISelector({
    editor,
    onOpenChange,
}: AISelectorProps) {
    // Define styles as constants to avoid repetition
    const containerClassName = "flex flex-col gap-2 p-5 bg-background text-foreground font-mono h-full overflow-hidden";
    const inputClassName = "w-full p-2 bg-muted border border-border text-foreground font-mono text-sm outline-none resize-vertical min-h-[40px] leading-normal block box-border focus:border-ring";
    const buttonClassName = "inline-flex items-center justify-center gap-1.5 px-2.5 py-1.5 bg-muted border border-border text-foreground cursor-pointer text-sm font-mono transition-colors hover:bg-muted/80 hover:border-ring";
    const loadingClassName = "flex items-center justify-center gap-2 p-4 text-sm text-muted-foreground";
    const completionAreaClassName = "max-h-[200px] overflow-y-auto text-sm leading-relaxed text-foreground p-0 font-sans";
    const completionActionsClassName = "flex gap-2 mt-2";
    const [inputValue, setInputValue] = useState("");

    // Remove 'error' as it's not used
    const { completion, complete, isLoading } = useCompletion({
        api: "/api/generate", // Ensure this matches your API route
        // id: "tiptap-ai", // Optional ID for completion state management
        onResponse: (response) => {
            if (response.status === 429) {
                toast.error("Rate limit exceeded. Please try again later.");
            } else if (!response.ok) {
                 toast.error(`Error from AI: ${response.statusText || response.status}`);
            }
            // Clear input after successful request initiation (even if streaming)
             setInputValue("");
        },
        onError: (e) => {
            toast.error(`AI request failed: ${e.message}`);
        },
        // onFinish: (_prompt, completion) => {
        //     // Optional: Do something when completion finishes streaming
        //     // Maybe auto-focus the input again or highlight the result
        // }
    });

    const hasCompletion = completion.length > 0;
    
    // Process completion to extract markdown from code blocks if needed
    const processedCompletion = completion.replace(/^```markdown\n([\s\S]*?)\n```$/m, '$1').replace(/^```\n([\s\S]*?)\n```$/m, '$1');
    

    // Function to handle selecting a command from AISelectorCommands
    const handleCommandSelect = useCallback((prompt: string, option: CommandOption['value']) => {
        if (!editor) return;

        // Use the prompt text passed from AISelectorCommands
        // AISelectorCommands already handles the logic for selecting the appropriate text
        // based on the command type (selected text or preceding text)
        const context = prompt;

        if (!context && option !== 'continue') { // Allow 'continue' command even with empty context
             toast.info("Please select text or add content to the editor first.");
             return;
        }

        // Highlight the text that will be sent (optional)
        // if (!editor.state.selection.empty) editor.chain().focus().setHighlight().run();

        // Send request with proper format for the API
        complete(context, { body: { prompt: context, option } });

    }, [editor, complete]);

    // Function to handle submitting custom input ("zap" command)
    const handleCustomSubmit = useCallback(() => {
        if (!editor || !inputValue) return;

        const { empty } = editor.state.selection;
        const context = empty ? editor.getText() : getSelectedText(editor);

        if (!context) {
             toast.info("Please select text or add content to the editor first.");
             return;
        }

        // Highlight selected text (optional)
        // if (!empty) editor.chain().focus().setHighlight({ color: '#c1ecf9' }).run();

        // Send request with proper format for the API
        complete(context, {
            body: { prompt: context, option: "zap", command: inputValue },
        });
        // Input value is cleared in onResponse now
    }, [editor, complete, inputValue]);


    // Function to REPLACE the selection with the AI completion
    const replaceCompletion = useCallback(() => {
        if (!editor || !completion) return;

        // Trim the raw completion string first
        const trimmedCompletion = completion.trim();

        if (!trimmedCompletion) {
            toast.info("AI response was empty after trimming.");
            return;
        }

        // Determine the range to replace
        let from = 0;
        let to = editor.state.doc.content.size;
        
        // If there's a selection, use it; otherwise replace all content
        if (!editor.state.selection.empty) {
            from = editor.state.selection.from;
            to = editor.state.selection.to;
        }
        
        // Convert markdown to HTML for TiptapEditor
        const htmlContent = marked.parse(processedCompletion) as string;
        editor.chain().focus().deleteRange({ from, to }).insertContentAt(from, htmlContent, {
            parseOptions: { preserveWhitespace: false }
        }).unsetHighlight().run();
        onOpenChange(false); // Close selector after inserting

    }, [editor, completion, processedCompletion, onOpenChange]);

    // Function to APPEND the AI completion after the selection
    const appendCompletion = useCallback(() => {
        if (!editor || !completion) return;

        // Trim the raw completion string first
        const trimmedCompletion = completion.trim();

        if (!trimmedCompletion) {
            toast.info("AI response was empty after trimming.");
            return;
        }

        // Convert markdown to HTML and insert at the end of selection
        const { to } = editor.state.selection;
        const htmlContent = marked.parse(processedCompletion) as string;
        editor.chain().focus().insertContentAt(to, htmlContent, {
            parseOptions: { preserveWhitespace: false }
        }).unsetHighlight().run();
        onOpenChange(false); // Close selector after inserting
    }, [editor, completion, processedCompletion, onOpenChange]);


    // Function to discard the AI completion
    const discardCompletion = useCallback(() => {
        if (!editor) return;
        editor.chain().focus().unsetHighlight().run(); // Remove highlight if applied
        onOpenChange(false); // Close selector
    }, [editor, onOpenChange]);


    if (!editor) return null; // Don't render if editor isn't ready

    return (
        <div className={containerClassName}>
            {/* Display Completion Result */}
            {hasCompletion && !isLoading && (
                <>
                    <div className={completionAreaClassName}>
                        {/* Use react-markdown to render potential markdown */}
                        <Markdown 
                            className="prose prose-base max-w-none text-foreground font-sans whitespace-pre-wrap text-base"
                            options={{
                                overrides: {
                                    h1: { props: { className: 'text-lg font-bold mb-2 text-foreground' } },
                                    h2: { props: { className: 'text-base font-bold mb-2 text-foreground' } },
                                    h3: { props: { className: 'text-sm font-bold mb-2 text-foreground' } },
                                    p: { props: { className: 'mb-2 text-foreground' } },
                                    strong: { props: { className: 'font-bold text-foreground' } },
                                    em: { props: { className: 'italic text-foreground' } },
                                    code: { props: { className: 'bg-muted px-1 py-0.5 text-xs font-mono text-foreground' } },
                                    pre: { props: { className: 'bg-muted border border-border p-2 text-xs overflow-x-auto font-mono text-foreground' } },
                                    ul: { props: { className: 'ml-4 list-disc mb-2' } },
                                    ol: { props: { className: 'ml-4 list-decimal mb-2' } },
                                    li: { props: { className: 'mb-1 text-foreground' } }
                                }
                            }}
                        >
                            {processedCompletion}
                        </Markdown>
                    </div>
                    <div className={completionActionsClassName}>
                         {/* Replace Button */}
                         <button type="button" onClick={replaceCompletion} className={buttonClassName} title="Replace selection">
                             <Replace size={16} /> Replace
                         </button>
                         {/* Append Button */}
                         <button type="button" onClick={appendCompletion} className={buttonClassName} title="Append after selection">
                             <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="lucide lucide-plus-square"><rect width="18" height="18" x="3" y="3" rx="2"/><path d="M8 12h8"/><path d="M12 8v8"/></svg>
                              Append
                         </button>
                         {/* Discard Button */}
                         <button type="button" onClick={discardCompletion} className={buttonClassName} title="Discard">
                             <Trash2 size={16} /> Discard
                         </button>
                    </div>
                </>
            )}

            {/* Display Loading State */}
            {isLoading && (
                <div className={loadingClassName}>
                    <Sparkles size={16} className="animate-pulse" />
                    <span>AI is thinking...</span>
                    <Loader2 size={16} className="animate-spin" />
                </div>
            )}

            {/* Display Input/Commands when not loading/no completion */}
            {!isLoading && !hasCompletion && (
                <>
                    {/* Use a div wrapper for positioning the button relative to the textarea */}
                    <div className="relative">
                        <textarea
                            className={inputClassName}
                            value={inputValue}
                            onChange={(e) => setInputValue(e.target.value)}
                            placeholder="Ask AI to edit or generate..."
                            rows={2}
                            onKeyDown={(e) => {
                                // Submit on Enter unless Shift is pressed
                                if (e.key === 'Enter' && !e.shiftKey) {
                                    e.preventDefault(); // Prevent default newline on Enter
                                    handleCustomSubmit();
                                }
                                // Default behavior (Shift+Enter for newline) is allowed
                            }}
                        />
                        {/* Position button inside the relative div */}
                        <button
                            type="button"
                            onClick={handleCustomSubmit}
                            className="absolute right-1.5 top-1/2 -translate-y-1/2 p-1 hover:bg-muted/60 disabled:opacity-50 disabled:cursor-not-allowed"
                            disabled={!inputValue}
                            title="Submit custom command"
                        >
                            <ArrowUp size={16} />
                        </button>
                    </div>
                    <AISelectorCommands editor={editor} onSelect={handleCommandSelect} />
                </>
            )}
        </div>
    );
}
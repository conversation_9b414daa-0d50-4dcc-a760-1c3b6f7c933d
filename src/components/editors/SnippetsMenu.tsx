import React, { useState } from 'react';
import { Editor } from '@tiptap/react';
import { useQuery } from "convex/react";
import { api } from "@/../convex/_generated/api";
import { debug } from '@/lib/utils';
import { Id } from "@/../convex/_generated/dataModel";

// Type for Convex snippet (matches schema)
type ConvexSnippet = {
    _id: Id<"snippets">;
    name: string;
    content: string;
    createdAt: number;
    updatedAt?: number;
};

interface SnippetsMenuProps {
    editor: Editor;
    onClose: () => void; // Function to close the menu
}

const SnippetsMenu: React.FC<SnippetsMenuProps> = ({ editor, onClose }) => {
    // --- State Variables ---
    const [confirmingInsertId, setConfirmingInsertId] = useState<Id<"snippets"> | null>(null); // For inline insert confirmation

    // --- Fetch Snippets with Convex Real-time Query ---
    const snippets = useQuery(api.snippets.getAllSnippets);
    const isLoading = snippets === undefined;
    const error = null; // Convex handles errors differently

    // --- Insert Snippet Logic ---
    const performInsert = (snippet: ConvexSnippet, mode: 'replace' | 'append') => {
        if (!editor) return;
        try {
            const jsonContent = JSON.parse(snippet.content);
            if (mode === 'replace') {
                editor.chain().focus().setContent(jsonContent).run();
                debug(`Replaced editor content with snippet: ${snippet.name}`);
            } else { // append JSON
                const endPos = editor.state.doc.content.size;
                editor.commands.insertContentAt(endPos, jsonContent, { parseOptions: { preserveWhitespace: 'full' } });
                editor.chain().focus().run(); // Refocus after insert
                debug(`Appended snippet JSON content: ${snippet.name}`);
            }
        } catch (error) {
            debug("Snippet content is not valid JSON, inserting as plain text/HTML:", error);
            if (mode === 'replace') {
                editor.chain().focus().setContent(snippet.content).run();
                debug(`Replaced editor content with snippet (plain text): ${snippet.name}`);
            } else { // append plain text/HTML
                editor.chain().focus().insertContentAt(editor.state.doc.content.size, snippet.content, {
                    parseOptions: { preserveWhitespace: false }
                }).run();
                 editor.chain().focus().run(); // Refocus after insert
                debug(`Appended snippet content (parsed as text/HTML): ${snippet.name}`);
            }
        }
        
        // Manually trigger an update event to ensure onChange is called
        // This will update the parent component's state and trigger localStorage save
        const customEvent = new CustomEvent('update', {
            bubbles: true,
            detail: { editor }
        });
        editor.view.dom.dispatchEvent(customEvent);
        
        setConfirmingInsertId(null); // Clear any inline confirmation
        onClose(); // Close the main menu
    };

    // Called when snippet name is clicked
    const handleSnippetClick = (snippet: ConvexSnippet) => {
        if (!editor) return;
        
        // Check if editor has meaningful content (not just empty paragraphs)
        const hasContent = editor.state.doc.textContent.trim().length > 0;
        
        if (!hasContent) {
            performInsert(snippet, 'replace'); // Insert directly if editor is empty
        } else {
            setConfirmingInsertId(snippet._id); // Trigger inline confirmation
        }
    };

    // Inline Insert Confirmation Handlers
    const handleInlineReplace = (snippet: ConvexSnippet) => {
        performInsert(snippet, 'replace');
    };
    const handleInlineAppend = (snippet: ConvexSnippet) => {
        performInsert(snippet, 'append');
    };
    const handleCancelInlineInsert = () => {
        setConfirmingInsertId(null);
    };

    // --- Render ---
    return (
        <>
            {/* Outer div styled by .snippet-menu-centered-popover */}
            <div>
                {isLoading && <div className="snippet-loading">Loading snippets...</div>}
                {error && <div className="snippet-error">Error: {error}</div>}
                {!isLoading && !error && snippets && snippets.length === 0 && (
                    <div className="snippet-empty">No snippets found. Please ask an admin to create snippets.</div>
                )}

                <ul className="space-y-2 p-4">
                    {snippets?.map((snippet) => (
                        <li key={snippet._id} className="border border-border rounded p-3 hover:bg-muted/10 transition-colors">
                            {/* --- Conditional Rendering Logic --- */}
                            {confirmingInsertId === snippet._id ? (
                                // Inline Insert Confirmation
                                <div className="flex flex-col space-y-3">
                                    <span className="text-sm text-muted-foreground">Insert &quot;{snippet.name}&quot;?</span>
                                    <div className="flex gap-2">
                                        <button 
                                            type="button" 
                                            onClick={(e) => { e.stopPropagation(); handleInlineReplace(snippet); }} 
                                            className="px-3 py-1 text-xs bg-primary text-primary-foreground hover:bg-primary/90 transition-colors cursor-pointer"
                                            title="Replace Content"
                                        >
                                            Replace
                                        </button>
                                        <button 
                                            type="button" 
                                            onClick={(e) => { e.stopPropagation(); handleInlineAppend(snippet); }} 
                                            className="px-3 py-1 text-xs bg-secondary text-secondary-foreground hover:bg-secondary/90 transition-colors cursor-pointer"
                                            title="Append Content"
                                        >
                                            Append
                                        </button>
                                        <button 
                                            type="button" 
                                            onClick={(e) => { e.stopPropagation(); handleCancelInlineInsert(); }} 
                                            className="px-3 py-1 text-xs border border-border hover:bg-muted/20 transition-colors cursor-pointer"
                                            title="Cancel Insert"
                                        >
                                            Cancel
                                        </button>
                                    </div>
                                </div>
                            ) : (
                                // Normal View - Only show snippet name for insertion
                                <div
                                    onClick={(e) => { e.stopPropagation(); handleSnippetClick(snippet); }}
                                    className="cursor-pointer text-foreground hover:text-primary transition-colors font-mono"
                                    title={`Insert "${snippet.name}"`}
                                >
                                    {snippet.name}
                                </div>
                            )}
                        </li>
                    ))}
                </ul>
            </div>
        </>
    );
};

export default SnippetsMenu;
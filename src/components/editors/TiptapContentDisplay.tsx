import { renderTiptapJsonStringToHtml } from './TiptapEditor';

interface TiptapContentDisplayProps {
    content: string;
    className?: string;
    forceTheme?: "light" | "dark";
}

export default function TiptapContentDisplay({ content, className = "", forceTheme }: TiptapContentDisplayProps) {
    // Use explicit colors when forcing light theme
    const baseClasses = forceTheme === 'light' 
        ? "prose prose-sm max-w-none text-black leading-relaxed [&_p]:mb-2 [&_p]:min-h-[1.5em] [&_p]:text-black [&_p]:font-sans [&_strong]:font-bold [&_strong]:text-black [&_em]:italic [&_em]:text-black [&_h1]:font-black [&_h1]:text-black [&_h1]:text-3xl [&_h1]:mt-2 [&_h1]:mb-2 [&_h1]:leading-tight [&_h2]:[font-family:var(--font-ultrabold)] [&_h2]:text-black [&_h2]:text-2xl [&_h2]:mt-2 [&_h2]:mb-2 [&_h2]:leading-tight [&_h3]:[font-family:var(--font-ultrabold)] [&_h3]:text-black [&_h3]:text-lg [&_h3]:mt-2 [&_h3]:mb-2 [&_h3]:leading-tight [&_ul]:pl-8 [&_ul]:mb-4 [&_ul]:list-disc [&_ul]:font-ultrabold [&_ol]:pl-8 [&_ol]:mb-4 [&_ol]:list-decimal [&_ol]:font-ultrabold [&_li]:mb-1 [&_li]:font-mono [&_li>p]:mb-1 [&_li>p]:font-sans [&_blockquote]:border-l-4 [&_blockquote]:border-gray-300 [&_blockquote]:ml-0 [&_blockquote]:pl-4 [&_blockquote]:text-gray-600 [&_blockquote]:italic [&_blockquote]:my-4 [&_blockquote]:font-mono [&_pre]:bg-gray-100 [&_pre]:border [&_pre]:border-gray-300 [&_pre]:text-black [&_pre]:p-3 [&_pre]:rounded-lg [&_pre]:my-4 [&_pre]:whitespace-pre-wrap [&_pre]:break-all [&_pre]:[font-family:'neue-machina-Medium',monospace] [&_code]:bg-purple-100 [&_code]:rounded [&_code]:px-1 [&_code]:py-0.5 [&_code]:text-sm [&_code]:text-purple-800 [&_code]:[font-family:'neue-machina-Medium',monospace] [&_pre_code]:bg-transparent [&_pre_code]:p-0 [&_pre_code]:text-inherit [&_pre_code]:rounded-none [&_pre_code]:text-black [&_hr]:block [&_hr]:border-none [&_hr]:border-t-2 [&_hr]:border-gray-300 [&_hr]:my-6 [&_hr]:w-full [&_hr]:h-0"
        : "prose prose-sm max-w-none text-foreground leading-relaxed [&_p]:mb-2 [&_p]:min-h-[1.5em] [&_p]:text-foreground [&_p]:font-sans [&_strong]:font-bold [&_strong]:text-foreground [&_em]:italic [&_em]:text-foreground [&_h1]:font-black [&_h1]:text-foreground [&_h1]:text-3xl [&_h1]:mt-2 [&_h1]:mb-2 [&_h1]:leading-tight [&_h2]:[font-family:var(--font-ultrabold)] [&_h2]:text-foreground [&_h2]:text-2xl [&_h2]:mt-2 [&_h2]:mb-2 [&_h2]:leading-tight [&_h3]:[font-family:var(--font-ultrabold)] [&_h3]:text-foreground [&_h3]:text-lg [&_h3]:mt-2 [&_h3]:mb-2 [&_h3]:leading-tight [&_ul]:pl-8 [&_ul]:mb-4 [&_ul]:list-disc [&_ul]:font-ultrabold [&_ol]:pl-8 [&_ol]:mb-4 [&_ol]:list-decimal [&_ol]:font-ultrabold [&_li]:mb-1 [&_li]:font-mono [&_li>p]:mb-1 [&_li>p]:font-sans [&_blockquote]:border-l-4 [&_blockquote]:border-primary [&_blockquote]:ml-0 [&_blockquote]:pl-4 [&_blockquote]:text-muted-foreground [&_blockquote]:italic [&_blockquote]:my-4 [&_blockquote]:font-mono [&_pre]:bg-muted [&_pre]:border [&_pre]:border-border [&_pre]:text-foreground [&_pre]:p-3 [&_pre]:rounded-lg [&_pre]:my-4 [&_pre]:whitespace-pre-wrap [&_pre]:break-all [&_pre]:[font-family:'neue-machina-Medium',monospace] [&_code]:bg-primary/20 [&_code]:rounded [&_code]:px-1 [&_code]:py-0.5 [&_code]:text-sm [&_code]:text-primary [&_code]:[font-family:'neue-machina-Medium',monospace] [&_pre_code]:bg-transparent [&_pre_code]:p-0 [&_pre_code]:text-inherit [&_pre_code]:rounded-none [&_pre_code]:text-foreground [&_hr]:block [&_hr]:border-none [&_hr]:border-t-2 [&_hr]:border-border [&_hr]:my-6 [&_hr]:w-full [&_hr]:h-0";
    
    return (
        <div 
            className={`${baseClasses} ${className}`}
            dangerouslySetInnerHTML={{ __html: renderTiptapJsonStringToHtml(content) }}
        />
    );
}
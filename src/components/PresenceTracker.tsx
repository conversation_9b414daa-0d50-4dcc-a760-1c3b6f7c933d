"use client";

import { useEffect, useState, useCallback, useRef } from 'react';
import { useQuery, useMutation } from "convex/react";
import { api } from "@/../convex/_generated/api";
import { useUser } from '@/hooks/useUser';
import { debug } from '@/lib/utils';

// Generate session ID for this browser tab
function generateSessionId(): string {
    return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
}

// Get or create session ID
function getSessionId(): string {
    if (typeof window === 'undefined') return '';
    
    let sessionId = sessionStorage.getItem('convex_session_id');
    if (!sessionId) {
        sessionId = generateSessionId();
        sessionStorage.setItem('convex_session_id', sessionId);
    }
    return sessionId;
}

export default function PresenceTracker() {
    const { user } = useUser();
    
    // Track all authenticated users (including admins)
    const shouldTrack = !!user;
    
    // Real-time query from Convex
    const onlineUsersEnabled = useQuery(api.settings.getOnlineUsersSetting);
    
    // Mutations
    const updatePresence = useMutation(api.presence.updatePresence);
    const setOffline = useMutation(api.presence.setOffline);
    
    // Session management
    const [sessionId] = useState<string>(() => getSessionId());
    const heartbeatRef = useRef<NodeJS.Timeout | null>(null);
    const isActiveRef = useRef<boolean>(true);

    // Heartbeat function to maintain online status
    const sendHeartbeat = useCallback(async () => {
        if (!shouldTrack || !onlineUsersEnabled || !sessionId) {
            debug('Presence heartbeat skipped:', { 
                shouldTrack, 
                onlineUsersEnabled, 
                sessionId: !!sessionId,
                userRole: user?.role 
            });
            return;
        }
        
        try {
            debug('Sending presence heartbeat for user:', user.username);
            await updatePresence({
                userId: user._id,
                sessionId: sessionId,
            });
            debug('Presence heartbeat sent successfully');
        } catch (error) {
            debug('Error sending presence heartbeat:', error);
        }
    }, [shouldTrack, onlineUsersEnabled, sessionId, updatePresence, user]);

    // Setup heartbeat when online users is enabled and user should be tracked
    useEffect(() => {
        if (!shouldTrack || !onlineUsersEnabled || !sessionId) {
            // Clean up existing heartbeat
            if (heartbeatRef.current) {
                clearInterval(heartbeatRef.current);
                heartbeatRef.current = null;
            }
            return;
        }

        debug('Starting presence tracking for user:', user.username, 'role:', user.role);

        // Send initial heartbeat
        sendHeartbeat();

        // Setup periodic heartbeat (every 60 seconds)
        heartbeatRef.current = setInterval(sendHeartbeat, 60 * 1000);

        return () => {
            if (heartbeatRef.current) {
                clearInterval(heartbeatRef.current);
                heartbeatRef.current = null;
            }
        };
    }, [shouldTrack, onlineUsersEnabled, sessionId, sendHeartbeat, user]);

    // Handle browser events for presence tracking
    useEffect(() => {
        if (!shouldTrack || !onlineUsersEnabled || !sessionId) return;

        const handleVisibilityChange = () => {
            isActiveRef.current = !document.hidden;
            if (isActiveRef.current) {
                // Send heartbeat when tab becomes active
                sendHeartbeat();
            }
        };

        const handleBeforeUnload = async () => {
            // Mark user as offline when leaving
            try {
                await setOffline({
                    userId: user._id,
                    sessionId: sessionId,
                });
            } catch (error) {
                debug('Error setting offline on unload:', error);
            }
        };

        const handleFocus = () => {
            isActiveRef.current = true;
            sendHeartbeat();
        };

        const handleBlur = () => {
            isActiveRef.current = false;
        };

        // Add event listeners
        document.addEventListener('visibilitychange', handleVisibilityChange);
        window.addEventListener('beforeunload', handleBeforeUnload);
        window.addEventListener('focus', handleFocus);
        window.addEventListener('blur', handleBlur);

        return () => {
            document.removeEventListener('visibilitychange', handleVisibilityChange);
            window.removeEventListener('beforeunload', handleBeforeUnload);
            window.removeEventListener('focus', handleFocus);
            window.removeEventListener('blur', handleBlur);
        };
    }, [shouldTrack, onlineUsersEnabled, sessionId, sendHeartbeat, setOffline, user]);

    // This component doesn't render anything
    return null;
}
interface SquareSpinnerProps {
  size?: 'sm' | 'md' | 'lg';
  className?: string;
}

export function SquareSpinner({ size = 'md', className = '' }: SquareSpinnerProps) {
  const dotSizeClasses = {
    sm: 'w-2 h-2',
    md: 'w-3 h-3', 
    lg: 'w-4 h-4'
  };

  return (
    <div className={`flex items-center gap-1 ${className}`} role="status" aria-label="Loading">
      <div className={`${dotSizeClasses[size]} bg-accent animate-pulse`}></div>
      <div className={`${dotSizeClasses[size]} bg-accent animate-pulse`} style={{ animationDelay: '0.2s' }}></div>
      <div className={`${dotSizeClasses[size]} bg-accent animate-pulse`} style={{ animationDelay: '0.4s' }}></div>
    </div>
  );
}
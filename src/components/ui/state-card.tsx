import { SquareSpinner } from './square-spinner';

interface StateCardProps {
  state: 'loading' | 'info' | 'success' | 'error' | 'warning' | 'empty' | 'completed';
  title: string;
  message?: string;
}

export function StateCard({ state, title, message }: StateCardProps) {
  const stateStyles = {
    loading: 'border-secondary text-secondary',
    info: 'border-secondary text-secondary', 
    success: 'border-[#15B14B] text-[#15B14B]',
    error: 'border-destructive text-destructive',
    warning: 'border-accent text-accent',
    empty: 'border-secondary text-secondary',
    completed: 'border-[#15B14B] text-[#15B14B]'
  };

  return (
    <div className={`bg-background/80 border-2 p-8 text-center w-full max-w-[800px] ${stateStyles[state]}`}>
      {state === 'loading' && (
        <div className="flex justify-center mb-6">
          <SquareSpinner size="md" />
        </div>
      )}
      <h2 className="font-ultrabold text-2xl text-primary m-0 mb-6">
        {title}
      </h2>
      {message && (
        <p className="font-mono text-xl text-accent m-0">
          {message}
        </p>
      )}
    </div>
  );
}
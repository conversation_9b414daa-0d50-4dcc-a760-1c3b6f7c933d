"use client"

import * as React from "react"
import { useTheme } from "next-themes"
import { Moon, Sun, Palette } from "lucide-react"
import { cn } from "@/lib/utils"

type ThemeSwitcherProps = React.ComponentProps<"div">

export function ThemeSwitcher({ className, ...props }: ThemeSwitcherProps) {
  const { setTheme, resolvedTheme } = useTheme()
  const [mounted, setMounted] = React.useState(false)

  React.useEffect(() => {
    setMounted(true)
  }, [])

  if (!mounted) return null

  return (
    <div
      className={cn(
        "flex items-center gap-2 rounded-none px-2 py-1.5 text-sm text-foreground cursor-default select-none",
        className
      )}
      {...props}
    >
      <Palette className="size-4" />
      <span className="flex-1">Theme</span>
      <div className="flex items-center gap-1">
        <button
          onClick={() => setTheme("dark")}
          className={cn(
            "p-1 rounded-none hover:bg-primary hover:text-primary-foreground transition-colors",
            resolvedTheme === "dark" && "bg-primary text-primary-foreground"
          )}
        >
          <Moon className="size-4" />
        </button>
        <button
          onClick={() => setTheme("light")}
          className={cn(
            "p-1 rounded-none hover:bg-primary hover:text-primary-foreground transition-colors",
            resolvedTheme === "light" && "bg-primary text-primary-foreground"
          )}
        >
          <Sun className="size-4" />
        </button>
      </div>
    </div>
  )
}
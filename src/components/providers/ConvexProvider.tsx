"use client";

import { ReactNode } from "react";
import { ConvexProvider as ConvexClientProvider } from "convex/react";
import { ConvexReactClient } from "convex/react";

if (!process.env.NEXT_PUBLIC_CONVEX_URL) {
    throw new Error('NEXT_PUBLIC_CONVEX_URL is not defined');
}

const convex = new ConvexReactClient(process.env.NEXT_PUBLIC_CONVEX_URL, {
  // Leverage v1.25.2 improvements
  verbose: process.env.NODE_ENV === 'development',
});

export function ConvexProvider({ children }: { children: ReactNode }) {
    return (
        <ConvexClientProvider client={convex}>
            {children}
        </ConvexClientProvider>
    );
}
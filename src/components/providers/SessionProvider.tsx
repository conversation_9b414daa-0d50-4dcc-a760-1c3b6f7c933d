'use client';

import { Session } from 'next-auth';
import { SessionProvider as NextAuthSessionProvider } from 'next-auth/react';
import PresenceTracker from '@/components/PresenceTracker';

export default function SessionProvider({
  children,
  session
}: {
  children: React.ReactNode;
  session: Session | null;
}) {
  return (
    <NextAuthSessionProvider 
      session={session} 
      refetchInterval={30 * 60} // Increase to 30 minutes
    >
      <PresenceTracker />
      {children}
    </NextAuthSessionProvider>
  );
}
"use client";

import { useActionState } from 'react';
import { useRouter } from 'next/navigation';
import { ConvexError } from 'convex/values';
import { useAction } from "convex/react";
import { api } from "@/../convex/_generated/api";
import { Input } from '@/components/ui/input';
import { PasswordInput } from '@/components/ui/password-input';
import { Button } from '@/components/ui/button';
import { Label } from '../ui/label';
import { useFormStatus } from 'react-dom';

function RegisterSubmitButton() {
    const { pending } = useFormStatus();
    
    return (
        <Button type="submit" disabled={pending} className="w-full">
            {pending ? "Registering..." : "REGISTER"}
        </Button>
    );
}


interface RegisterFormProps {
    eventName: string;
}

export default function RegisterForm({ eventName }: RegisterFormProps) {
    const router = useRouter();
    const registerUser = useAction(api.users.registerUser);
    
    // React 19 Actions for registration form
    const [result, registerAction, isPending] = useActionState(
        async (_previousState: { error: string | null; success: string | null }, formData: FormData): Promise<{ error: string; success: null } | { error: null; success: string }> => {
            try {
                const username = formData.get("username") as string;
                const name = formData.get("name") as string;
                const password = formData.get("password") as string;
                const confirmPassword = formData.get("confirmPassword") as string;

                // Validate required fields
                if (!username || !name || !password || !confirmPassword) {
                    return { error: 'Please fill in all fields', success: null };
                }

                // Validate username format
                if (username.includes(' ')) {
                    return { error: 'Username cannot contain spaces', success: null };
                }

                if (username !== username.toLowerCase()) {
                    return { error: 'Username must be lowercase', success: null };
                }

                if (password !== confirmPassword) {
                    return { error: 'Passwords do not match', success: null };
                }

                const data = await registerUser({
                    username,
                    name,
                    password,
                });

                // Show appropriate success message based on whether it's a new or existing user
                let successMessage = '';
                if (data.data!.user.autoApproved) {
                    if (data.data!.user.existingUser) {
                        successMessage = `Welcome back! You've been registered for ${eventName}. You can now log in.`;
                    } else {
                        successMessage = `Registration successful for ${eventName}! You can now log in.`;
                    }
                } else {
                    if (data.data!.user.existingUser) {
                        successMessage = `Welcome back! Your registration for ${eventName} is pending approval.`;
                    } else {
                        successMessage = `Registration successful for ${eventName}! Please wait for administrator approval.`;
                    }
                }

                // If auto-approved, redirect to login after showing message
                if (data.data!.user.autoApproved) {
                    setTimeout(() => {
                        router.push('/login');
                    }, 2500); // Show message for 2.5 seconds
                }

                return { error: null, success: successMessage };
            } catch (error) {
                if (error instanceof ConvexError) {
                    const errorData = error.data as { message: string };
                    return { error: errorData.message, success: null };
                } else {
                    return { error: error instanceof Error ? error.message : 'Registration failed', success: null };
                }
            }
        },
        { error: null, success: "" }
    );


    return (
        <form action={registerAction} className="space-y-5">
            <div className="space-y-4">
                <div className="space-y-2">
                    <Label htmlFor="name">
                        Name
                    </Label>
                    <Input
                        id="name"
                        name="name"
                        type="text"
                        disabled={isPending}
                        className="input-standard"
                    />
                </div>
                
                <div className="space-y-2">
                    <Label htmlFor="username">
                        Username
                    </Label>
                    <Input
                        id="username"
                        name="username"
                        type="text"
                        disabled={isPending}
                        className="input-standard"
                        onInput={(e) => {
                            const target = e.target as HTMLInputElement;
                            target.value = target.value.toLowerCase().replace(/\s+/g, '');
                        }}
                    />
                </div>
                
                <div className="space-y-2">
                    <Label htmlFor="password">
                        Password
                    </Label>
                    <PasswordInput
                        id="password"
                        name="password"
                        disabled={isPending}
                        className="input-standard"
                    />
                </div>
                
                <div className="space-y-2">
                    <Label htmlFor="confirmPassword">
                        Confirm Password
                    </Label>
                    <PasswordInput
                        id="confirmPassword"
                        name="confirmPassword"
                        disabled={isPending}
                        className="input-standard"
                    />
                </div>
            </div>
            
            {result?.error && (
                <div className="text-sm bg-primary/10 border border-primary/20 text-primary p-3">
                    {result.error}
                </div>
            )}
            
            {result?.success && (
                <div className="text-sm bg-secondary/10 border border-secondary/20 text-secondary p-3">
                    {result.success}
                </div>
            )}
            
            <RegisterSubmitButton />
        </form>
    );
}
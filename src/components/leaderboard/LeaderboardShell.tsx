"use client";

import { useState, useEffect } from 'react';
import { useTheme } from 'next-themes';
import Image from 'next/image';
import Link from 'next/link';
import { ArrowLeft, Sun, Moon } from 'lucide-react';
import { Button } from '@/components/ui/button';

interface LeaderboardShellProps {
  eventName: string;
  sessionName?: string;
  children: React.ReactNode;
}

export default function LeaderboardShell({ eventName, sessionName, children }: LeaderboardShellProps) {
  const { theme, setTheme, resolvedTheme } = useTheme();
  const [mounted, setMounted] = useState(false);

  // Wait for hydration to complete
  useEffect(() => {
    setMounted(true);
  }, []);

  const toggleTheme = () => {
    setTheme(theme === 'dark' ? 'light' : 'dark');
  };

  // Prevent hydration mismatch by showing loading state
  if (!mounted) {
    return (
      <div className="relative w-full min-h-screen bg-background">
        <div className="relative z-[1] flex flex-col items-center w-full min-h-screen pt-20 pb-8 px-4">
          <div className="w-full text-center mb-8">
            <h1 className="text-4xl lg:text-5xl font-bold text-foreground mb-2">
              Loading...
            </h1>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="relative w-full min-h-screen bg-background">
      {/* Background Image */}
      <Image 
        src={mounted && resolvedTheme === 'light' ? "/images/background-light2.jpg" : "/images/background-01.jpg"}
        alt="Background"
        fill
        className="absolute inset-0 object-cover z-0"
        priority
      />
      
      {/* Back Link */}
      <Button 
        asChild
        variant="default"
        size="lg"
        className="absolute top-4 left-4 z-10"
      >
        <Link href="/admin">
          <ArrowLeft className="h-5 w-5" />
          Back
        </Link>
      </Button>

      {/* Theme Switcher */}
      <Button
        onClick={toggleTheme}
        variant="ghost"
        size="icon"
        className="absolute top-4 right-4 z-10 bg-transparent hover:bg-transparent border-0"
        title={mounted && resolvedTheme === 'dark' ? 'Switch to Light Mode' : 'Switch to Dark Mode'}
      >
        {mounted && resolvedTheme === 'light' ? (
          <Moon className="h-5 w-5" />
        ) : (
          <Sun className="h-5 w-5" />
        )}
      </Button>

      {/* Main Content Container */}
      <div className="relative z-[1] flex flex-col items-center w-full min-h-screen pt-20 pb-8 px-4">
        {/* Header Section */}
        <div className="w-full text-center mb-8 relative">
          <h1 className="text-4xl lg:text-5xl font-bold text-foreground mb-2">
            {eventName}
          </h1>
          {sessionName && (
            <h2 className="text-2xl lg:text-3xl font-medium text-secondary">
              {sessionName}
            </h2>
          )}
        </div>

        {/* Content Area */}
        <div className="w-full max-w-none px-6">
          {children}
        </div>
      </div>
    </div>
  );
}
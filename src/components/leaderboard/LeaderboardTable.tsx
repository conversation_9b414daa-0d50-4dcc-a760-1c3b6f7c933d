"use client";

import { ErrorBoundary } from 'react-error-boundary';
import { Medal } from 'lucide-react';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { StateCard } from '@/components/ui/state-card';

interface LeaderboardItem {
  type: string; // Will be "idea" | "quickfire" but comes as string from Convex
  ideaId?: string;
  quickfireId?: string;
  ideaName?: string;
  ideaDescription?: string;
  idea?: string;
  comments?: string;
  question?: string;
  teamName?: string;
  eventName: string;
  sessionName: string;
  sessionId?: string;
  totalScore: number;
  voteCount: number;
  totalExpectedVoters: number;
}

interface LeaderboardTableProps {
  data: LeaderboardItem[];
}

function LeaderboardTableContent({ data }: LeaderboardTableProps) {
  if (!data || data.length === 0) {
    return (
      <div className="flex justify-center">
        <StateCard state="empty" title="No leaderboard data available" />
      </div>
    );
  }

  return (
    <div className="bg-card/90 border-2 border-accent p-10 w-full overflow-x-auto">
      {/* Desktop Table */}
      <div className="hidden md:block">
        <Table className="w-full">
          <TableHeader>
            <TableRow className="border-b-2 border-accent hover:bg-transparent">
              <TableHead className="text-accent font-ultrabold text-2xl text-center w-32 py-6">
                Rank
              </TableHead>
              <TableHead className="text-accent font-ultrabold text-2xl text-left py-6">
                Idea
              </TableHead>
              {data.length > 0 && data[0].type !== "quickfire" && (
                <TableHead className="text-accent font-ultrabold text-2xl text-left py-6">
                  Team
                </TableHead>
              )}
              <TableHead className="text-accent font-ultrabold text-2xl text-center w-40 py-6">
                Score
              </TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {data.map((item, index) => (
              <TableRow 
                key={item.type === "idea" ? item.ideaId : item.quickfireId}
                className="border-b border-accent/20 hover:bg-accent/5 transition-colors"
              >
                <TableCell className="text-foreground text-3xl text-center font-ultrabold py-8">
                  {index + 1}
                </TableCell>
                <TableCell className="text-foreground py-8">
                  <div className="space-y-3">
                    <div className="text-primary text-2xl font-medium font-mono leading-relaxed">
                      {item.type === "idea" ? item.ideaName : item.idea}
                    </div>
                  </div>
                </TableCell>
                {item.type !== "quickfire" && (
                  <TableCell className="text-foreground text-xl font-medium py-8">
                    {item.teamName || "N/A"}
                  </TableCell>
                )}
                <TableCell className="text-secondary text-4xl text-center font-ultrabold py-8">
                  {item.totalScore}
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>

      {/* Mobile Cards */}
      <div className="md:hidden space-y-6">
        {data.map((item, index) => (
          <div 
            key={item.type === "idea" ? item.ideaId : item.quickfireId}
            className="bg-card border border-accent p-6 transition-colors"
          >
            {/* Header: Rank and Score */}
            <div className="flex items-center justify-between mb-6">
              {/* Rank with Medal Icon */}
              <div className="flex items-center gap-3">
                <Medal className="h-8 w-8 text-accent" />
                <span className="text-foreground text-3xl font-ultrabold">{index + 1}</span>
              </div>
              
              {/* Score */}
              <div className="text-secondary text-5xl font-ultrabold">
                {item.totalScore}
              </div>
            </div>

            {/* Content */}
            <div className="space-y-4">
              <div className="text-primary text-2xl font-medium font-mono leading-relaxed">
                {item.type === "idea" ? item.ideaName : item.idea}
              </div>

              {item.type !== "quickfire" && item.teamName && (
                <div className="text-foreground text-xl font-medium">
                  Team: {item.teamName}
                </div>
              )}
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}

function ErrorFallback({ error }: { error: Error }) {
  return (
    <div className="flex justify-center">
      <StateCard state="error" title="Something went wrong" message={error.message} />
    </div>
  );
}

export default function LeaderboardTable({ data }: LeaderboardTableProps) {
  return (
    <ErrorBoundary FallbackComponent={ErrorFallback}>
      <LeaderboardTableContent data={data} />
    </ErrorBoundary>
  );
}
"use client";

import { useQuery } from 'convex/react';
import { api } from '@/../convex/_generated/api';
import { Loader2, RefreshCw } from 'lucide-react';

export default function MaintenanceMode() {
  const isRestoring = useQuery(api.settings.getRestoreStatus);

  // Don't render anything while query is loading (undefined)
  if (isRestoring === undefined) return null;
  
  if (!isRestoring) return null;

  return (
    <div className="fixed inset-0 bg-background/80 backdrop-blur-sm z-50 flex items-center justify-center animate-in fade-in duration-500">
      <div className="bg-card border border-border rounded-lg p-8 max-w-md mx-4 text-center animate-in slide-in-from-bottom-4 duration-700 delay-200">
        <div className="flex justify-center mb-4">
          <RefreshCw className="h-12 w-12 text-accent animate-spin" />
        </div>
        
        <h1 className="text-2xl font-bold text-foreground mb-2">
          Restore in Progress
        </h1>
        
        <p className="text-muted-foreground mb-4">
          The system is currently restoring from a backup. This may take several minutes.
        </p>
        
        <div className="flex items-center justify-center gap-2 text-sm text-accent">
          <Loader2 className="h-4 w-4 animate-spin" />
          <span>Please wait...</span>
        </div>
        
        <p className="text-xs text-muted-foreground mt-4">
          The application will be available once the restore is complete.
        </p>
      </div>
    </div>
  );
}
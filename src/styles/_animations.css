/* Base transition for all animated elements */
.fade-in {
    animation: fadeIn 0.3s ease-in-out;
    animation-fill-mode: both;
}

.slide-in-top {
    animation: slideInTop 0.3s ease-in-out;
    animation-fill-mode: both;
}

.slide-in-up {
    animation: slideInUp 0.3s ease-in-out;
    animation-fill-mode: both;
    will-change: transform, opacity;
}

/* Keyframes */
@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

@keyframes slideInTop {
    from {
        transform: translateY(-20px);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

@keyframes slideInUp {
    from {
        transform: translateY(20px);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

/* Reduce motion preferences */
@media (prefers-reduced-motion: reduce) {
    .fade-in,
    .slide-in-top,
    .slide-in-up {
        animation: none;
        opacity: 1;
        transform: none;
    }
}

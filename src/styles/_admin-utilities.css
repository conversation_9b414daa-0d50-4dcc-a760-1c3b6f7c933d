/* Admin Component Utility Classes */
/* Reduces repetitive long class combinations across admin components */

/* Button Variants - Used 24+ times across components */

.btn-icon {
    @apply h-8 w-8 p-0 hover:!bg-transparent hover:text-primary shrink-0;
}

.btn-icon-destructive {
    @apply h-8 w-8 p-0 hover:!bg-transparent hover:text-destructive shrink-0;
}

/* Input Standardization - Used across all forms */
.input-standard {
    @apply w-full shadow-none focus-visible:ring-0 focus-visible:ring-offset-0;
}

.input-numeric {
    @apply w-24 shadow-none focus-visible:ring-0 focus-visible:ring-offset-0;
}

/* Card Layouts - Responsive patterns used 19+ times */
.admin-card {
    @apply flex flex-col sm:flex-row sm:items-center gap-4 p-4 border border-border transition-colors;
}

.admin-card.border-primary {
    @apply border-primary;
}

.admin-card-content {
    @apply flex items-center gap-4 flex-1 min-w-0;
}

.admin-card-actions {
    @apply flex items-center gap-3 flex-wrap sm:flex-nowrap;
}

/* Status Badges - Various status indicators */
.status-badge-base {
    @apply inline-flex items-center px-2 py-1 rounded-md text-sm font-medium border;
}

.status-badge-active {
    @apply bg-accent/10 text-accent border-accent/20;
}

.status-badge-inactive {
    @apply bg-primary/10 text-primary border-primary/20;
}

/* Interactive Elements - Radio groups and selections */
.interactive-option {
    @apply flex items-center space-x-3 p-3 border rounded-md transition-colors cursor-pointer hover:bg-muted/20;
}

/* Error States - Consistent error styling */
.error-display {
    @apply text-destructive text-sm bg-destructive/10 border border-destructive/20 rounded-md p-3;
}

/* Loading States */
.loading-text {
    @apply text-muted-foreground;
}

/* Empty States */
.empty-state {
    @apply text-muted-foreground text-center py-8;
}

/* Color Picker Theme - Replaces 1,400+ character className */
.color-picker-container {
    @apply w-full flex justify-center;
}

.color-picker-container * {
    @apply !rounded-none;
}

.color-picker-container div[id*='rbgcp-color-picker'] {
    @apply !bg-background;
}

.color-picker-container .rbgcp-wrapper {
    @apply !rounded-none !bg-background !border !border-border;
}

.color-picker-container .rbgcp-body {
    @apply !rounded-none !bg-background !text-foreground;
}

.color-picker-container .rbgcp-input,
.color-picker-container input[id*='rbgcp'] {
    @apply !bg-input !border-border !text-foreground;
}

.color-picker-container .rbgcp-input:focus {
    @apply !border-ring;
}

.color-picker-container .rbgcp-eyedropper,
.color-picker-container .rbgcp-control-icon-btn,
.color-picker-container .rbgcp-control-btn {
    @apply !bg-muted !border-border !text-foreground;
}

.color-picker-container .rbgcp-eyedropper:hover,
.color-picker-container .rbgcp-control-icon-btn:hover,
.color-picker-container .rbgcp-control-btn:hover {
    @apply !bg-muted/80;
}

.color-picker-container .rbgcp-control-btn-selected {
    @apply !bg-primary !text-primary-foreground;
}

.color-picker-container .rbgcp-color-preview {
    @apply !border-border;
}

.color-picker-container .rbgcp-control-btn-wrapper {
    @apply !bg-muted;
}

.color-picker-container .rbgcp-color-model-dropdown,
.color-picker-container .rbgcp-control-input {
    @apply !bg-input !border-border !text-foreground;
}
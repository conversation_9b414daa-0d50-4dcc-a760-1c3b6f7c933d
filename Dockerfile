FROM oven/bun:1.2.18 AS base

# Install dependencies only when needed
WORKDIR /app

# Install dependencies based on the preferred package manager
COPY package.json bun.lock* ./
RUN bun install --frozen-lockfile

# Rebuild the source code only when needed
FROM oven/bun:1.2.18 AS builder
WORKDIR /app

# Add build time arguments
ARG NEXTAUTH_URL
ARG NODE_ENV
ARG NEXT_PUBLIC_CONVEX_URL

# Set environment variables
ENV NEXTAUTH_URL=${NEXTAUTH_URL}
ENV NODE_ENV=production
ENV NEXT_PUBLIC_CONVEX_URL='https://votingconvex.lionx.me'
ENV CONVEX_SELF_HOSTED_URL='https://votingconvex.lionx.me'

COPY --from=base /app/node_modules ./node_modules
COPY . .

# Disable telemetry during the build
ENV NEXT_TELEMETRY_DISABLED=1

# Build Next.js
RUN bun run build
# Production image, copy all the files and run next
FROM oven/bun:1.2.18-alpine AS runner
WORKDIR /app

ENV NODE_ENV=production
ENV NEXT_TELEMETRY_DISABLED=1


# Install unzip utility for backup restoration
RUN apk add --no-cache unzip

RUN addgroup --system --gid 1001 nodejs
RUN adduser --system --uid 1001 nextjs

# Create cache directory and set permissions
RUN mkdir -p .next/cache && chown -R nextjs:nodejs .next

# Copy built application
COPY --from=builder /app/public ./public
COPY --from=builder /app/.next/standalone ./
COPY --from=builder /app/.next/static ./.next/static

# Create backups directory for backup system
RUN mkdir -p ./backups && chown -R nextjs:nodejs ./backups

# Set permissions for nextjs user
RUN chown -R nextjs:nodejs .next

USER nextjs

# Expose Next.js port
EXPOSE 3000

# Start the application using standalone server
CMD ["bun", "server.js"]
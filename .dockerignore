# Dependencies
node_modules
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Next.js build artifacts
.next/
.next/cache/
out/

# Environment files (security) - .env needed for build
.env.local
.env.development.local
.env.test.local
.env.production.local

# IDE and editor files
.vscode/
.idea/
*.swp
*.swo

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Git
.git/
.gitignore

# Documentation and specs (not needed in production)
README.md
CLAUDE.md
Docs/
Specs/
design-ref/
notepad/
context/

# Development artifacts
tsconfig.tsbuildinfo
*.tgz

# Testing
coverage/
.nyc_output

# Logs
logs
*.log

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Docker related
Dockerfile*
docker-compose*
.dockerignore

# Build scripts
build-and-push.sh
compose.build.yml
compose.prod.yml

# Temporary files and backups (local development)
backups/
screenshots/

# Convex generated files are needed for Docker build
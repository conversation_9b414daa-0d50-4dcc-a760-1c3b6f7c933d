# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview
LionX is a real-time voting and idea management platform built with Next.js 15.3.4 and Convex 1.25.2. The application supports three distinct session types (Ideas, Sparks, Quickfire) with role-based access control and real-time collaboration features.

## Core Features
- **User Management**: Role-based access (admin, team lead, team member) with approval workflows
- **Event Management**: Multi-event support with session-based activities
- **Idea Submission**: Teams can submit and present ideas during sessions
- **Voting System**: Comprehensive voting on ideas with scoring (0-10) and comments
- **Quickfire Voting**: Real-time voting on quick ideas/questions
- **Sparks**: Configurable forms for structured data collection
- **Leaderboards**: Individual and team-based scoring across sessions
- **Real-time Presence**: Track online users and activity

## User Roles
- **Admin**: Full system access, user approval, event management
- **Team Lead**: Team management capabilities
- **Team Member**: Idea submission and voting participation


## Three Distinct Session Types & Data Systems

LionX operates with **three completely separate systems**, each with distinct workflows, data tables, and purposes:

### 🧠 **Ideas System** (Team Collaboration → Voting)
**Tables**: `ideas` → `votes`

**Multi-Team Lead Workflow**:
1. **Individual Submission**: Each team lead submits their own ideas separately during Ideas sessions
2. **Separate Ownership**: Ideas are stored per team lead (via `userId` field) - not combined
3. **Team Completion Logic**: Team is only marked as "finished" when **ALL team leads** in that team have submitted **ALL their ideas**
4. **Voting Phase**: Once teams are marked as finished, everyone votes on submitted ideas (scoring 0-10 + comments)
5. **Leaderboards**: Track performance across sessions with individual and team scoring

**Advanced Business Logic**:
- **Multiple Team Leads Per Team**: A single team can have multiple team leads who submit independently
- **Individual Accountability**: Each team lead's ideas remain separate and attributable to them
- **Collective Completion**: Team completion requires **every single idea** from **every team lead** to be submitted
- **Parallel Submission**: Team leads can submit their ideas in any order - team isn't finished until all are done

**Example Scenario**:
```
Team Alpha has 3 team leads:
- Alice submits 2 ideas ✅ (submitted)
- Bob submits 3 ideas ✅ (submitted) 
- Charlie has 1 idea ❌ (not submitted)
Result: Team Alpha remains "not finished" until Charlie submits
```

**Key Features**:
- Role: Team leads create/submit individually, everyone votes collectively
- Sophisticated multi-user submission workflow with collective team completion tracking
- Individual idea ownership with team-level completion requirements
- Comprehensive voting with scores and comments
- Session-based organization with advanced finished team tracking logic

### ⚡ **Quickfire System** (Admin-Driven → Instant Voting)  
**Tables**: `quickfires` → `quickfireVotes`
**Workflow**:
1. Admins create quickfire items (questions/ideas)
2. Admins activate quickfires during Quickfire sessions
3. Everyone votes on active quickfires one-by-one
4. Real-time voting results and statistics

**Key Features**:
- Role: Admins create/activate, everyone votes
- No submission workflow - direct voting
- Simple scoring system for rapid feedback
- Order-based presentation with voting activation

### 🌟 **Sparks System** (Structured Data Collection)
**Tables**: `sparks` → `sparkSubmissions`
**Workflow**:
1. Admins create spark form configurations (fields, types, validation)
2. Team leads fill out spark forms during Sparks sessions  
3. Data collected and organized by teams
4. No voting - pure data collection and analysis

**Key Features**:
- Role: Admins create forms, team leads submit data
- Dynamic form builder with multiple field types
- Structured data collection and reporting
- No voting mechanism - submission only

## Key Workflows
1. Users register and await admin approval (when auto approval is off in settings)
2. Admins create events, sessions, teams and assign users to teams
3. **Ideas Sessions**: Team leads submit ideas → teams complete submissions → everyone votes
4. **Quickfire Sessions**: Admins create/activate quickfires → everyone votes in real-time
5. **Sparks Sessions**: Admins create forms → team leads submit structured data
6. Leaderboards track performance across Ideas and Quickfire sessions
7. Admins manage all session types but don't participate in voting
8. Data and workflows are completely separate between the three systems

## Development Commands
- `bun run dev` - Start development server (NEVER RUN IT - NOT ALLOWED)
- `bun run build` - Build production version (NEVER RUN IT - NOT ALLOWED)
- `bun run start` - Start production server (NEVER RUN IT - NOT ALLOWED)
- `bun run lint` - Run ESLint (ALLOWED)
- `bun tsc --noEmit` - Run TypeScript type checking (ALLOWED)


## Project Architecture

# Technology Stack

## Core Framework
- **Next.js 15.3.4**: React framework with App Router, TypeScript, and standalone output
- **React 19**: Latest React with concurrent features
- **TypeScript 5**: Strict type checking enabled

## Backend & Database
- **Convex 1.25.2**: Real-time backend-as-a-service with TypeScript
- **NextAuth 4.24.11**: Authentication with credential provider
- **bcryptjs**: Password hashing

## UI & Styling
- **Tailwind CSS 4**: Utility-first CSS framework with custom theme
- **Radix UI**: Headless component primitives for accessibility
- **Shadcn UI**: Component library with custom theme
- **Lucide React**: Icon library
- **Motion 12**: Animation library
- **Custom fonts**: Gotham and Neue Machina font families

## Rich Text & Content
- **Tiptap**: Rich text editor with extensions
- **AI SDK**: OpenAI integration for content generation
- **Markdown support**: markdown-to-jsx, react-markdown

## Development Tools
- **ESLint 9**: Code linting with Next.js config
- **Sentry**: Error monitoring and performance tracking
- **Docker**: Containerization support


### Core Architecture

#### App Structure (Next.js App Router)
- `/admin` - Admin dashboard for managing events, users, teams, voting
- `/user` - User interface for idea submission, voting, and sparks
- `/leaderboard` - Public leaderboards (all sessions and by session)
- `/print` - Print-optimized views for teams and sessions

#### Database Schema (Convex)
**Core Infrastructure Tables:**
- `users` - User accounts with role-based access (admin, teamLead, teamMember)
- `events` - Event containers for organizing sessions
- `teams` - Team organization within events
- `sessions` - Session management with different types (Ideas, Sparks, Quickfire)

**Three Separate Data Systems:**

**Ideas System Tables:**
- `ideas` - Individual team lead submitted ideas with presentation details (separate per `userId`)
- `votes` - Voting on ideas with scores (0-10) and comments

**Sparks System Tables:**
- `sparks` - Dynamic form configurations with field definitions
- `sparkSubmissions` - User responses to spark forms

**Quickfire System Tables:**
- `quickfires` - Admin-created quickfire items with voting control
- `quickfireVotes` - Real-time voting on quickfire items

#### Authentication Flow
- Custom credentials provider validates against Convex users table
- Password hashing with bcrypt
- Role-based access control (admin, teamLead, teamMember)
- Event-based user registration validation
- Session management with JWT tokens

#### Key Features
- **Real-time updates**: Convex provides live data synchronization
- **Role-based access**: Different interfaces for admin vs users
- **Event-driven**: All content organized around active events
- **Team-based**: Users assigned to teams within events
- **Voting system**: Scoring system for ideas with comments
- **Sparks**: Dynamic forms for collecting structured data
- **Presence tracking**: Real-time user online status

### File Organization

#### Frontend (`src/`)
- `app/` - Next.js app router pages and layouts
- `components/` - Reusable UI components
  - `ui/` - Base UI components (buttons, inputs, etc.)
  - `providers/` - Context providers for auth, theme, Convex
  - `leaderboard/` - Leaderboard-specific components
  - `teams/` - Team management components
- `hooks/` - Custom React hooks
- `lib/` - Utility functions and configurations
- `types/` - TypeScript type definitions

#### Backend (`convex/`)
- `schema.ts` - Database schema definitions
- Individual files for each table operations (users.ts, events.ts, etc.)
- `lib/` - Shared backend utilities

### Environment Variables Required
- `NEXTAUTH_SECRET` - NextAuth.js secret key
- `CONVEX_SELF_HOSTED_URL` - Convex backend URL
- `SENTRY_*` - Sentry configuration for error monitoring

### Development Notes
- Uses Bun as package manager
- TypeScript strict mode enabled
- Source maps configured for Sentry
- PWA-ready with manifest.json
- Docker containerization support
- Sentry error monitoring integrated

### Key Patterns
- Server components for initial data fetching
- Client components for interactive features
- Convex queries and mutations for data operations
- Role-based component rendering
- Error boundaries for graceful error handling
- Real-time subscriptions for live updates

## Important Implementation Details

### Convex Integration
- Self-hosted Convex instance
- All data operations go through Convex API
- Real-time subscriptions for live updates
- Optimistic updates for better UX

### Authentication
- NextAuth.js with custom credentials provider
- Users must be approved by admin before login
- Role-based access control throughout app
- Event-based user registration validation

### State Management
- Convex handles server state
- React hooks for component state
- Context providers for global state (auth, theme)

### UI Components
- Radix UI primitives with custom styling
- Tailwind CSS for utility-first styling
- Dark/light theme support
- Responsive design patterns


## Key References:
 - [Convex Best Practices 2025](Context/Docs/Convex-best-Practices-2025.md)
 - [Convex functions Map guide](Context/Maps/ConvexMap.md) __Remember to update when updating functions__
 - [Convex Helpers Map guide](Context/Maps/ConvexHelpersMap.md) __Remember to update when updating helper functions__
 - [Components Convex Relationship guide](Context/Maps/components-convex-relationship.md) __Remember to update when updating components__
 - [LionX Component-Convex Reference guide](Context/Maps/LionX-Component-Convex-Reference.md) __Remember to update when updating components__
 - [Components Map guide](Context/Maps/ComponentsMap.md) __Remember to update when updating components__

## Database Best Practices & Code Standards

When working with this codebase, **ALWAYS** follow these critical best practices:

### 🔥 MANDATORY Database Patterns
1. **Use Helper Functions**: NEVER write direct `ctx.db.query()` calls in main functions. Always use helper functions from `convex/lib/` directories.
2. **Leverage Optimized Indexes**: Use compound indexes and optimized query patterns from helper functions.
3. **Batch Operations**: Use `batchLoad*` functions to eliminate N+1 queries.
4. **Standardized Responses**: Use `responseUtils.ts` for consistent API responses.
5. **Avoid .filter() on Database Queries**: Replace `.filter()` with `.withIndex()` conditions or filter in TypeScript code for better performance.
6. **Use .collect() Sparingly**: Only use `.collect()` with small result sets (<1000 docs). Use limits, or indexes for larger datasets.

### 📋 Required Helper Usage

**Core Infrastructure Helpers:**
- **Users**: Use `userHelpers.ts` functions (e.g., `getUserById`, `batchLoadUsers`)
- **Teams**: Use `teamHelpers.ts` functions (e.g., `getTeamById`, `batchLoadTeams`)
- **Sessions**: Use `sessionHelpers.ts` functions (e.g., `getSessionById`, `getActiveSession`, `updateSession`)
- **Events**: Use `eventHelpers.ts` functions (e.g., `getActiveEvent`, `validateEventExists`)
- **Settings**: Use `settingsHelpers.ts` functions (e.g., `getSettingByKey`, `getAutoApprovalStatus`)

**System-Specific Helpers:**

**Ideas System:**
- **Ideas**: Use `ideaHelpers.ts` functions (e.g., `getIdeasByUserAndSession`, `getIdeasByTeamAndSession`)
  - **Individual Ownership**: Ideas are stored per team lead (`userId` field)
  - **Team Completion**: Use team-level queries to check if all team leads have submitted all their ideas
- **Votes**: Use `voteHelpers.ts` functions (e.g., `getVotesByIdea`, `upsertVote`, `batchLoadVotesByIdea`)

**Sparks System:**
- **Sparks**: Use `sparkHelpers.ts` functions (e.g., `createSparkHelper`, `updateSparkHelper`, `getAllSparks`)
- **Spark Submissions**: Use enrichment functions from `sparkHelpers.ts` and `enrichmentHelpers.ts`

**Quickfire System:**
- **Quickfires**: Use `quickfireHelpers.ts` functions (e.g., `getQuickfireById`, `createQuickfire`)
- **Quickfire Votes**: Use `voteHelpers.ts` functions (e.g., `upsertQuickfireVote`, `getQuickfireVotesByQuickfire`)

### 🚫 Anti-Patterns to AVOID
- Direct database queries in main functions
- N+1 query patterns
- Using `.filter()` on database queries instead of indexes
- Using `.collect()` on potentially large result sets
- Inconsistent error handling
- Missing batch operations
- Hardcoded database field access
- Sequential `ctx.runMutation`/`ctx.runQuery` calls from actions

### ✅ Code Quality Standards
- Use TypeScript strict mode
- Follow existing authentication patterns with `auth.ts`
- Implement proper error boundaries
- Use enrichment functions for data relationships
- Follow modular architecture patterns (see users.ts refactoring)

### 🔧 Performance Requirements
- Always use compound indexes when filtering by multiple fields
- Implement batch loading for related data
- Use helper functions for consistent query optimization
- Follow established patterns for data enrichment
- Replace `.filter()` with `.withIndex()` for better performance
- Use `.take()` or pagination instead of `.collect()` for large datasets
- Use single `ctx.runMutation`/`ctx.runQuery` calls instead of sequential calls

When working with this codebase, always consider the real-time nature of the application and the role-based access patterns. Test changes across different user roles and ensure proper error handling for network issues.
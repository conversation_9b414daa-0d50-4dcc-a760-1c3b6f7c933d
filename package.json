{"name": "lionx", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "dev:turbo": "next dev --turbopack", "build": "next build", "build:turbo": "next build --turbopack", "start": "next start", "lint": "next lint"}, "dependencies": {"@ai-sdk/openai": "^1.3.16", "@radix-ui/react-accordion": "^1.2.11", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-collapsible": "^1.1.11", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slider": "^1.3.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tooltip": "^1.2.7", "@tiptap/extension-bubble-menu": "^2.11.7", "@tiptap/extension-highlight": "^2.11.7", "@tiptap/extension-placeholder": "^2.14.0", "@tiptap/extension-text-align": "^2.11.7", "@tiptap/html": "^2.11.7", "@tiptap/pm": "^2.11.7", "@tiptap/react": "^2.11.7", "@tiptap/starter-kit": "^2.11.7", "@types/adm-zip": "^0.5.7", "@types/archiver": "^6.0.3", "@types/lru-cache": "^7.10.9", "@types/marked": "^5.0.2", "adm-zip": "^0.5.16", "ai": "^4.3.9", "archiver": "^7.0.1", "bcryptjs": "^3.0.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "convex": "^1.25.2", "date-fns": "^4.1.0", "lru-cache": "^11.0.2", "lucide-react": "^0.517.0", "markdown-to-jsx": "^7.7.8", "marked": "^15.0.12", "next": "15.4", "next-auth": "^4.24.11", "next-themes": "^0.4.6", "qrcode.react": "^4.2.0", "react": "^19.1.0", "react-best-gradient-color-picker": "^3.0.14", "react-dom": "^19.1.0", "react-error-boundary": "^6.0.0", "react-icons": "^5.4.0", "react-markdown": "^10.1.0", "sonner": "^2.0.3", "tailwind-merge": "^3.3.1", "ts-pattern": "^5.7.0", "unzipper": "^0.12.3"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/lodash": "^4.17.15", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@types/webpack": "^5.28.5", "eslint": "^9", "eslint-config-next": "15.1.6", "tailwindcss": "^4", "tw-animate-css": "^1.3.4", "typescript": "^5"}, "packageManager": "yarn@1.22.22+sha512.a6b2f7906b721bba3d67d4aff083df04dad64c399707841b7acf00f6b133b7ac24255f2652fa22ae3534329dc6180534e98d17432037ff6fd140556e2bb3137e"}